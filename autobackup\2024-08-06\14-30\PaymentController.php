<?php
/**
 * Payment Controller
 * 
 * This controller handles all payment-related functionality.
 */
class PaymentController extends Controller {
    private $paymentModel;
    private $showModel;
    private $registrationModel;
    private $userModel;
    private $auth;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->auth = new Auth();
        
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $this->paymentModel = $this->model('PaymentModel');
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->userModel = $this->model('UserModel');
        
        // Include the PaymentMethodsHelper
        require_once APPROOT . '/models/PaymentMethodsHelper.php';
    }
    
    // ... [other methods] ...
    
    /**
     * Coordinator payment settings
     */
    public function coordinatorSettings() {
        // Check if user is coordinator or admin
        if (!$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $userId = $this->auth->getCurrentUserId();
        $isAdmin = $this->auth->hasRole('admin');
        
        // Debug mode - only visible to admins
        $debug = isset($_GET['debug']) && $_GET['debug'] == '1' && $isAdmin;
        
        if ($debug) {
            echo "<h2>Debug Information</h2>";
            echo "<p>User ID: {$userId}</p>";
            echo "<p>Is Admin: " . ($isAdmin ? 'Yes' : 'No') . "</p>";
        }
        
        // Always get coordinator payment settings, regardless of who is viewing
        if ($debug) echo "<p>Getting coordinator settings (is_admin = 0)...</p>";
        
        // Get the settings directly from the database to bypass any potential caching
        $db = new Database();
        $db->query('SELECT * FROM payment_settings WHERE is_admin = :is_admin ORDER BY setting_key');
        $db->bind(':is_admin', 0); // 0 for coordinator settings
        $results = $db->resultSet();
        
        $paymentSettings = [];
        foreach ($results as $result) {
            $paymentSettings[$result->setting_key] = $result->setting_value;
            
            if ($debug) {
                $value = $result->setting_key == 'paypal_secret' ? '********' : $result->setting_value;
                echo "<p>Setting: {$result->setting_key} = {$value}</p>";
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            if ($debug) {
                echo "<h3>POST Data</h3>";
                echo "<pre>" . print_r($_POST, true) . "</pre>";
            }
            
            // Update coordinator payment settings
            $coordinatorSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($coordinatorSettings as $setting) {
                if (isset($_POST[$setting])) {
                    if ($debug) echo "<p>Updating setting: {$setting}</p>";
                    $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting], false);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Coordinator default payment settings have been updated successfully', 'success');
            $this->redirect('payment/coordinatorSettings');
        } else {
            // Display settings form
            $data = [
                'title' => 'Coordinator Default Payment Settings',
                'payment_settings' => $paymentSettings,
                'is_admin_view' => $isAdmin,
                'debug_mode' => $debug
            ];
            
            if ($debug) {
                echo "<h3>Data being passed to view</h3>";
                echo "<pre>" . print_r($data, true) . "</pre>";
                echo "<hr>";
            }
            
            $this->view('coordinator/default_payment_settings', $data);
        }
    }
}
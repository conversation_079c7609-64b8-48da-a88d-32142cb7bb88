/**
 * PWA Features CSS for Rowan Elite Rides Events & Shows
 * Styles for PWA-specific components and enhanced mobile features
 * 
 * CAMERA LAYERING FIX APPLIED:
 * - Camera modal z-index increased from 1070 to 999998
 * - QR scanner modal z-index increased from 1070 to 999998  
 * - Video elements (#camera-video, #qr-video) z-index set to 999999
 * - This ensures camera video renders above navigation elements (z-index: 999999)
 */

/* PWA Install Button */
.pwa-install-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    background: #FFD700 !important;
    color: #1338BE !important;
    border: 2px solid #1338BE !important;
    border-radius: 50px !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4) !important;
    transition: all 0.3s ease;
    display: none;
}

.pwa-install-btn:hover {
    transform: translateY(-2px);
    background: #FFC107 !important;
    color: #1338BE !important;
    border-color: #1338BE !important;
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.6) !important;
}

.pwa-install-btn i {
    margin-right: 8px;
}

/* Online Status Indicator */
.online-indicator {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1060;
    transition: all 0.3s ease;
}

.online-indicator.online {
    background: #28a745;
    color: white;
}

.online-indicator.offline {
    background: #1338BE;
    color: white;
    animation: pulse 2s infinite;
}

/* Notification Prompt */
.notification-prompt {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1055;
    margin: 0;
    border-radius: 0;
    border: none;
    border-bottom: 3px solid #1338BE;
}

/* Update Banner */
.update-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1055;
    margin: 0;
    border-radius: 0;
    border: none;
    border-bottom: 3px solid #17a2b8;
}

/* Toast Container */
.toast-container {
    z-index: 1060;
}

.toast {
    min-width: 300px;
    margin-bottom: 10px;
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.toast-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
}

.toast-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

.toast-error {
    background: linear-gradient(135deg, #1338BE, #0f2a9a);
    color: white;
}

.toast-header {
    background: transparent;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: inherit;
}

.toast-body {
    color: inherit;
}

/* Camera modal backdrop to block page content */
.camera-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 1.0);
    z-index: 999997;
    pointer-events: none;
}

/* Camera Modal - Portrait layout with banner space */
.camera-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background:
        linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.95) 13%,
            rgba(0, 0, 0, 0.1) 13%,
            rgba(0, 0, 0, 0.1) 85%,
            rgba(0, 0, 0, 0.95) 85%,
            rgba(0, 0, 0, 0.95) 100%);
    z-index: 999998;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    touch-action: none;
}

.camera-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Camera banner at top */
.camera-banner {
    height: 13vh;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.camera-banner img {
    height: 95%;
    width: auto;
    object-fit: contain;
}

.camera-banner .banner-text {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 10px;
}

/* Logo banner specific styling */
.camera-banner .logo-banner {
    height: 95%;
    width: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* Camera viewfinder area */
.camera-viewfinder {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#camera-video {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 10px;
    max-width: 500px;
}

.camera-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 20px;
}

.camera-controls .btn {
    border-radius: 50px;
    padding: 12px 24px;
    font-weight: 600;
    min-width: 120px;
    font-size: 1.1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.camera-controls .btn:hover,
.camera-controls .btn:focus {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.camera-controls .btn-danger {
    background: linear-gradient(135deg, #1338BE, #0f2a9a);
    border: none;
}

.camera-controls .btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

/* QR Scanner Modal - Portrait layout with banner space */
.qr-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background:
        linear-gradient(to bottom,
            rgba(0, 0, 0, 0.95) 0%,
            rgba(0, 0, 0, 0.95) 13%,
            rgba(0, 0, 0, 0.1) 13%,
            rgba(0, 0, 0, 0.1) 85%,
            rgba(0, 0, 0, 0.95) 85%,
            rgba(0, 0, 0, 0.95) 100%);
    z-index: 999998;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    touch-action: none;
}

.qr-scanner-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* QR Scanner banner at top */
.qr-banner {
    height: 13vh;
    width: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.qr-banner img {
    height: 95%;
    width: auto;
    object-fit: contain;
}

.qr-banner .banner-text {
    color: white;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 10px;
}

/* Logo banner specific styling for QR */
.qr-banner .logo-banner {
    height: 95%;
    width: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* QR Scanner viewfinder area */
.qr-viewfinder {
    flex: 1;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#qr-video {
    width: 100%;
    height: 70%;
    object-fit: cover;
    border-radius: 10px;
    max-width: 400px;
}

.qr-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 250px;
    height: 250px;
    pointer-events: none;
}

.qr-target {
    width: 100%;
    height: 100%;
    border: 3px solid #00FF00;
    border-radius: 10px;
    position: relative;
    animation: qr-scan 2s infinite;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
}

.qr-target::before,
.qr-target::after {
    content: '';
    position: absolute;
    width: 30px;
    height: 30px;
    border: 4px solid #00FF00;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.7);
}

.qr-target::before {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
}

.qr-target::after {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
}

@keyframes qr-scan {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.qr-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}



.qr-instructions {
    color: white;
    margin-top: 15px;
    font-size: 1.1rem;
}

/* Enhanced Mobile Navigation */
.mobile-nav-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    border-top: 2px solid #1338BE;
    padding: 10px 0;
    z-index: 1050;
    display: none;
}

.mobile-nav-bottom.show {
    display: block;
}

.mobile-nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    max-width: 500px;
    margin: 0 auto;
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #cccccc;
    transition: all 0.3s ease;
    padding: 8px 12px;
    border-radius: 10px;
    min-width: 60px;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
    color: #1338BE;
    background: rgba(19, 56, 190, 0.1);
    transform: translateY(-2px);
}

.mobile-nav-item i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.mobile-nav-item span {
    font-size: 0.7rem;
    font-weight: 600;
}

/* Quick Actions Floating Button */
.quick-actions-fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 1049;
}

.fab-main {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    transition: all 0.3s ease;
    cursor: pointer;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
}

.fab-actions {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.fab-actions.show {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.fab-action {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #ffffff;
    border: 2px solid #1338BE;
    color: #1338BE;
    font-size: 1.2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fab-action:hover {
    background: #1338BE;
    color: white;
    transform: scale(1.1);
}

/* Offline Queue Indicator */
.offline-queue {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: #ffc107;
    color: #212529;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    z-index: 1050;
    display: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.offline-queue:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.offline-queue.show {
    display: block;
}

.offline-queue i {
    margin-right: 6px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Biometric Login Button */
.biometric-login {
    background: linear-gradient(135deg, #6f42c1, #5a32a3);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.biometric-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(111, 66, 193, 0.3);
}

.biometric-login i {
    margin-right: 8px;
}

/* Share Button */
.share-button {
    background: #17a2b8;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.share-button:hover {
    background: #138496;
    transform: translateY(-1px);
}

.share-button i {
    margin-right: 6px;
}

/* PWA Splash Screen Styles */
@media (display-mode: standalone) {
    body {
        padding-top: env(safe-area-inset-top);
        padding-bottom: env(safe-area-inset-bottom);
    }
    
    .mobile-nav-bottom.show {
        padding-bottom: calc(10px + env(safe-area-inset-bottom));
    }
    
    .pwa-install-btn {
        display: none !important;
    }
}

/* Desktop Design - Hide FAB on larger screens */
@media (min-width: 992px) {
    .quick-actions-fab {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-nav-bottom {
        display: block;
    }
    
    .pwa-install-btn {
        bottom: 90px;
    }
    
    .quick-actions-fab {
        bottom: 150px;
    }
    
    .toast {
        min-width: 280px;
        margin: 5px;
    }
    
    .camera-controls,
    .qr-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .camera-controls .btn,
    .qr-controls .btn {
        min-width: 100px;
    }
}

@media (max-width: 480px) {
    .mobile-nav-item {
        min-width: 50px;
        padding: 6px 8px;
    }
    
    .mobile-nav-item i {
        font-size: 1rem;
    }
    
    .mobile-nav-item span {
        font-size: 0.6rem;
    }
    
    .fab-main {
        width: 48px;
        height: 48px;
        font-size: 1.3rem;
    }
    
    .fab-action {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .toast-warning {
        background: linear-gradient(135deg, #856404, #6c5300);
        color: white;
    }
    
    .fab-action {
        background: #2d2d2d;
        border-color: #dc3545;
        color: #dc3545;
    }

    .fab-action:hover {
        background: #dc3545;
        color: white;
    }
}

/* Print Styles */
@media print {
    .pwa-install-btn,
    .online-indicator,
    .notification-prompt,
    .update-banner,
    .mobile-nav-bottom,
    .quick-actions-fab,
    .offline-queue,
    .toast-container {
        display: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .pwa-install-btn,
    .fab-main {
        border: 2px solid currentColor;
    }
    
    .mobile-nav-item:hover,
    .mobile-nav-item.active {
        border: 1px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .pwa-install-btn,
    .fab-main,
    .fab-action,
    .mobile-nav-item,
    .toast,
    .offline-queue {
        transition: none;
    }
    
    .online-indicator.offline,
    .qr-target,
    .offline-queue i {
        animation: none;
    }
}

/* Facebook Login Button States */
#facebook-login-btn:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
}

#facebook-login-btn:disabled:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
}

#facebook-login-status {
    transition: all 0.3s ease;
    font-weight: 500;
}

#facebook-login-status.show {
    display: block !important;
}

/* Spinner animation for loading states */
.fa-spinner.fa-spin {
    animation: fa-spin 1s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
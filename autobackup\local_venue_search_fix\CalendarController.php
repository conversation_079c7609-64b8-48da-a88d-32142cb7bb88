Backup created during local venue search fix - v3.60.0
Date: 2025-01-27

Changes made:
1. Modified searchVenues method to handle local_only parameter
2. Added local venue search functionality that searches calendar_venues table
3. Updated view files to send local_only=true parameter for form venue searches
4. Fixed CSRF token validation for AJAX requests with header-based tokens
5. Enhanced venue search to include address column
6. Added automatic address field population using standard field names: address1, address2, city, state, zipcode
7. Updated clearVenueSelection functions to clear address fields when venue is deselected

Files modified:
- controllers/CalendarController.php (searchVenues method)
- models/CalendarModel.php (searchVenues method - added address search)
- helpers/csrf_helper.php (CSRF token validation and header handling)
- views/user/create_show.php (selectVenue and clearVenueSelection functions)
- views/calendar/create_event.php (already had address population)
- views/calendar/edit_event.php (already had address population)
- views/coordinator/edit_show.php (added address population)
- views/admin/shows/add_with_template.php (added address population)
- views/admin/shows/edit_with_template.php (added address population)

Standard field names used for address population:
- address1: venue.address
- address2: cleared (not provided by venue data)
- city: venue.city
- state: venue.state
- zipcode: venue.zip
<?php
/**
 * Test camera APIs to see if they're working now
 */

session_start();

echo "<h2>Camera API Tests</h2>";

// Test 1: Basic camera banners API
echo "<h3>1. Testing camera-banners.php:</h3>";
$url1 = 'https://events.rowaneliterides.com/api/camera-banners.php';
$response1 = file_get_contents($url1);
echo "<strong>Response:</strong><br>";
echo "<pre>" . htmlspecialchars($response1) . "</pre>";

echo "<hr>";

// Test 2: Camera banner settings API
echo "<h3>2. Testing camera-banner-settings.php:</h3>";
$url2 = 'https://events.rowaneliterides.com/api/camera-banner-settings.php';
$response2 = file_get_contents($url2);
echo "<strong>Response:</strong><br>";
echo "<pre>" . htmlspecialchars($response2) . "</pre>";

echo "<hr>";

// Test 3: Camera banners admin API (requires login)
echo "<h3>3. Testing camera-banners-admin.php:</h3>";
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . session_name() . '=' . session_id()
    ]
]);

$url3 = 'https://events.rowaneliterides.com/api/camera-banners-admin.php';
$response3 = file_get_contents($url3, false, $context);
echo "<strong>Response:</strong><br>";
echo "<pre>" . htmlspecialchars($response3) . "</pre>";

echo "<hr>";

// Show session info
echo "<h3>Current Session:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Self-delete
unlink(__FILE__);
?>
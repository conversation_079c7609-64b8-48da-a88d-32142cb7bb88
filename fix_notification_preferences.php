<?php
echo "🔧 Fixing notification_preferences.php file...\n";

$sourceFile = 'd:/Downloads/events and shows/views/user/notification_preferences_fixed.php';
$targetFile = 'd:/Downloads/events and shows/views/user/notification_preferences.php';

if (!file_exists($sourceFile)) {
    echo "❌ Source file not found: $sourceFile\n";
    exit(1);
}

$content = file_get_contents($sourceFile);

if (file_put_contents($targetFile, $content)) {
    echo "✅ Successfully replaced notification_preferences.php\n";
    echo "✅ File now shows subscription management only\n";
    
    // Clean up
    unlink($sourceFile);
    echo "✅ Cleaned up temporary file\n";
    
    echo "\n🎉 DONE! Now /user/notification_preferences should show only subscriptions\n";
} else {
    echo "❌ Failed to write to target file\n";
}
?>
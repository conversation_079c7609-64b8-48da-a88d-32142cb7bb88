<?php
/**
 * Test script for the cleanup test users functionality
 * 
 * This script tests the fixed cleanup functionality by:
 * 1. Generating a small number of test users
 * 2. Running the cleanup function
 * 3. Verifying the results
 */

// Load configuration
require_once 'config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once 'core/Database.php';
require_once 'core/Auth.php';

// Include the test user generator
require_once 'database/test_data/generate_test_users.php';

echo "<h1>Test User Cleanup Functionality</h1>";
echo "<p>Testing the fixed cleanup functionality...</p>";

try {
    $generator = new TestUserGenerator();
    
    echo "<h2>Step 1: Generate a few test users</h2>";
    echo "<pre>";
    $generated = $generator->generateUsers(10, true);
    echo "</pre>";
    
    echo "<h2>Step 2: Run cleanup function</h2>";
    echo "<pre>";
    $deleted = $generator->cleanupTestUsers(true);
    echo "</pre>";
    
    echo "<h2>Results Summary</h2>";
    echo "<p><strong>Generated:</strong> {$generated} test users</p>";
    echo "<p><strong>Deleted:</strong> {$deleted} test users</p>";
    
    if ($deleted > 0) {
        echo "<p style='color: green;'><strong>✓ Cleanup functionality is working correctly!</strong></p>";
    } else {
        echo "<p style='color: orange;'><strong>⚠ No users were deleted. This might be expected if no test users matched the cleanup criteria.</strong></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>✗ Error occurred:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='database/test_data/generate_test_users.php'>← Back to Test User Generator</a></p>

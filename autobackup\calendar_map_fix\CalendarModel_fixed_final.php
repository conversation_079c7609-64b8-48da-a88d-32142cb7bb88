    public function getEventsWithLocation($filters = [])
    {
        try {
            // Let's use a completely different approach to avoid parameter numbering issues
            // We'll use a direct SQL query with concatenated values instead of prepared statements
            // for the IN clause, and named parameters for the rest
            
            $sql = "SELECT e.*, c.name as calendar_name, s.name as show_name
                    FROM calendar_events e
                    LEFT JOIN calendars c ON e.calendar_id = c.id
                    LEFT JOIN shows s ON e.show_id = s.id
                    WHERE 1=1";
            
            $params = [];
            
            // Filter by start date
            if (isset($filters['start'])) {
                $sql .= " AND e.end_date >= :start_date";
                $params[':start_date'] = $filters['start'];
            }
            
            // Filter by end date
            if (isset($filters['end'])) {
                $sql .= " AND e.start_date <= :end_date";
                $params[':end_date'] = $filters['end'];
            }
            
            // Filter by calendar ID
            if (isset($filters['calendar_id'])) {
                $sql .= " AND e.calendar_id = :single_calendar_id";
                $params[':single_calendar_id'] = $filters['calendar_id'];
            }
            
            // Filter by multiple calendar IDs - use direct SQL concatenation for the IN clause
            if (isset($filters['calendar_ids']) && is_array($filters['calendar_ids']) && !empty($filters['calendar_ids'])) {
                // Sanitize the calendar IDs (ensure they're all integers)
                $calendarIds = array_map(function($id) {
                    return (int)$id;
                }, $filters['calendar_ids']);
                
                // Join them with commas for the IN clause
                $calendarIdsStr = implode(',', $calendarIds);
                
                // Add directly to the SQL (safe because we've sanitized the values)
                $sql .= " AND e.calendar_id IN ($calendarIdsStr)";
            }
            
            // Filter by state
            if (isset($filters['state'])) {
                $sql .= " AND e.state = :state";
                $params[':state'] = $filters['state'];
            }
            
            // Location-based filtering using Haversine formula
            if (isset($filters['radius']) && isset($filters['lat']) && isset($filters['lng'])) {
                // Earth's radius in miles
                $earthRadius = 3959;
                
                // Use different parameter names to avoid any confusion
                $sql .= " AND (
                    $earthRadius * acos(
                        cos(radians(:lat_param)) * 
                        cos(radians(e.lat)) * 
                        cos(radians(e.lng) - radians(:lng_param)) + 
                        sin(radians(:lat_param2)) * 
                        sin(radians(e.lat))
                    ) <= :radius_param
                )";
                
                $params[':lat_param'] = $filters['lat'];
                $params[':lng_param'] = $filters['lng'];
                $params[':lat_param2'] = $filters['lat'];
                $params[':radius_param'] = $filters['radius'];
            }
            
            // Only include events with location data
            $sql .= " AND (e.address1 IS NOT NULL OR e.city IS NOT NULL OR e.state IS NOT NULL)";
            
            // Order by start date
            $sql .= " ORDER BY e.start_date ASC";
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::getEventsWithLocation - SQL: " . $sql);
                error_log("CalendarModel::getEventsWithLocation - Params: " . json_encode($params));
            }
            
            $this->db->query($sql);
            
            // Bind all parameters
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            $results = $this->db->resultSet();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::getEventsWithLocation - Found " . count($results) . " events");
            }
            
            return $results;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsWithLocation: ' . $e->getMessage());
            throw $e;
        }
    }
<?php
/**
 * Quick Database Analysis
 * 
 * This script provides a quick overview of your database usage
 * without the detailed HTML output.
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "=== Quick Database Analysis ===\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";

// Get all tables
$tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
echo "Total tables in database: " . count($tables) . "\n";

// Analyze table usage in PHP files
$usedTables = [];
$directories = ['controllers', 'models', 'views'];
$patterns = [
    '/FROM\s+`?(\w+)`?/i',
    '/JOIN\s+`?(\w+)`?/i',
    '/INSERT\s+INTO\s+`?(\w+)`?/i',
    '/UPDATE\s+`?(\w+)`?/i'
];

foreach ($directories as $dir) {
    $fullPath = APPROOT . '/' . $dir;
    if (is_dir($fullPath)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($fullPath));
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                
                foreach ($patterns as $pattern) {
                    if (preg_match_all($pattern, $content, $matches)) {
                        foreach ($matches[1] as $tableName) {
                            $tableName = trim($tableName, '`');
                            if (in_array($tableName, $tables)) {
                                $usedTables[$tableName] = true;
                            }
                        }
                    }
                }
            }
        }
    }
}

$usedTableNames = array_keys($usedTables);
$unusedTables = array_diff($tables, $usedTableNames);

echo "Tables used in PHP code: " . count($usedTableNames) . "\n";
echo "Potentially unused tables: " . count($unusedTables) . "\n\n";

echo "=== USED TABLES ===\n";
foreach ($usedTableNames as $table) {
    $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
    echo "✅ $table ($count rows)\n";
}

if (!empty($unusedTables)) {
    echo "\n=== POTENTIALLY UNUSED TABLES ===\n";
    foreach ($unusedTables as $table) {
        try {
            $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
            echo "❌ $table ($count rows) - Not found in PHP code\n";
        } catch (Exception $e) {
            echo "❌ $table (Error accessing) - Not found in PHP code\n";
        }
    }
}

echo "\n=== RECOMMENDATIONS ===\n";

if (count($unusedTables) > 0) {
    echo "1. Review the " . count($unusedTables) . " unused tables listed above\n";
    echo "2. If they contain no important data, consider removing them\n";
    echo "3. This will simplify your database and improve performance\n";
} else {
    echo "1. All tables are being used - good database hygiene!\n";
}

echo "4. Run the full analysis for detailed relationship mapping:\n";
echo "   http://yoursite.com/analyze_actual_database_usage.php\n";
echo "5. Add foreign key constraints only for the used tables:\n";
echo "   mysql < sql/improve_actual_database_relationships.sql\n";

echo "\n=== SUMMARY ===\n";
$usagePercent = round((count($usedTableNames) / count($tables)) * 100, 1);
echo "Database efficiency: $usagePercent% of tables are actively used\n";

if ($usagePercent < 70) {
    echo "⚠️  Consider database cleanup - many unused tables detected\n";
} elseif ($usagePercent < 90) {
    echo "✅ Good database usage with some cleanup opportunities\n";
} else {
    echo "🎉 Excellent database efficiency!\n";
}

echo "\nAnalysis completed at: " . date('Y-m-d H:i:s') . "\n";
?>
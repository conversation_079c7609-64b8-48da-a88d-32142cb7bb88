<?php
/**
 * Test Targeted Scoring Cleanup
 * 
 * This script tests the enhanced scoring cleanup to ensure it only removes
 * demo scoring data and protects real scoring data from legitimate shows.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

echo "<h1>Targeted Scoring Cleanup Test</h1>";
echo "<p>Testing the enhanced scoring cleanup to ensure it only removes demo data...</p>";

try {
    $db = new Database();
    $generator = new DemoDataGenerator();
    
    echo "<h2>Current Scoring Data in Database</h2>";
    
    // Check scoring tables
    $scoringTables = [
        'judge_total_scores',
        'judge_metric_scores', 
        'vehicle_total_scores',
        'vehicle_metric_scores',
        'fan_votes'
    ];
    
    $beforeCounts = [];
    foreach ($scoringTables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM {$table}");
            $result = $db->single();
            $beforeCounts[$table] = $result->count ?? 0;
        } catch (Exception $e) {
            $beforeCounts[$table] = 'N/A (table not found)';
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table</th><th>Total Records</th></tr>";
    foreach ($beforeCounts as $table => $count) {
        echo "<tr><td>{$table}</td><td>{$count}</td></tr>";
    }
    echo "</table>";
    
    // Check for demo shows
    $demoShowNames = [
        'Southern California Classic Car Showcase',
        'Miami Beach Exotic Car Festival',
        'Texas Muscle Car Madness',
        'Arizona Desert Classic Concours',
        'Atlanta Import Tuner Expo',
        'Rocky Mountain Vintage Rally',
        'Pacific Northwest Euro Fest',
        'Music City Hot Rod Nationals',
        'Charlotte Motor Speedway Car Show',
        'Las Vegas Strip Supercar Spectacular'
    ];
    
    echo "<h3>Demo Shows Analysis</h3>";
    $demoShowIds = [];
    $foundDemoShows = [];
    
    foreach ($demoShowNames as $showName) {
        $stmt = $db->getConnection()->prepare("SELECT id, name, coordinator_id FROM shows WHERE name = ?");
        $stmt->execute([$showName]);
        $result = $stmt->fetch(PDO::FETCH_OBJ);
        if ($result) {
            $demoShowIds[] = $result->id;
            $foundDemoShows[] = $result;
        }
    }
    
    if (!empty($foundDemoShows)) {
        echo "<p>Found " . count($foundDemoShows) . " demo shows:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Coordinator ID</th></tr>";
        foreach ($foundDemoShows as $show) {
            echo "<tr><td>{$show->id}</td><td>" . htmlspecialchars($show->name) . "</td><td>{$show->coordinator_id}</td></tr>";
        }
        echo "</table>";
        
        // Check scoring data for demo shows
        if (!empty($demoShowIds)) {
            $placeholders = str_repeat('?,', count($demoShowIds) - 1) . '?';
            
            echo "<h3>Scoring Data for Demo Shows</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Table</th><th>Demo Show Records</th></tr>";
            
            foreach ($scoringTables as $table) {
                try {
                    $stmt = $db->getConnection()->prepare("SELECT COUNT(*) as count FROM {$table} WHERE show_id IN ({$placeholders})");
                    $stmt->execute($demoShowIds);
                    $result = $stmt->fetch(PDO::FETCH_OBJ);
                    $demoCount = $result->count ?? 0;
                    
                    echo "<tr><td>{$table}</td><td>{$demoCount}</td></tr>";
                } catch (Exception $e) {
                    echo "<tr><td>{$table}</td><td>N/A</td></tr>";
                }
            }
            echo "</table>";
        }
        
    } else {
        echo "<p>✅ No demo shows found in database.</p>";
    }
    
    // Check for demo users
    $db->query("SELECT COUNT(*) as count FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
    $demoUserResult = $db->single();
    $demoUserCount = $demoUserResult->count ?? 0;
    
    echo "<h3>Demo Users</h3>";
    echo "<p>Found {$demoUserCount} demo users with test domains.</p>";
    
    // Show all shows for comparison
    echo "<h3>All Shows in Database</h3>";
    $db->query("SELECT id, name, coordinator_id, created_at FROM shows ORDER BY created_at DESC LIMIT 20");
    $allShows = $db->resultSet();
    
    if (!empty($allShows)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Coordinator ID</th><th>Created</th><th>Type</th></tr>";
        
        foreach ($allShows as $show) {
            $type = in_array($show->name, $demoShowNames) ? 
                '<span style="color: red; font-weight: bold;">DEMO</span>' : 
                '<span style="color: green;">REAL</span>';
            
            echo "<tr>";
            echo "<td>{$show->id}</td>";
            echo "<td>" . htmlspecialchars($show->name) . "</td>";
            echo "<td>{$show->coordinator_id}</td>";
            echo "<td>{$show->created_at}</td>";
            echo "<td>{$type}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test actions
    if (!empty($foundDemoShows) || $demoUserCount > 0) {
        echo "<h2>Test Actions</h2>";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>⚠️ Targeted Cleanup Available</strong><br>";
        echo "The cleanup will only remove scoring data from demo shows and demo judges, protecting real data.";
        echo "</div>";
        
        echo "<p><a href='?action=test_scoring_cleanup' class='btn btn-warning'>🎯 Test Targeted Scoring Cleanup</a></p>";
        echo "<p><a href='?action=safe_cleanup' class='btn btn-primary'>🛡️ Run Safe Cleanup</a></p>";
        
    } else {
        echo "<h2>Status</h2>";
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>✅ Clean Database!</strong> No demo shows or users detected.";
        echo "</div>";
    }
    
    echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Status</a></p>";
    
    // Handle test actions
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        if ($_GET['action'] === 'test_scoring_cleanup') {
            echo "<h2>Testing Targeted Scoring Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            // Get demo user IDs
            $db->query("SELECT id FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
            $demoUsers = $db->resultSet();
            $demoUserIds = array_column($demoUsers, 'id');
            
            // Use reflection to call the private methods for testing
            $reflection = new ReflectionClass($generator);
            
            $scoringMethod = $reflection->getMethod('cleanupDemoScoringData');
            $scoringMethod->setAccessible(true);
            $scoringDeleted = $scoringMethod->invoke($generator, $demoUserIds, true);
            
            $fanVotesMethod = $reflection->getMethod('cleanupDemoFanVotes');
            $fanVotesMethod->setAccessible(true);
            $fanVotesDeleted = $fanVotesMethod->invoke($generator, true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<h3>Cleanup Results</h3>";
            echo "<ul>";
            foreach ($scoringDeleted as $table => $count) {
                if ($count > 0) {
                    echo "<li><strong>{$table}:</strong> {$count} records deleted</li>";
                }
            }
            echo "<li><strong>fan_votes:</strong> {$fanVotesDeleted} records deleted</li>";
            echo "</ul>";
            
            echo "<p><a href='?'>← Back to Status</a></p>";
            
        } elseif ($_GET['action'] === 'safe_cleanup') {
            echo "<h2>Running Safe Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            $deletedCount = $generator->safeCleanupDemoData(true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Total {$deletedCount} demo records deleted safely</p>";
            echo "<p><a href='?'>← Back to Status</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 14px;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-warning { background-color: #ffc107; color: black; }

.btn:hover { opacity: 0.8; }
</style>

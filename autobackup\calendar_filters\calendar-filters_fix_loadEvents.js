/**
 * Calendar Advanced Filtering System - Fixed loadEvents function
 * 
 * This script provides advanced filtering functionality for the calendar views
 * (month, week, day, list, and map).
 * 
 * Fix: Added support for both refetchEvents and loadEvents methods to handle
 * different calendar implementations.
 */

// Original applyFilters function:
/*
function applyFilters() {
    // Initialize calendar filters first
    updateCalendarFilters();
    
    // Determine which view we're in and call the appropriate function
    if (activeFilters.currentView === 'map') {
        if (typeof loadEvents === 'function') {
            loadEvents();
        }
    } else {
        // For calendar views
        if (typeof calendar !== 'undefined' && typeof calendar.refetchEvents === 'function') {
            calendar.refetchEvents();
        }
    }
}
*/

// Fixed applyFilters function:
function applyFilters() {
    // Initialize calendar filters first
    updateCalendarFilters();
    
    // Determine which view we're in and call the appropriate function
    if (activeFilters.currentView === 'map') {
        if (typeof loadEvents === 'function') {
            loadEvents();
        }
    } else {
        // For calendar views - handle different calendar implementations
        if (typeof calendar !== 'undefined') {
            // Check which refresh method is available
            if (typeof calendar.refetchEvents === 'function') {
                // FullCalendar implementation
                calendar.refetchEvents();
            } else if (typeof calendar.loadEvents === 'function') {
                // CustomCalendar implementation
                calendar.loadEvents();
            } else {
                console.error('No compatible calendar refresh method found');
                // Fallback - reload the page
                window.location.reload();
            }
        } else {
            console.error('Calendar object not found');
        }
    }
}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Banner Fix Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .banner-container {
            width: 100%;
            height: 100px;
            background: black;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            border-radius: 5px;
            position: relative;
        }
        .debug-messages {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Camera Banner Fix Test</h1>
    
    <div class="test-container">
        <h3>Banner System Test:</h3>
        <p>This will test the fixed banner rotation system that should show logo first, then rotate banners.</p>
        
        <div id="status" class="status info">
            Initializing banner system...
        </div>
        
        <button onclick="testAPI()">Test API First</button>
        <button onclick="startBannerTest()">Start Banner Test</button>
        <button onclick="stopBannerTest()">Stop Test</button>
        <button onclick="clearDebug()">Clear Debug</button>
        
        <div id="banner-container" class="banner-container">
            Loading banner system...
        </div>
        
        <div id="debug-messages" class="debug-messages">
            Debug messages will appear here...
        </div>
    </div>

    <!-- Load the actual banner system -->
    <script>
        // Set BASE_URL for the banner system
        const BASE_URL = window.location.origin;
        
        // Enable debug mode for testing
        localStorage.setItem('camera_banner_debug', 'true');
        
        // Debug function
        function debug(message) {
            const debugDiv = document.getElementById('debug-messages');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function startBannerTest() {
            debug('🎬 Starting banner test - EXACTLY like camera modal...');
            updateStatus('Starting banner test...', 'info');
            
            if (window.cameraBanner) {
                debug('✅ CameraBanner instance found');
                debug('📊 Banner version: ' + window.cameraBanner.version);
                
                // FORCE reload banners from API to ensure fresh data
                debug('🔄 Force reloading banners from API...');
                updateStatus('Loading fresh banners from API...', 'info');
                
                window.cameraBanner.loadBanners().then(() => {
                    debug('✅ Banners loaded from API');
                    debug('📊 Total banners: ' + window.cameraBanner.banners.length);
                    debug('📋 Logo banners: ' + window.cameraBanner.banners.filter(b => b.is_logo).length);
                    debug('📋 Other banners: ' + window.cameraBanner.otherBanners.length);
                    
                    // Log first few banners for debugging
                    if (window.cameraBanner.banners.length > 0) {
                        debug('🔍 First banner: ' + JSON.stringify(window.cameraBanner.banners[0]));
                        if (window.cameraBanner.banners.length > 1) {
                            debug('🔍 Second banner: ' + JSON.stringify(window.cameraBanner.banners[1]));
                        }
                    }
                    
                    // Start rotation EXACTLY like camera modal does
                    debug('🚀 Starting rotation - should show logo first for 5 seconds');
                    window.cameraBanner.startRotation('banner-container');
                    updateStatus('Banner rotation started - Logo first, then database banners', 'success');
                    debug('🔄 Banner rotation started - watch for logo -> banners transition');
                    
                }).catch(error => {
                    debug('❌ Failed to load banners from API: ' + error.message);
                    updateStatus('Failed to load banners: ' + error.message, 'error');
                    
                    // Show what we have as fallback
                    if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                        debug('📦 Using fallback banners: ' + window.cameraBanner.banners.length);
                        window.cameraBanner.startRotation('banner-container');
                    }
                });
            } else {
                debug('❌ CameraBanner not found!');
                updateStatus('CameraBanner system not loaded', 'error');
            }
        }
        
        function stopBannerTest() {
            if (window.cameraBanner) {
                window.cameraBanner.stopRotation();
                debug('⏹️ Banner rotation stopped');
                updateStatus('Banner rotation stopped', 'info');
            }
        }
        
        function clearDebug() {
            document.getElementById('debug-messages').innerHTML = 'Debug messages cleared...<br>';
        }
        
        async function testAPI() {
            debug('🔍 Testing API endpoints directly...');
            updateStatus('Testing API endpoints...', 'info');
            
            try {
                // Test camera banners API
                debug('📡 Testing /api/cameraBanners...');
                const response = await fetch(BASE_URL + '/api/cameraBanners');
                debug('📊 API Response Status: ' + response.status);
                debug('📊 API Response OK: ' + response.ok);
                
                if (response.ok) {
                    const data = await response.json();
                    debug('✅ API Response Success: ' + data.success);
                    debug('📊 API Banner Count: ' + (data.banners ? data.banners.length : 0));
                    debug('⏱️ API Delay Setting: ' + data.delay + 'ms');
                    
                    if (data.banners && data.banners.length > 0) {
                        debug('🔍 First API Banner: ' + JSON.stringify(data.banners[0]));
                        debug('🔍 Logo Banners: ' + data.banners.filter(b => b.is_logo).length);
                        debug('🔍 Other Banners: ' + data.banners.filter(b => !b.is_logo).length);
                        updateStatus('API working - ' + data.banners.length + ' banners found', 'success');
                    } else {
                        debug('❌ API returned no banners');
                        updateStatus('API working but no banners found', 'error');
                    }
                } else {
                    debug('❌ API returned error status: ' + response.status);
                    const text = await response.text();
                    debug('❌ API Error Response: ' + text);
                    updateStatus('API error: ' + response.status, 'error');
                }
            } catch (error) {
                debug('❌ API Test Failed: ' + error.message);
                updateStatus('API test failed: ' + error.message, 'error');
            }
        }
        
        // Wait for banner system to load - EXACTLY like camera modal
        let checkCount = 0;
        const checkInterval = setInterval(() => {
            checkCount++;
            if (window.cameraBanner) {
                debug('✅ CameraBanner system loaded (attempt ' + checkCount + ')');
                debug('📊 Version: ' + window.cameraBanner.version);
                debug('🔧 Debug mode: ' + window.cameraBanner.isDebugMode());
                updateStatus('Banner system loaded - Ready to test', 'success');
                clearInterval(checkInterval);
                
                // Auto-start test after 2 seconds - Test API first, then banner system
                setTimeout(async () => {
                    debug('🚀 Auto-starting tests...');
                    await testAPI();
                    setTimeout(() => {
                        debug('🎬 Now starting banner test with fresh API data...');
                        startBannerTest();
                    }, 1000);
                }, 2000);
            } else if (checkCount > 20) {
                debug('❌ CameraBanner system failed to load after 20 attempts');
                updateStatus('Banner system failed to load', 'error');
                clearInterval(checkInterval);
            } else {
                debug('⏳ Waiting for CameraBanner system... (attempt ' + checkCount + ')');
            }
        }, 500);
    </script>
    
    <!-- Load the banner system -->
    <script src="/public/js/camera-banner.js"></script>
</body>
</html>
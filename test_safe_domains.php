<?php
/**
 * Test script to verify safe domain functionality
 * 
 * This script verifies that:
 * 1. Test users are generated with safe domains only
 * 2. Cleanup functions only target safe domains
 * 3. Real users with real domains are protected
 */

// Load configuration
require_once 'config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once 'core/Database.php';
require_once 'core/Auth.php';

// Include the test user generator
require_once 'database/test_data/generate_test_users.php';

echo "<h1>Safe Domain Verification Test</h1>";
echo "<p>This test verifies that the generator and cleanup functions use safe domains only.</p>";

try {
    $db = new Database();
    $generator = new TestUserGenerator();
    
    // Define safe and real domains for comparison
    $safeDomains = ['gmai1.com', 'yaho0.com', 'hotmai1.com', 'out1ook.com', 'test-example.com'];
    $realDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
    
    echo "<h2>Domain Configuration</h2>";
    echo "<p><strong>Safe Test Domains (used by generator):</strong> " . implode(', ', $safeDomains) . "</p>";
    echo "<p><strong>Real Domains (protected from cleanup):</strong> " . implode(', ', $realDomains) . "</p>";
    
    echo "<h2>Current Database State</h2>";
    
    // Check total users
    $db->query("SELECT COUNT(*) as count FROM users");
    $totalResult = $db->single();
    $totalUsers = $totalResult->count ?? 0;
    echo "<p><strong>Total users in database:</strong> {$totalUsers}</p>";
    
    // Check users with real domains
    $realDomainConditions = array_map(function($domain) {
        return "email LIKE '%@{$domain}'";
    }, $realDomains);
    $realDomainClause = "(" . implode(' OR ', $realDomainConditions) . ")";
    
    $db->query("SELECT COUNT(*) as count FROM users WHERE " . $realDomainClause);
    $realResult = $db->single();
    $realUsers = $realResult->count ?? 0;
    echo "<p><strong>Users with REAL domains:</strong> {$realUsers}</p>";
    
    // Check users with safe domains
    $safeDomainConditions = array_map(function($domain) {
        return "email LIKE '%@{$domain}'";
    }, $safeDomains);
    $safeDomainClause = "(" . implode(' OR ', $safeDomainConditions) . ")";
    
    $db->query("SELECT COUNT(*) as count FROM users WHERE " . $safeDomainClause);
    $safeResult = $db->single();
    $safeUsers = $safeResult->count ?? 0;
    echo "<p><strong>Users with SAFE test domains:</strong> {$safeUsers}</p>";
    
    if ($realUsers > 0) {
        echo "<div style='background: #ffffcc; padding: 10px; border: 1px solid #cccc00; margin: 10px 0;'>";
        echo "<strong>PROTECTION ACTIVE:</strong> {$realUsers} real users found with real email domains. ";
        echo "These users are PROTECTED and will NOT be affected by cleanup operations.";
        echo "</div>";
        
        // Show sample real users (first 3)
        echo "<h3>Sample Real Users (Protected)</h3>";
        $db->query("SELECT id, name, email FROM users WHERE " . $realDomainClause . " LIMIT 3");
        $realSamples = $db->resultSet();
        
        if (!empty($realSamples)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            foreach ($realSamples as $user) {
                echo "<tr>";
                echo "<td>{$user->id}</td>";
                echo "<td>{$user->name}</td>";
                echo "<td>{$user->email}</td>";
                echo "<td style='color: green;'><strong>PROTECTED</strong></td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    if ($safeUsers > 0) {
        echo "<h3>Sample Test Users (Safe to Delete)</h3>";
        $db->query("SELECT id, name, email FROM users WHERE " . $safeDomainClause . " LIMIT 5");
        $safeSamples = $db->resultSet();
        
        if (!empty($safeSamples)) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th></tr>";
            foreach ($safeSamples as $user) {
                echo "<tr>";
                echo "<td>{$user->id}</td>";
                echo "<td>{$user->name}</td>";
                echo "<td>{$user->email}</td>";
                echo "<td style='color: orange;'>Test User</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h2>Test Actions</h2>";
    echo "<p><a href='?action=generate_safe'>Generate 5 Test Users (Safe Domains)</a></p>";
    echo "<p><a href='?action=test_cleanup_simple'>Test Simple Cleanup (Safe Only)</a></p>";
    echo "<p><a href='?action=test_cleanup_pattern'>Test Pattern Cleanup (Safe Only)</a></p>";
    echo "<p><a href='?action=verify_protection'>Verify Real User Protection</a></p>";
    
    // Handle test actions
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        switch ($_GET['action']) {
            case 'generate_safe':
                echo "<h2>Generating 5 Test Users with Safe Domains</h2>";
                echo "<pre>";
                $generated = $generator->generateUsers(5, true);
                echo "</pre>";
                echo "<p><strong>Result:</strong> Generated {$generated} users with safe domains</p>";
                echo "<p><a href='?'>← Refresh to see updated counts</a></p>";
                break;
                
            case 'test_cleanup_simple':
                echo "<h2>Testing Simple Cleanup (Safe Domains Only)</h2>";
                echo "<pre>";
                $deleted = $generator->cleanupTestUsersSimple(true);
                echo "</pre>";
                echo "<p><strong>Result:</strong> Deleted {$deleted} test users</p>";
                echo "<p><a href='?'>← Refresh to see updated counts</a></p>";
                break;
                
            case 'test_cleanup_pattern':
                echo "<h2>Testing Pattern Cleanup (Safe Domains Only)</h2>";
                echo "<pre>";
                $deleted = $generator->cleanupTestUsers(true);
                echo "</pre>";
                echo "<p><strong>Result:</strong> Deleted {$deleted} test users</p>";
                echo "<p><a href='?'>← Refresh to see updated counts</a></p>";
                break;
                
            case 'verify_protection':
                echo "<h2>Verifying Real User Protection</h2>";
                
                // Count real users before
                $db->query("SELECT COUNT(*) as count FROM users WHERE " . $realDomainClause);
                $beforeResult = $db->single();
                $beforeCount = $beforeResult->count ?? 0;
                
                echo "<p><strong>Real users before cleanup test:</strong> {$beforeCount}</p>";
                
                // Run cleanup
                echo "<p>Running cleanup test...</p>";
                echo "<pre>";
                $deleted = $generator->cleanupTestUsersSimple(false); // Silent mode
                echo "</pre>";
                
                // Count real users after
                $db->query("SELECT COUNT(*) as count FROM users WHERE " . $realDomainClause);
                $afterResult = $db->single();
                $afterCount = $afterResult->count ?? 0;
                
                echo "<p><strong>Real users after cleanup test:</strong> {$afterCount}</p>";
                echo "<p><strong>Test users deleted:</strong> {$deleted}</p>";
                
                if ($beforeCount == $afterCount) {
                    echo "<div style='background: #ccffcc; padding: 10px; border: 1px solid #00cc00; margin: 10px 0;'>";
                    echo "<strong>✓ PROTECTION VERIFIED:</strong> All real users are safe! No real users were deleted.";
                    echo "</div>";
                } else {
                    echo "<div style='background: #ffcccc; padding: 10px; border: 1px solid #cc0000; margin: 10px 0;'>";
                    echo "<strong>✗ WARNING:</strong> Real user count changed! This should not happen.";
                    echo "</div>";
                }
                
                echo "<p><a href='?'>← Refresh to see updated counts</a></p>";
                break;
                
            default:
                echo "<p>Unknown test action.</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>✗ Error occurred:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='database/test_data/generate_test_users.php'>← Back to Test User Generator</a></p>";

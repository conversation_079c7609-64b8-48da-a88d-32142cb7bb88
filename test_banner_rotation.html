<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner Rotation Test - No Cache</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .banner-container {
            width: 100%;
            height: 100px;
            background: black;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            border-radius: 5px;
        }
        .debug-messages {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Banner Rotation Test (No Cache)</h1>
    
    <div class="test-container">
        <h3>Test Banners:</h3>
        <p>This will test banner rotation without PWA cache interference.</p>
        
        <button onclick="startTest()">Start Banner Test</button>
        <button onclick="stopTest()">Stop Test</button>
        <button onclick="clearDebug()">Clear Debug</button>
        
        <div id="banner-container" class="banner-container">
            Click "Start Banner Test" to begin
        </div>
        
        <div id="debug-messages" class="debug-messages">
            Debug messages will appear here...
        </div>
    </div>

    <script>
        // Test banners - same as your setup
        const testBanners = [
            { id: 1, type: 'text', text: 'Banner 1: Welcome to our Event Platform!', active: true, is_logo: false },
            { id: 2, type: 'text', text: 'Banner 2: Check out our upcoming events!', active: true, is_logo: false },
            { id: 3, type: 'text', text: 'Banner 3: Register your vehicle today!', active: true, is_logo: false }
        ];
        
        let currentIndex = 0;
        let rotationInterval = null;
        
        function debug(message) {
            const debugDiv = document.getElementById('debug-messages');
            const timestamp = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        function showBanner(banner) {
            debug(`🚀 showBanner() CALLED for: "${banner.text}"`);
            
            const container = document.getElementById('banner-container');
            
            if (!container) {
                debug(`❌ Container not found!`);
                return;
            }
            
            debug(`📦 Container exists: ${container.id}`);
            
            if (!document.contains(container)) {
                debug(`❌ Container removed from DOM!`);
                return;
            }
            
            debug(`🔄 Displaying: ${banner.type} - "${banner.text}"`);
            
            // Clear container
            debug(`🧹 Clearing container...`);
            container.innerHTML = '';
            debug(`✅ Container cleared, now adding content...`);
            
            // Add text
            debug(`📝 About to show text: "${banner.text}"`);
            try {
                container.textContent = banner.text;
                debug(`✅ Text banner displayed successfully`);
            } catch (error) {
                debug(`💥 ERROR showing text: ${error.message}`);
            }
        }
        
        function startTest() {
            debug(`🎬 Starting banner rotation test...`);
            debug(`📊 Total banners: ${testBanners.length}`);
            
            // Stop any existing rotation
            if (rotationInterval) {
                clearInterval(rotationInterval);
            }
            
            currentIndex = 0;
            
            // Show first banner immediately
            showBanner(testBanners[currentIndex]);
            
            // Start rotation every 3 seconds
            rotationInterval = setInterval(() => {
                currentIndex = (currentIndex + 1) % testBanners.length;
                debug(`⏰ Interval tick: Moving to banner ${currentIndex + 1} of ${testBanners.length}`);
                showBanner(testBanners[currentIndex]);
            }, 3000);
            
            debug(`🔄 Rotation started - changing every 3 seconds`);
        }
        
        function stopTest() {
            if (rotationInterval) {
                clearInterval(rotationInterval);
                rotationInterval = null;
                debug(`⏹️ Rotation stopped`);
            }
        }
        
        function clearDebug() {
            document.getElementById('debug-messages').innerHTML = 'Debug messages cleared...<br>';
        }
    </script>
</body>
</html>

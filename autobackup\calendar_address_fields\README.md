# Calendar Address Fields Update

This update modifies the calendar event system to use separate address fields (address1, address2, city, state, zipcode) instead of a single location field. It also adds a map view that allows filtering events by distance.

## Changes

1. Modified the `calendar_events` table to add new address fields
2. Updated the event creation and editing forms to use the new fields
3. Added a map view for the calendar with distance-based filtering
4. Added a state dropdown for quick navigation on the map

## Files Modified

- database/calendar_update.sql (new file)
- models/CalendarModel.php
- controllers/CalendarController.php
- views/calendar/create_event.php
- views/calendar/edit_event.php
- views/calendar/event.php
- views/calendar/custom_index_fixed.php
- views/calendar/map_view.php (new file)
- public/js/calendar-map.js (new file)
- public/css/custom-calendar.css (updated)
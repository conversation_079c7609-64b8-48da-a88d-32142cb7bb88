<?php require APPROOT . '/views/includes/header.php'; ?>

<!-- DataTables CSS -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold"><?php echo $data['title']; ?></h1>
            <p class="text-muted">Comprehensive payment management and analytics</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo URLROOT; ?>/payment/pending" class="btn btn-warning me-2">
                <i class="fas fa-clock me-1"></i> 
                Pending Payments 
                <?php if ($data['pending_count'] > 0): ?>
                    <span class="badge bg-light text-dark"><?php echo $data['pending_count']; ?></span>
                <?php endif; ?>
            </a>
            <a href="<?php echo URLROOT; ?>/payment/settings" class="btn btn-primary me-2">
                <i class="fas fa-cog me-1"></i> Settings
            </a>
            <a href="<?php echo URLROOT; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Settings
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">

            <!-- Debug Information (remove in production) -->
            <?php if (defined('DEBUG_MODE') && DEBUG_MODE) : ?>
                <div class="alert alert-info">
                    <h5>Debug Information:</h5>
                    <p><strong>Total Payments Loaded:</strong> <?php echo count($data['payments']); ?></p>
                    <p><strong>Payment Types:</strong></p>
                    <ul>
                        <?php 
                        $types = [];
                        foreach ($data['payments'] as $payment) {
                            $types[$payment->payment_type] = ($types[$payment->payment_type] ?? 0) + 1;
                        }
                        foreach ($types as $type => $count) {
                            echo "<li>$type: $count</li>";
                        }
                        ?>
                    </ul>
                    <p><strong>Payment Statuses:</strong></p>
                    <ul>
                        <?php 
                        $statuses = [];
                        foreach ($data['payments'] as $payment) {
                            $statuses[$payment->payment_status] = ($statuses[$payment->payment_status] ?? 0) + 1;
                        }
                        foreach ($statuses as $status => $count) {
                            echo "<li>$status: $count</li>";
                        }
                        ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Payment Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">Total Payments</h4>
                                    <h2><?php echo number_format($data['stats']->total_payments); ?></h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-credit-card fa-2x"></i>
                                </div>
                            </div>
                            <small>$<?php echo number_format($data['stats']->total_amount, 2); ?> total value</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">Completed</h4>
                                    <h2><?php echo number_format($data['stats']->completed_payments); ?></h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                            </div>
                            <small>$<?php echo number_format($data['stats']->completed_amount, 2); ?> received</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">Pending</h4>
                                    <h2><?php echo number_format($data['stats']->pending_payments); ?></h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-clock fa-2x"></i>
                                </div>
                            </div>
                            <small>$<?php echo number_format($data['stats']->pending_amount, 2); ?> awaiting</small>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">Rejected</h4>
                                    <h2><?php echo number_format($data['stats']->rejected_payments); ?></h2>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-times-circle fa-2x"></i>
                                </div>
                            </div>
                            <small>Declined transactions</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Type Breakdown -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="card-title mb-0">Payment Types</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6 text-center">
                                    <h3 class="text-info"><?php echo number_format($data['stats']->registration_payments); ?></h3>
                                    <p class="mb-0">Registration Payments</p>
                                </div>
                                <div class="col-6 text-center">
                                    <h3 class="text-primary"><?php echo number_format($data['stats']->listing_payments); ?></h3>
                                    <p class="mb-0">Show Listing Payments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="card-title mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="<?php echo URLROOT; ?>/payment/pending" class="btn btn-outline-warning">
                                    <i class="fas fa-clock me-2"></i> Review Pending Payments
                                </a>
                                <a href="<?php echo URLROOT; ?>/payment/methods" class="btn btn-outline-primary">
                                    <i class="fas fa-credit-card me-2"></i> Manage Payment Methods
                                </a>
                                <a href="<?php echo URLROOT; ?>/adminListing/index" class="btn btn-outline-success">
                                    <i class="fas fa-ticket-alt me-2"></i> Listing Fee Management
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- All Payments Table -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">All Payments</h5>
                    <div>
                        <button class="btn btn-light btn-sm" onclick="exportPayments()">
                            <i class="fas fa-download me-1"></i> Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs mb-3" id="paymentTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                                All Payments
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab" aria-controls="completed" aria-selected="false">
                                Completed
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="false">
                                Pending
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="rejected-tab" data-bs-toggle="tab" data-bs-target="#rejected" type="button" role="tab" aria-controls="rejected" aria-selected="false">
                                Rejected
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content" id="paymentTabsContent">
                        <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                            <?php if (empty($data['payments'])) : ?>
                                <div class="alert alert-info">No payment records found.</div>
                            <?php else : ?>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="paymentsTable">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Date</th>
                                                <th>User</th>
                                                <th>Type</th>
                                                <th>Description</th>
                                                <th>Amount</th>
                                                <th>Method</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($data['payments'] as $payment) : ?>
                                                <tr data-status="<?php echo $payment->payment_status; ?>">
                                                    <td><?php echo $payment->id; ?></td>
                                                    <td><?php echo date('M d, Y', strtotime($payment->created_at)); ?></td>
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($payment->user_name); ?></strong>
                                                            <br>
                                                            <small class="text-muted"><?php echo htmlspecialchars($payment->user_email); ?></small>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if ($payment->payment_type == 'registration') : ?>
                                                            <span class="badge bg-info">Registration</span>
                                                        <?php elseif ($payment->payment_type == 'show_listing') : ?>
                                                            <span class="badge bg-primary">Show Listing</span>
                                                        <?php else : ?>
                                                            <span class="badge bg-secondary"><?php echo ucfirst($payment->payment_type); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <small><?php echo htmlspecialchars($payment->description); ?></small>
                                                    </td>
                                                    <td>$<?php echo number_format($payment->amount, 2); ?></td>
                                                    <td><?php echo htmlspecialchars($payment->payment_method_name); ?></td>
                                                    <td>
                                                        <?php if ($payment->payment_status == 'completed') : ?>
                                                            <span class="badge bg-success">Completed</span>
                                                        <?php elseif ($payment->payment_status == 'pending') : ?>
                                                            <span class="badge bg-warning text-dark">Pending</span>
                                                        <?php elseif ($payment->payment_status == 'rejected') : ?>
                                                            <span class="badge bg-danger">Rejected</span>
                                                        <?php else : ?>
                                                            <span class="badge bg-secondary"><?php echo ucfirst($payment->payment_status); ?></span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <?php if ($payment->payment_status == 'pending') : ?>
                                                                <a href="<?php echo URLROOT; ?>/payment/approve/<?php echo $payment->id; ?>" 
                                                                   class="btn btn-success" title="Approve Payment">
                                                                    <i class="fas fa-check"></i>
                                                                </a>
                                                                <a href="<?php echo URLROOT; ?>/payment/details/<?php echo $payment->id; ?>#manual" 
                                                                   class="btn btn-info" title="Process Manual Payment">
                                                                    <i class="fas fa-credit-card"></i>
                                                                </a>
                                                                <button type="button" class="btn btn-danger reject-payment" 
                                                                        data-id="<?php echo $payment->id; ?>" title="Reject Payment">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            <a href="<?php echo URLROOT; ?>/payment/details/<?php echo $payment->id; ?>" 
                                                               class="btn btn-primary" title="View Details">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Other tab content will be filtered via JavaScript -->
                        <div class="tab-pane fade" id="completed" role="tabpanel" aria-labelledby="completed-tab"></div>
                        <div class="tab-pane fade" id="pending" role="tabpanel" aria-labelledby="pending-tab"></div>
                        <div class="tab-pane fade" id="rejected" role="tabpanel" aria-labelledby="rejected-tab"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Payment Modal -->
<div class="modal fade" id="rejectPaymentModal" tabindex="-1" aria-labelledby="rejectPaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="rejectPaymentModalLabel">Reject Payment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="rejectPaymentForm" action="" method="post">
                <?php echo generateCsrfInput(); ?>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="admin_notes" class="form-label">Reason for Rejection</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3" required></textarea>
                        <div class="form-text">Please provide a reason for rejecting this payment. This will be visible to the user.</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Reject Payment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.print.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable
    const paymentsTable = $('#paymentsTable').DataTable({
        order: [[0, 'desc']],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        dom: 'Bfrtip',
        buttons: [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });
    
    // Handle tab changes to filter the table
    document.querySelectorAll('button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const status = event.target.id.replace('-tab', '');
            
            if (status === 'all') {
                paymentsTable.search('').draw();
            } else {
                // Filter by status
                paymentsTable.column(7).search(status, true, false).draw();
            }
        });
    });
    
    // Handle reject payment button clicks
    document.querySelectorAll('.reject-payment').forEach(button => {
        button.addEventListener('click', function() {
            const paymentId = this.getAttribute('data-id');
            const rejectForm = document.getElementById('rejectPaymentForm');
            rejectForm.action = '<?php echo URLROOT; ?>/payment/reject/' + paymentId;
            
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('rejectPaymentModal'));
            modal.show();
        });
    });
});

// Export function
function exportPayments() {
    // Trigger DataTable export
    $('#paymentsTable').DataTable().button('.buttons-excel').trigger();
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
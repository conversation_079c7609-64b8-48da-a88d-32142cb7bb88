<?php
/**
 * Quick Database Relationship Checker
 * 
 * This script provides a quick overview of your database relationships
 * and identifies potential issues.
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

// Simple database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

echo "<h1>Database Relationship Overview</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .good { color: green; }
    .warning { color: orange; }
    .error { color: red; }
    .section { margin: 30px 0; }
</style>";

// 1. Check Foreign Key Constraints
echo "<div class='section'>";
echo "<h2>Foreign Key Constraints</h2>";

try {
    $foreignKeys = $pdo->query("
        SELECT 
            kcu.TABLE_NAME,
            kcu.COLUMN_NAME,
            kcu.CONSTRAINT_NAME,
            kcu.REFERENCED_TABLE_NAME,
            kcu.REFERENCED_COLUMN_NAME,
            rc.DELETE_RULE,
            rc.UPDATE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
            ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
            AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_SCHEMA = DATABASE()
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY kcu.TABLE_NAME, kcu.COLUMN_NAME
    ")->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "<p class='error'>❌ Error checking foreign keys: " . $e->getMessage() . "</p>";
    echo "<p>Trying alternative method...</p>";
    
    // Fallback query without DELETE_RULE and UPDATE_RULE
    try {
        $foreignKeys = $pdo->query("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                CONSTRAINT_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME,
                'N/A' as DELETE_RULE,
                'N/A' as UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY TABLE_NAME, COLUMN_NAME
        ")->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e2) {
        echo "<p class='error'>❌ Could not retrieve foreign key information: " . $e2->getMessage() . "</p>";
        $foreignKeys = [];
    }
}

if (empty($foreignKeys)) {
    echo "<p class='warning'>⚠️ No foreign key constraints found! This may indicate relationship issues.</p>";
} else {
    echo "<p class='good'>✅ Found " . count($foreignKeys) . " foreign key constraints</p>";
    echo "<table>";
    echo "<tr><th>Table</th><th>Column</th><th>References</th><th>Delete Rule</th><th>Update Rule</th></tr>";
    foreach ($foreignKeys as $fk) {
        echo "<tr>";
        echo "<td>{$fk['TABLE_NAME']}</td>";
        echo "<td>{$fk['COLUMN_NAME']}</td>";
        echo "<td>{$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</td>";
        echo "<td>{$fk['DELETE_RULE']}</td>";
        echo "<td>{$fk['UPDATE_RULE']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}
echo "</div>";

// 2. Check for Orphaned Records
echo "<div class='section'>";
echo "<h2>Orphaned Records Check</h2>";

$orphanChecks = [
    'Vehicles without owners' => "
        SELECT COUNT(*) as count 
        FROM vehicles v 
        LEFT JOIN users u ON v.owner_id = u.id 
        WHERE u.id IS NULL
    ",
    'Registrations without valid shows' => "
        SELECT COUNT(*) as count 
        FROM registrations r 
        LEFT JOIN shows s ON r.show_id = s.id 
        WHERE s.id IS NULL
    ",
    'Registrations without valid vehicles' => "
        SELECT COUNT(*) as count 
        FROM registrations r 
        LEFT JOIN vehicles v ON r.vehicle_id = v.id 
        WHERE v.id IS NULL
    ",
    'Registrations without valid owners' => "
        SELECT COUNT(*) as count 
        FROM registrations r 
        LEFT JOIN users u ON r.owner_id = u.id 
        WHERE u.id IS NULL
    ",
    'Payments without valid users' => "
        SELECT COUNT(*) as count 
        FROM payments p 
        LEFT JOIN users u ON p.user_id = u.id 
        WHERE u.id IS NULL
    ",
    'Calendar events without valid calendars' => "
        SELECT COUNT(*) as count 
        FROM calendar_events ce 
        LEFT JOIN calendars c ON ce.calendar_id = c.id 
        WHERE c.id IS NULL
    ",
    'Registrations without valid categories' => "
        SELECT COUNT(*) as count 
        FROM registrations r 
        LEFT JOIN show_categories sc ON r.category_id = sc.id 
        WHERE r.category_id IS NOT NULL AND sc.id IS NULL
    "
];

echo "<table>";
echo "<tr><th>Check</th><th>Count</th><th>Status</th></tr>";

foreach ($orphanChecks as $description => $query) {
    try {
        $result = $pdo->query($query)->fetch(PDO::FETCH_ASSOC);
        $count = $result['count'];
        
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td>$count</td>";
        
        if ($count == 0) {
            echo "<td class='good'>✅ Good</td>";
        } else {
            echo "<td class='error'>❌ Issues found</td>";
        }
        echo "</tr>";
    } catch (Exception $e) {
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td>N/A</td>";
        echo "<td class='warning'>⚠️ Table may not exist</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 3. Table Relationships Overview
echo "<div class='section'>";
echo "<h2>Main Table Relationships</h2>";

$relationships = [
    'users' => ['vehicles (owner_id)', 'registrations (owner_id)', 'payments (user_id)', 'calendars (owner_id)'],
    'shows' => ['registrations (show_id)', 'categories (show_id)', 'age_weights (show_id)', 'calendar_events (show_id)'],
    'vehicles' => ['registrations (vehicle_id)'],
    'calendars' => ['calendar_events (calendar_id)'],
    'registrations' => ['payments (registration_id)', 'judge_scores (registration_id)']
];

echo "<table>";
echo "<tr><th>Parent Table</th><th>Child Relationships</th></tr>";

foreach ($relationships as $parent => $children) {
    echo "<tr>";
    echo "<td><strong>$parent</strong></td>";
    echo "<td>" . implode('<br>', $children) . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 4. Database Statistics
echo "<div class='section'>";
echo "<h2>Database Statistics</h2>";

$stats = [
    'Total Users' => "SELECT COUNT(*) as count FROM users",
    'Total Shows' => "SELECT COUNT(*) as count FROM shows",
    'Total Vehicles' => "SELECT COUNT(*) as count FROM vehicles",
    'Total Registrations' => "SELECT COUNT(*) as count FROM registrations",
    'Total Payments' => "SELECT COUNT(*) as count FROM payments",
    'Total Calendar Events' => "SELECT COUNT(*) as count FROM calendar_events"
];

echo "<table>";
echo "<tr><th>Metric</th><th>Count</th></tr>";

foreach ($stats as $description => $query) {
    try {
        $result = $pdo->query($query)->fetch(PDO::FETCH_ASSOC);
        $count = number_format($result['count']);
        
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td>$count</td>";
        echo "</tr>";
    } catch (Exception $e) {
        echo "<tr>";
        echo "<td>$description</td>";
        echo "<td class='warning'>Table not found</td>";
        echo "</tr>";
    }
}
echo "</table>";
echo "</div>";

// 5. Recommendations
echo "<div class='section'>";
echo "<h2>Recommendations</h2>";
echo "<ul>";

if (empty($foreignKeys)) {
    echo "<li class='error'>❌ <strong>Critical:</strong> Add foreign key constraints to ensure data integrity</li>";
    echo "<li>📄 Run the SQL script: <code>sql/improve_database_relationships.sql</code></li>";
}

echo "<li>🔧 Run regular maintenance: <code>php scripts/database_maintenance.php all</code></li>";
echo "<li>📊 Generate visual diagram: <code>php generate_database_diagram.php html</code></li>";
echo "<li>📖 Review documentation: <code>docs/database_structure_and_relationships.md</code></li>";
echo "<li>🔍 Monitor orphaned records regularly</li>";
echo "<li>📈 Add indexes for frequently queried columns</li>";
echo "</ul>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>Next Steps</h2>";
echo "<ol>";
echo "<li>Review the foreign key constraints section above</li>";
echo "<li>If no foreign keys exist, run <code>sql/improve_database_relationships.sql</code></li>";
echo "<li>Fix any orphaned records found</li>";
echo "<li>Set up regular database maintenance using <code>scripts/database_maintenance.php</code></li>";
echo "<li>Generate a visual diagram for better understanding</li>";
echo "</ol>";
echo "</div>";

echo "<p><em>Generated on: " . date('Y-m-d H:i:s') . "</em></p>";
?>
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Add Default Metric</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/defaultMetrics" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Default Metrics
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <?php require APPROOT . '/views/includes/admin_settings_sidebar.php'; ?>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Add Default Metric</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/admin/addDefaultMetric" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback"><?php echo $name_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo $description; ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Weight</label>
                                    <input type="number" class="form-control <?php echo (!empty($weight_err)) ? 'is-invalid' : ''; ?>" id="weight" name="weight" value="<?php echo $weight; ?>" min="1">
                                    <div class="invalid-feedback"><?php echo $weight_err; ?></div>
                                    <div class="form-text">Higher weight gives this metric more importance in the final score.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_score" class="form-label">Max Score</label>
                                    <input type="number" class="form-control <?php echo (!empty($max_score_err)) ? 'is-invalid' : ''; ?>" id="max_score" name="max_score" value="<?php echo $max_score; ?>" min="1">
                                    <div class="invalid-feedback"><?php echo $max_score_err; ?></div>
                                    <div class="form-text">The maximum score a judge can give for this metric.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo BASE_URL; ?>/admin/defaultMetrics" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Default Metric</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
    /**
     * Process a manual payment
     * 
     * @param string $type Payment type (registration or show_listing)
     * @param int $id Related ID (registration ID or show ID)
     */
    public function processManualPayment($type, $id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Validate payment type
        if (!in_array($type, ['registration', 'show_listing'])) {
            $this->redirect('home/error/Invalid%20payment%20type');
            return;
        }
        
        // Get related record
        if ($type == 'registration') {
            $registration = $this->registrationModel->getRegistrationById($id);
            if (!$registration) {
                $this->redirect('home/not_found');
                return;
            }
            
            // Check if user is admin or coordinator of the show
            $show = $this->showModel->getShowById($registration->show_id);
            if (!$this->auth->hasRole('admin') && $show->coordinator_id != $this->auth->getCurrentUserId()) {
                $this->redirect('home/access_denied');
                return;
            }
            
            $userId = $registration->owner_id;
            $amount = $show->registration_fee;
            $relatedId = $registration->id;
            
        } else { // show_listing
            // Only admins can process show listing payments
            if (!$this->auth->hasRole('admin')) {
                $this->redirect('home/access_denied');
                return;
            }
            
            $show = $this->showModel->getShowById($id);
            if (!$show) {
                $this->redirect('home/not_found');
                return;
            }
            
            $userId = $show->coordinator_id;
            $amount = $show->listing_fee;
            if ($amount <= 0) {
                $amount = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
            }
            $relatedId = $show->id;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
            $notes = !empty($_POST['notes']) ? trim($_POST['notes']) : null;
            
            // Process manual payment
            $paymentData = [
                'user_id' => $userId,
                'amount' => $amount,
                'payment_method_id' => $paymentMethodId,
                'payment_reference' => $paymentReference,
                'payment_type' => $type,
                'related_id' => $relatedId,
                'notes' => $notes,
                'processed_by' => $this->auth->getCurrentUserId(),
                'admin_notes' => 'Manual payment processed by ' . $this->auth->getCurrentUserName()
            ];
            
            if ($this->paymentModel->processManualPayment($paymentData)) {
                if ($type == 'registration') {
                    $this->setFlashMessage('payment_success', 'Registration payment processed successfully', 'success');
                    $this->redirect('registration/view/' . $id);
                } else {
                    $this->setFlashMessage('payment_success', 'Show listing payment processed successfully', 'success');
                    $this->redirect('show/manage/' . $id);
                }
            } else {
                $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
                $this->redirect('payment/processManualPayment/' . $type . '/' . $id);
            }
        } else {
            // Get payment methods
            $paymentMethods = $this->paymentModel->getPaymentMethods();
            
            // Display form
            $data = [
                'title' => 'Process Manual Payment',
                'payment_type' => $type,
                'related_id' => $id,
                'amount' => $amount,
                'payment_methods' => $paymentMethods
            ];
            
            if ($type == 'registration') {
                $data['registration'] = $registration;
                $data['show'] = $show;
            } else {
                $data['show'] = $show;
            }
            
            $this->view('payments/process_manual', $data);
        }
    }
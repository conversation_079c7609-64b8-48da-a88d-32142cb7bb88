<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Club Management</h1>
            <p class="text-muted mb-0">Optimized for managing thousands of clubs</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-info me-2 d-none d-sm-inline">
                <i class="fas fa-calendar me-2"></i> Calendar
            </a>
            <a href="<?php echo URLROOT; ?>/calendar/createClub" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2 d-none d-sm-inline"></i> Create Club
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php flash('calendar_message'); ?>

    <!-- Club Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Club Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-4 col-lg">
                            <div class="card h-100 border-primary shadow-sm club-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">All Clubs</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['club_counts']['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Clubs</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-4 col-lg">
                            <div class="card h-100 border-success shadow-sm club-overview-card" 
                                 data-filter="verified" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Verified</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['club_counts']['verified'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Verified Clubs</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-4 col-lg">
                            <div class="card h-100 border-warning shadow-sm club-overview-card" 
                                 data-filter="unverified" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Unverified</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['club_counts']['unverified'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Verification</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-4 col-lg">
                            <div class="card h-100 border-info shadow-sm club-overview-card" 
                                 data-filter="with_owner" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">With Owner</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['club_counts']['with_owner'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Have Owners</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-4 col-lg">
                            <div class="card h-100 border-danger shadow-sm club-overview-card" 
                                 data-filter="without_owner" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-danger mb-2">No Owner</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['club_counts']['without_owner'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Need Owners</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Club Details Section (Lazy Loaded) -->
    <div class="row mb-4 club-section" id="club-section" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary bg-opacity-25">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <span class="badge bg-primary me-2">Club Details</span>
                            <span class="badge bg-secondary" id="club-count-display">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <a href="<?php echo URLROOT; ?>/calendar/requestClubOwnership" class="btn btn-sm btn-warning">
                                <i class="fas fa-crown me-1"></i>Request Ownership
                            </a>
                            <button class="btn btn-sm btn-outline-secondary" onclick="closeClubSection()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter Controls -->
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search-clubs" class="form-label">Search Clubs</label>
                            <input type="text" class="form-control" id="search-clubs" 
                                   placeholder="Search by name, description, or email...">
                        </div>
                        <div class="col-md-2">
                            <label for="owner-filter" class="form-label">Ownership</label>
                            <select class="form-select" id="owner-filter">
                                <option value="all">All Clubs</option>
                                <option value="owned">My Clubs</option>
                                <option value="not_owned">Not My Clubs</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="verification-filter" class="form-label">Verification</label>
                            <select class="form-select" id="verification-filter">
                                <option value="all">All Status</option>
                                <option value="verified">Verified</option>
                                <option value="unverified">Unverified</option>
                                <option value="pending">Pending</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="per-page-clubs" class="form-label">Per Page</label>
                            <select class="form-select" id="per-page-clubs">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="searchClubs()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearClubSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div class="card-body text-center" id="loading-clubs">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading clubs...</p>
                </div>
                
                <!-- Clubs Content (Will be populated via AJAX) -->
                <div id="clubs-content" style="display: none;">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Club overview card click handlers
    document.querySelectorAll('.club-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadClubSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-clubs').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchClubs();
        }
    });

    // Filter change handlers
    document.getElementById('owner-filter').addEventListener('change', searchClubs);
    document.getElementById('verification-filter').addEventListener('change', searchClubs);
    document.getElementById('per-page-clubs').addEventListener('change', searchClubs);
});

function loadClubSection(filter = 'all') {
    // Show the club section
    const section = document.getElementById('club-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        if (filter === 'verified' || filter === 'unverified') {
            document.getElementById('verification-filter').value = filter;
        } else if (filter === 'with_owner') {
            document.getElementById('owner-filter').value = 'owned';
        } else if (filter === 'without_owner') {
            document.getElementById('owner-filter').value = 'not_owned';
        }
    }

    // Load clubs
    loadClubs(1);
}

function closeClubSection() {
    const section = document.getElementById('club-section');
    section.style.display = 'none';
}

function loadClubs(page = 1) {
    const loadingDiv = document.getElementById('loading-clubs');
    const contentDiv = document.getElementById('clubs-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-clubs').value;
    const ownerFilter = document.getElementById('owner-filter').value;
    const verificationFilter = document.getElementById('verification-filter').value;
    const perPage = document.getElementById('per-page-clubs').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        owner_filter: ownerFilter,
        verification_filter: verificationFilter
    });

    // Make AJAX request
    fetch('<?php echo BASE_URL; ?>/calendar/loadClubs?' + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderClubs(data);
        } else {
            showError(data.error || 'Failed to load clubs');
        }
    })
    .catch(error => {
        console.error('Error loading clubs:', error);
        showError('Network error occurred');
    });
}

function searchClubs() {
    loadClubs(1);
}

function clearClubSearch() {
    document.getElementById('search-clubs').value = '';
    document.getElementById('owner-filter').value = 'all';
    document.getElementById('verification-filter').value = 'all';
    loadClubs(1);
}

function renderClubs(data) {
    const loadingDiv = document.getElementById('loading-clubs');
    const contentDiv = document.getElementById('clubs-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render clubs table and pagination
    let html = '';

    if (data.clubs.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No clubs found.</p></div>';
    } else {
        html = renderClubsTable(data.clubs, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update club count display
    document.getElementById('club-count-display').textContent = data.pagination.total_clubs.toLocaleString();
}

function renderClubsTable(clubs, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th width="60">Logo</th><th>Name</th><th>Contact</th><th>Owner</th><th>Members</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    clubs.forEach(club => {
        html += '<tr>';
        html += '<td>';
        if (club.logo) {
            html += `<img src="${club.logo}" alt="${club.name} Logo" class="club-logo">`;
        } else {
            html += '<div class="club-logo-placeholder"><i class="fas fa-users"></i></div>';
        }
        html += '</td>';
        html += '<td><strong>' + club.name + '</strong>';
        if (club.description) {
            html += '<br><small class="text-muted">' + (club.description.length > 50 ? club.description.substring(0, 50) + '...' : club.description) + '</small>';
        }
        html += '</td>';
        html += '<td>';
        if (club.email) html += `<div><i class="fas fa-envelope me-1"></i> ${club.email}</div>`;
        if (club.phone) html += `<div><i class="fas fa-phone me-1"></i> ${club.phone}</div>`;
        if (club.website) html += `<div><i class="fas fa-globe me-1"></i> <a href="${club.website}" target="_blank">Website</a></div>`;
        if (!club.email && !club.phone && !club.website) html += '<span class="text-muted">No contact info</span>';
        html += '</td>';
        html += '<td>';
        if (club.owner_name) {
            html += `<div><i class="fas fa-crown text-warning me-1"></i> ${club.owner_name}</div>`;
            if (club.is_verified) {
                html += '<small class="text-success"><i class="fas fa-check-circle me-1"></i>Verified</small>';
            } else {
                html += '<small class="text-warning"><i class="fas fa-clock me-1"></i>Pending</small>';
            }
        } else {
            html += '<span class="text-muted"><i class="fas fa-user-slash me-1"></i>No owner</span>';
        }
        html += '</td>';
        html += '<td><span class="badge bg-info">' + (club.member_count || 0) + '</span></td>';
        html += '<td>' + getClubActions(club.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_clubs.toLocaleString()} clubs`;
    html += '</div>';

    return html;
}

function getClubActions(clubId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/calendar/editClub/${clubId}" class="btn btn-primary">
                <i class="fas fa-edit"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/calendar/manageClubMembers/${clubId}" class="btn btn-info">
                <i class="fas fa-users"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/calendar/deleteClub/${clubId}" class="btn btn-danger" onclick="return confirm('Are you sure?')">
                <i class="fas fa-trash"></i>
            </a>
        </div>
    `;
}

function renderPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadClubs(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadClubs(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadClubs(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showError(message) {
    const loadingDiv = document.getElementById('loading-clubs');
    const contentDiv = document.getElementById('clubs-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}
</script>

<!-- Custom CSS -->
<style>
    .club-logo {
        width: 40px;
        height: 40px;
        object-fit: contain;
        border-radius: 4px;
    }

    .club-logo-placeholder {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #e9ecef;
        border-radius: 4px;
        color: #6c757d;
    }
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>

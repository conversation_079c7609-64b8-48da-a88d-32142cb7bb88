<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - RER Events</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .offline-container {
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .offline-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #dc3545;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            color: #cccccc;
        }
        
        .features-list {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 2rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .features-list h3 {
            color: #dc3545;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .features-list ul {
            list-style: none;
        }
        
        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .features-list li:last-child {
            border-bottom: none;
        }
        
        .features-list li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .retry-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .retry-button:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
        
        .cached-data {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid #28a745;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 2rem 0;
        }
        
        .cached-data h4 {
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .cached-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .cached-link {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            text-decoration: none;
            color: #ffffff;
            transition: all 0.3s ease;
        }
        
        .cached-link:hover {
            background: rgba(255, 255, 255, 0.2);
            color: #dc3545;
        }
        
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            animation: pulse 2s infinite;
        }
        
        .online {
            background: #28a745 !important;
            animation: none;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            .offline-icon {
                font-size: 3rem;
            }
            
            .features-list {
                padding: 1.5rem;
            }
            
            .cached-links {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="status-indicator" id="status">Offline</div>
    
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1>You're Offline</h1>
        <p>Don't worry! You can still use many features of RER Events while offline. Your actions will sync automatically when you're back online.</p>
        
        <div class="features-list">
            <h3>Available Offline Features</h3>
            <ul>
                <li>View cached event listings</li>
                <li>Browse your dashboard</li>
                <li>Fill out registration forms</li>
                <li>Score vehicles (judges)</li>
                <li>View saved show details</li>
                <li>Access your profile</li>
                <li>Take photos for registrations</li>
                <li>Scan QR codes</li>
            </ul>
        </div>
        
        <div class="cached-data" id="cachedData" style="display: none;">
            <h4>Available Cached Content</h4>
            <div class="cached-links" id="cachedLinks">
                <!-- Cached links will be populated by JavaScript -->
            </div>
        </div>
        
        <button class="retry-button" onclick="checkConnection()">
            🔄 Check Connection
        </button>
        
        <button class="retry-button" onclick="goHome()">
            🏠 Go to Homepage
        </button>
    </div>
    
    <script>
        // Check online status
        function updateStatus() {
            const status = document.getElementById('status');
            if (navigator.onLine) {
                status.textContent = 'Online';
                status.classList.add('online');
                // Redirect to requested page or home
                setTimeout(() => {
                    window.location.href = sessionStorage.getItem('offline-redirect') || '/';
                }, 1000);
            } else {
                status.textContent = 'Offline';
                status.classList.remove('online');
            }
        }
        
        function checkConnection() {
            updateStatus();
            if (!navigator.onLine) {
                alert('Still offline. Please check your internet connection.');
            }
        }
        
        function goHome() {
            window.location.href = '/';
        }
        
        // Load cached content
        async function loadCachedContent() {
            try {
                const cache = await caches.open('rer-events-v1.0.0');
                const requests = await cache.keys();
                
                const cachedPages = requests
                    .filter(request => 
                        request.url.includes('/dashboard') ||
                        request.url.includes('/calendar') ||
                        request.url.includes('/events') ||
                        request.url.includes('/user/')
                    )
                    .slice(0, 6); // Show max 6 cached pages
                
                if (cachedPages.length > 0) {
                    const cachedData = document.getElementById('cachedData');
                    const cachedLinks = document.getElementById('cachedLinks');
                    
                    cachedData.style.display = 'block';
                    
                    cachedPages.forEach(request => {
                        const url = new URL(request.url);
                        const link = document.createElement('a');
                        link.href = url.pathname;
                        link.className = 'cached-link';
                        link.innerHTML = `
                            <div style="font-weight: bold;">${getPageTitle(url.pathname)}</div>
                            <div style="font-size: 0.9rem; opacity: 0.8;">${url.pathname}</div>
                        `;
                        cachedLinks.appendChild(link);
                    });
                }
            } catch (error) {
                console.log('Could not load cached content:', error);
            }
        }
        
        function getPageTitle(pathname) {
            const titles = {
                '/': 'Homepage',
                '/dashboard': 'Dashboard',
                '/user/dashboard': 'My Dashboard',
                '/calendar': 'Events Calendar',
                '/events': 'Event Listings',
                '/user/profile': 'My Profile',
                '/user/registrations': 'My Registrations'
            };
            
            return titles[pathname] || pathname.split('/').pop() || 'Cached Page';
        }
        
        // Event listeners
        window.addEventListener('online', updateStatus);
        window.addEventListener('offline', updateStatus);
        
        // Initialize
        updateStatus();
        loadCachedContent();
        
        // Auto-check connection every 5 seconds
        setInterval(updateStatus, 5000);
    </script>
</body>
</html>
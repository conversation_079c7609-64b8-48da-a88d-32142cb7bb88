<?php
/**
 * Camera Banner Admin API
 * Handles CRUD operations for camera banners
 */

// Suppress errors initially to prevent HTML output
error_reporting(0);
ini_set('display_errors', 0);

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__DIR__));
}

// Set JSON headers first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

try {
    require_once APPROOT . '/config/config.php';
    require_once APPROOT . '/core/Database.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load required files',
        'error' => $e->getMessage()
    ]);
    exit;
}

// Debug mode check
$debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : false;

// Add debug logging if enabled
if ($debug_mode) {
    error_log("Camera banners admin API accessed - Method: " . $_SERVER['REQUEST_METHOD']);
}

// Check if user is admin (following the existing codebase pattern)
session_start();
if (!isset($_SESSION['user_id']) || ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator')) {
    http_response_code(403);
    echo json_encode([
        'success' => false, 
        'message' => 'Access denied. Admin or Coordinator role required.',
        'debug' => $debug_mode ? [
            'user_id' => $_SESSION['user_id'] ?? 'not set',
            'user_role' => $_SESSION['user_role'] ?? 'not set',
            'session_data' => array_keys($_SESSION)
        ] : null
    ]);
    exit;
}

try {
    // Add error reporting for debugging
    if ($debug_mode) {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        error_log("Attempting to create Database instance");
    }
    
    $db = new Database();
    
    if ($debug_mode) {
        error_log("Database instance created successfully");
    }

    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // Check if camera_banners table exists
        $db->query("SHOW TABLES LIKE 'camera_banners'");
        $db->execute();
        $table_exists = $db->rowCount() > 0;

        $banners = [];
        
        if ($table_exists) {
            // Get all banners for admin
            $db->query("SELECT * FROM camera_banners ORDER BY sort_order ASC, created_at DESC");
            $db->execute();
            $results = $db->resultSet();
            
            foreach ($results as $row) {
                $banner = [
                    'id' => (int)$row->id,
                    'type' => $row->type ?: 'text',
                    'text' => $row->text ?: '',
                    'image_path' => $row->image_path ?: '',
                    'alt_text' => $row->alt_text ?: '',
                    'active' => (bool)$row->active,
                    'sort_order' => (int)$row->sort_order,
                    'created_at' => $row->created_at ?: '',
                    'updated_at' => $row->updated_at ?: ''
                ];
                
                // Ensure image path is absolute and not empty
                if ($banner['image_path'] && !str_starts_with($banner['image_path'], 'http')) {
                    $banner['image_path'] = '/uploads/banners/' . basename($banner['image_path']);
                }
                
                $banners[] = $banner;
            }
        }

        echo json_encode([
            'success' => true,
            'banners' => $banners,
            'count' => count($banners)
        ]);

    } elseif ($method === 'POST') {
        $action = $_POST['action'] ?? 'create';
        
        if ($action === 'create') {
            // Create new banner
            $type = $_POST['type'] ?? 'text';
            $text = $_POST['text'] ?? null;
            $alt_text = $_POST['alt_text'] ?? null;
            $active = isset($_POST['active']) ? 1 : 0;
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            $image_path = null;
            
            // Handle image upload
            if ($type === 'image' && isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $image_path = handleImageUpload($_FILES['image']);
                if (!$image_path) {
                    throw new Exception('Failed to upload image');
                }
            }
            
            $db->query("INSERT INTO camera_banners (type, text, image_path, alt_text, active, sort_order) 
                       VALUES (:type, :text, :image_path, :alt_text, :active, :sort_order)");
            $db->bind(':type', $type);
            $db->bind(':text', $text);
            $db->bind(':image_path', $image_path);
            $db->bind(':alt_text', $alt_text);
            $db->bind(':active', $active);
            $db->bind(':sort_order', $sort_order);
            $db->execute();
            
            echo json_encode([
                'success' => true,
                'message' => 'Banner created successfully',
                'id' => $db->lastInsertId()
            ]);
            
        } elseif ($action === 'update') {
            // Update existing banner
            $id = (int)$_POST['id'];
            $type = $_POST['type'] ?? 'text';
            $text = $_POST['text'] ?? null;
            $alt_text = $_POST['alt_text'] ?? null;
            $active = isset($_POST['active']) ? 1 : 0;
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            // Get current banner
            $db->query("SELECT image_path FROM camera_banners WHERE id = :id");
            $db->bind(':id', $id);
            $db->execute();
            $current_banner = $db->single();
            
            $image_path = $current_banner ? $current_banner->image_path : null;
            
            // Handle image upload
            if ($type === 'image' && isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $new_image_path = handleImageUpload($_FILES['image']);
                if ($new_image_path) {
                    // Delete old image if exists
                    if ($image_path && file_exists('../uploads/banners/' . basename($image_path))) {
                        unlink('../uploads/banners/' . basename($image_path));
                    }
                    $image_path = $new_image_path;
                }
            }
            
            $db->query("UPDATE camera_banners 
                       SET type = :type, text = :text, image_path = :image_path, alt_text = :alt_text, 
                           active = :active, sort_order = :sort_order, updated_at = CURRENT_TIMESTAMP 
                       WHERE id = :id");
            $db->bind(':type', $type);
            $db->bind(':text', $text);
            $db->bind(':image_path', $image_path);
            $db->bind(':alt_text', $alt_text);
            $db->bind(':active', $active);
            $db->bind(':sort_order', $sort_order);
            $db->bind(':id', $id);
            $db->execute();
            
            echo json_encode([
                'success' => true,
                'message' => 'Banner updated successfully'
            ]);
            
        } elseif ($action === 'delete') {
            // Delete banner
            $id = (int)$_POST['id'];
            
            // Get banner to delete image file
            $db->query("SELECT image_path FROM camera_banners WHERE id = :id");
            $db->bind(':id', $id);
            $db->execute();
            $banner = $db->single();
            
            if ($banner) {
                // Delete image file if exists
                if ($banner->image_path && file_exists('../uploads/banners/' . basename($banner->image_path))) {
                    unlink('../uploads/banners/' . basename($banner->image_path));
                }
                
                // Delete from database
                $db->query("DELETE FROM camera_banners WHERE id = :id");
                $db->bind(':id', $id);
                $db->execute();
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Banner deleted successfully'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Banner not found'
                ]);
            }
        }
    }

} catch (PDOException $e) {
    $error_response = [
        'success' => false,
        'message' => 'Database error occurred'
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);

} catch (Exception $e) {
    $error_response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);
}

function handleImageUpload($file) {
    $upload_dir = '../uploads/banners/';
    
    // Create directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Validate file type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowed_types)) {
        throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
    }
    
    // Validate file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('File too large. Maximum size is 5MB.');
    }
    
    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'banner_' . uniqid() . '.' . $extension;
    $filepath = $upload_dir . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return $filename;
    } else {
        throw new Exception('Failed to save uploaded file.');
    }
}
?>
<?php
/**
 * PWA Endpoints Test Script
 * Tests the PWA API endpoints to ensure they're working correctly
 */

require_once 'config/config.php';
require_once 'core/Database.php';
require_once 'controllers/PWAController.php';

echo "Testing PWA Endpoints...\n\n";

// Test 1: VAPID Key Configuration
echo "1. Testing VAPID Key Configuration:\n";
echo "   VAPID_PUBLIC_KEY defined: " . (defined('VAPID_PUBLIC_KEY') ? 'Yes' : 'No') . "\n";
echo "   VAPID_PRIVATE_KEY defined: " . (defined('VAPID_PRIVATE_KEY') ? 'Yes' : 'No') . "\n";

if (defined('VAPID_PUBLIC_KEY')) {
    echo "   Public key length: " . strlen(VAPID_PUBLIC_KEY) . " characters\n";
    echo "   Public key preview: " . substr(VAPID_PUBLIC_KEY, 0, 20) . "...\n";
}

echo "\n";

// Test 2: PWA Controller Instantiation
echo "2. Testing PWA Controller:\n";
try {
    $pwaController = new PWAController();
    echo "   ✓ PWA Controller created successfully\n";
} catch (Exception $e) {
    echo "   ✗ PWA Controller failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: VAPID Key Endpoint (simulate)
echo "3. Testing VAPID Key Endpoint:\n";
try {
    // Capture output
    ob_start();
    $pwaController->getVapidKey();
    $output = ob_get_clean();
    
    $response = json_decode($output, true);
    
    if ($response && isset($response['success']) && $response['success']) {
        echo "   ✓ VAPID key endpoint working\n";
        echo "   ✓ Response format valid\n";
        if (isset($response['publicKey'])) {
            echo "   ✓ Public key present in response\n";
            echo "   Key length: " . strlen($response['publicKey']) . " characters\n";
        }
    } else {
        echo "   ✗ VAPID key endpoint failed\n";
        echo "   Response: " . $output . "\n";
    }
} catch (Exception $e) {
    echo "   ✗ VAPID key endpoint error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Database Tables
echo "4. Testing Database Tables:\n";
try {
    $db = new Database();
    
    $tables = ['push_subscriptions', 'notification_preferences'];
    
    foreach ($tables as $table) {
        $db->query("SHOW TABLES LIKE :table");
        $db->bind(':table', $table);
        $result = $db->single();
        
        if ($result) {
            echo "   ✓ Table '$table' exists\n";
            
            // Get row count
            $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $db->single();
            echo "     Rows: " . ($count ? $count->count : 0) . "\n";
        } else {
            echo "   ✗ Table '$table' missing\n";
        }
    }
} catch (Exception $e) {
    echo "   ✗ Database test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: API Routing Test
echo "5. Testing API Routing:\n";
echo "   Expected endpoints:\n";
echo "   - /api/notifications/vapid-key\n";
echo "   - /api/notifications/subscribe\n";
echo "   - /api/pwa/vapid-key (alternative)\n";
echo "   - /api/pwa/subscribe (alternative)\n";

echo "\nTest completed!\n";
echo "\nTo test the endpoints in browser:\n";
echo "1. Visit: " . BASE_URL . "/api/notifications/vapid-key\n";
echo "2. Should return JSON with success:true and publicKey\n";
?>
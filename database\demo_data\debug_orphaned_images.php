<?php
/**
 * Debug Orphaned Images
 * 
 * This script helps debug why orphaned images aren't being found.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

echo "<h1>Debug Orphaned Images</h1>";
echo "<p>Debugging why orphaned images aren't being found...</p>";

try {
    $db = new Database();
    
    echo "<h2>1. Check Images with Show Entity Type</h2>";
    
    // Get all images with entity_type = 'show'
    $db->query("SELECT id, file_name, entity_type, entity_id FROM images WHERE entity_type = 'show' ORDER BY entity_id");
    $showImages = $db->resultSet();
    
    echo "<p><strong>Images with entity_type='show':</strong> " . count($showImages) . "</p>";
    
    if (!empty($showImages)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image ID</th><th>Filename</th><th>Entity Type</th><th>Entity ID</th><th>Show Exists?</th></tr>";
        
        foreach ($showImages as $image) {
            // Check if show exists
            $stmt = $db->getConnection()->prepare("SELECT id FROM shows WHERE id = ?");
            $stmt->execute([$image->entity_id]);
            $showExists = $stmt->fetch() ? 'YES' : 'NO';
            $color = $showExists == 'NO' ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>{$image->entity_type}</td>";
            echo "<td>{$image->entity_id}</td>";
            echo "<td style='color: {$color}; font-weight: bold;'>{$showExists}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>2. Check Images with Vehicle Entity Type</h2>";
    
    // Get all images with entity_type = 'vehicle'
    $db->query("SELECT id, file_name, entity_type, entity_id FROM images WHERE entity_type = 'vehicle' ORDER BY entity_id");
    $vehicleImages = $db->resultSet();
    
    echo "<p><strong>Images with entity_type='vehicle':</strong> " . count($vehicleImages) . "</p>";
    
    if (!empty($vehicleImages)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image ID</th><th>Filename</th><th>Entity Type</th><th>Entity ID</th><th>Vehicle Exists?</th></tr>";
        
        foreach ($vehicleImages as $image) {
            // Check if vehicle exists
            $stmt = $db->getConnection()->prepare("SELECT id FROM vehicles WHERE id = ?");
            $stmt->execute([$image->entity_id]);
            $vehicleExists = $stmt->fetch() ? 'YES' : 'NO';
            $color = $vehicleExists == 'NO' ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>{$image->entity_type}</td>";
            echo "<td>{$image->entity_id}</td>";
            echo "<td style='color: {$color}; font-weight: bold;'>{$vehicleExists}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Test Orphaned Image Queries</h2>";
    
    // Test the exact queries from the cleanup method
    echo "<h3>Orphaned Vehicle Images Query</h3>";
    $db->query("SELECT i.* FROM images i 
                WHERE i.entity_type = 'vehicle' 
                AND i.entity_id NOT IN (SELECT id FROM vehicles)");
    $orphanedVehicleImages = $db->resultSet();
    
    echo "<p><strong>Orphaned Vehicle Images Found:</strong> " . count($orphanedVehicleImages) . "</p>";
    
    if (!empty($orphanedVehicleImages)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image ID</th><th>Filename</th><th>Entity ID</th></tr>";
        foreach ($orphanedVehicleImages as $image) {
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>{$image->entity_id}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>Orphaned Show Images Query</h3>";
    $db->query("SELECT i.* FROM images i 
                WHERE i.entity_type = 'show' 
                AND i.entity_id NOT IN (SELECT id FROM shows)");
    $orphanedShowImages = $db->resultSet();
    
    echo "<p><strong>Orphaned Show Images Found:</strong> " . count($orphanedShowImages) . "</p>";
    
    if (!empty($orphanedShowImages)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image ID</th><th>Filename</th><th>Entity ID</th></tr>";
        foreach ($orphanedShowImages as $image) {
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>{$image->entity_id}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>4. Check Show IDs</h2>";
    
    // Get all show IDs
    $db->query("SELECT id, name FROM shows ORDER BY id");
    $allShows = $db->resultSet();
    
    echo "<p><strong>Total Shows in Database:</strong> " . count($allShows) . "</p>";
    
    if (!empty($allShows)) {
        echo "<h3>Show IDs (First 20)</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Show ID</th><th>Show Name</th></tr>";
        
        $showCount = 0;
        foreach ($allShows as $show) {
            if ($showCount >= 20) break;
            echo "<tr>";
            echo "<td>{$show->id}</td>";
            echo "<td>" . htmlspecialchars($show->name) . "</td>";
            echo "</tr>";
            $showCount++;
        }
        echo "</table>";
        
        if (count($allShows) > 20) {
            echo "<p>... and " . (count($allShows) - 20) . " more shows</p>";
        }
    }
    
    echo "<h2>5. Check Vehicle IDs</h2>";
    
    // Get all vehicle IDs
    $db->query("SELECT id, make, model, year FROM vehicles ORDER BY id");
    $allVehicles = $db->resultSet();
    
    echo "<p><strong>Total Vehicles in Database:</strong> " . count($allVehicles) . "</p>";
    
    if (!empty($allVehicles)) {
        echo "<h3>Vehicle IDs (First 20)</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Vehicle ID</th><th>Make</th><th>Model</th><th>Year</th></tr>";
        
        $vehicleCount = 0;
        foreach ($allVehicles as $vehicle) {
            if ($vehicleCount >= 20) break;
            echo "<tr>";
            echo "<td>{$vehicle->id}</td>";
            echo "<td>" . htmlspecialchars($vehicle->make) . "</td>";
            echo "<td>" . htmlspecialchars($vehicle->model) . "</td>";
            echo "<td>{$vehicle->year}</td>";
            echo "</tr>";
            $vehicleCount++;
        }
        echo "</table>";
        
        if (count($allVehicles) > 20) {
            echo "<p>... and " . (count($allVehicles) - 20) . " more vehicles</p>";
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<ul>";
    echo "<li>Show Images: " . count($showImages) . "</li>";
    echo "<li>Vehicle Images: " . count($vehicleImages) . "</li>";
    echo "<li>Orphaned Vehicle Images: " . count($orphanedVehicleImages) . "</li>";
    echo "<li>Orphaned Show Images: " . count($orphanedShowImages) . "</li>";
    echo "<li>Total Shows: " . count($allShows) . "</li>";
    echo "<li>Total Vehicles: " . count($allVehicles) . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 12px;
    width: 100%;
}

th, td {
    padding: 6px 10px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}
</style>

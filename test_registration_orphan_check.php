<?php
/**
 * Test Registration Orphan Check
 * 
 * Test the improved orphan checking logic for registrations
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Check if user is admin (following your site's pattern)
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>🧪 Registration Orphan Check Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
    .error { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0; }
    .info { background: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 10px 0; }
    .warning { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .issue { background-color: #fff3cd; }
</style>";

try {
    // Test database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "</div>";
    
    // Check if registrations table exists
    echo "<h2>🔍 Checking Registrations Table Structure</h2>";
    
    try {
        $columns = $pdo->query("DESCRIBE registrations")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>";
        echo "<h3>✅ Registrations Table Found</h3>";
        echo "<p>Table structure:</p>";
        echo "<table>";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        foreach ($columns as $col) {
            echo "<tr>";
            echo "<td>{$col['Field']}</td>";
            echo "<td>{$col['Type']}</td>";
            echo "<td>{$col['Null']}</td>";
            echo "<td>{$col['Key']}</td>";
            echo "<td>{$col['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Registrations Table Issue</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
        exit;
    }
    
    // Get basic registration statistics
    echo "<h2>📊 Registration Statistics</h2>";
    
    $totalRegs = $pdo->query("SELECT COUNT(*) as count FROM registrations")->fetch()['count'];
    echo "<div class='info'>";
    echo "<h3>📋 Basic Stats</h3>";
    echo "<p><strong>Total Registrations:</strong> $totalRegs</p>";
    echo "</div>";
    
    // Test the improved orphan checking logic
    echo "<h2>🔍 Testing Improved Orphan Detection</h2>";
    
    // Check for registrations with invalid shows
    echo "<h3>1. Registrations with Invalid Shows</h3>";
    try {
        $orphanedByShow = $pdo->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id
            FROM registrations r
            LEFT JOIN shows s ON r.show_id = s.id
            WHERE s.id IS NULL
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($orphanedByShow)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Found " . count($orphanedByShow) . " registrations with invalid shows</h4>";
            echo "<table>";
            echo "<tr><th>Registration ID</th><th>Show ID</th><th>Vehicle ID</th><th>Owner ID</th></tr>";
            foreach ($orphanedByShow as $reg) {
                echo "<tr class='issue'>";
                echo "<td>{$reg['id']}</td>";
                echo "<td>{$reg['show_id']} (missing)</td>";
                echo "<td>{$reg['vehicle_id']}</td>";
                echo "<td>{$reg['owner_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div class='success'>";
            echo "<h4>✅ No registrations with invalid shows</h4>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h4>❌ Error checking shows: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
    
    // Check for registrations with invalid vehicles
    echo "<h3>2. Registrations with Invalid Vehicles</h3>";
    try {
        $orphanedByVehicle = $pdo->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id
            FROM registrations r
            LEFT JOIN vehicles v ON r.vehicle_id = v.id
            WHERE v.id IS NULL
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($orphanedByVehicle)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Found " . count($orphanedByVehicle) . " registrations with invalid vehicles</h4>";
            echo "<table>";
            echo "<tr><th>Registration ID</th><th>Show ID</th><th>Vehicle ID</th><th>Owner ID</th></tr>";
            foreach ($orphanedByVehicle as $reg) {
                echo "<tr class='issue'>";
                echo "<td>{$reg['id']}</td>";
                echo "<td>{$reg['show_id']}</td>";
                echo "<td>{$reg['vehicle_id']} (missing)</td>";
                echo "<td>{$reg['owner_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div class='success'>";
            echo "<h4>✅ No registrations with invalid vehicles</h4>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h4>❌ Error checking vehicles: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
    
    // Check for registrations with invalid owners
    echo "<h3>3. Registrations with Invalid Owners</h3>";
    try {
        $orphanedByOwner = $pdo->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id
            FROM registrations r
            LEFT JOIN users u ON r.owner_id = u.id
            WHERE u.id IS NULL
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($orphanedByOwner)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Found " . count($orphanedByOwner) . " registrations with invalid owners</h4>";
            echo "<table>";
            echo "<tr><th>Registration ID</th><th>Show ID</th><th>Vehicle ID</th><th>Owner ID</th></tr>";
            foreach ($orphanedByOwner as $reg) {
                echo "<tr class='issue'>";
                echo "<td>{$reg['id']}</td>";
                echo "<td>{$reg['show_id']}</td>";
                echo "<td>{$reg['vehicle_id']}</td>";
                echo "<td>{$reg['owner_id']} (missing)</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div class='success'>";
            echo "<h4>✅ No registrations with invalid owners</h4>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h4>❌ Error checking owners: " . $e->getMessage() . "</h4>";
        echo "</div>";
    }
    
    // Check for registrations with invalid categories (using show_categories table)
    echo "<h3>4. Registrations with Invalid Categories</h3>";
    try {
        $orphanedByCategory = $pdo->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, r.category_id
            FROM registrations r
            LEFT JOIN show_categories sc ON r.category_id = sc.id
            WHERE r.category_id IS NOT NULL AND sc.id IS NULL
            LIMIT 10
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($orphanedByCategory)) {
            echo "<div class='warning'>";
            echo "<h4>⚠️ Found " . count($orphanedByCategory) . " registrations with invalid categories</h4>";
            echo "<table>";
            echo "<tr><th>Registration ID</th><th>Show ID</th><th>Vehicle ID</th><th>Owner ID</th><th>Category ID</th></tr>";
            foreach ($orphanedByCategory as $reg) {
                echo "<tr class='issue'>";
                echo "<td>{$reg['id']}</td>";
                echo "<td>{$reg['show_id']}</td>";
                echo "<td>{$reg['vehicle_id']}</td>";
                echo "<td>{$reg['owner_id']}</td>";
                echo "<td>{$reg['category_id']} (missing)</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div class='success'>";
            echo "<h4>✅ No registrations with invalid categories</h4>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div class='warning'>";
        echo "<h4>⚠️ Category check not available: " . $e->getMessage() . "</h4>";
        echo "<p>This might be normal if show_categories table doesn't exist or category_id column is not used.</p>";
        echo "</div>";
    }
    
    // Test the old vs new logic comparison
    echo "<h2>🔄 Comparing Old vs New Logic</h2>";
    
    try {
        // Old logic (OR condition - more aggressive)
        $oldLogic = $pdo->query("
            SELECT COUNT(*) as count
            FROM registrations r
            LEFT JOIN shows s ON r.show_id = s.id
            LEFT JOIN vehicles v ON r.vehicle_id = v.id
            LEFT JOIN users u ON r.owner_id = u.id
            WHERE s.id IS NULL OR v.id IS NULL OR u.id IS NULL
        ")->fetch()['count'];
        
        // New logic (separate counts)
        $newLogicShow = $pdo->query("
            SELECT COUNT(*) as count
            FROM registrations r
            LEFT JOIN shows s ON r.show_id = s.id
            WHERE s.id IS NULL
        ")->fetch()['count'];
        
        $newLogicVehicle = $pdo->query("
            SELECT COUNT(*) as count
            FROM registrations r
            LEFT JOIN vehicles v ON r.vehicle_id = v.id
            WHERE v.id IS NULL
        ")->fetch()['count'];
        
        $newLogicOwner = $pdo->query("
            SELECT COUNT(*) as count
            FROM registrations r
            LEFT JOIN users u ON r.owner_id = u.id
            WHERE u.id IS NULL
        ")->fetch()['count'];
        
        echo "<div class='info'>";
        echo "<h3>📊 Logic Comparison</h3>";
        echo "<table>";
        echo "<tr><th>Check Type</th><th>Count</th><th>Description</th></tr>";
        echo "<tr><td><strong>Old Logic (OR)</strong></td><td>$oldLogic</td><td>Any registration with ANY missing reference</td></tr>";
        echo "<tr><td>Invalid Shows</td><td>$newLogicShow</td><td>Registrations referencing non-existent shows</td></tr>";
        echo "<tr><td>Invalid Vehicles</td><td>$newLogicVehicle</td><td>Registrations referencing non-existent vehicles</td></tr>";
        echo "<tr><td>Invalid Owners</td><td>$newLogicOwner</td><td>Registrations referencing non-existent users</td></tr>";
        echo "</table>";
        
        if ($oldLogic > 0) {
            echo "<p><strong>Note:</strong> The old logic found $oldLogic total issues. The new logic breaks this down by specific problem type for better diagnosis and targeted cleanup.</p>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Error comparing logic: " . $e->getMessage() . "</h3>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<div class='info'>";
echo "<p><strong>Improved Orphan Detection:</strong> The new logic provides more specific and actionable information about registration integrity issues.</p>";
echo "<p><strong>Benefits:</strong></p>";
echo "<ul>";
echo "<li>Separate reporting for each type of orphan (shows, vehicles, owners, categories)</li>";
echo "<li>More targeted cleanup operations</li>";
echo "<li>Better understanding of data integrity issues</li>";
echo "<li>Safer cleanup with specific queries for each issue type</li>";
echo "</ul>";
echo "<p><strong>Next Step:</strong> <a href='scripts/database_maintenance.php?task=check'>Run the improved maintenance check</a></p>";
echo "</div>";

echo "<p><a href='analyze_actual_database_usage.php'>← Back to Database Analysis</a></p>";
?>
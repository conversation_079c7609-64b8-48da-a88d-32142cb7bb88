<?php
/**
 * Actual Database Usage Analyzer
 * 
 * This script analyzes your PHP files to determine which tables are actually used
 * and what relationships exist in practice, not just in theory.
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

class DatabaseUsageAnalyzer {
    private $db;
    private $tablesInSchema = [];
    private $tablesUsedInCode = [];
    private $relationships = [];
    private $unusedTables = [];
    private $debugMode = false;
    
    public function __construct($debugMode = false) {
        $this->debugMode = $debugMode;
        try {
            $this->db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }
    
    public function analyze() {
        echo "<h1>Actual Database Usage Analysis</h1>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .used { color: green; font-weight: bold; }
            .unused { color: red; font-weight: bold; }
            .warning { color: orange; }
            .section { margin: 30px 0; }
            .code-snippet { background: #f5f5f5; padding: 10px; font-family: monospace; font-size: 12px; }
        </style>";
        
        $this->getTablesFromSchema();
        $this->analyzeCodeUsage();
        $this->findUnusedTables();
        $this->analyzeRelationships();
        $this->generateReport();
    }
    
    private function getTablesFromSchema() {
        $result = $this->db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $this->tablesInSchema = $result;
        
        echo "<div class='section'>";
        echo "<h2>Tables in Database Schema</h2>";
        echo "<p>Found " . count($this->tablesInSchema) . " tables in database</p>";
        echo "<div class='code-snippet'>" . implode(', ', $this->tablesInSchema) . "</div>";
        echo "</div>";
    }
    
    private function analyzeCodeUsage() {
        echo "<div class='section'>";
        echo "<h2>Analyzing PHP Files for Table Usage...</h2>";
        
        $directories = ['controllers', 'models', 'views'];
        // More specific patterns to avoid false matches
        $patterns = [
            '/\bFROM\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bJOIN\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bINSERT\s+INTO\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bUPDATE\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bDELETE\s+FROM\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bSHOW\s+TABLES\s+LIKE\s+[\'"]([a-zA-Z_][a-zA-Z0-9_]*)[\'\"]/i',
            '/\bCREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bALTER\s+TABLE\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            '/\bDROP\s+TABLE\s+(?:IF\s+EXISTS\s+)?`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i',
            // Additional patterns for your codebase
            '/bind\s*\(\s*[\'\"]:tableName[\'\"],\s*[\'"]([a-zA-Z_][a-zA-Z0-9_]*)[\'\"]\s*\)/i',
            '/table_name\s*=\s*[\'"]([a-zA-Z_][a-zA-Z0-9_]*)[\'\"]/i',
            '/TRUNCATE\s+TABLE\s+`?([a-zA-Z_][a-zA-Z0-9_]*)`?\b/i'
        ];
        
        foreach ($directories as $dir) {
            $fullPath = APPROOT . '/' . $dir;
            if (is_dir($fullPath)) {
                $this->scanDirectory($dir, $patterns);
            }
        }
        
        // Also scan root directory for any PHP files
        $this->scanDirectory('', $patterns, false);
        
        echo "<p>Found " . count($this->tablesUsedInCode) . " tables referenced in PHP code</p>";
        echo "</div>";
    }
    
    private function scanDirectory($dir, $patterns, $recursive = true) {
        $fullPath = APPROOT . '/' . $dir;
        if (!is_dir($fullPath)) {
            return;
        }
        
        $iterator = $recursive ? 
            new RecursiveIteratorIterator(new RecursiveDirectoryIterator($fullPath)) :
            new DirectoryIterator($fullPath);
            
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $this->analyzeFile($file->getPathname(), $patterns);
            }
        }
    }
    
    private function analyzeFile($filePath, $patterns) {
        $content = file_get_contents($filePath);
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[1] as $tableName) {
                    $tableName = trim($tableName, '`');
                    
                    // Additional validation: must be a valid table name format
                    if (preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $tableName) && 
                        strlen($tableName) > 2 && 
                        strlen($tableName) < 64) {
                        
                        // Only include if it's actually a table in our schema
                        if (in_array($tableName, $this->tablesInSchema)) {
                            if (!isset($this->tablesUsedInCode[$tableName])) {
                                $this->tablesUsedInCode[$tableName] = [];
                            }
                            
                            if (!in_array($filePath, $this->tablesUsedInCode[$tableName])) {
                                $this->tablesUsedInCode[$tableName][] = $filePath;
                            }
                        }
                    }
                }
            }
        }
    }
    
    private function findUnusedTables() {
        $this->unusedTables = array_diff($this->tablesInSchema, array_keys($this->tablesUsedInCode));
        
        echo "<div class='section'>";
        echo "<h2>Table Usage Analysis</h2>";
        
        echo "<h3>Tables Used in Code (" . count($this->tablesUsedInCode) . ")</h3>";
        echo "<table>";
        echo "<tr><th>Table Name</th><th>Used In Files</th><th>File Count</th></tr>";
        
        foreach ($this->tablesUsedInCode as $table => $files) {
            $fileCount = count($files);
            $fileList = implode('<br>', array_map(function($f) { 
                return basename($f); 
            }, array_slice($files, 0, 5)));
            
            if ($fileCount > 5) {
                $fileList .= "<br><em>... and " . ($fileCount - 5) . " more</em>";
            }
            
            echo "<tr>";
            echo "<td class='used'>$table</td>";
            echo "<td>$fileList</td>";
            echo "<td>$fileCount</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (!empty($this->unusedTables)) {
            echo "<h3 class='unused'>🗑️ Tables Not Found in Your PHP Code (" . count($this->unusedTables) . ")</h3>";
            echo "<div style='background: #fff3cd; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
            echo "<p><strong>⚠️ Important:</strong> These tables exist in your database but aren't referenced in your PHP code. This could mean:</p>";
            echo "<ul>";
            echo "<li>They're truly unused and can be removed</li>";
            echo "<li>They're used by external tools or scripts not analyzed</li>";
            echo "<li>They're legacy tables from old features</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<table>";
            echo "<tr><th>Table Name</th><th>Row Count</th><th>Action You Can Take</th></tr>";
            
            $emptyTables = [];
            $tablesWithData = [];
            
            foreach ($this->unusedTables as $table) {
                try {
                    $count = $this->db->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                    
                    if ($count == 0) {
                        $emptyTables[] = $table;
                        $action = "<span style='color: green;'>✅ <strong>Safe to drop</strong> - Empty table</span>";
                        $rowClass = "style='background: #d4edda;'";
                    } else {
                        $tablesWithData[] = $table;
                        $action = "<span style='color: orange;'>⚠️ <strong>Investigate first</strong> - Has $count rows of data</span>";
                        $rowClass = "style='background: #fff3cd;'";
                    }
                    
                    echo "<tr $rowClass>";
                    echo "<td class='unused'><strong>$table</strong></td>";
                    echo "<td style='text-align: center;'>$count</td>";
                    echo "<td>$action</td>";
                    echo "</tr>";
                } catch (Exception $e) {
                    echo "<tr style='background: #f8d7da;'>";
                    echo "<td class='unused'>$table</td>";
                    echo "<td>Error</td>";
                    echo "<td><span style='color: red;'>❌ Cannot access - check permissions</span></td>";
                    echo "</tr>";
                }
            }
            echo "</table>";
            
            // Provide specific SQL commands
            if (!empty($emptyTables)) {
                echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>";
                echo "<h4>✅ Empty Tables - Safe to Remove</h4>";
                echo "<p>These tables are empty and can likely be dropped safely:</p>";
                echo "<div style='background: #fff; padding: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;'>";
                foreach (array_slice($emptyTables, 0, 5) as $table) {
                    echo "DROP TABLE IF EXISTS `$table`;<br>";
                }
                if (count($emptyTables) > 5) {
                    echo "<em>... and " . (count($emptyTables) - 5) . " more empty tables</em>";
                }
                echo "</div>";
                echo "<p><strong>⚠️ Always backup your database before running these commands!</strong></p>";
                echo "</div>";
            }
            
            if (!empty($tablesWithData)) {
                echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 20px 0;'>";
                echo "<h4>⚠️ Tables with Data - Investigate First</h4>";
                echo "<p>These tables contain data but aren't used in your PHP code. Check what this data is:</p>";
                echo "<div style='background: #fff; padding: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;'>";
                foreach (array_slice($tablesWithData, 0, 3) as $table) {
                    echo "SELECT * FROM `$table` LIMIT 5; -- Check what data is in $table<br>";
                }
                if (count($tablesWithData) > 3) {
                    echo "<em>... check the other " . (count($tablesWithData) - 3) . " tables similarly</em>";
                }
                echo "</div>";
                echo "</div>";
            }
            
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<p class='used'>✅ <strong>Excellent!</strong> All tables in your database are actively used by your PHP code.</p>";
            echo "</div>";
        }
        
        echo "</div>";
    }
    
    private function analyzeRelationships() {
        echo "<div class='section'>";
        echo "<h2>Actual Relationships Found in Code</h2>";
        
        // Analyze JOIN patterns to find relationships
        $joinPatterns = [
            '/JOIN\s+`?(\w+)`?\s+\w+\s+ON\s+\w+\.(\w+)\s*=\s*\w+\.(\w+)/i',
            '/FROM\s+`?(\w+)`?\s+\w+.*?JOIN\s+`?(\w+)`?\s+\w+\s+ON\s+\w+\.(\w+)\s*=\s*\w+\.(\w+)/i'
        ];
        
        $relationships = [];
        
        foreach (['controllers', 'models', 'views'] as $dir) {
            if (is_dir($dir)) {
                $this->findRelationshipsInDirectory($dir, $joinPatterns, $relationships);
            }
        }
        
        if (!empty($relationships)) {
            echo "<table>";
            echo "<tr><th>Relationship</th><th>Found In</th><th>Frequency</th></tr>";
            
            foreach ($relationships as $rel => $info) {
                echo "<tr>";
                echo "<td>$rel</td>";
                echo "<td>" . implode('<br>', array_unique($info['files'])) . "</td>";
                echo "<td>" . $info['count'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No explicit JOIN relationships found in code analysis</p>";
            echo "<p>This might indicate:</p>";
            echo "<ul>";
            echo "<li>Relationships are handled by ORM or framework</li>";
            echo "<li>Simple queries without explicit JOINs</li>";
            echo "<li>Foreign key relationships not enforced in code</li>";
            echo "</ul>";
        }
        
        echo "</div>";
    }
    
    private function findRelationshipsInDirectory($dir, $patterns, &$relationships) {
        $fullPath = APPROOT . '/' . $dir;
        if (!is_dir($fullPath)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($fullPath));
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $content = file_get_contents($file->getPathname());
                
                foreach ($patterns as $pattern) {
                    if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                        foreach ($matches as $match) {
                            $rel = isset($match[4]) ? 
                                "{$match[1]} ↔ {$match[2]} ({$match[3]} = {$match[4]})" :
                                "{$match[1]} ↔ relationship";
                            
                            if (!isset($relationships[$rel])) {
                                $relationships[$rel] = ['count' => 0, 'files' => []];
                            }
                            
                            $relationships[$rel]['count']++;
                            $relationships[$rel]['files'][] = basename($file->getPathname());
                        }
                    }
                }
            }
        }
    }
    
    private function generateReport() {
        echo "<div class='section'>";
        echo "<h2>📊 What This Analysis Tells You</h2>";
        
        $usedCount = count($this->tablesUsedInCode);
        $totalCount = count($this->tablesInSchema);
        $unusedCount = count($this->unusedTables);
        $usagePercentage = round(($usedCount / $totalCount) * 100, 1);
        
        echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba; margin: 20px 0;'>";
        echo "<h3>🎯 Quick Summary</h3>";
        echo "<ul style='font-size: 16px;'>";
        echo "<li><strong>Your database has $totalCount tables total</strong></li>";
        echo "<li><strong>$usedCount tables ($usagePercentage%) are actively used by your website</strong></li>";
        echo "<li><strong>$unusedCount tables appear to be unused</strong></li>";
        echo "</ul>";
        echo "</div>";
        
        // Actionable recommendations based on the data
        echo "<h3>🚀 What You Should Do</h3>";
        
        if ($usagePercentage >= 90) {
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>";
            echo "<h4>✅ Excellent Database Health</h4>";
            echo "<p>Your database is very clean! Almost all tables are being used. No immediate action needed.</p>";
            echo "</div>";
        } elseif ($usagePercentage >= 70) {
            echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>";
            echo "<h4>⚠️ Good Database Health - Minor Cleanup Recommended</h4>";
            echo "<p>Your database is in good shape, but you have some unused tables that could be cleaned up.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>";
            echo "<h4>🔧 Database Cleanup Recommended</h4>";
            echo "<p>You have quite a few unused tables. Consider cleaning them up to improve performance and reduce complexity.</p>";
            echo "</div>";
        }
        
        echo "<h3>📋 Specific Actions You Can Take</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>";
        
        if ($unusedCount > 0) {
            echo "<h4>1. 🗑️ Review Unused Tables ($unusedCount tables)</h4>";
            echo "<p><strong>What to do:</strong></p>";
            echo "<ul>";
            echo "<li>Check if these tables have any data: <code>SELECT COUNT(*) FROM table_name;</code></li>";
            echo "<li>If they're empty and truly unused, you can safely drop them</li>";
            echo "<li>If they have data, investigate why they're not being used in your code</li>";
            echo "</ul>";
            
            echo "<p><strong>Unused tables to investigate:</strong></p>";
            echo "<div style='background: #fff; padding: 10px; border: 1px solid #ccc; font-family: monospace; font-size: 12px;'>";
            echo implode(', ', array_slice($this->unusedTables, 0, 10));
            if (count($this->unusedTables) > 10) {
                echo "<br><em>... and " . (count($this->unusedTables) - 10) . " more (see full list above)</em>";
            }
            echo "</div>";
        }
        
        echo "<h4>2. 🔍 Focus on Your Most Important Tables</h4>";
        echo "<p><strong>These are your most actively used tables:</strong></p>";
        
        // Sort tables by usage frequency
        $sortedTables = $this->tablesUsedInCode;
        uasort($sortedTables, function($a, $b) {
            return count($b) - count($a);
        });
        
        $topTables = array_slice($sortedTables, 0, 5, true);
        echo "<ul>";
        foreach ($topTables as $table => $files) {
            $fileCount = count($files);
            echo "<li><strong>$table</strong> - used in $fileCount files</li>";
        }
        echo "</ul>";
        echo "<p><strong>What to do:</strong> Make sure these tables have proper indexes and are optimized since they're used most frequently.</p>";
        
        echo "<h4>3. 🛠️ Database Maintenance Tasks</h4>";
        echo "<ul>";
        echo "<li><strong>Backup first:</strong> Always backup your database before making changes</li>";
        echo "<li><strong>Check for data:</strong> Before dropping any table, verify it's truly empty</li>";
        echo "<li><strong>Test thoroughly:</strong> After any changes, test your website functionality</li>";
        echo "<li><strong>Document changes:</strong> Keep track of what you remove for future reference</li>";
        echo "</ul>";
        
        echo "</div>";
        
        echo "<h3>🎯 Priority Actions</h3>";
        echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #0066cc; margin: 10px 0;'>";
        
        if ($unusedCount > 10) {
            echo "<p><strong>HIGH PRIORITY:</strong> You have $unusedCount unused tables. Start by checking if the top 5-10 unused tables are truly unnecessary.</p>";
        } elseif ($unusedCount > 0) {
            echo "<p><strong>MEDIUM PRIORITY:</strong> You have $unusedCount unused tables. Review them when you have time for database cleanup.</p>";
        } else {
            echo "<p><strong>LOW PRIORITY:</strong> Your database is clean! Focus on optimizing your most-used tables for performance.</p>";
        }
        
        echo "</div>";
        
        echo "<h3>❓ Need Help?</h3>";
        echo "<p>If you're unsure about any of these recommendations:</p>";
        echo "<ul>";
        echo "<li>Start with tables that have zero data</li>";
        echo "<li>Make backups before making any changes</li>";
        echo "<li>Test one table at a time</li>";
        echo "<li>Keep this analysis report for reference</li>";
        echo "</ul>";
        
        echo "</div>";
        
        echo "<p><em>Analysis completed on: " . date('Y-m-d H:i:s') . " | Database: " . DB_NAME . "</em></p>";
    }
}

// Run the analysis
$debugMode = isset($_GET['debug']) && $_GET['debug'] == '1';
$analyzer = new DatabaseUsageAnalyzer($debugMode);
$analyzer->analyze();
?>
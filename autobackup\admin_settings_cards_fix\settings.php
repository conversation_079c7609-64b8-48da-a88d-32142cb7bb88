<?php require APPROOT . '/views/includes/header.php'; ?>

<style>
    .text-purple {
        color: #6f42c1 !important;
    }
    .bg-purple {
        background-color: #6f42c1 !important;
    }
</style>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">System Settings</h1>
            <p class="text-muted">Manage your application settings and configurations</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> System settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Settings Dashboard -->
    <div class="row g-4 mb-5">

        <!-- Email Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-envelope text-danger fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Email Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure SMTP server, email templates, and notification settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_email" class="stretched-link text-decoration-none">
                        <span class="d-none">View Email Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification System Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-bell text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Notifications</h4>
                    </div>
                    <p class="card-text text-muted">Manage notification settings, SMS providers, queue, testing, and installation.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_notifications" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- QR Code Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-qrcode text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">QR Codes</h4>
                    </div>
                    <p class="card-text text-muted">Configure QR code settings, analytics, and generation options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_qr_codes" class="stretched-link text-decoration-none">
                        <span class="d-none">View QR Code Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Judging & Metrics Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-gavel text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Judging & Metrics</h4>
                    </div>
                    <p class="card-text text-muted">Manage judging metrics, categories, and age weights for shows.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_judging" class="stretched-link text-decoration-none">
                        <span class="d-none">View Judging Settings</span>
                    </a>
                </div>
            </div>
        </div>



        <!-- Payment Methods Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-secondary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-credit-card text-secondary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Payment Methods</h4>
                    </div>
                    <p class="card-text text-muted">Configure payment gateways, methods, and transaction settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_payment" class="stretched-link text-decoration-none">
                        <span class="d-none">View Payment Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Forms & Fields Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-dark bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-dark fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Forms & Fields</h4>
                    </div>
                    <p class="card-text text-muted">Manage System Forms, Fields, and Templates.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_forms" class="stretched-link text-decoration-none">
                        <span class="d-none">View Forms & Fields</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Media & Appearance Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-images text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Media & Appearance</h4>
                    </div>
                    <p class="card-text text-muted">Configure image settings, optimization, and storage options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_media" class="stretched-link text-decoration-none">
                        <span class="d-none">View Media & Appearance</span>
                    </a>
                </div>
            </div>
        </div>



        <!-- Listing Fee Management Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-ticket-alt text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Listing Fees</h4>
                    </div>
                    <p class="card-text text-muted">Manage listing fees, discount codes, and pre-approved coordinators.</p>
                    <a href="<?php echo BASE_URL; ?>/adminListing" class="stretched-link text-decoration-none">
                        <span class="d-none">View Listing Fee Management</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Calendar & Map Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Calendar & Map</h4>
                    </div>
                    <p class="card-text text-muted">Configure calendar display options, map provider settings, and geocode event locations.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="stretched-link text-decoration-none">
                        <span class="d-none">View Calendar & Map Settings</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Club Ownership Verification Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-shield-alt text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Club Ownership</h4>
                        <?php
                        // Get pending verification count
                        $pendingCount = 0;
                        try {
                            $calendarModel = new CalendarModel();
                            $stats = $calendarModel->getVerificationStatistics();
                            $pendingCount = $stats['pending'] ?? 0;
                        } catch (Exception $e) {
                            // Silently handle error
                        }
                        ?>
                        <?php if ($pendingCount > 0): ?>
                            <span class="badge bg-danger ms-2">
                                <?php echo $pendingCount; ?> pending
                            </span>
                        <?php endif; ?>
                    </div>
                    <p class="card-text text-muted">Review and manage club ownership verification requests from users.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_club_ownership" class="stretched-link text-decoration-none">
                        <span class="d-none">View Club Ownership Settings</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Developer Tools Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-purple bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-code text-purple fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Developer Tools</h4>
                    </div>
                    <p class="card-text text-muted">Advanced tools for system administration and development.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_developer" class="stretched-link text-decoration-none">
                        <span class="d-none">View Developer Tools</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Application Details</h5>
                            <ul class="list-unstyled">
                                <li><strong>Version:</strong> <?php echo APP_VERSION; ?></li>
                                <li><strong>Environment:</strong> <?php echo DEBUG_MODE ? 'Development' : 'Production'; ?></li>
                                <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
                                <li><strong>Database:</strong> MySQL</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>System Status</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check-circle text-success me-2"></i> Database Connected</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> File Uploads Working</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Sessions Active</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Security Headers Set</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
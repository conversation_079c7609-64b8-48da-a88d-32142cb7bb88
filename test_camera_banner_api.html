<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Banner API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-response {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .banner-preview {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .banner-image {
            max-width: 200px;
            max-height: 100px;
            object-fit: contain;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Camera Banner API Test</h1>
        
        <div id="status">Loading...</div>
        
        <h3>API Response:</h3>
        <div id="apiResponse"></div>
        
        <h3>Banner Preview:</h3>
        <div id="bannerPreview"></div>
        
        <h3>Test Banner Rotation:</h3>
        <div id="testContainer" style="height: 80px; border: 2px solid #007bff; display: flex; align-items: center; justify-content: center; background: #f8f9fa; margin: 10px 0;">
            <div id="banner-test-content">
                <div class="banner-text">Test Container</div>
            </div>
        </div>
        
        <button onclick="testBannerRotation()" class="btn" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            Start Banner Rotation Test
        </button>
        
        <button onclick="stopBannerRotation()" class="btn" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
            Stop Banner Rotation
        </button>
    </div>

    <script>
        let testBannerInstance = null;
        
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            const responseDiv = document.getElementById('apiResponse');
            const previewDiv = document.getElementById('bannerPreview');
            
            try {
                statusDiv.innerHTML = '<div class="api-response">Testing API endpoint...</div>';
                
                const response = await fetch('/api/camera-banners.php');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = '<div class="api-response success">✅ API working correctly!</div>';
                    
                    responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    
                    // Show banner previews
                    let previewHTML = '';
                    data.banners.forEach((banner, index) => {
                        previewHTML += `
                            <div class="banner-preview">
                                <strong>Banner ${index + 1}:</strong> ${banner.type}
                                ${banner.is_logo ? ' <span style="background: #ffc107; padding: 2px 6px; border-radius: 3px; font-size: 12px;">LOGO</span>' : ''}
                                <br>
                                ${banner.type === 'image' && banner.image_path ? 
                                    `<img src="${banner.image_path}" alt="${banner.alt_text}" class="banner-image" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                                     <div style="display: none; color: red;">❌ Image failed to load: ${banner.image_path}</div>` : 
                                    `<div style="padding: 10px; background: #e9ecef; border-radius: 3px;">${banner.text}</div>`
                                }
                            </div>
                        `;
                    });
                    previewDiv.innerHTML = previewHTML;
                    
                } else {
                    statusDiv.innerHTML = '<div class="api-response error">❌ API returned error</div>';
                    responseDiv.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                }
                
            } catch (error) {
                statusDiv.innerHTML = '<div class="api-response error">❌ API request failed</div>';
                responseDiv.innerHTML = `<pre>Error: ${error.message}</pre>`;
            }
        }
        
        function testBannerRotation() {
            if (window.cameraBanner) {
                console.log('Starting banner rotation test...');
                window.cameraBanner.startRotation('banner-test-content');
            } else {
                alert('Camera banner system not loaded! Check if camera-banner.js is included.');
            }
        }
        
        function stopBannerRotation() {
            if (window.cameraBanner) {
                console.log('Stopping banner rotation test...');
                window.cameraBanner.stopRotation();
            }
        }
        
        // Run test on page load
        document.addEventListener('DOMContentLoaded', function() {
            testAPI();
            
            // Check if camera banner system is loaded
            setTimeout(() => {
                if (window.cameraBanner) {
                    console.log('✅ Camera banner system loaded');
                } else {
                    console.log('❌ Camera banner system not loaded');
                }
            }, 1000);
        });
    </script>
</body>
</html>
<?php
/**
 * Admin Controller
 * 
 * This controller handles all admin-related functionality.
 */
class AdminController extends Controller {
    private $userModel;
    private $showModel;
    private $settingsModel;
    private $printableTemplateModel;
    private $judgingModel;
    private $systemFieldManager;
    private $registrationModel;
    private $vehicleModel;
    private $categoryModel;
    private $defaultCategoryModel;
    private $defaultMetricModel;
    private $defaultAgeWeightModel;
    private $awardModel;
    private $auth;
    private $db;
    

    
    /**
     * Process textarea fields
     * 
     * This function ensures that textarea fields are properly included in the form data
     * even if they are empty or not submitted.
     * 
     * @param array $data Form data
     * @param array $textareaFields Array of textarea field names
     * @return array Updated form data
     */
    private function processTextareaFields($data, $textareaFields) {
        foreach ($textareaFields as $field) {
            if (!isset($data[$field])) {
                $data[$field] = '';
            }
        }
        return $data;
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is an admin
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
        $this->settingsModel = $this->model('SettingsModel');
        $this->printableTemplateModel = $this->model('PrintableTemplateModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->systemFieldManager = $this->model('SystemFieldManager');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->categoryModel = $this->model('CategoryModel');
        $this->defaultCategoryModel = $this->model('DefaultCategoryModel');
        $this->defaultMetricModel = $this->model('DefaultMetricModel');
        $this->defaultAgeWeightModel = $this->model('DefaultAgeWeightModel');
        $this->awardModel = $this->model('AwardModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('admin/dashboard');
    }
    
    // ... [rest of the file content]
}
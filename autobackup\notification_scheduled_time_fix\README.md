# Notification System Database Column Fix

## Issue Description
When subscribing to show notifications with registration, users encountered a fatal error:
```
SQLSTATE[42S22]: Column not found: 1054 Unknown column 'scheduled_time' in 'field list'
```

## Root Cause
The `NotificationModel.php` was trying to insert data into a column named `scheduled_time`, but the actual database table `notification_queue` uses the column name `scheduled_for`.

## Database Schema
The correct `notification_queue` table structure (from `notification_queue.sql`):
```sql
CREATE TABLE `notification_queue` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `subscription_id` int DEFAULT NULL,
  `notification_type` enum('email','sms','push','toast') COLLATE utf8mb4_unicode_ci NOT NULL,
  `event_id` int DEFAULT NULL,
  `event_type` enum('calendar_event','car_show','test') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notification_category` enum('event_reminder','registration_deadline','test') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'event_reminder',
  `scheduled_for` datetime NOT NULL,  -- This is the correct column name
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  -- ... other columns
);
```

## Fix Applied
**File:** `models/NotificationModel.php`
**Method:** `scheduleNotification()` (around line 313-325)

**Before:**
```php
$this->db->query('INSERT INTO notification_queue 
                  (user_id, subscription_id, notification_type, event_id, event_type, 
                   notification_category, scheduled_time, subject, message) 
                  VALUES (:user_id, :subscription_id, :notification_type, :event_id, :event_type, 
                          :notification_category, :scheduled_time, :subject, :message)');
// ...
$this->db->bind(':scheduled_time', $scheduledTime->format('Y-m-d H:i:s'));
```

**After:**
```php
$this->db->query('INSERT INTO notification_queue 
                  (user_id, subscription_id, notification_type, event_id, event_type, 
                   notification_category, scheduled_for, subject, message) 
                  VALUES (:user_id, :subscription_id, :notification_type, :event_id, :event_type, 
                          :notification_category, :scheduled_for, :subject, :message)');
// ...
$this->db->bind(':scheduled_for', $scheduledTime->format('Y-m-d H:i:s'));
```

## Files Modified
1. `models/NotificationModel.php` - Fixed column name from `scheduled_time` to `scheduled_for`

## Files Verified (No Changes Needed)
1. `models/NotificationService.php` - Already using correct column name
2. `cron/process_notifications.php` - Uses NotificationModel methods, no direct SQL
3. `controllers/AdminNotificationController.php` - Uses NotificationModel methods, no direct SQL
4. `controllers/NotificationController.php` - Uses NotificationModel methods, no direct SQL

## Testing
After applying this fix, users should be able to:
1. Subscribe to show notifications without encountering the database error
2. Receive scheduled notifications properly
3. View the notification queue in admin panel

## Impact
- **Risk Level:** Low - This is a simple column name correction
- **Backward Compatibility:** No impact - only fixes broken functionality
- **Data Loss:** None - no existing data is affected

## Date Applied
June 23, 2025

## Version
This fix should be included in the next minor version update.
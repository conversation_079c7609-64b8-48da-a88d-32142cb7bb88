<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Refresh</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
            text-align: center;
        }
        .refresh-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 20px auto;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            width: 100%;
            max-width: 300px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="refresh-container">
        <h1>🚫 Disable PWA Caching</h1>
        <p>Completely disable PWA caching for 100% dynamic site behavior.</p>
        
        <div id="status" class="status info">
            Ready to refresh PWA...
        </div>
        
        <button onclick="refreshPWA()">Disable Caching & Test Banners</button>

        <div style="margin-top: 20px; text-align: left;">
            <h3>What this does:</h3>
            <ol>
                <li>Updates service worker to v1.0.29-no-cache</li>
                <li>Completely disables all PWA caching</li>
                <li>Clears all existing cached files</li>
                <li>Ensures 100% dynamic site behavior</li>
                <li>Redirects to main site for testing</li>
            </ol>
        </div>
    </div>

    <script>
        async function refreshPWA() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.className = 'status info';
                statusDiv.textContent = '🔄 Updating service worker to disable caching...';

                // Force service worker update
                if ('serviceWorker' in navigator) {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        await registration.update();
                        statusDiv.textContent = '🔄 Service worker updated, clearing ALL caches...';

                        // Clear ALL caches
                        if ('caches' in window) {
                            const cacheNames = await caches.keys();
                            await Promise.all(
                                cacheNames.map(cacheName => caches.delete(cacheName))
                            );
                        }
                    }
                }

                // Clear browser storage too
                localStorage.clear();
                sessionStorage.clear();

                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ All caching disabled! Redirecting to test...';
                
                // Redirect to main site with cache buster
                setTimeout(() => {
                    window.location.href = '/?refresh=' + Date.now();
                }, 2000);
                
            } catch (error) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '⚠️ Update completed with warnings. Redirecting...';
                
                setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }
        }
    </script>
</body>
</html>

<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">User Management</h1>
            <p class="text-muted mb-0">Manage all users and their roles across the platform</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/users/add" class="btn btn-success me-2">
                <i class="fas fa-plus me-2 d-none d-sm-inline"></i> Add User
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/dashboard" class="btn btn-outline-primary">
                <i class="fas fa-tachometer-alt me-2 d-none d-sm-inline"></i> Admin Dashboard
            </a>
        </div>
    </div>

    <?php flash('admin_message'); ?>

    <!-- User Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>User Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-primary shadow-sm user-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-primary mb-2">Total Users</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($user_counts['total_users'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">All Registered</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm user-overview-card" 
                                 data-filter="active" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Active Users</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($user_counts['active_users'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Currently Active</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm user-overview-card" 
                                 data-filter="new_month" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">New This Month</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($user_counts['new_this_month'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Recent Signups</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm user-overview-card" 
                                 data-filter="admin" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Admin Users</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($user_counts['admin_users'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Administrators</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Role Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <?php if (!empty($user_stats['role_distribution'])): ?>
                            <?php 
                            $roleLabels = [
                                'admin' => 'Admins',
                                'coordinator' => 'Coordinators', 
                                'judge' => 'Judges',
                                'staff' => 'Staff',
                                'user' => 'Users'
                            ];
                            $totalRoles = array_sum($user_stats['role_distribution']);
                            $displayedRoles = 0;
                            ?>
                            <?php foreach ($user_stats['role_distribution'] as $role => $count): ?>
                                <?php if ($displayedRoles < 4): ?>
                                    <div class="col-6 mb-2">
                                        <h6 class="text-primary"><?php echo $count; ?></h6>
                                        <small class="text-muted"><?php echo $roleLabels[$role] ?? ucfirst($role); ?></small>
                                    </div>
                                    <?php $displayedRoles++; ?>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <p class="text-muted">No role data available</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Activity Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success"><?php echo number_format($user_stats['recent_registrations'] ?? 0); ?></h4>
                            <small class="text-muted">New This Week</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info"><?php echo number_format($user_stats['login_activity'] ?? 0); ?></h4>
                            <small class="text-muted">Active (30 days)</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/users/add" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-user-plus fa-2x mb-2"></i>
                                <span>Add New User</span>
                                <small class="text-muted mt-1">Create user account</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/users/roles" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-user-cog fa-2x mb-2"></i>
                                <span>Manage Roles</span>
                                <small class="text-muted mt-1">Assign user roles</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <a href="<?php echo BASE_URL; ?>/admin/users/manage_staff" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-users-cog fa-2x mb-2"></i>
                                <span>Manage Staff</span>
                                <small class="text-muted mt-1">Staff assignments</small>
                            </a>
                        </div>
                        <div class="col-6 col-md-3">
                            <button onclick="exportUsers()" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-download fa-2x mb-2"></i>
                                <span>Export Users</span>
                                <small class="text-muted mt-1">Download CSV</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Section (Lazy Loaded) -->
    <div class="user-section" id="user-section" style="display: none;">
        <div class="card">
            <div class="card-header bg-primary bg-opacity-25">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <span class="badge bg-primary me-2">User Details</span>
                        <span class="badge bg-secondary" id="user-count-display">0</span>
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-secondary" onclick="closeUserSection()">
                            <i class="fas fa-times"></i> Close
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Search and Filter Controls -->
            <div class="card-body border-bottom">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label for="search-users" class="form-label">Search Users</label>
                        <input type="text" class="form-control" id="search-users" 
                               placeholder="Search by name or email...">
                    </div>
                    <div class="col-md-2">
                        <label for="role-filter" class="form-label">Role</label>
                        <select class="form-select" id="role-filter">
                            <option value="">All Roles</option>
                            <option value="admin">Administrator</option>
                            <option value="coordinator">Coordinator</option>
                            <option value="judge">Judge</option>
                            <option value="staff">Staff</option>
                            <option value="user">User</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status-filter" class="form-label">Status</label>
                        <select class="form-select" id="status-filter">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="per-page-users" class="form-label">Per Page</label>
                        <select class="form-select" id="per-page-users">
                            <option value="10">10</option>
                            <option value="20" selected>20</option>
                            <option value="50">50</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary" onclick="searchUsers()">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearUserSearch()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-success" onclick="exportUsers()">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Loading Indicator -->
            <div class="card-body text-center" id="loading-users">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 text-muted">Loading users...</p>
            </div>
            
            <!-- Users Content (Will be populated via AJAX) -->
            <div id="users-content" style="display: none;">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // User overview card click handlers
    document.querySelectorAll('.user-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadUserSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-users').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchUsers();
        }
    });

    // Filter change handlers
    document.getElementById('role-filter').addEventListener('change', searchUsers);
    document.getElementById('status-filter').addEventListener('change', searchUsers);
    document.getElementById('per-page-users').addEventListener('change', searchUsers);
});

function loadUserSection(filter = 'all') {
    // Show the user section
    const section = document.getElementById('user-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        if (filter === 'active' || filter === 'inactive' || filter === 'pending') {
            document.getElementById('status-filter').value = filter;
        } else if (filter === 'admin') {
            document.getElementById('role-filter').value = 'admin';
        } else if (filter === 'new_month') {
            // For new this month, we'll handle this in the backend
            document.getElementById('status-filter').value = 'new_month';
        }
    }

    // Load users
    loadUsers(1);
}

function closeUserSection() {
    const section = document.getElementById('user-section');
    section.style.display = 'none';
}

function loadUsers(page = 1) {
    const loadingDiv = document.getElementById('loading-users');
    const contentDiv = document.getElementById('users-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-users').value;
    const roleFilter = document.getElementById('role-filter').value;
    const statusFilter = document.getElementById('status-filter').value;
    const perPage = document.getElementById('per-page-users').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        role: roleFilter,
        status: statusFilter
    });

    // Make AJAX request
    fetch(`<?php echo BASE_URL; ?>/admin/loadUsers?` + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderUsers(data);
        } else {
            showUserError(data.error || 'Failed to load users');
        }
    })
    .catch(error => {
        console.error('Error loading users:', error);
        showUserError('Network error occurred');
    });
}

function searchUsers() {
    loadUsers(1);
}

function clearUserSearch() {
    document.getElementById('search-users').value = '';
    document.getElementById('role-filter').value = '';
    document.getElementById('status-filter').value = '';
    loadUsers(1);
}

function renderUsers(data) {
    const loadingDiv = document.getElementById('loading-users');
    const contentDiv = document.getElementById('users-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render users table and pagination
    let html = '';

    if (data.users.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No users found.</p></div>';
    } else {
        html = renderUsersTable(data.users, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update user count display
    document.getElementById('user-count-display').textContent = data.pagination.total_users.toLocaleString();
}

function renderUsersTable(users, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>ID</th><th>Name</th><th>Email</th><th>Role</th><th>Status</th><th>Joined</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    users.forEach(user => {
        html += '<tr>';
        html += '<td><strong>#' + user.id + '</strong></td>';
        html += '<td>';
        if (user.profile_image) {
            html += '<img src="' + user.profile_image + '" class="rounded-circle me-2" width="32" height="32">';
        }
        html += '<strong>' + user.name + '</strong></td>';
        html += '<td>' + user.email + '</td>';
        html += '<td>' + getRoleBadge(user.role) + '</td>';
        html += '<td>' + getStatusBadge(user.status) + '</td>';
        html += '<td>' + formatDate(user.created_at) + '</td>';
        html += '<td>' + getUserActions(user.id, user.role) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderUserPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_users.toLocaleString()} users`;
    html += '</div>';

    return html;
}

function getRoleBadge(role) {
    const roleBadges = {
        'admin': '<span class="badge bg-danger">Administrator</span>',
        'coordinator': '<span class="badge bg-primary">Coordinator</span>',
        'judge': '<span class="badge bg-info">Judge</span>',
        'staff': '<span class="badge bg-warning text-dark">Staff</span>',
        'user': '<span class="badge bg-secondary">User</span>'
    };
    return roleBadges[role] || '<span class="badge bg-light text-dark">' + role + '</span>';
}

function getStatusBadge(status) {
    switch (status) {
        case 'active':
            return '<span class="badge bg-success">Active</span>';
        case 'inactive':
            return '<span class="badge bg-secondary">Inactive</span>';
        case 'pending':
            return '<span class="badge bg-warning text-dark">Pending</span>';
        default:
            return '<span class="badge bg-light text-dark">' + status + '</span>';
    }
}

function getUserActions(userId, userRole) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/admin/users/edit/${userId}" class="btn btn-primary" title="Edit User">
                <i class="fas fa-edit"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/users/assign_role/${userId}" class="btn btn-info" title="Assign Role">
                <i class="fas fa-user-cog"></i>
            </a>
            ${userRole !== 'admin' ? `<button onclick="deleteUser(${userId})" class="btn btn-danger" title="Delete User"><i class="fas fa-trash"></i></button>` : ''}
        </div>
    `;
}

function renderUserPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadUsers(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadUsers(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showUserError(message) {
    const loadingDiv = document.getElementById('loading-users');
    const contentDiv = document.getElementById('users-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        fetch(`<?php echo BASE_URL; ?>/admin/deleteUser/${userId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Reload current page
                loadUsers(getCurrentPage());
            } else {
                alert(data.error || 'Failed to delete user');
            }
        })
        .catch(error => {
            console.error('Error deleting user:', error);
            alert('Network error occurred while deleting user');
        });
    }
}

function getCurrentPage() {
    // Get current page from pagination or default to 1
    const activePage = document.querySelector('.pagination .page-item.active .page-link');
    return activePage ? parseInt(activePage.textContent) : 1;
}

function exportUsers() {
    // Get current filter values
    const search = document.getElementById('search-users').value;
    const roleFilter = document.getElementById('role-filter').value;
    const statusFilter = document.getElementById('status-filter').value;

    // Build export URL with filters
    const params = new URLSearchParams({
        search: search,
        role: roleFilter,
        status: statusFilter,
        export: 'csv'
    });

    const exportUrl = `<?php echo BASE_URL; ?>/admin/exportUsers?` + params.toString();

    // Open export URL in new window
    window.open(exportUrl, '_blank');
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>

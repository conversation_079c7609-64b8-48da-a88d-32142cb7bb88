<!DOCTYPE html>
<html>
<head>
    <title>Cache Bust Verification</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 15px; margin: 10px 0; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #log { background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h2>Cache Bust Verification Test</h2>
    
    <div id="status" class="status info">
        Testing cache-busting mechanism...
    </div>
    
    <button onclick="testCacheBust()">Test Cache Bust</button>
    <button onclick="testCurrentVersion()">Check Current Version</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <div id="log"></div>

    <script>
        function log(msg) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${msg}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(msg, type = 'info') {
            const status = document.getElementById('status');
            status.innerHTML = msg;
            status.className = `status ${type}`;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function testCurrentVersion() {
            log('=== Testing Current Version ===');
            
            if (window.cameraBanner) {
                log('✓ CameraBanner exists');
                log('- Version: ' + window.cameraBanner.version);
                updateStatus(`Current version: ${window.cameraBanner.version}`, 'success');
            } else {
                log('✗ CameraBanner not found');
                updateStatus('CameraBanner not loaded', 'error');
            }
        }
        
        function testCacheBust() {
            log('=== Testing Cache Bust Mechanism ===');
            updateStatus('Testing cache bust...', 'info');
            
            // Remove existing script
            const existingScript = document.querySelector('script[src*="camera-banner.js"]');
            if (existingScript) {
                log('✓ Found existing script, removing...');
                existingScript.remove();
                // Clear the global variable
                delete window.cameraBanner;
                log('✓ Cleared window.cameraBanner');
            } else {
                log('⚠ No existing script found');
            }
            
            // Load fresh script with cache bust
            const timestamp = Date.now();
            const script = document.createElement('script');
            script.src = `/public/js/camera-banner.js?v=${timestamp}&cache=bust`;
            
            log(`Loading script: ${script.src}`);
            
            script.onload = function() {
                log('✓ Script loaded successfully');
                
                setTimeout(() => {
                    if (window.cameraBanner) {
                        log('✓ CameraBanner instance created');
                        log('- New version: ' + window.cameraBanner.version);
                        log('- Banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'loading...'));
                        updateStatus(`Cache bust successful! Version: ${window.cameraBanner.version}`, 'success');
                        
                        // Test banner loading
                        if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                            log(`✓ Banners already loaded: ${window.cameraBanner.banners.length}`);
                        } else {
                            log('⚠ Banners not loaded yet, attempting to load...');
                            window.cameraBanner.loadBanners().then(() => {
                                log(`✓ Banners loaded: ${window.cameraBanner.banners.length}`);
                            }).catch(error => {
                                log('✗ Banner loading failed: ' + error.message);
                            });
                        }
                    } else {
                        log('✗ CameraBanner instance not created after script load');
                        updateStatus('Cache bust failed - no instance created', 'error');
                    }
                }, 100);
            };
            
            script.onerror = function() {
                log('✗ Script loading failed');
                updateStatus('Cache bust failed - script loading error', 'error');
            };
            
            document.head.appendChild(script);
        }
        
        // Initial test
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, checking initial state...');
                testCurrentVersion();
            }, 1000);
        });
    </script>
</body>
</html>
/**
 * Calendar Advanced Filtering System - BACKUP VERSION
 * 
 * This script provides advanced filtering functionality for the calendar views
 * (month, week, day, list, and map).
 * 
 * TIMEZONE IMPLEMENTATION: Updated to handle timezone-aware date filtering
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize filter state
    const activeFilters = {
        startDate: null,
        endDate: null,
        state: '',
        city: '',
        venue: '',
        clubs: [],
        radius: 50,
        lat: null,
        lng: null,
        keyword: '',
        categories: [],
        tags: [],
        priceMin: 0,
        priceMax: 500,
        currentView: 'calendar' // 'calendar' or 'map'
    };
    
    // Filter elements
    let startDateFilter = document.getElementById('start-date-filter');
    let endDateFilter = document.getElementById('end-date-filter');
    let stateFilter = document.getElementById('state-filter');
    let cityFilter = document.getElementById('city-filter');
    let venueFilter = document.getElementById('venue-filter');
    let radiusFilter = document.getElementById('radius-filter');
    let keywordFilter = document.getElementById('keyword-filter');
    let categoryFilter = document.getElementById('category-filter');
    let tagFilter = document.getElementById('tag-filter');
    let priceRangeSlider = document.getElementById('price-range-slider');
    let priceRangeValue = document.getElementById('price-range-value');
    let priceMinInput = document.getElementById('price-min');
    let priceMaxInput = document.getElementById('price-max');
    
    // Initialize filter system
    initFilters();
    
    /**
     * Initialize the filter system
     */
    function initFilters() {
        // Initialize filter elements
        initFilterElements();
        
        // Initialize event listeners
        initEventListeners();
        
        // Initialize jQuery UI elements if jQuery is available
        initJQueryUI();
        
        // Make the filter system available globally
        window.calendarFilters = {
            activeFilters: activeFilters,
            updateCalendarFilters: updateCalendarFilters,
            getFilterParams: getFilterParams,
            resetFilters: resetFilters,
            searchLocation: searchLocation
        };
    }
    
    /**
     * Initialize filter elements
     */
    function initFilterElements() {
        // Get filter elements
        startDateFilter = document.getElementById('start-date-filter');
        endDateFilter = document.getElementById('end-date-filter');
        stateFilter = document.getElementById('state-filter');
        cityFilter = document.getElementById('city-filter');
        venueFilter = document.getElementById('venue-filter');
        radiusFilter = document.getElementById('radius-filter');
        keywordFilter = document.getElementById('keyword-filter');
        categoryFilter = document.getElementById('category-filter');
        tagFilter = document.getElementById('tag-filter');
        priceRangeSlider = document.getElementById('price-range-slider');
        priceRangeValue = document.getElementById('price-range-value');
        priceMinInput = document.getElementById('price-min');
        priceMaxInput = document.getElementById('price-max');
        
        // Set default values
        if (radiusFilter) {
            radiusFilter.value = activeFilters.radius;
            const radiusValue = document.getElementById('radius-value');
            if (radiusValue) {
                radiusValue.textContent = activeFilters.radius;
            }
        }
        
        if (priceMinInput) priceMinInput.value = activeFilters.priceMin;
        if (priceMaxInput) priceMaxInput.value = activeFilters.priceMax;
    }
    
    /**
     * Initialize event listeners for filter elements
     */
    function initEventListeners() {
        // Date filters
        if (startDateFilter) {
            startDateFilter.addEventListener('change', function() {
                activeFilters.startDate = this.value ? this.value : null;
            });
        }
        
        if (endDateFilter) {
            endDateFilter.addEventListener('change', function() {
                activeFilters.endDate = this.value ? this.value : null;
            });
        }
        
        // Location filters
        if (stateFilter) {
            stateFilter.addEventListener('change', function() {
                activeFilters.state = this.value;
                
                // Load cities for the selected state
                if (this.value) {
                    loadCities(this.value);
                } else {
                    // Clear city filter
                    if (cityFilter) {
                        cityFilter.innerHTML = '<option value="">All Cities</option>';
                        activeFilters.city = '';
                    }
                }
            });
            
            // Load states on page load
            loadStates();
        }
        
        if (cityFilter) {
            cityFilter.addEventListener('change', function() {
                activeFilters.city = this.value;
            });
        }
        
        if (venueFilter) {
            venueFilter.addEventListener('change', function() {
                activeFilters.venue = this.value;
            });
            
            // Load venues on page load
            loadVenues();
        }
        
        // Radius filter
        if (radiusFilter) {
            radiusFilter.addEventListener('input', function() {
                activeFilters.radius = parseInt(this.value);
                const radiusValue = document.getElementById('radius-value');
                if (radiusValue) {
                    radiusValue.textContent = this.value;
                }
            });
        }
        
        // Keyword filter
        if (keywordFilter) {
            keywordFilter.addEventListener('input', function() {
                activeFilters.keyword = this.value;
            });
        }
        
        // Category filter
        if (categoryFilter) {
            categoryFilter.addEventListener('change', function() {
                activeFilters.categories = Array.from(this.selectedOptions).map(option => option.value).filter(value => value !== '');
            });
            
            // Load categories on page load
            loadCategories();
        }
        
        // Tag filter
        if (tagFilter) {
            tagFilter.addEventListener('change', function() {
                activeFilters.tags = Array.from(this.selectedOptions).map(option => option.value).filter(value => value !== '');
            });
            
            // Load tags on page load
            loadTags();
        }
        
        // Price range inputs
        if (priceMinInput) {
            priceMinInput.addEventListener('change', function() {
                const value = parseInt(this.value);
                if (!isNaN(value) && value >= 0) {
                    activeFilters.priceMin = value;
                    
                    // Update slider if it exists
                    if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && $('#price-range-slider').length) {
                        $('#price-range-slider').slider('values', 0, value);
                        updatePriceRangeDisplay();
                    }
                }
            });
        }
        
        if (priceMaxInput) {
            priceMaxInput.addEventListener('change', function() {
                const value = parseInt(this.value);
                if (!isNaN(value) && value >= 0) {
                    activeFilters.priceMax = value;
                    
                    // Update slider if it exists
                    if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && $('#price-range-slider').length) {
                        $('#price-range-slider').slider('values', 1, value);
                        updatePriceRangeDisplay();
                    }
                }
            });
        }
        
        // Calendar checkboxes
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateCalendarFilters();
            });
        });
        
        // Reset filters button
        const resetFiltersBtn = document.getElementById('reset-filters');
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', resetFilters);
        }
        
        // Apply filters button
        const applyFiltersBtn = document.getElementById('apply-filters');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', function() {
                // This will be handled by the calendar view
            });
        }
        
        // Location search
        const locationSearchBtn = document.getElementById('search-location-btn');
        if (locationSearchBtn) {
            locationSearchBtn.addEventListener('click', searchLocation);
        }
        
        const locationSearchInput = document.getElementById('location-search');
        if (locationSearchInput) {
            locationSearchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchLocation();
                }
            });
        }
        
        // Filter toggle
        const filterToggle = document.getElementById('filter-toggle');
        const filterContent = document.getElementById('filter-content');
        
        if (filterToggle && filterContent) {
            filterToggle.addEventListener('click', function() {
                // Toggle the filter content
                if (filterContent.style.display === 'none') {
                    filterContent.style.display = 'block';
                    filterToggle.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Filters';
                } else {
                    filterContent.style.display = 'none';
                    filterToggle.innerHTML = '<i class="fas fa-chevron-down"></i> Show Filters';
                }
            });
        }
    }
    
    /**
     * Initialize jQuery UI elements
     */
    function initJQueryUI() {
        // Initialize price range slider if jQuery UI is available
        if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && $('#price-range-slider').length) {
            $('#price-range-slider').slider({
                range: true,
                min: 0,
                max: 500,
                values: [activeFilters.priceMin, activeFilters.priceMax],
                slide: function(event, ui) {
                    activeFilters.priceMin = ui.values[0];
                    activeFilters.priceMax = ui.values[1];
                    updatePriceRangeDisplay();
                }
            });
            
            // Initial display
            updatePriceRangeDisplay();
        }
    }
    
    /**
     * Update price range display
     */
    function updatePriceRangeDisplay() {
        if (priceRangeValue) {
            priceRangeValue.textContent = `$${activeFilters.priceMin} - $${activeFilters.priceMax}`;
        }
        
        if (priceMinInput) {
            priceMinInput.value = activeFilters.priceMin;
        }
        
        if (priceMaxInput) {
            priceMaxInput.value = activeFilters.priceMax;
        }
    }
    
    /**
     * Update calendar filters based on checkboxes
     */
    function updateCalendarFilters() {
        // Get selected calendar IDs
        const selectedCalendars = [];
        document.querySelectorAll('.calendar-checkbox:checked').forEach(checkbox => {
            selectedCalendars.push(checkbox.value);
        });
        
        // Update active filters
        activeFilters.clubs = selectedCalendars;
    }
    
    /**
     * Reset all filters to default values
     */
    function resetFilters() {
        // Reset date filters
        if (startDateFilter) startDateFilter.value = '';
        if (endDateFilter) endDateFilter.value = '';
        
        // Reset location filters
        if (stateFilter) stateFilter.value = '';
        if (cityFilter) {
            cityFilter.innerHTML = '<option value="">All Cities</option>';
            cityFilter.value = '';
        }
        if (venueFilter) venueFilter.value = '';
        
        // Reset radius filter
        if (radiusFilter) {
            radiusFilter.value = 50;
            const radiusValue = document.getElementById('radius-value');
            if (radiusValue) radiusValue.textContent = '50';
        }
        
        // Reset keyword filter
        if (keywordFilter) keywordFilter.value = '';
        
        // Reset calendar checkboxes
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        
        // Reset category filter
        if (categoryFilter) {
            for (let i = 0; i < categoryFilter.options.length; i++) {
                categoryFilter.options[i].selected = false;
            }
        }
        
        // Reset tag filter
        if (tagFilter) {
            for (let i = 0; i < tagFilter.options.length; i++) {
                tagFilter.options[i].selected = false;
            }
        }
        
        // Reset price range slider
        if (typeof $ !== 'undefined' && typeof $.fn.slider !== 'undefined' && $('#price-range-slider').length) {
            $('#price-range-slider').slider('values', [0, 500]);
            if (priceRangeValue) priceRangeValue.textContent = '$0 - $500';
            if (priceMinInput) priceMinInput.value = 0;
            if (priceMaxInput) priceMaxInput.value = 500;
        }
        
        // Reset active filters object
        activeFilters.startDate = null;
        activeFilters.endDate = null;
        activeFilters.state = '';
        activeFilters.city = '';
        activeFilters.venue = '';
        activeFilters.clubs = [];
        activeFilters.radius = 50;
        activeFilters.lat = null;
        activeFilters.lng = null;
        activeFilters.keyword = '';
        activeFilters.categories = [];
        activeFilters.tags = [];
        activeFilters.priceMin = 0;
        activeFilters.priceMax = 500;
    }
    
    /**
     * Load states for the filter
     */
    function loadStates() {
        if (!stateFilter) return;
        
        fetch(URLROOT + '/calendar/getStates')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (stateFilter.options.length > 1) {
                    stateFilter.remove(1);
                }
                
                // Add states to the dropdown
                data.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state.state;
                    option.textContent = state.state;
                    stateFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading states:', error);
            });
    }
    
    /**
     * Load cities for the selected state
     * 
     * @param {string} state The selected state
     */
    function loadCities(state) {
        if (!cityFilter) return;
        
        fetch(URLROOT + '/calendar/getCities?state=' + encodeURIComponent(state))
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (cityFilter.options.length > 1) {
                    cityFilter.remove(1);
                }
                
                // Add cities to the dropdown
                data.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.city;
                    option.textContent = city.city;
                    cityFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading cities:', error);
            });
    }
    
    /**
     * Load venues for the filter
     */
    function loadVenues() {
        if (!venueFilter) return;
        
        fetch(URLROOT + '/calendar/getVenues')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (venueFilter.options.length > 1) {
                    venueFilter.remove(1);
                }
                
                // Add venues to the dropdown
                data.forEach(venue => {
                    const option = document.createElement('option');
                    option.value = venue.id;
                    option.textContent = venue.name;
                    venueFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading venues:', error);
            });
    }
    
    /**
     * Load categories for the filter
     */
    function loadCategories() {
        if (!categoryFilter) return;
        
        fetch(URLROOT + '/calendar/getCategories')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                // Add categories to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = `${category.name} (${category.event_count})`;
                        categoryFilter.appendChild(option);
                    });
                } else {
                    // If no categories, add a placeholder option
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No categories available';
                    categoryFilter.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error loading categories:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load categories';
                
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                categoryFilter.appendChild(option);
            });
    }
    
    /**
     * Load tags for the filter
     */
    function loadTags() {
        if (!tagFilter) return;
        
        fetch(URLROOT + '/calendar/getTags')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                // Add tags to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag.id;
                        option.textContent = `${tag.name} (${tag.event_count})`;
                        tagFilter.appendChild(option);
                    });
                } else {
                    // If no tags, add a placeholder option
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No tags available';
                    tagFilter.appendChild(option);
                }
            })
            .catch(error => {
                console.error('Error loading tags:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load tags';
                
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                tagFilter.appendChild(option);
            });
    }

    /**
     * Search for a location and update filters
     */
    function searchLocation() {
        const locationInput = document.getElementById('location-search');
        if (!locationInput || !locationInput.value) return;
        
        // This will be handled by the map view
        if (activeFilters.currentView === 'map' && window.mapEvents && typeof window.mapEvents.searchLocation === 'function') {
            window.mapEvents.searchLocation();
        }
    }
    
    /**
     * Get filter parameters for API calls
     * 
     * @return {string} Filter parameters as a query string
     */
    function getFilterParams() {
        const params = [];
        
        // Add calendar IDs
        if (activeFilters.clubs.length > 0) {
            params.push(`calendar_id=${activeFilters.clubs.join(',')}`);
        }
        
        // Add date range
        if (activeFilters.startDate) {
            params.push(`start=${activeFilters.startDate}`);
        }
        
        if (activeFilters.endDate) {
            params.push(`end=${activeFilters.endDate}`);
        }
        
        // Add location filters
        if (activeFilters.state) {
            params.push(`state=${encodeURIComponent(activeFilters.state)}`);
        }
        
        if (activeFilters.city) {
            params.push(`city=${encodeURIComponent(activeFilters.city)}`);
        }
        
        if (activeFilters.venue) {
            params.push(`venue_id=${activeFilters.venue}`);
        }
        
        // Add keyword filter
        if (activeFilters.keyword) {
            params.push(`keyword=${encodeURIComponent(activeFilters.keyword)}`);
        }
        
        if (activeFilters.lat && activeFilters.lng && activeFilters.radius) {
            params.push(`lat=${activeFilters.lat}`);
            params.push(`lng=${activeFilters.lng}`);
            params.push(`radius=${activeFilters.radius}`);
        }
        
        if (activeFilters.categories.length > 0) {
            params.push(`category=${activeFilters.categories.join(',')}`);
        }
        
        if (activeFilters.tags.length > 0) {
            params.push(`tag=${activeFilters.tags.join(',')}`);
        }
        
        if (activeFilters.priceMin > 0 || activeFilters.priceMax < 500) {
            params.push(`price_range=${activeFilters.priceMin}-${activeFilters.priceMax}`);
        }
        
        return params.join('&');
    }
});
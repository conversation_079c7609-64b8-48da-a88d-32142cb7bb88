/**
 * Calendar Advanced Filtering System
 * 
 * This script provides advanced filtering functionality for the calendar views
 * (month, week, day, list, and map).
 */

document.addEventListener('DOMContentLoaded', function() {
    // Debug mode detection
    const DEBUG_MODE = typeof window.DEBUG_MODE !== 'undefined' ? window.DEBUG_MODE : false;
    
    if (DEBUG_MODE) {
        console.log('Calendar filters system initializing...');
    }
    
    // Initialize filter state
    const activeFilters = {
        startDate: null,
        endDate: null,
        calendars: [],
        state: '',
        city: '',
        venue: '',
        clubs: [],
        radius: document.getElementById('radius-filter') ? parseInt(document.getElementById('radius-filter').value) : 50,
        lat: null,
        lng: null,
        keyword: '',
        categories: [],
        tags: [],
        priceMin: 0,
        priceMax: 500,
        currentView: 'calendar' // 'calendar' or 'map'
    };

    // Initialize filter elements
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const stateFilter = document.getElementById('state-filter');
    const cityFilter = document.getElementById('city-filter');
    const venueFilter = document.getElementById('venue-filter');
    const clubFilter = document.getElementById('club-filter');
    const radiusFilter = document.getElementById('radius-filter');
    const radiusValue = document.getElementById('radius-value');
    const locationSearch = document.getElementById('location-search');
    const searchLocationBtn = document.getElementById('search-location-btn');
    const keywordFilter = document.getElementById('keyword-filter');
    const categoryFilter = document.getElementById('category-filter');
    const tagFilter = document.getElementById('tag-filter');
    const priceRangeValue = document.getElementById('price-range-value');
    const priceMinInput = document.getElementById('price-min');
    const priceMaxInput = document.getElementById('price-max');
    const applyFiltersBtn = document.getElementById('apply-filters');
    const resetFiltersBtn = document.getElementById('reset-filters');

    // Initialize event listeners
    initEventListeners();
    
    // Initialize calendar filters
    updateCalendarFilters();
    
    // Load filter options
    loadFilterOptions();
    
    // Initialize calendar checkboxes
    document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateCalendarFilters);
    });
    
    /**
     * Initialize event listeners for filter elements
     */
    function initEventListeners() {
        // Date range inputs
        if (startDateInput) {
            startDateInput.addEventListener('change', function() {
                activeFilters.startDate = this.value ? this.value + ' 00:00:00' : null;
            });
        }
        
        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                activeFilters.endDate = this.value ? this.value + ' 23:59:59' : null;
            });
        }
        
        // State filter
        if (stateFilter) {
            stateFilter.addEventListener('change', function() {
                activeFilters.state = this.value;
                
                // Reset city filter when state changes
                if (cityFilter) {
                    cityFilter.value = '';
                    activeFilters.city = '';
                    
                    // Load cities for the selected state
                    if (this.value) {
                        loadCities(this.value);
                    } else {
                        // Clear city options except the first one
                        while (cityFilter.options.length > 1) {
                            cityFilter.remove(1);
                        }
                    }
                }
            });
        }
        
        // City filter
        if (cityFilter) {
            cityFilter.addEventListener('change', function() {
                activeFilters.city = this.value;
                
                // Load venues for the selected city
                if (this.value && venueFilter) {
                    loadVenues(activeFilters.state, this.value);
                } else if (venueFilter) {
                    // Clear venue options except the first one
                    while (venueFilter.options.length > 1) {
                        venueFilter.remove(1);
                    }
                }
            });
        }
        
        // Venue filter
        if (venueFilter) {
            venueFilter.addEventListener('change', function() {
                activeFilters.venue = this.value;
            });
        }
        
        // Club filter
        if (clubFilter) {
            clubFilter.addEventListener('change', function() {
                const selectedOptions = Array.from(this.selectedOptions);
                activeFilters.clubs = selectedOptions.map(option => option.value).filter(value => value !== '');
            });
        }
        
        // Radius filter
        if (radiusFilter) {
            radiusFilter.addEventListener('input', function() {
                activeFilters.radius = parseInt(this.value);
                if (radiusValue) {
                    radiusValue.textContent = this.value;
                }
            });
        }
        
        // Location search
        if (searchLocationBtn && locationSearch) {
            searchLocationBtn.addEventListener('click', function() {
                const location = locationSearch.value.trim();
                if (location) {
                    geocodeLocation(location);
                }
            });
            
            // Also trigger search on Enter key
            locationSearch.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const location = this.value.trim();
                    if (location) {
                        geocodeLocation(location);
                    }
                }
            });
        }
        
        // Keyword filter
        if (keywordFilter) {
            keywordFilter.addEventListener('input', function() {
                activeFilters.keyword = this.value.trim();
            });
        }
        
        // Category filter
        if (categoryFilter) {
            categoryFilter.addEventListener('change', function() {
                const selectedOptions = Array.from(this.selectedOptions);
                activeFilters.categories = selectedOptions.map(option => option.value).filter(value => value !== '');
            });
        }
        
        // Tag filter
        if (tagFilter) {
            tagFilter.addEventListener('change', function() {
                const selectedOptions = Array.from(this.selectedOptions);
                activeFilters.tags = selectedOptions.map(option => option.value).filter(value => value !== '');
            });
        }
        
        // Apply filters button
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', applyFilters);
        }
        
        // Reset filters button
        if (resetFiltersBtn) {
            resetFiltersBtn.addEventListener('click', resetFilters);
        }
    }
    
    /**
     * Update calendar filters based on checkboxes
     */
    function updateCalendarFilters() {
        const selectedCalendars = [];
        
        document.querySelectorAll('.calendar-checkbox:checked').forEach(checkbox => {
            selectedCalendars.push(checkbox.value);
        });
        
        activeFilters.calendars = selectedCalendars;
        
        // Log the selected calendars for debugging
        console.log('Selected calendars (advanced filter):', selectedCalendars);
        
        // Synchronize with quick calendar toggles
        document.querySelectorAll('.calendar-toggle').forEach(toggle => {
            const calendarId = toggle.value;
            const shouldBeChecked = selectedCalendars.includes(calendarId);
            
            // Only update if the state is different to avoid infinite loops
            if (toggle.checked !== shouldBeChecked) {
                toggle.checked = shouldBeChecked;
                if (DEBUG_MODE) {
                    console.log(`Synchronized quick toggle for calendar ${calendarId} to ${shouldBeChecked}`);
                }
            }
        });
    }

    /**
     * Load states for the filter
     */
    function loadStates() {
        if (!stateFilter) return;
        
        fetch(URLROOT + '/calendar/getStates')
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (stateFilter.options.length > 1) {
                    stateFilter.remove(1);
                }
                
                // Add states to the dropdown
                data.forEach(state => {
                    const option = document.createElement('option');
                    option.value = state.state;
                    option.textContent = `${state.state} (${state.event_count})`;
                    stateFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading states:', error);
            });
    }

    /**
     * Load cities for the filter based on selected state
     * 
     * @param {string} state Selected state
     */
    function loadCities(state) {
        if (!cityFilter) return;
        
        fetch(URLROOT + '/calendar/getCities?state=' + encodeURIComponent(state))
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (cityFilter.options.length > 1) {
                    cityFilter.remove(1);
                }
                
                // Add cities to the dropdown
                data.forEach(city => {
                    const option = document.createElement('option');
                    option.value = city.city;
                    option.textContent = `${city.city} (${city.event_count})`;
                    cityFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading cities:', error);
            });
    }

    /**
     * Load venues for the filter based on selected state and city
     * 
     * @param {string} state Selected state
     * @param {string} city Selected city
     */
    function loadVenues(state, city) {
        if (!venueFilter) return;
        
        const params = new URLSearchParams();
        if (state) params.append('state', state);
        if (city) params.append('city', city);
        
        fetch(URLROOT + '/calendar/getVenues?' + params.toString())
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (venueFilter.options.length > 1) {
                    venueFilter.remove(1);
                }
                
                // Add venues to the dropdown
                data.forEach(venue => {
                    const option = document.createElement('option');
                    option.value = venue.id;
                    option.textContent = `${venue.name} (${venue.event_count})`;
                    venueFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading venues:', error);
            });
    }

    /**
     * Load clubs for the filter
     */
    function loadClubs() {
        if (!clubFilter) return;
        
        fetch(URLROOT + '/calendar/getClubs')
            .then(response => response.json())
            .then(data => {
                // Clear existing options except the first one
                while (clubFilter.options.length > 1) {
                    clubFilter.remove(1);
                }
                
                // Add clubs to the dropdown
                data.forEach(club => {
                    const option = document.createElement('option');
                    option.value = club.id;
                    option.textContent = `${club.name} (${club.event_count})`;
                    clubFilter.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading clubs:', error);
            });
    }
    
    /**
     * Load categories for the filter
     */
    function loadCategories() {
        if (!categoryFilter) return;
        
        // Disable the category filter while loading
        categoryFilter.disabled = true;
        
        fetch(URLROOT + '/calendar/getCategories')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                // Add categories to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = `${category.name} (${category.event_count})`;
                        categoryFilter.appendChild(option);
                    });
                    // Enable the filter since we have categories
                    categoryFilter.disabled = false;
                } else {
                    // If no categories, add a placeholder option and keep disabled
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No categories available';
                    categoryFilter.appendChild(option);
                    
                    // Keep the filter disabled
                    categoryFilter.disabled = true;
                    
                    // Remove category from active filters
                    activeFilters.categories = [];
                    
                    if (DEBUG_MODE) {
                        console.log('No categories available, disabling category filter');
                    }
                }
            })
            .catch(error => {
                console.error('Error loading categories:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load categories';
                
                // Clear existing options except the first one
                while (categoryFilter.options.length > 1) {
                    categoryFilter.remove(1);
                }
                
                categoryFilter.appendChild(option);
                
                // Keep the filter disabled
                categoryFilter.disabled = true;
                
                // Remove category from active filters
                activeFilters.categories = [];
            });
    }
    
    /**
     * Load tags for the filter
     */
    function loadTags() {
        if (!tagFilter) return;
        
        // Disable the tag filter while loading
        tagFilter.disabled = true;
        
        fetch(URLROOT + '/calendar/getTags')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                // Add tags to the dropdown
                if (Array.isArray(data) && data.length > 0) {
                    data.forEach(tag => {
                        const option = document.createElement('option');
                        option.value = tag.id;
                        option.textContent = `${tag.name} (${tag.event_count})`;
                        tagFilter.appendChild(option);
                    });
                    // Enable the filter since we have tags
                    tagFilter.disabled = false;
                } else {
                    // If no tags, add a placeholder option and keep disabled
                    const option = document.createElement('option');
                    option.disabled = true;
                    option.textContent = 'No tags available';
                    tagFilter.appendChild(option);
                    
                    // Keep the filter disabled
                    tagFilter.disabled = true;
                    
                    // Remove tags from active filters
                    activeFilters.tags = [];
                    
                    if (DEBUG_MODE) {
                        console.log('No tags available, disabling tag filter');
                    }
                }
            })
            .catch(error => {
                console.error('Error loading tags:', error);
                
                // Add a placeholder option on error
                const option = document.createElement('option');
                option.disabled = true;
                option.textContent = 'Failed to load tags';
                
                // Clear existing options except the first one
                while (tagFilter.options.length > 1) {
                    tagFilter.remove(1);
                }
                
                tagFilter.appendChild(option);
                
                // Keep the filter disabled
                tagFilter.disabled = true;
                
                // Remove tags from active filters
                activeFilters.tags = [];
            });
    }
    
    /**
     * Initialize price range slider
     */
    function initPriceRangeSlider() {
        // Check if noUiSlider is available
        if (typeof noUiSlider === 'undefined') {
            console.error('noUiSlider is not available');
            return;
        }
        
        const priceRangeSlider = document.getElementById('price-range-slider');
        if (!priceRangeSlider) return;
        
        // Initialize slider
        noUiSlider.create(priceRangeSlider, {
            start: [0, 500],
            connect: true,
            range: {
                'min': 0,
                'max': 500
            },
            step: 5,
            format: {
                to: function (value) {
                    return Math.round(value);
                },
                from: function (value) {
                    return Number(value);
                }
            }
        });
        
        // Update price range value display and hidden inputs
        priceRangeSlider.noUiSlider.on('update', function (values, handle) {
            const min = values[0];
            const max = values[1];
            
            if (priceRangeValue) {
                priceRangeValue.textContent = `$${min} - $${max}`;
            }
            
            if (priceMinInput) {
                priceMinInput.value = min;
                activeFilters.priceMin = parseInt(min);
            }
            
            if (priceMaxInput) {
                priceMaxInput.value = max;
                activeFilters.priceMax = parseInt(max);
            }
        });
    }
    
    /**
     * Load all filter options
     */
    function loadFilterOptions() {
        loadStates();
        loadClubs();
        loadCategories();
        loadTags();
        
        // Initialize price range slider if available
        if (document.getElementById('price-range-slider')) {
            initPriceRangeSlider();
        }
    }
    
    /**
     * Geocode a location string to coordinates
     * 
     * @param {string} location Location string to geocode
     */
    function geocodeLocation(location) {
        // Show loading indicator
        if (searchLocationBtn) {
            searchLocationBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
            searchLocationBtn.disabled = true;
        }
        
        // Use the server-side geocoding endpoint
        fetch(URLROOT + '/calendar/geocode?address=' + encodeURIComponent(location))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    activeFilters.lat = data.lat;
                    activeFilters.lng = data.lng;
                    
                    // Update UI to show the geocoded location
                    if (locationSearch) {
                        locationSearch.value = data.formatted_address || location;
                    }
                    
                    // Apply filters automatically
                    applyFilters();
                } else {
                    alert('Location not found. Please try a different search term.');
                }
            })
            .catch(error => {
                console.error('Error geocoding location:', error);
                alert('Error geocoding location. Please try again.');
            })
            .finally(() => {
                // Reset button
                if (searchLocationBtn) {
                    searchLocationBtn.innerHTML = '<i class="fas fa-search"></i>';
                    searchLocationBtn.disabled = false;
                }
            });
    }
    
    /**
     * Apply filters to the current view
     */
    function applyFilters() {
        // Initialize calendar filters first
        updateCalendarFilters();
        
        if (DEBUG_MODE) {
            console.log('Applying filters with calendars:', activeFilters.calendars);
        }
        
        // Check if any calendars are selected
        if (activeFilters.calendars.length === 0) {
            if (DEBUG_MODE) {
                console.log('No calendars selected, showing empty calendar');
            }
            
            // For FullCalendar implementation
            if (typeof calendar !== 'undefined') {
                if (typeof calendar.getEvents === 'function') {
                    // FullCalendar implementation - clear events
                    const events = calendar.getEvents();
                    events.forEach(event => event.remove());
                } else if (typeof calendar.events !== 'undefined') {
                    // CustomCalendar implementation - clear events array
                    calendar.events = [];
                    calendar.renderView();
                }
                
                // Update upcoming events section if it exists
                const upcomingEventsContainer = document.getElementById('upcoming-events');
                if (upcomingEventsContainer) {
                    upcomingEventsContainer.innerHTML = '<div class="p-3">No events to display. Please select at least one calendar.</div>';
                }
                
                return;
            }
        }
        
        // Build the filter URL for debugging
        if (DEBUG_MODE) {
            const params = getFilterParams();
            console.log('Filter URL would be: ' + URLROOT + '/calendar/getEvents?' + params);
        }
        
        // Determine which view we're in and call the appropriate function
        if (activeFilters.currentView === 'map') {
            if (typeof loadEvents === 'function') {
                loadEvents();
            }
        } else {
            // For calendar views - handle different calendar implementations
            if (typeof calendar !== 'undefined') {
                // Check which refresh method is available
                if (typeof calendar.refetchEvents === 'function') {
                    // FullCalendar implementation
                    calendar.refetchEvents();
                } else if (typeof calendar.loadEvents === 'function') {
                    // CustomCalendar implementation
                    // First clear existing events to ensure we don't have stale data
                    calendar.events = [];
                    
                    // Get the current view date range
                    const viewRange = calendar.getViewDateRange();
                    
                    // Get the filter parameters
                    const params = getFilterParams();
                    
                    // Add date range if not already in params
                    if (!params.includes('start=') && viewRange.startDate) {
                        params.push(`start=${viewRange.startDate.toISOString()}`);
                    }
                    if (!params.includes('end=') && viewRange.endDate) {
                        params.push(`end=${viewRange.endDate.toISOString()}`);
                    }
                    
                    // Build the URL
                    const url = `${URLROOT}/calendar/getEvents?${params.join('&')}`;
                    
                    if (DEBUG_MODE) {
                        console.log('Immediately fetching events with filters from:', url);
                    }
                    
                    // Show loading indicator
                    const calendarEl = document.getElementById('calendar');
                    if (calendarEl) {
                        calendarEl.classList.add('loading');
                        const loadingIndicator = document.createElement('div');
                        loadingIndicator.className = 'calendar-loading-indicator';
                        loadingIndicator.innerHTML = '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
                        calendarEl.appendChild(loadingIndicator);
                    }
                    
                    // Immediately fetch events with the current filters
                    fetch(url)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            if (DEBUG_MODE) {
                                console.log(`Immediately received ${data.length} events from server with filters`);
                            }
                            
                            // Clear any existing events
                            calendar.events = [];
                            
                            // Add the new events
                            calendar.addEvents(data);
                            
                            // Render the view to show the new events
                            calendar.renderView();
                            
                            // Also create a new event source for future navigation
                            const eventSource = (fetchInfo, successCallback, failureCallback) => {
                                // Get the filter parameters again (they might have changed)
                                const params = getFilterParams();
                                
                                // Add date range from fetchInfo
                                if (!params.includes('start=') && fetchInfo.startStr) {
                                    params.push(`start=${fetchInfo.startStr}`);
                                }
                                if (!params.includes('end=') && fetchInfo.endStr) {
                                    params.push(`end=${fetchInfo.endStr}`);
                                }
                                
                                // Build the URL
                                const url = `${URLROOT}/calendar/getEvents?${params.join('&')}`;
                                
                                if (DEBUG_MODE) {
                                    console.log('Fetching events with filters from:', url);
                                }
                                
                                // Fetch the events
                                fetch(url)
                                    .then(response => {
                                        if (!response.ok) {
                                            throw new Error(`HTTP error! Status: ${response.status}`);
                                        }
                                        return response.json();
                                    })
                                    .then(data => {
                                        if (DEBUG_MODE) {
                                            console.log(`Received ${data.length} events from server with filters`);
                                        }
                                        
                                        if (typeof successCallback === 'function') {
                                            successCallback(data);
                                        }
                                        return data;
                                    })
                                    .catch(error => {
                                        console.error('Error loading events with filters:', error);
                                        if (typeof failureCallback === 'function') {
                                            failureCallback(error);
                                        }
                                        return [];
                                    });
                            };
                            
                            // Set the new event source for future navigation
                            calendar.options.eventSources = [eventSource];
                        })
                        .catch(error => {
                            console.error('Error immediately loading events with filters:', error);
                        })
                        .finally(() => {
                            // Remove loading indicator
                            if (calendarEl) {
                                calendarEl.classList.remove('loading');
                                const loadingIndicator = calendarEl.querySelector('.calendar-loading-indicator');
                                if (loadingIndicator) {
                                    loadingIndicator.remove();
                                }
                            }
                        });
                } else {
                    console.error('No compatible calendar refresh method found');
                    // Fallback - reload the page
                    window.location.reload();
                }
            } else {
                console.error('Calendar object not found');
            }
        }
    }

    /**
     * Reset all filters to default values
     */
    function resetFilters() {
        // Reset date range
        if (startDateInput) startDateInput.value = '';
        if (endDateInput) endDateInput.value = '';
        
        // Reset state and dependent filters
        if (stateFilter) stateFilter.value = '';
        if (cityFilter) {
            cityFilter.value = '';
            // Clear city options except the first one
            while (cityFilter.options.length > 1) {
                cityFilter.remove(1);
            }
        }
        if (venueFilter) {
            venueFilter.value = '';
            // Clear venue options except the first one
            while (venueFilter.options.length > 1) {
                venueFilter.remove(1);
            }
        }
        
        // Reset club filter
        if (clubFilter) {
            for (let i = 0; i < clubFilter.options.length; i++) {
                clubFilter.options[i].selected = i === 0;
            }
        }
        
        // Reset radius filter
        if (radiusFilter) {
            radiusFilter.value = 50;
            if (radiusValue) radiusValue.textContent = '50';
        }
        
        // Reset location search
        if (locationSearch) locationSearch.value = '';
        
        // Reset keyword filter
        if (keywordFilter) keywordFilter.value = '';
        
        // Reset category filter
        if (categoryFilter) {
            for (let i = 0; i < categoryFilter.options.length; i++) {
                categoryFilter.options[i].selected = i === 0;
            }
        }
        
        // Reset tag filter
        if (tagFilter) {
            for (let i = 0; i < tagFilter.options.length; i++) {
                tagFilter.options[i].selected = i === 0;
            }
        }
        
        // Reset price range
        if (document.getElementById('price-range-slider') && 
            document.getElementById('price-range-slider').noUiSlider) {
            document.getElementById('price-range-slider').noUiSlider.set([0, 500]);
        }
        
        // Reset filter state
        activeFilters.startDate = null;
        activeFilters.endDate = null;
        activeFilters.state = '';
        activeFilters.city = '';
        activeFilters.venue = '';
        activeFilters.clubs = [];
        activeFilters.radius = 50;
        activeFilters.lat = null;
        activeFilters.lng = null;
        activeFilters.keyword = '';
        activeFilters.categories = [];
        activeFilters.tags = [];
        activeFilters.priceMin = 0;
        activeFilters.priceMax = 500;
        
        // Check all calendar checkboxes
        document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
        
        // Update calendar filters
        updateCalendarFilters();
        
        // Apply filters
        applyFilters();
    }
    
    /**
     * Get filter parameters as a query string
     * 
     * @returns {string[]} Array of filter parameters
     */
    function getFilterParams() {
        const params = [];
        
        if (activeFilters.startDate) {
            params.push(`start=${activeFilters.startDate}`);
        }
        
        if (activeFilters.endDate) {
            params.push(`end=${activeFilters.endDate}`);
        }
        
        // Always include calendar_id parameter, even if empty
        // This allows the server to know when no calendars are selected
        params.push(`calendar_id=${activeFilters.calendars.join(',')}`);
        
        // Log the calendar filter for debugging
        console.log('Calendar filter:', activeFilters.calendars.join(','));
        
        if (activeFilters.state) {
            params.push(`state=${encodeURIComponent(activeFilters.state)}`);
        }
        
        if (activeFilters.city) {
            params.push(`city=${encodeURIComponent(activeFilters.city)}`);
        }
        
        if (activeFilters.venue) {
            params.push(`venue_id=${activeFilters.venue}`);
        }
        
        if (activeFilters.clubs.length > 0) {
            params.push(`club_id=${activeFilters.clubs.join(',')}`);
        }
        
        if (activeFilters.keyword) {
            params.push(`keyword=${encodeURIComponent(activeFilters.keyword)}`);
        }
        
        if (activeFilters.lat && activeFilters.lng && activeFilters.radius) {
            params.push(`lat=${activeFilters.lat}`);
            params.push(`lng=${activeFilters.lng}`);
            params.push(`radius=${activeFilters.radius}`);
        }
        
        if (activeFilters.categories.length > 0) {
            params.push(`category=${activeFilters.categories.join(',')}`);
        }
        
        if (activeFilters.tags.length > 0) {
            params.push(`tag=${activeFilters.tags.join(',')}`);
        }
        
        if (activeFilters.priceMin > 0 || activeFilters.priceMax < 500) {
            params.push(`price_range=${activeFilters.priceMin}-${activeFilters.priceMax}`);
        }
        
        return params;
    }
    
    // Expose the filter system to the global scope
    window.calendarFilters = {
        applyFilters: applyFilters,
        resetFilters: resetFilters,
        getFilterParams: getFilterParams,
        updateCalendarFilters: updateCalendarFilters,
        activeFilters: activeFilters
    };
});
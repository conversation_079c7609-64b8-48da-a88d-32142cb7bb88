<?php
/**
 * Database Relationship Diagram Generator
 * 
 * This script generates a visual representation of database relationships
 * in various formats (HTML, Mermaid, PlantUML)
 * 
 * Usage: Access via admin dashboard or direct URL (admin only)
 * Formats: html, mermaid, plantuml
 */

// Define APPROOT (now in root directory)
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

class DatabaseDiagramGenerator {
    private $db;
    private $tables = [];
    private $relationships = [];
    
    public function __construct($database) {
        $this->db = $database;
        $this->analyzeTables();
        $this->analyzeRelationships();
    }
    
    private function analyzeTables() {
        // Get all tables
        $query = "SHOW TABLES";
        $result = $this->db->query($query);
        
        if (!$result) {
            throw new Exception("Failed to get table list: " . $this->db->error);
        }
        
        while ($row = $result->fetch_array()) {
            $tableName = $row[0];
            try {
                $this->tables[$tableName] = $this->getTableStructure($tableName);
            } catch (Exception $e) {
                // Skip tables that can't be analyzed
                error_log("Warning: Could not analyze table '$tableName': " . $e->getMessage());
            }
        }
    }
    
    private function getTableStructure($tableName) {
        $structure = [
            'columns' => [],
            'primary_key' => null,
            'foreign_keys' => []
        ];
        
        // Get column information
        $query = "DESCRIBE `$tableName`";
        $result = $this->db->query($query);
        
        if (!$result) {
            throw new Exception("Failed to describe table '$tableName': " . $this->db->error);
        }
        
        while ($row = $result->fetch_assoc()) {
            $structure['columns'][] = [
                'name' => $row['Field'],
                'type' => $row['Type'],
                'null' => $row['Null'],
                'key' => $row['Key'],
                'default' => $row['Default'],
                'extra' => $row['Extra']
            ];
            
            if ($row['Key'] === 'PRI') {
                $structure['primary_key'] = $row['Field'];
            }
        }
        
        // Get foreign key information - using simpler, more compatible approach
        try {
            $query = "
                SELECT 
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = '$tableName' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ";
            
            $result = $this->db->query($query);
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $structure['foreign_keys'][] = [
                        'COLUMN_NAME' => $row['COLUMN_NAME'],
                        'REFERENCED_TABLE_NAME' => $row['REFERENCED_TABLE_NAME'],
                        'REFERENCED_COLUMN_NAME' => $row['REFERENCED_COLUMN_NAME'],
                        'DELETE_RULE' => 'NO ACTION', // Default value
                        'UPDATE_RULE' => 'NO ACTION'  // Default value
                    ];
                }
            }
        } catch (Exception $e) {
            // If foreign key query fails, just log it and continue without foreign keys
            error_log("Warning: Could not get foreign keys for table '$tableName': " . $e->getMessage());
        }
        
        return $structure;
    }
    
    private function analyzeRelationships() {
        foreach ($this->tables as $tableName => $structure) {
            foreach ($structure['foreign_keys'] as $fk) {
                $this->relationships[] = [
                    'from_table' => $tableName,
                    'from_column' => $fk['COLUMN_NAME'],
                    'to_table' => $fk['REFERENCED_TABLE_NAME'],
                    'to_column' => $fk['REFERENCED_COLUMN_NAME'],
                    'delete_rule' => $fk['DELETE_RULE'],
                    'update_rule' => $fk['UPDATE_RULE']
                ];
            }
        }
    }
    
    public function generateHTML() {
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Relationship Diagram</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .table-container { display: flex; flex-wrap: wrap; gap: 20px; }
        .table { 
            background: white; 
            border: 2px solid #333; 
            border-radius: 8px; 
            min-width: 250px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .table-header { 
            background: #333; 
            color: white; 
            padding: 10px; 
            font-weight: bold; 
            text-align: center;
        }
        .table-body { padding: 0; }
        .column { 
            padding: 5px 10px; 
            border-bottom: 1px solid #eee; 
            font-size: 12px;
        }
        .column:last-child { border-bottom: none; }
        .primary-key { background: #ffe6e6; font-weight: bold; }
        .foreign-key { background: #e6f3ff; }
        .column-type { color: #666; font-size: 10px; }
        .relationships { margin-top: 30px; }
        .relationship { 
            background: white; 
            padding: 10px; 
            margin: 5px 0; 
            border-left: 4px solid #007bff; 
            border-radius: 4px;
        }
        .core-tables { border: 3px solid #28a745; }
        .support-tables { border: 3px solid #ffc107; }
        .junction-tables { border: 3px solid #dc3545; }
        .legend { 
            background: white; 
            padding: 15px; 
            border-radius: 8px; 
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .legend-item { 
            display: inline-block; 
            margin-right: 20px; 
            padding: 5px 10px; 
            border-radius: 4px; 
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Relationship Diagram</h1>
        
        <div class="legend">
            <h3>Legend</h3>
            <span class="legend-item core-tables">Core Tables (Main Entities)</span>
            <span class="legend-item support-tables">Support Tables (Configuration/Metadata)</span>
            <span class="legend-item junction-tables">Junction Tables (Many-to-Many)</span>
            <br><br>
            <span class="legend-item primary-key">Primary Key</span>
            <span class="legend-item foreign-key">Foreign Key</span>
        </div>
        
        <div class="table-container">';
        
        // Categorize tables
        $coreTableNames = ['users', 'shows', 'vehicles', 'registrations', 'calendars', 'calendar_events', 'payments'];
        $junctionTableNames = ['calendar_club_members', 'calendar_event_clubs', 'judge_assignments'];
        
        foreach ($this->tables as $tableName => $structure) {
            $tableClass = 'table';
            if (in_array($tableName, $coreTableNames)) {
                $tableClass .= ' core-tables';
            } elseif (in_array($tableName, $junctionTableNames)) {
                $tableClass .= ' junction-tables';
            } else {
                $tableClass .= ' support-tables';
            }
            
            $html .= "<div class=\"$tableClass\">";
            $html .= "<div class=\"table-header\">$tableName</div>";
            $html .= "<div class=\"table-body\">";
            
            foreach ($structure['columns'] as $column) {
                $columnClass = 'column';
                if ($column['key'] === 'PRI') {
                    $columnClass .= ' primary-key';
                } elseif ($this->isForeignKey($tableName, $column['name'])) {
                    $columnClass .= ' foreign-key';
                }
                
                $html .= "<div class=\"$columnClass\">";
                $html .= "<strong>{$column['name']}</strong>";
                $html .= "<div class=\"column-type\">{$column['type']}</div>";
                $html .= "</div>";
            }
            
            $html .= "</div></div>";
        }
        
        $html .= '</div>
        
        <div class="relationships">
            <h2>Relationships</h2>';
            
        foreach ($this->relationships as $rel) {
            $html .= "<div class=\"relationship\">";
            $html .= "<strong>{$rel['from_table']}.{$rel['from_column']}</strong> → ";
            $html .= "<strong>{$rel['to_table']}.{$rel['to_column']}</strong>";
            $html .= " <em>({$rel['delete_rule']})</em>";
            $html .= "</div>";
        }
        
        $html .= '</div>
    </div>
</body>
</html>';
        
        return $html;
    }
    
    public function generateMermaid() {
        $mermaid = "erDiagram\n";
        
        // Add tables
        foreach ($this->tables as $tableName => $structure) {
            $mermaid .= "    $tableName {\n";
            foreach ($structure['columns'] as $column) {
                $type = $this->simplifyType($column['type']);
                $key = '';
                if ($column['key'] === 'PRI') $key = ' PK';
                if ($this->isForeignKey($tableName, $column['name'])) $key .= ' FK';
                
                $mermaid .= "        $type {$column['name']}$key\n";
            }
            $mermaid .= "    }\n\n";
        }
        
        // Add relationships
        foreach ($this->relationships as $rel) {
            $mermaid .= "    {$rel['to_table']} ||--o{ {$rel['from_table']} : \"{$rel['from_column']}\"\n";
        }
        
        return $mermaid;
    }
    
    private function isForeignKey($tableName, $columnName) {
        foreach ($this->relationships as $rel) {
            if ($rel['from_table'] === $tableName && $rel['from_column'] === $columnName) {
                return true;
            }
        }
        return false;
    }
    
    private function simplifyType($type) {
        if (strpos($type, 'int') !== false) return 'int';
        if (strpos($type, 'varchar') !== false) return 'string';
        if (strpos($type, 'text') !== false) return 'text';
        if (strpos($type, 'datetime') !== false) return 'datetime';
        if (strpos($type, 'timestamp') !== false) return 'timestamp';
        if (strpos($type, 'decimal') !== false) return 'decimal';
        if (strpos($type, 'enum') !== false) return 'enum';
        return 'other';
    }
    
    public function saveToFile($content, $filename) {
        file_put_contents($filename, $content);
        echo "Diagram saved to: $filename\n";
    }
}

// Main execution
try {
    // Initialize database connection
    $database = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($database->connect_error) {
        die("Connection failed: " . $database->connect_error);
    }
    
    $generator = new DatabaseDiagramGenerator($database);
    
    // Get format from URL parameter or command line argument
    $format = 'html'; // default
    
    if (php_sapi_name() === 'cli') {
        // Command line execution
        $format = isset($argv[1]) ? $argv[1] : 'html';
    } else {
        // Web execution
        $format = isset($_GET['format']) ? $_GET['format'] : 'html';
    }
    
    switch ($format) {
        case 'html':
            $content = $generator->generateHTML();
            $generator->saveToFile($content, 'database_diagram.html');
            break;
            
        case 'mermaid':
            $content = $generator->generateMermaid();
            $generator->saveToFile($content, 'database_diagram.mmd');
            echo "To view: Copy content to https://mermaid.live/\n";
            break;
            
        default:
            echo "Usage: Access with ?format=html or ?format=mermaid\n";
            break;
    }
    
    $database->close();
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
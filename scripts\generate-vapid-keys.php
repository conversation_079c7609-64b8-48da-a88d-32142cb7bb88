<?php
/**
 * VAPID Key Generator
 * Generates public/private VAPID key pairs for push notifications
 */

// Function to generate VAPID keys
function generateVAPIDKeys() {
    // Generate a private key
    $privateKey = openssl_pkey_new([
        'curve_name' => 'prime256v1',
        'private_key_type' => OPENSSL_KEYTYPE_EC,
    ]);
    
    if (!$privateKey) {
        throw new Exception('Failed to generate private key');
    }
    
    // Export private key
    openssl_pkey_export($privateKey, $privateKeyPEM);
    
    // Get public key
    $publicKeyDetails = openssl_pkey_get_details($privateKey);
    $publicKeyPEM = $publicKeyDetails['key'];
    
    // Convert to base64url format for web push
    $privateKeyRaw = openssl_pkey_get_details($privateKey)['ec']['d'];
    $publicKeyRaw = substr($publicKeyDetails['ec']['key'], 1); // Remove first byte (0x04)
    
    $privateKeyBase64 = rtrim(strtr(base64_encode($privateKeyRaw), '+/', '-_'), '=');
    $publicKeyBase64 = rtrim(strtr(base64_encode($publicKeyRaw), '+/', '-_'), '=');
    
    return [
        'private_key' => $privateKeyBase64,
        'public_key' => $publicKeyBase64,
        'private_key_pem' => $privateKeyPEM,
        'public_key_pem' => $publicKeyPEM
    ];
}

try {
    echo "Generating VAPID keys...\n\n";
    
    $keys = generateVAPIDKeys();
    
    echo "=== VAPID KEYS GENERATED ===\n\n";
    echo "Public Key (add to config.php):\n";
    echo "define('VAPID_PUBLIC_KEY', '{$keys['public_key']}');\n\n";
    echo "Private Key (add to config.php):\n";
    echo "define('VAPID_PRIVATE_KEY', '{$keys['private_key']}');\n\n";
    echo "Subject (add to config.php - use your website URL or email):\n";
    echo "define('VAPID_SUBJECT', 'mailto:<EMAIL>');\n\n";
    
    echo "=== ADD THESE TO YOUR config/config.php FILE ===\n\n";
    echo "// VAPID Keys for Push Notifications\n";
    echo "define('VAPID_PUBLIC_KEY', '{$keys['public_key']}');\n";
    echo "define('VAPID_PRIVATE_KEY', '{$keys['private_key']}');\n";
    echo "define('VAPID_SUBJECT', 'mailto:<EMAIL>');\n\n";
    
    // Save to a file for easy copying
    $configContent = "\n// VAPID Keys for Push Notifications\n";
    $configContent .= "define('VAPID_PUBLIC_KEY', '{$keys['public_key']}');\n";
    $configContent .= "define('VAPID_PRIVATE_KEY', '{$keys['private_key']}');\n";
    $configContent .= "define('VAPID_SUBJECT', 'mailto:<EMAIL>');\n";
    
    file_put_contents(__DIR__ . '/vapid-config.txt', $configContent);
    echo "Keys also saved to: " . __DIR__ . "/vapid-config.txt\n";
    
} catch (Exception $e) {
    echo "Error generating VAPID keys: " . $e->getMessage() . "\n";
}
?>
<?php
/**
 * Payment Controller
 * 
 * This controller handles all payment-related functionality.
 */
class PaymentController extends Controller {
    private $paymentModel;
    private $showModel;
    private $registrationModel;
    private $userModel;
    private $auth;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->auth = new Auth();
        
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $this->paymentModel = $this->model('PaymentModel');
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->userModel = $this->model('UserModel');
    }
    
    /**
     * Payment dashboard
     */
    public function index() {
        // Get user payments
        $userId = $this->auth->getCurrentUserId();
        $payments = $this->paymentModel->getPaymentsByUser($userId);
        
        $data = [
            'title' => 'Payment History',
            'payments' => $payments
        ];
        
        $this->view('payments/index', $data);
    }
    
    /**
     * Process registration payment
     * 
     * @param int $registrationId Registration ID
     */
    public function registration($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            
            // Get payment method
            $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
            
            if (!$paymentMethod) {
                $this->setFlashMessage('payment_error', 'Invalid payment method', 'danger');
                $this->redirect('payment/registration/' . $registrationId);
                return;
            }
            
            // Check if this is a free show and free payment method
            if ($show->is_free && $paymentMethod->name == 'Free') {
                // Update registration payment status
                $paymentData = [
                    'id' => $registrationId,
                    'payment_status' => 'completed',
                    'fee' => 0.00,
                    'payment_method_id' => $paymentMethodId
                ];
                
                if ($this->registrationModel->updatePayment($paymentData)) {
                    $this->setFlashMessage('payment_success', 'Registration confirmed for free show', 'success');
                    $this->redirect('registration/view/' . $registrationId);
                    return;
                } else {
                    $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                    $this->redirect('payment/registration/' . $registrationId);
                    return;
                }
            }
            
            // Handle different payment methods
            switch ($paymentMethod->name) {
                case 'PayPal':
                    // Redirect to PayPal payment page
                    $this->redirect('payment/paypal/' . $registrationId);
                    break;
                    
                case 'CashApp':
                case 'Venmo':
                    // Update registration with payment method
                    $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
                    
                    $paymentData = [
                        'id' => $registrationId,
                        'payment_status' => 'pending',
                        'fee' => $show->registration_fee,
                        'payment_method_id' => $paymentMethodId,
                        'payment_reference' => $paymentReference
                    ];
                    
                    if ($this->registrationModel->updatePayment($paymentData)) {
                        // Create payment record
                        $paymentData = [
                            'user_id' => $this->auth->getCurrentUserId(),
                            'amount' => $show->registration_fee,
                            'payment_method_id' => $paymentMethodId,
                            'payment_status' => 'pending',
                            'payment_reference' => $paymentReference,
                            'payment_type' => 'registration',
                            'related_id' => $registrationId,
                            'notes' => 'Registration payment for ' . $show->name
                        ];
                        
                        $this->paymentModel->createPayment($paymentData);
                        
                        $this->setFlashMessage('payment_success', 'Payment information submitted. Your registration is pending approval.', 'success');
                        $this->redirect('registration/view/' . $registrationId);
                    } else {
                        $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                        $this->redirect('payment/registration/' . $registrationId);
                    }
                    break;
                    
                default:
                    $this->setFlashMessage('payment_error', 'Unsupported payment method', 'danger');
                    $this->redirect('payment/registration/' . $registrationId);
                    break;
            }
        } else {
            // Display payment form
            $data = [
                'title' => 'Registration Payment',
                'registration' => $registration,
                'show' => $show,
                'payment_methods' => $paymentMethods,
                'cashapp_id' => $this->paymentModel->getPaymentSetting('cashapp_id'),
                'venmo_id' => $this->paymentModel->getPaymentSetting('venmo_id')
            ];
            
            $this->view('payments/registration', $data);
        }
    }
    
    /**
     * Process show listing payment
     * 
     * @param int $showId Show ID
     */
    public function showListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if listing fee is already paid
        if ($show->listing_paid) {
            $this->setFlashMessage('payment_info', 'Show listing fee has already been paid', 'info');
            $this->redirect('show/manage/' . $showId);
            return;
        }
        
        // Check if user is exempt from listing fees
        if ($this->paymentModel->isUserExemptFromListingFees($this->auth->getCurrentUserId())) {
            // Mark show as paid
            $this->showModel->updateShowListingPaymentStatus($showId, true);
            
            $this->setFlashMessage('payment_success', 'Show listing fee waived based on your account privileges', 'success');
            $this->redirect('show/manage/' . $showId);
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $paymentMethodId = trim($_POST['payment_method_id']);
            
            // Get payment method
            $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
            
            if (!$paymentMethod) {
                $this->setFlashMessage('payment_error', 'Invalid payment method', 'danger');
                $this->redirect('payment/showListing/' . $showId);
                return;
            }
            
            // Handle different payment methods
            switch ($paymentMethod->name) {
                case 'PayPal':
                    // Redirect to PayPal payment page
                    $this->redirect('payment/paypalShowListing/' . $showId);
                    break;
                    
                case 'CashApp':
                case 'Venmo':
                    // Create payment record
                    $paymentReference = !empty($_POST['payment_reference']) ? trim($_POST['payment_reference']) : null;
                    
                    $paymentData = [
                        'user_id' => $this->auth->getCurrentUserId(),
                        'amount' => $listingFee,
                        'payment_method_id' => $paymentMethodId,
                        'payment_status' => 'pending',
                        'payment_reference' => $paymentReference,
                        'payment_type' => 'show_listing',
                        'related_id' => $showId,
                        'notes' => 'Show listing fee for ' . $show->name
                    ];
                    
                    if ($this->paymentModel->createPayment($paymentData)) {
                        $this->setFlashMessage('payment_success', 'Payment information submitted. Your show listing is pending approval.', 'success');
                        $this->redirect('show/manage/' . $showId);
                    } else {
                        $this->setFlashMessage('payment_error', 'Failed to process payment', 'danger');
                        $this->redirect('payment/showListing/' . $showId);
                    }
                    break;
                    
                default:
                    $this->setFlashMessage('payment_error', 'Unsupported payment method', 'danger');
                    $this->redirect('payment/showListing/' . $showId);
                    break;
            }
        } else {
            // Display payment form
            $data = [
                'title' => 'Show Listing Payment',
                'show' => $show,
                'listing_fee' => $listingFee,
                'payment_methods' => $paymentMethods,
                'cashapp_id' => $this->paymentModel->getPaymentSetting('cashapp_id'),
                'venmo_id' => $this->paymentModel->getPaymentSetting('venmo_id')
            ];
            
            $this->view('payments/show_listing', $data);
        }
    }
    
    /**
     * PayPal payment for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypal($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get PayPal settings
        $paypalClientId = $this->paymentModel->getPaymentSetting('paypal_client_id');
        $paypalSandbox = $this->paymentModel->getPaymentSetting('paypal_sandbox') === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        $data = [
            'title' => 'PayPal Payment',
            'registration' => $registration,
            'show' => $show,
            'paypal_client_id' => $paypalClientId,
            'paypal_sandbox' => $paypalSandbox,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/paypalSuccess/' . $registrationId,
            'cancel_url' => BASE_URL . '/payment/paypalCancel/' . $registrationId
        ];
        
        $this->view('payments/paypal', $data);
    }
    
    /**
     * PayPal payment for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowListing($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get PayPal settings
        $paypalClientId = $this->paymentModel->getPaymentSetting('paypal_client_id');
        $paypalSandbox = $this->paymentModel->getPaymentSetting('paypal_sandbox') === 'true';
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        $data = [
            'title' => 'PayPal Payment',
            'show' => $show,
            'listing_fee' => $listingFee,
            'paypal_client_id' => $paypalClientId,
            'paypal_sandbox' => $paypalSandbox,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/paypalShowSuccess/' . $showId,
            'cancel_url' => BASE_URL . '/payment/paypalShowCancel/' . $showId
        ];
        
        $this->view('payments/paypal_show', $data);
    }
    
    /**
     * PayPal success callback for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypalSuccess($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Verify payment with PayPal API (simplified for now)
        $paymentVerified = true;
        
        if ($paymentVerified) {
            // Update registration payment status
            $paymentData = [
                'id' => $registrationId,
                'payment_status' => 'completed',
                'fee' => $show->registration_fee,
                'payment_method_id' => $paymentMethod->id,
                'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown')
            ];
            
            if ($this->registrationModel->updatePayment($paymentData)) {
                // Create payment record
                $paymentData = [
                    'user_id' => $this->auth->getCurrentUserId(),
                    'amount' => $show->registration_fee,
                    'payment_method_id' => $paymentMethod->id,
                    'payment_status' => 'completed',
                    'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown'),
                    'payment_type' => 'registration',
                    'related_id' => $registrationId,
                    'notes' => 'Registration payment for ' . $show->name
                ];
                
                $this->paymentModel->createPayment($paymentData);
                
                $this->setFlashMessage('payment_success', 'Payment completed successfully. Your registration is confirmed.', 'success');
                $this->redirect('registration/view/' . $registrationId);
            } else {
                $this->setFlashMessage('payment_error', 'Failed to update registration', 'danger');
                $this->redirect('payment/registration/' . $registrationId);
            }
        } else {
            $this->setFlashMessage('payment_error', 'Payment verification failed', 'danger');
            $this->redirect('payment/registration/' . $registrationId);
        }
    }
    
    /**
     * PayPal cancel callback for registration
     * 
     * @param int $registrationId Registration ID
     */
    public function paypalCancel($registrationId) {
        $this->setFlashMessage('payment_info', 'Payment was cancelled', 'info');
        $this->redirect('payment/registration/' . $registrationId);
    }
    
    /**
     * PayPal success callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowSuccess($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('PayPal');
        
        // Get listing fee
        $listingFee = $show->listing_fee;
        if ($listingFee <= 0) {
            $listingFee = $this->paymentModel->getPaymentSetting('default_show_listing_fee');
        }
        
        // Verify payment with PayPal API (simplified for now)
        $paymentVerified = true;
        
        if ($paymentVerified) {
            // Mark show as paid
            if ($this->showModel->updateShowListingPaymentStatus($showId, true)) {
                // Create payment record
                $paymentData = [
                    'user_id' => $this->auth->getCurrentUserId(),
                    'amount' => $listingFee,
                    'payment_method_id' => $paymentMethod->id,
                    'payment_status' => 'completed',
                    'payment_reference' => 'PayPal Transaction ID: ' . (isset($_GET['paymentId']) ? $_GET['paymentId'] : 'Unknown'),
                    'payment_type' => 'show_listing',
                    'related_id' => $showId,
                    'notes' => 'Show listing fee for ' . $show->name
                ];
                
                $this->paymentModel->createPayment($paymentData);
                
                $this->setFlashMessage('payment_success', 'Payment completed successfully. Your show listing is confirmed.', 'success');
                $this->redirect('show/manage/' . $showId);
            } else {
                $this->setFlashMessage('payment_error', 'Failed to update show listing status', 'danger');
                $this->redirect('payment/showListing/' . $showId);
            }
        } else {
            $this->setFlashMessage('payment_error', 'Payment verification failed', 'danger');
            $this->redirect('payment/showListing/' . $showId);
        }
    }
    
    /**
     * PayPal cancel callback for show listing
     * 
     * @param int $showId Show ID
     */
    public function paypalShowCancel($showId) {
        $this->setFlashMessage('payment_info', 'Payment was cancelled', 'info');
        $this->redirect('payment/showListing/' . $showId);
    }
    
    /**
     * View payment details
     * 
     * @param int $id Payment ID
     */
    public function details($id) {
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this payment or is admin
        if ($payment->user_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Payment Details',
            'payment' => $payment
        ];
        
        $this->view('payments/details', $data);
    }
    
    /**
     * Venmo payment page
     * 
     * @param int $registrationId Registration ID
     */
    public function venmo($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get Venmo ID
        $venmoId = $this->paymentModel->getPaymentSetting('venmo_id');
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('Venmo');
        
        $data = [
            'title' => 'Venmo Payment',
            'registration' => $registration,
            'show' => $show,
            'venmo_id' => $venmoId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/venmoSuccess/' . $registrationId
        ];
        
        $this->view('payments/venmo', $data);
    }
    
    /**
     * CashApp payment page
     * 
     * @param int $registrationId Registration ID
     */
    public function cashapp($registrationId) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user owns this registration
        if ($registration->owner_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Get CashApp ID
        $cashappId = $this->paymentModel->getPaymentSetting('cashapp_id');
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodByName('CashApp');
        
        $data = [
            'title' => 'Cash App Payment',
            'registration' => $registration,
            'show' => $show,
            'cashapp_id' => $cashappId,
            'payment_method' => $paymentMethod,
            'return_url' => BASE_URL . '/payment/cashappSuccess/' . $registrationId
        ];
        
        $this->view('payments/cashapp', $data);
    }
    
    /**
     * Venmo success callback
     * 
     * @param int $registrationId Registration ID
     */
    public function venmoSuccess($registrationId) {
        $this->setFlashMessage('payment_success', 'Your Venmo payment is being processed. Your registration will be confirmed once payment is verified.', 'success');
        $this->redirect('registration/view/' . $registrationId);
    }
    
    /**
     * CashApp success callback
     * 
     * @param int $registrationId Registration ID
     */
    public function cashappSuccess($registrationId) {
        $this->setFlashMessage('payment_success', 'Your Cash App payment is being processed. Your registration will be confirmed once payment is verified.', 'success');
        $this->redirect('registration/view/' . $registrationId);
    }
    
    /**
     * Admin payment settings
     */
    public function settings() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        // Get payment settings
        $paymentSettings = $this->paymentModel->getAllPaymentSettings();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update payment settings
            $settings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id',
                'currency',
                'show_listing_fee_enabled',
                'default_registration_fee',
                'default_show_listing_fee'
            ];
            
            foreach ($settings as $setting) {
                if (isset($_POST[$setting])) {
                    $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting]);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Payment settings updated successfully', 'success');
            $this->redirect('payment/settings');
        } else {
            // Display settings form
            $data = [
                'title' => 'Payment Settings',
                'payment_methods' => $paymentMethods,
                'payment_settings' => $paymentSettings
            ];
            
            $this->view('admin/payments/settings', $data);
        }
    }
    
    /**
     * Admin payment methods
     */
    public function methods() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        $data = [
            'title' => 'Payment Methods',
            'payment_methods' => $paymentMethods
        ];
        
        $this->view('admin/payments/methods', $data);
    }
    
    /**
     * Admin add/edit payment method
     * 
     * @param int $id Payment method ID (optional)
     */
    public function editMethod($id = null) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method if editing
        $paymentMethod = null;
        if ($id) {
            $paymentMethod = $this->paymentModel->getPaymentMethodById($id);
            
            if (!$paymentMethod) {
                $this->redirect('home/not_found');
                return;
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'instructions' => trim($_POST['instructions']),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'requires_approval' => isset($_POST['requires_approval']) ? 1 : 0
            ];
            
            // Validate data
            $errors = [];
            
            if (empty($data['name'])) {
                $errors['name'] = 'Name is required';
            }
            
            if (empty($errors)) {
                if ($id) {
                    // Update payment method
                    $data['id'] = $id;
                    
                    if ($this->paymentModel->updatePaymentMethod($data)) {
                        $this->setFlashMessage('method_success', 'Payment method updated successfully', 'success');
                        $this->redirect('payment/methods');
                    } else {
                        $this->setFlashMessage('method_error', 'Failed to update payment method', 'danger');
                    }
                } else {
                    // Create payment method
                    if ($this->paymentModel->createPaymentMethod($data)) {
                        $this->setFlashMessage('method_success', 'Payment method created successfully', 'success');
                        $this->redirect('payment/methods');
                    } else {
                        $this->setFlashMessage('method_error', 'Failed to create payment method', 'danger');
                    }
                }
            }
            
            // If we get here, there were errors
            $data['errors'] = $errors;
            $data['title'] = $id ? 'Edit Payment Method' : 'Add Payment Method';
            $data['payment_method'] = $paymentMethod;
            
            $this->view('admin/payments/edit_method', $data);
        } else {
            // Display form
            $data = [
                'title' => $id ? 'Edit Payment Method' : 'Add Payment Method',
                'payment_method' => $paymentMethod,
                'errors' => []
            ];
            
            $this->view('admin/payments/edit_method', $data);
        }
    }
    
    /**
     * Admin delete payment method
     * 
     * @param int $id Payment method ID
     */
    public function deleteMethod($id) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment method
        $paymentMethod = $this->paymentModel->getPaymentMethodById($id);
        
        if (!$paymentMethod) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete payment method
            if ($this->paymentModel->deletePaymentMethod($id)) {
                $this->setFlashMessage('method_success', 'Payment method deleted successfully', 'success');
            } else {
                $this->setFlashMessage('method_error', 'Failed to delete payment method', 'danger');
            }
            
            $this->redirect('payment/methods');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Delete Payment Method',
                'payment_method' => $paymentMethod
            ];
            
            $this->view('admin/payments/delete_method', $data);
        }
    }
    
    /**
     * Admin pending payments
     */
    public function pending() {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get pending payments
        $pendingPayments = $this->paymentModel->getPendingPayments();
        
        // Get pending registration payments
        $pendingRegistrationPayments = $this->paymentModel->getPendingRegistrationPayments();
        
        $data = [
            'title' => 'Pending Payments',
            'pending_payments' => $pendingPayments,
            'pending_registration_payments' => $pendingRegistrationPayments
        ];
        
        $this->view('admin/payments/pending', $data);
    }
    
    /**
     * Admin approve payment
     * 
     * @param int $id Payment ID
     */
    public function approve($id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $adminNotes = !empty($_POST['admin_notes']) ? trim($_POST['admin_notes']) : null;
            
            // Update payment status
            if ($this->paymentModel->updatePaymentStatus($id, 'completed', null, $adminNotes)) {
                // If this is a registration payment, update registration status
                if ($payment->payment_type == 'registration') {
                    $paymentData = [
                        'id' => $payment->related_id,
                        'payment_status' => 'completed'
                    ];
                    
                    $this->registrationModel->updatePayment($paymentData);
                }
                
                // If this is a show listing payment, update show status
                if ($payment->payment_type == 'show_listing') {
                    $this->showModel->updateShowListingPaymentStatus($payment->related_id, true);
                }
                
                $this->setFlashMessage('payment_success', 'Payment approved successfully', 'success');
            } else {
                $this->setFlashMessage('payment_error', 'Failed to approve payment', 'danger');
            }
            
            $this->redirect('payment/pending');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Approve Payment',
                'payment' => $payment
            ];
            
            $this->view('admin/payments/approve', $data);
        }
    }
    
    /**
     * Admin reject payment
     * 
     * @param int $id Payment ID
     */
    public function reject($id) {
        // Check if user is admin or coordinator
        if (!$this->auth->hasRole(['admin', 'coordinator'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment
        $payment = $this->paymentModel->getPaymentById($id);
        
        if (!$payment) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $adminNotes = !empty($_POST['admin_notes']) ? trim($_POST['admin_notes']) : null;
            
            // Update payment status
            if ($this->paymentModel->updatePaymentStatus($id, 'rejected', null, $adminNotes)) {
                $this->setFlashMessage('payment_success', 'Payment rejected successfully', 'success');
            } else {
                $this->setFlashMessage('payment_error', 'Failed to reject payment', 'danger');
            }
            
            $this->redirect('payment/pending');
        } else {
            // Display confirmation form
            $data = [
                'title' => 'Reject Payment',
                'payment' => $payment
            ];
            
            $this->view('admin/payments/reject', $data);
        }
    }
}
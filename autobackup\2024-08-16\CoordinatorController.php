<?php
/**
 * Coordinator Controller
 * 
 * This controller handles all coordinator-related functionality.
 */
class CoordinatorController extends Controller {
    private $userModel;
    private $showModel;
    private $registrationModel;
    private $vehicleModel;
    private $categoryModel;
    private $judgingModel;
    private $awardModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a coordinator or admin
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->categoryModel = $this->model('CategoryModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->awardModel = $this->model('AwardModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('coordinator/dashboard');
    }
    
    // ... [rest of the file content]
}
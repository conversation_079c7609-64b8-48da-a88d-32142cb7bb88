<?php
/**
 * Backup of the registration.php file before QR code path fix
 * Date: 2024-08-18
 * 
 * Issue: QR code image was not displaying because the path was not added to the image filename
 * Fix: Added the path /uploads/qrcodes/ to the image filename
 */

// Original code:
/*
<?php if (!empty($data['registration']->qr_code)) : ?>
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">Registration QR Code</h5>
        </div>
        <div class="card-body text-center">
            <img src="<?php echo URLROOT . '/' . $data['registration']->qr_code; ?>" class="img-fluid" style="max-width: 200px;" alt="QR Code">
            <p class="mt-2 mb-0">Scan for quick access</p>
        </div>
    </div>
<?php endif; ?>
*/

// Fixed code:
/*
<?php if (!empty($data['registration']->qr_code)) : ?>
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">Registration QR Code</h5>
        </div>
        <div class="card-body text-center">
            <img src="<?php echo URLROOT . '/uploads/qrcodes/' . $data['registration']->qr_code; ?>" class="img-fluid" style="max-width: 200px;" alt="QR Code">
            <p class="mt-2 mb-0">Scan for quick access</p>
        </div>
    </div>
<?php endif; ?>
*/
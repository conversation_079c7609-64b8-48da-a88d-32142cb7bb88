# Geocoding Enhancement Fix

## Problem
The geocoding functionality works in the batch geocoding tool but fails when creating or editing events through the regular forms.

## Root Cause
The batch geocoding tool uses the enhanced `geocodeEvent()` function with multiple fallback mechanisms, while the regular event forms use the simpler `geocodeAddress()` function directly without fallbacks.

## Solution
Update the CalendarController.php file to use the enhanced geocoding method in both the createEvent and editEvent methods.

## Files to Update

1. **CalendarController.php**

   In the `createEvent` method (around line 409), replace:
   ```php
   // Create address array for geocoding
   $addressData = [
       'address1' => $data['address1'],
       'address2' => $data['address2'],
       'city' => $data['city'],
       'state' => $data['state'],
       'zipcode' => $data['zipcode']
   ];
   
   // Attempt to geocode the address
   $coordinates = geocodeAddress($addressData, $mapSettings);
   
   // If geocoding was successful, update coordinates
   if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
       $data['lat'] = $coordinates['lat'];
       $data['lng'] = $coordinates['lng'];
   }
   ```

   With:
   ```php
   // Use the enhanced geocodeEvent function instead of geocodeAddress
   // This provides better fallback mechanisms and error handling
   $data = geocodeEvent($data, $mapSettings, 'createEvent');
   
   // Log the geocoding attempt
   error_log("Geocoding attempt for new event: " . 
       ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
       " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
   // If geocoding failed, add a warning message
   if (!$data['lat'] || !$data['lng']) {
       flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
   }
   ```

   In the `editEvent` method (around line 587), make a similar replacement but use 'editEvent' as the context and include the event ID:
   ```php
   // Use the enhanced geocodeEvent function instead of geocodeAddress
   // This provides better fallback mechanisms and error handling
   $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
   
   // Log the geocoding attempt
   error_log("Geocoding attempt for event ID {$id}: " . 
       ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
       " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
   // If geocoding failed, add a warning message
   if (!$data['lat'] || !$data['lng']) {
       flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
   }
   ```

## Benefits of This Fix

1. **Consistent Geocoding**: The same robust geocoding logic will be used everywhere in the application
2. **Better User Experience**: Users will get feedback when geocoding fails
3. **Improved Success Rate**: The enhanced method tries multiple approaches when geocoding fails
4. **Better Debugging**: Detailed logs will help identify problematic addresses
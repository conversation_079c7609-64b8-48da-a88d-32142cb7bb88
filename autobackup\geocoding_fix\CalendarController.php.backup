<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.2 - Fixed geocoding issues
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 * - Fixed geocoding to use enhanced method with fallbacks
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $showModel;
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('auth/login');
        }
        
        // Initialize models
        $this->calendarModel = $this->model('CalendarModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        
        // Check if calendar tables exist
        if (!$this->calendarModel->tablesExist()) {
            // Create tables
            if (!$this->calendarModel->createTables()) {
                die('Error creating calendar tables');
            }
            
            // Create default calendar
            $defaultCalendar = [
                'name' => 'Main Calendar',
                'description' => 'Default calendar for all events',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $_SESSION['user_id']
            ];
            
            $calendarId = $this->calendarModel->createCalendar($defaultCalendar);
            
            // Sync with existing shows
            if ($calendarId) {
                $this->calendarModel->syncEventsWithShows($calendarId);
            }
        }
    }
}
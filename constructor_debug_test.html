<!DOCTYPE html>
<html>
<head>
    <title>Constructor Debug Test</title>
</head>
<body>
    <h2>Constructor Debug Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the script and modify the constructor to add debugging
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Original script loaded');
                
                // Replace the constructor with a debug version
                const modifiedCode = code.replace(
                    /constructor\(\) \{[\s\S]*?try \{[\s\S]*?this\.version = '3\.63\.11-baseurl-fix';/,
                    `constructor() {
                        window.constructorDebug = [];
                        window.constructorDebug.push('Constructor started');
                        
                        try {
                            window.constructorDebug.push('Entering try block');
                            window.constructorDebug.push('About to set version');
                            this.version = '3.63.11-baseurl-fix';
                            window.constructorDebug.push('Version set to: ' + this.version);`
                ).replace(
                    /} catch \(error\) \{[\s\S]*?console\.error\('CameraBanner constructor error:', error\);[\s\S]*?\}/,
                    `} catch (error) {
                            window.constructorDebug.push('Constructor error: ' + error.message);
                            window.constructorDebug.push('Error stack: ' + error.stack);
                            console.error('CameraBanner constructor error:', error);
                        }
                        window.constructorDebug.push('Constructor finished');`
                );
                
                output('Modified constructor, executing...');
                
                try {
                    eval(modifiedCode);
                    
                    output('Script executed successfully');
                    
                    // Show debug info
                    if (window.constructorDebug) {
                        output('=== Constructor Debug Log ===');
                        window.constructorDebug.forEach((msg, i) => {
                            output((i + 1) + '. ' + msg);
                        });
                    }
                    
                    if (window.cameraBanner) {
                        output('=== Instance Properties ===');
                        output('- version: ' + window.cameraBanner.version);
                        output('- typeof version: ' + typeof window.cameraBanner.version);
                        output('- banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined'));
                        
                        // Try to set version manually
                        output('=== Manual Version Test ===');
                        window.cameraBanner.version = 'MANUAL_TEST';
                        output('- After manual set: ' + window.cameraBanner.version);
                        
                        // Check if version property exists
                        output('- hasOwnProperty version: ' + window.cameraBanner.hasOwnProperty('version'));
                        output('- version in object: ' + ('version' in window.cameraBanner));
                    }
                    
                } catch (error) {
                    output('Execution error: ' + error.message);
                    output('Stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
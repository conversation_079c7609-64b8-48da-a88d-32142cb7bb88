<?php
/**
 * Backup note for simplifying display number badges
 * Date: 2024-08-18
 * 
 * Issue: Need to simplify display number badges by removing the word "Display"
 * Fix: Removed the word "Display" from badges and changed column header to "Car #"
 */

// Original code in registration.php:
/*
<?php if (!empty($data['registration']->display_number)) : ?>
    <span class="badge bg-warning text-dark ms-2" style="font-size: 1rem;">
        Display #<?php echo $data['registration']->display_number; ?>
    </span>
<?php endif; ?>
*/

// Changed to:
/*
<?php if (!empty($data['registration']->display_number)) : ?>
    <span class="badge bg-warning text-dark ms-2" style="font-size: 1rem;">
        #<?php echo $data['registration']->display_number; ?>
    </span>
<?php endif; ?>
*/

// Original code in registrations.php:
/*
<th>Display #</th>

<?php if (!empty($registration->display_number)) : ?>
    <span class="badge bg-warning text-dark" style="font-size: 1rem;">
        <?php echo $registration->display_number; ?>
    </span>
<?php else : ?>
*/

// Changed to:
/*
<th>Car #</th>

<?php if (!empty($registration->display_number)) : ?>
    <span class="badge bg-warning text-dark" style="font-size: 1rem;">
        #<?php echo $registration->display_number; ?>
    </span>
<?php else : ?>
*/
<?php
/**
 * Create Listing Fee Tables
 * 
 * This script creates the necessary tables for the listing fee system.
 */

// Define APPROOT
define('APPROOT', dirname(dirname(__FILE__)));

// Include database class
require_once APPROOT . '/libraries/Database.php';

// Create database instance
$db = new Database();

// Create listing_fee table
try {
    $db->query('CREATE TABLE IF NOT EXISTS `listing_fee` (
        `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
        `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `fee_type` enum("per_show","monthly") NOT NULL DEFAULT "per_show",
        `code` varchar(50) NOT NULL,
        `code_used` tinyint(1) NOT NULL DEFAULT 0,
        `expiry_date` date DEFAULT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        <PERSON><PERSON>AR<PERSON> KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    
    $result = $db->execute();
    
    if ($result) {
        echo "Successfully created listing_fee table\n";
    } else {
        echo "Failed to create listing_fee table\n";
    }
} catch (Exception $e) {
    echo "Error creating listing_fee table: " . $e->getMessage() . "\n";
}

// Create pre_approved_cords table
try {
    $db->query('CREATE TABLE IF NOT EXISTS `pre_approved_cords` (
        `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
        `coordinator_id` int(11) UNSIGNED NOT NULL,
        `begin_date` date NOT NULL,
        `end_date` date NOT NULL,
        `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `coordinator_id` (`coordinator_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    
    $result = $db->execute();
    
    if ($result) {
        echo "Successfully created pre_approved_cords table\n";
    } else {
        echo "Failed to create pre_approved_cords table\n";
    }
} catch (Exception $e) {
    echo "Error creating pre_approved_cords table: " . $e->getMessage() . "\n";
}

// Add listing_fee and listing_paid columns to shows table if they don't exist
try {
    // Check if listing_fee column exists
    $db->query("SHOW COLUMNS FROM `shows` LIKE 'listing_fee'");
    $listingFeeExists = $db->single();
    
    if (!$listingFeeExists) {
        $db->query("ALTER TABLE `shows` ADD COLUMN `listing_fee` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `is_free`");
        $result = $db->execute();
        
        if ($result) {
            echo "Successfully added listing_fee column to shows table\n";
        } else {
            echo "Failed to add listing_fee column to shows table\n";
        }
    } else {
        echo "listing_fee column already exists in shows table\n";
    }
    
    // Check if listing_paid column exists
    $db->query("SHOW COLUMNS FROM `shows` LIKE 'listing_paid'");
    $listingPaidExists = $db->single();
    
    if (!$listingPaidExists) {
        $db->query("ALTER TABLE `shows` ADD COLUMN `listing_paid` tinyint(1) NOT NULL DEFAULT 0 AFTER `listing_fee`");
        $result = $db->execute();
        
        if ($result) {
            echo "Successfully added listing_paid column to shows table\n";
        } else {
            echo "Failed to add listing_paid column to shows table\n";
        }
    } else {
        echo "listing_paid column already exists in shows table\n";
    }
} catch (Exception $e) {
    echo "Error modifying shows table: " . $e->getMessage() . "\n";
}

// Add exempt_from_listing_fees column to users table if it doesn't exist
try {
    // Check if exempt_from_listing_fees column exists
    $db->query("SHOW COLUMNS FROM `users` LIKE 'exempt_from_listing_fees'");
    $exemptExists = $db->single();
    
    if (!$exemptExists) {
        $db->query("ALTER TABLE `users` ADD COLUMN `exempt_from_listing_fees` tinyint(1) NOT NULL DEFAULT 0");
        $result = $db->execute();
        
        if ($result) {
            echo "Successfully added exempt_from_listing_fees column to users table\n";
        } else {
            echo "Failed to add exempt_from_listing_fees column to users table\n";
        }
    } else {
        echo "exempt_from_listing_fees column already exists in users table\n";
    }
} catch (Exception $e) {
    echo "Error modifying users table: " . $e->getMessage() . "\n";
}

// Add default settings
try {
    // Check if default_listing_fee setting exists
    $db->query("SELECT * FROM settings WHERE name = 'default_listing_fee'");
    $defaultFeeExists = $db->single();
    
    if (!$defaultFeeExists) {
        $db->query("INSERT INTO settings (name, value, category) VALUES ('default_listing_fee', '25.00', 'payment')");
        $result = $db->execute();
        
        if ($result) {
            echo "Successfully added default_listing_fee setting\n";
        } else {
            echo "Failed to add default_listing_fee setting\n";
        }
    } else {
        echo "default_listing_fee setting already exists\n";
    }
    
    // Check if listing_fee_type setting exists
    $db->query("SELECT * FROM settings WHERE name = 'listing_fee_type'");
    $feeTypeExists = $db->single();
    
    if (!$feeTypeExists) {
        $db->query("INSERT INTO settings (name, value, category) VALUES ('listing_fee_type', 'per_show', 'payment')");
        $result = $db->execute();
        
        if ($result) {
            echo "Successfully added listing_fee_type setting\n";
        } else {
            echo "Failed to add listing_fee_type setting\n";
        }
    } else {
        echo "listing_fee_type setting already exists\n";
    }
} catch (Exception $e) {
    echo "Error adding default settings: " . $e->getMessage() . "\n";
}

echo "Listing fee tables setup complete\n";
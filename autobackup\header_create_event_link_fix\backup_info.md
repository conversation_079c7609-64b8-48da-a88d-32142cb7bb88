# Header Create Event Link Fix

## Issue
The "Create Event" link in the header Events dropdown menu was pointing to `/user/createEvent` instead of the correct `/calendar/createEvent` route.

## Files Modified
- `views/includes/header.php`

## Changes Made
1. Fixed desktop menu link from `/user/createEvent` to `/calendar/createEvent` (line 314)
2. Fixed mobile menu link from `/user/createEvent` to `/calendar/createEvent` (line 594)

## Date
<?php echo date('Y-m-d H:i:s'); ?>

## Version
Minor version increment recommended

## Testing
- Verify "Create Event" link works in desktop navigation
- Verify "Create Event" link works in mobile navigation
- Confirm both links navigate to the calendar create event page
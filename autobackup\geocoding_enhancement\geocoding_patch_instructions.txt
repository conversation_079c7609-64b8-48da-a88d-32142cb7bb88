## Geocoding Enhancement Patch Instructions

To fix the issue where geocoding works in the batch tool but not when creating/editing events, follow these steps:

1. Open the file: `/controllers/CalendarController.php`

2. Find the two sections that handle geocoding (around lines 408-435 and 586-613)

3. Replace both sections with the enhanced geocoding code:

```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'createEvent');
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for event: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}
```

## Why This Fixes the Issue

The original code was using the basic `geocodeAddress()` function directly, which doesn't have the same robust fallback mechanisms and error handling as the enhanced `geocodeEvent()` function used in the batch geocoding tool.

The enhanced function:
1. Tries multiple geocoding providers
2. Has better error handling
3. Attempts to simplify addresses when geocoding fails
4. Provides detailed logging

This change ensures that the same robust geocoding logic is used everywhere in the application.
# Google Maps API Version Update - v3.56.3

## Issue Resolved
Fixed the "RetiredVersion" warning by updating from deprecated version 3.55 to current stable version 3.60.

## Changes Made

### 1. Updated Google Maps API Version
**File**: `views/calendar/map.php`
- **Before**: `v=3.55` (retired version)
- **After**: `v=3.60` (current stable version)
- **Added**: `loading=async` parameter for better performance

### 2. Updated Test File
**File**: `autobackup/google_maps_cors_fix/test_google_maps.html`
- Updated to use same version 3.60 for consistency

### 3. Version Bump
**File**: `config/config.php`
- Updated APP_VERSION to 3.56.3

## Current Google Maps API Versions (2024)
According to Google's documentation, supported versions are:
- ✅ v=3.60 (latest stable - **now using**)
- ✅ v=3.59 (previous stable)
- ✅ v=3.58 (older stable)
- ✅ v=3.57 (older stable)
- ❌ v=3.55 (retired - was causing warning)

## Expected Results

### ✅ Fixed Issues:
- No more "RetiredVersion" warning
- Using current, supported Google Maps API
- Better performance with `loading=async`

### 🔄 CORS Status:
The CORS errors may still appear because they're related to Google's internal diagnostic calls, not the API version. However:
- Your map functionality continues to work correctly
- Events load and display properly
- All interactions (zoom, pan, markers) function normally

## Testing Steps

1. **Clear browser cache** completely
2. **Refresh the map page**
3. **Check console** - should see no "RetiredVersion" warning
4. **Verify map functionality**:
   - Map displays correctly
   - Events show as markers
   - Clicking markers opens info windows
   - Zoom and pan work smoothly

## If CORS Errors Persist

The CORS errors for `gen_204?csp_test=true` and RPC calls are **cosmetic only** and don't affect functionality. They occur because:

1. Google Maps makes internal diagnostic calls
2. These calls are blocked by browser CORS policy
3. Your server cannot control headers for Google's internal services
4. The core map functionality works regardless

## Alternative Solutions

If you want a completely clean console:

### Option 1: Switch to OpenStreetMap
- Go to Calendar → Map Settings
- Change provider to "OpenStreetMap (Free)"
- Zero CORS errors, completely free

### Option 2: Try Different Google Maps Version
```javascript
// Use quarterly release (more stable than weekly)
script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap&v=quarterly`;
```

## Conclusion

The retired version warning is now fixed. Your Google Maps implementation uses the current stable API version (3.60) and should perform optimally. Any remaining CORS errors are cosmetic and don't impact the user experience or functionality.
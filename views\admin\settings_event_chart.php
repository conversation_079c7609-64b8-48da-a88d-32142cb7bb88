<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Event Chart Settings</h1>
            <p class="text-muted">Configure Monthly Event Chart display options and behavior</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Calendar Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Event chart settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form action="<?php echo BASE_URL; ?>/admin/settings_event_chart" method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
        
        <div class="row">
            <!-- Event Chart Settings -->
            <div class="col-md-8 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-success text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-chart-bar me-2"></i> Monthly Event Chart Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_weekends" name="event_show_weekends" <?php echo isset($calendarSettings['event_show_weekends']) && $calendarSettings['event_show_weekends'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_weekends">
                                    Show Weekends in Event Chart
                                </label>
                            </div>
                            <div class="form-text">Display weekend columns in the Event chart timeline</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_enable_drag_drop" name="event_enable_drag_drop" <?php echo isset($calendarSettings['event_enable_drag_drop']) && $calendarSettings['event_enable_drag_drop'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_enable_drag_drop">
                                    Enable Drag & Drop in Event Chart
                                </label>
                            </div>
                            <div class="form-text">Allow users to drag and drop events to change dates (requires edit permissions)</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_today_line" name="event_show_today_line" <?php echo isset($calendarSettings['event_show_today_line']) && $calendarSettings['event_show_today_line'] !== false ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_today_line">
                                    Show Today Indicator Line
                                </label>
                            </div>
                            <div class="form-text">Display animated today indicator line across the Event chart</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_show_event_hover" name="event_show_event_hover" <?php echo isset($calendarSettings['event_show_event_hover']) && $calendarSettings['event_show_event_hover'] ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_show_event_hover">
                                    Show Event Hover Popups
                                </label>
                            </div>
                            <div class="form-text">Display event details when hovering over events in the Event chart</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_mobile_breakpoint" class="form-label">Mobile Breakpoint</label>
                            <select class="form-select" id="event_mobile_breakpoint" name="event_mobile_breakpoint">
                                <option value="576" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '576' ? 'selected' : ''; ?>>Small (576px)</option>
                                <option value="768" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '768' ? 'selected' : ''; ?>>Medium (768px)</option>
                                <option value="992" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '992' ? 'selected' : ''; ?>>Large (992px) - Default</option>
                                <option value="1200" <?php echo isset($calendarSettings['event_mobile_breakpoint']) && $calendarSettings['event_mobile_breakpoint'] == '1200' ? 'selected' : ''; ?>>Extra Large (1200px)</option>
                            </select>
                            <div class="form-text">Screen width below which the mobile card view is used instead of the Event chart</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Information Panel -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> About Event Chart</h3>
                    </div>
                    <div class="card-body p-4">
                        <h5>Monthly Event Chart</h5>
                        <p>The Monthly Event Chart is a timeline-based calendar view that displays events in a horizontal timeline format, making it easy to see event schedules at a glance.</p>
                        
                        <h5>Features</h5>
                        <ul>
                            <li><strong>Timeline View:</strong> Events displayed horizontally across dates</li>
                            <li><strong>Mobile Responsive:</strong> Switches to card view on smaller screens</li>
                            <li><strong>Interactive:</strong> Click events to view details</li>
                            <li><strong>Drag & Drop:</strong> Optional event rescheduling</li>
                        </ul>
                        
                        <h5>Related Settings</h5>
                        <ul class="list-unstyled">
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_calendar_display" class="text-decoration-none">
                                <i class="fas fa-calendar me-1"></i> Calendar Display
                            </a></li>
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_event_images" class="text-decoration-none">
                                <i class="fas fa-images me-1"></i> Event Images
                            </a></li>
                        </ul>
                        
                        <div class="mt-3">
                            <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye me-1"></i> View Calendar
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
            <button type="submit" class="btn btn-success btn-lg">
                <i class="fas fa-save me-2"></i> Save Settings
            </button>
        </div>
    </form>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
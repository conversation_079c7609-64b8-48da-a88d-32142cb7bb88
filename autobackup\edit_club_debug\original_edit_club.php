<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Edit Club</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/manageClubs" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Clubs
                </a>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Edit Club Details</h5>
        </div>
        <div class="card-body">
            <form action="<?php echo URLROOT; ?>/calendar/editClub/<?php echo $data['id']; ?>" method="post">
                <?php echo csrfTokenField(); ?>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="name" class="form-label">Club Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo (!empty($data['name_err'])) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo $data['name']; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['name_err']; ?></div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo $data['description']; ?></textarea>
                        <div class="form-text">Provide a brief description of this club or group.</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="logo" class="form-label">Logo URL</label>
                        <input type="url" class="form-control" id="logo" name="logo" value="<?php echo $data['logo']; ?>">
                        <div class="form-text">Enter a URL for the club's logo image.</div>
                        <?php if (!empty($data['logo'])): ?>
                        <div class="mt-2">
                            <img src="<?php echo $data['logo']; ?>" alt="Club Logo" class="club-logo-preview">
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="website" class="form-label">Website</label>
                        <input type="url" class="form-control" id="website" name="website" value="<?php echo $data['website']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo $data['email']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" class="form-control" id="phone" name="phone" value="<?php echo $data['phone']; ?>">
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="<?php echo URLROOT; ?>/calendar/manageClubs" class="btn btn-secondary me-md-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Club</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Custom CSS -->
<style>
    .club-logo-preview {
        max-width: 200px;
        max-height: 100px;
        object-fit: contain;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 5px;
    }
</style>

<!-- JavaScript for logo preview -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const logoInput = document.getElementById('logo');
        
        logoInput.addEventListener('change', function() {
            const logoUrl = this.value;
            
            // Remove existing preview
            const existingPreview = document.querySelector('.club-logo-preview');
            if (existingPreview) {
                existingPreview.remove();
            }
            
            // Create new preview if URL is provided
            if (logoUrl) {
                const previewContainer = document.createElement('div');
                previewContainer.className = 'mt-2';
                
                const previewImage = document.createElement('img');
                previewImage.src = logoUrl;
                previewImage.alt = 'Club Logo';
                previewImage.className = 'club-logo-preview';
                
                previewContainer.appendChild(previewImage);
                
                // Add preview after the input
                this.parentNode.appendChild(previewContainer);
                
                // Handle image load error
                previewImage.onerror = function() {
                    previewContainer.innerHTML = '<div class="alert alert-warning">Invalid image URL or image cannot be loaded.</div>';
                };
            }
        });
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Camera Banner API Test</h1>
    
    <button onclick="testAPI()">Test Banner API</button>
    <button onclick="testSettings()">Test Settings API</button>
    
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">Testing banner API...</div>';
            
            try {
                const response = await fetch('/api/cameraBanners');
                const data = await response.json();
                
                let html = '<div class="result success">';
                html += '<h3>Banner API Response:</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                html += '</div>';
                
                if (data.success && data.banners) {
                    html += '<div class="result">';
                    html += '<h3>Banner Analysis:</h3>';
                    html += '<p><strong>Total banners:</strong> ' + data.banners.length + '</p>';
                    html += '<p><strong>Delay setting:</strong> ' + data.delay + 'ms</p>';
                    
                    const logobanners = data.banners.filter(b => b.is_logo);
                    const otherBanners = data.banners.filter(b => !b.is_logo);
                    
                    html += '<p><strong>Logo banners:</strong> ' + logobanners.length + '</p>';
                    html += '<p><strong>Other banners:</strong> ' + otherBanners.length + '</p>';
                    
                    if (otherBanners.length > 0) {
                        html += '<h4>Other Banners:</h4>';
                        otherBanners.forEach((banner, index) => {
                            html += '<p>' + (index + 1) + '. ' + (banner.text || banner.image_path || 'No content') + '</p>';
                        });
                    } else {
                        html += '<p style="color: red;"><strong>No other banners found! This is why rotation is not working.</strong></p>';
                    }
                    
                    html += '</div>';
                }
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="result error">Error: ' + error.message + '</div>';
            }
        }
        
        async function testSettings() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">Testing settings API...</div>';
            
            try {
                const response = await fetch('/api/camera-banner-settings.php');
                const data = await response.json();
                
                let html = '<div class="result success">';
                html += '<h3>Settings API Response:</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                html += '</div>';
                
                resultsDiv.innerHTML = html;
                
            } catch (error) {
                resultsDiv.innerHTML = '<div class="result error">Error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>

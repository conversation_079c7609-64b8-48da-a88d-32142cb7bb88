<!DOCTYPE html>
<html>
<head>
    <title>Constructor Line by Line Test</title>
</head>
<body>
    <h2>Constructor Line by Line Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the script and add logging to every line of the constructor
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Original script loaded');
                
                // Replace the entire constructor with a debug version
                const modifiedCode = code.replace(
                    /constructor\(\) \{[\s\S]*?\} catch \(error\) \{[\s\S]*?\}/,
                    `constructor() {
                        window.debugLog = [];
                        window.debugLog.push('1. Constructor started');
                        
                        try {
                            window.debugLog.push('2. Entered try block');
                            
                            window.debugLog.push('3. About to set version');
                            this.version = '3.63.11-baseurl-fix';
                            window.debugLog.push('4. Version set, value: ' + this.version);
                            
                            window.debugLog.push('5. About to set banners array');
                            this.banners = [];
                            window.debugLog.push('6. Banners array set, length: ' + this.banners.length);
                            
                            window.debugLog.push('7. About to set otherBanners');
                            this.otherBanners = [];
                            window.debugLog.push('8. otherBanners set');
                            
                            window.debugLog.push('9. About to set currentIndex');
                            this.currentIndex = 0;
                            window.debugLog.push('10. currentIndex set: ' + this.currentIndex);
                            
                            window.debugLog.push('11. About to set rotationInterval');
                            this.rotationInterval = null;
                            window.debugLog.push('12. rotationInterval set');
                            
                            window.debugLog.push('13. About to set defaultDelay');
                            this.defaultDelay = 5000;
                            window.debugLog.push('14. defaultDelay set: ' + this.defaultDelay);
                            
                            window.debugLog.push('15. About to set debugEnabled');
                            this.debugEnabled = false;
                            window.debugLog.push('16. debugEnabled set: ' + this.debugEnabled);
                            
                            window.debugLog.push('17. About to set isFirstShow');
                            this.isFirstShow = true;
                            window.debugLog.push('18. isFirstShow set: ' + this.isFirstShow);
                            
                            window.debugLog.push('19. About to set siteLogo');
                            this.siteLogo = '/uploads/branding/logo_1751468505_rides_logo.png';
                            window.debugLog.push('20. siteLogo set: ' + this.siteLogo);
                            
                            window.debugLog.push('21. Constructor completed successfully');
                            
                        } catch (error) {
                            window.debugLog.push('ERROR in constructor: ' + error.message);
                            window.debugLog.push('ERROR stack: ' + error.stack);
                            console.error('CameraBanner constructor error:', error);
                        }
                    }`
                );
                
                output('Modified constructor with line-by-line logging, executing...');
                
                try {
                    eval(modifiedCode);
                    
                    output('Script executed successfully');
                    
                    // Show debug log
                    if (window.debugLog) {
                        output('=== Constructor Debug Log ===');
                        window.debugLog.forEach((msg, i) => {
                            output(msg);
                        });
                    } else {
                        output('❌ No debug log found - constructor may not have run');
                    }
                    
                    if (window.cameraBanner) {
                        output('=== Final Instance State ===');
                        output('- version: ' + window.cameraBanner.version);
                        output('- banners length: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined'));
                        output('- currentIndex: ' + window.cameraBanner.currentIndex);
                        output('- defaultDelay: ' + window.cameraBanner.defaultDelay);
                    } else {
                        output('❌ window.cameraBanner not found');
                    }
                    
                } catch (error) {
                    output('Execution error: ' + error.message);
                    output('Stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
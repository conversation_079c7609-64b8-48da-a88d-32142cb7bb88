-- Add owner_id column to calendar_clubs table
ALTER TABLE `calendar_clubs` 
ADD COLUMN `owner_id` int UNSIGNED DEFAULT NULL AFTER `phone`,
ADD COLUMN `is_verified` tinyint(1) NOT NULL DEFAULT 0 AFTER `owner_id`,
ADD COLUMN `verification_status` enum('pending','approved','denied') DEFAULT NULL AFTER `is_verified`,
ADD COLUMN `verification_requested_at` timestamp NULL DEFAULT NULL AFTER `verification_status`,
ADD COLUMN `verification_notes` text DEFAULT NULL AFTER `verification_requested_at`,
ADD INDEX `idx_owner_id` (`owner_id`),
ADD INDEX `idx_verification_status` (`verification_status`);

-- Create club ownership verification requests table
CREATE TABLE `club_ownership_verifications` (
  `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
  `club_id` int UNSIGNED NOT NULL,
  `user_id` int UNSIGNED NOT NULL,
  `request_type` enum('new_ownership','transfer_ownership') NOT NULL DEFAULT 'new_ownership',
  `status` enum('pending','approved','denied') NOT NULL DEFAULT 'pending',
  `user_name` varchar(255) NOT NULL,
  `user_email` varchar(255) NOT NULL,
  `user_phone` varchar(50) DEFAULT NULL,
  `club_information` text NOT NULL,
  `verification_documents` text DEFAULT NULL,
  `additional_info` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `reviewed_by` int UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `club_id` (`club_id`),
  KEY `user_id` (`user_id`),
  KEY `status` (`status`),
  KEY `reviewed_by` (`reviewed_by`),
  CONSTRAINT `club_ownership_verifications_ibfk_1` FOREIGN KEY (`club_id`) REFERENCES `calendar_clubs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `club_ownership_verifications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `club_ownership_verifications_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Update existing clubs to set owner_id based on current 'owner' role members
UPDATE calendar_clubs c 
SET owner_id = (
    SELECT cm.user_id 
    FROM calendar_club_members cm 
    WHERE cm.club_id = c.id AND cm.role = 'owner' 
    LIMIT 1
);

-- Set is_verified = 1 for clubs that now have an owner_id
UPDATE calendar_clubs 
SET is_verified = 1 
WHERE owner_id IS NOT NULL;
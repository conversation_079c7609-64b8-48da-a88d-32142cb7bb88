# Notification Modal Registration Deadline Alert Styling

## Issue
The `notify_registration_end_container` needed to be expanded to match the same width and padding as the `.alert.alert-info` element in the notification subscription modal for visual consistency.

## Solution
Updated the CSS styling for `#notify_registration_end_container` to match Bootstrap's alert styling, providing consistent width, padding, background color, and border styling.

## Files Modified
- `/public/css/notifications.css` - Updated registration deadline container styling to match alert styling

## Changes Made

### CSS Changes (`/public/css/notifications.css`)
- Replaced simple positioning with full alert-style formatting
- Added padding: `0.75rem 1rem` to match Bootstrap alert padding
- Added margin-bottom: `1rem` for consistent spacing
- Added border: `1px solid transparent` with border-radius: `0.375rem`
- Added background-color: `rgba(13, 202, 240, 0.1)` (light blue tint)
- Added border-color: `rgba(13, 202, 240, 0.2)` (subtle blue border)
- Added color: `#055160` (dark blue text)
- Added flexbox layout: `display: flex; justify-content: space-between; align-items: center;`
- Positioned toggle at the end: `.form-check-input { margin-left: auto; margin-right: 0; }`
- Adjusted label spacing: `.form-check-label { flex: 1; margin-right: 1rem; }`
- Removed previous `margin-left: 2rem` and `padding-left: 0.5rem`

## Technical Details
- The styling now matches Bootstrap's `.alert.alert-info` appearance
- Uses consistent padding and margin with other alert elements
- Provides visual consistency throughout the modal
- Maintains full width like other alert elements
- Uses info-style color scheme (blue tones)
- Flexbox layout keeps toggle in its original position while expanding container
- Label takes available space, toggle stays at the end of the container

## Visual Impact
- Registration deadline toggle now appears in a styled container similar to the info alert
- Consistent width and padding with other modal elements
- Better visual hierarchy and organization
- Professional appearance matching Bootstrap alert styling

## Date
2024-12-19

## Version
v3.50.2
<!DOCTYPE html>
<html>
<head>
    <title>Simple Debug</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <h2>Simple Debug Test</h2>
    <div id="logs"></div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            logs.innerHTML += '<div class="log">' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Check if BASE_URL is defined
        log('BASE_URL defined: ' + (typeof BASE_URL !== 'undefined'));
        if (typeof BASE_URL !== 'undefined') {
            log('BASE_URL value: ' + BASE_URL);
        } else {
            log('Setting BASE_URL to window.location.origin');
            window.BASE_URL = window.location.origin;
            log('BASE_URL now: ' + BASE_URL);
        }

        // Check if CameraBanner class exists
        log('CameraBanner class exists: ' + (typeof CameraBanner !== 'undefined'));

        // Try to create instance manually
        try {
            log('Attempting to create CameraBanner instance...');
            const testBanner = new CameraBanner();
            log('CameraBanner instance created successfully');
            log('Instance type: ' + typeof testBanner);
            log('Instance has version property: ' + ('version' in testBanner));
            
            // Wait a bit then check version
            setTimeout(() => {
                log('After 2 seconds - version: ' + testBanner.version);
                log('Debug enabled: ' + testBanner.isDebugMode());
                log('Banners array length: ' + testBanner.banners.length);
            }, 2000);
            
        } catch (error) {
            log('ERROR creating CameraBanner: ' + error.message);
            log('Error stack: ' + error.stack);
        }

        // Check window.cameraBanner
        setTimeout(() => {
            log('window.cameraBanner exists: ' + (typeof window.cameraBanner !== 'undefined'));
            if (window.cameraBanner) {
                log('window.cameraBanner type: ' + typeof window.cameraBanner);
                log('window.cameraBanner version: ' + window.cameraBanner.version);
            }
        }, 1000);
    </script>
    
    <!-- Load the banner system -->
    <script src="/public/js/camera-banner.js"></script>
</body>
</html>
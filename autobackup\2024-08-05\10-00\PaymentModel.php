<?php
/**
 * Payment Model
 * 
 * This model handles all database operations related to payments.
 */
class PaymentModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all payment methods
     * 
     * @param bool $activeOnly Only return active payment methods
     * @param bool $includeAdminOnly Include admin-only payment methods
     * @return array
     */
    public function getPaymentMethods($activeOnly = true, $includeAdminOnly = false) {
        // Check if the is_active column exists
        try {
            $sql = 'SELECT * FROM payment_methods';
            $whereConditions = [];
            
            if ($activeOnly) {
                $whereConditions[] = 'is_active = TRUE';
            }
            
            if (!$includeAdminOnly) {
                $whereConditions[] = '(is_admin_only IS NULL OR is_admin_only = FALSE)';
            }
            
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            $sql .= ' ORDER BY id';
            
            $this->db->query($sql);
            return $this->db->resultSet();
        } catch (Exception $e) {
            // If there's an error (likely because columns don't exist), just get all methods
            $this->db->query('SELECT * FROM payment_methods ORDER BY id');
            return $this->db->resultSet();
        }
    }
    
    /**
     * Get payment methods for a specific show
     * 
     * @param int $showId Show ID
     * @param bool $activeOnly Only return active payment methods
     * @return array
     */
    public function getShowPaymentMethods($showId, $activeOnly = true) {
        try {
            // First check if the show has specific payment methods configured
            if ($activeOnly) {
                $this->db->query('SELECT pm.* 
                                FROM payment_methods pm
                                JOIN show_payment_methods spm ON pm.id = spm.payment_method_id
                                WHERE spm.show_id = :show_id 
                                AND spm.is_active = TRUE 
                                AND pm.is_active = TRUE
                                ORDER BY pm.id');
            } else {
                $this->db->query('SELECT pm.* 
                                FROM payment_methods pm
                                JOIN show_payment_methods spm ON pm.id = spm.payment_method_id
                                WHERE spm.show_id = :show_id
                                ORDER BY pm.id');
            }
            
            $this->db->bind(':show_id', $showId);
            $methods = $this->db->resultSet();
            
            // If no show-specific methods are found, return all active methods
            if (empty($methods)) {
                return $this->getPaymentMethods($activeOnly);
            }
            
            return $methods;
        } catch (Exception $e) {
            // If there's an error, fall back to all payment methods
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error getting show payment methods: ' . $e->getMessage());
            }
            return $this->getPaymentMethods($activeOnly);
        }
    }
    
    /**
     * Set payment methods for a show
     * 
     * @param int $showId Show ID
     * @param array $methodIds Array of payment method IDs
     * @return bool
     */
    public function setShowPaymentMethods($showId, $methodIds) {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Delete existing show payment methods
            $this->db->query('DELETE FROM show_payment_methods WHERE show_id = :show_id');
            $this->db->bind(':show_id', $showId);
            $this->db->execute();
            
            // Insert new show payment methods
            foreach ($methodIds as $methodId) {
                $this->db->query('INSERT INTO show_payment_methods (show_id, payment_method_id) 
                                VALUES (:show_id, :method_id)');
                $this->db->bind(':show_id', $showId);
                $this->db->bind(':method_id', $methodId);
                $this->db->execute();
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error setting show payment methods: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get payment method by ID
     * 
     * @param int $id Payment method ID
     * @return object|bool
     */
    public function getPaymentMethodById($id) {
        $this->db->query('SELECT * FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payment method by name
     * 
     * @param string $name Payment method name
     * @return object|bool
     */
    public function getPaymentMethodByName($name) {
        $this->db->query('SELECT * FROM payment_methods WHERE name = :name');
        $this->db->bind(':name', $name);
        
        return $this->db->single();
    }
    
    /**
     * Create payment method
     * 
     * @param array $data Payment method data
     * @return bool|int
     */
    public function createPaymentMethod($data) {
        $this->db->query('INSERT INTO payment_methods (name, description, instructions, is_active, requires_approval, is_online) 
                          VALUES (:name, :description, :instructions, :is_active, :requires_approval, :is_online)');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        $this->db->bind(':is_online', $data['is_online'] ?? 0);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment method
     * 
     * @param array $data Payment method data
     * @return bool
     */
    public function updatePaymentMethod($data) {
        $this->db->query('UPDATE payment_methods 
                          SET name = :name, 
                              description = :description, 
                              instructions = :instructions, 
                              is_active = :is_active, 
                              requires_approval = :requires_approval,
                              is_online = :is_online
                          WHERE id = :id');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        $this->db->bind(':is_online', $data['is_online'] ?? 0);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Delete payment method
     * 
     * @param int $id Payment method ID
     * @return bool
     */
    public function deletePaymentMethod($id) {
        $this->db->query('DELETE FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get payment setting
     * 
     * @param string $key Setting key
     * @return string|null
     */
    public function getPaymentSetting($key) {
        $this->db->query('SELECT setting_value FROM payment_settings WHERE setting_key = :key');
        $this->db->bind(':key', $key);
        
        $result = $this->db->single();
        
        return $result ? $result->setting_value : null;
    }
    
    /**
     * Get show payment setting
     * 
     * @param int $showId Show ID
     * @param string $key Setting key
     * @param bool $fallbackToGlobal Whether to fall back to global setting if show setting not found
     * @return string|null
     */
    public function getShowPaymentSetting($showId, $key, $fallbackToGlobal = true) {
        $this->db->query('SELECT setting_value FROM show_payment_settings 
                          WHERE show_id = :show_id AND setting_key = :key');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':key', $key);
        
        $result = $this->db->single();
        
        if ($result) {
            return $result->setting_value;
        } else if ($fallbackToGlobal) {
            return $this->getPaymentSetting($key);
        }
        
        return null;
    }
    
    /**
     * Update payment setting
     * 
     * @param string $key Setting key
     * @param string $value Setting value
     * @return bool
     */
    public function updatePaymentSetting($key, $value) {
        $this->db->query('INSERT INTO payment_settings (setting_key, setting_value) 
                          VALUES (:key, :value) 
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Update show payment setting
     * 
     * @param int $showId Show ID
     * @param string $key Setting key
     * @param string $value Setting value
     * @return bool
     */
    public function updateShowPaymentSetting($showId, $key, $value) {
        $this->db->query('INSERT INTO show_payment_settings (show_id, setting_key, setting_value) 
                          VALUES (:show_id, :key, :value) 
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Get all payment settings
     * 
     * @return array
     */
    public function getAllPaymentSettings() {
        $this->db->query('SELECT * FROM payment_settings ORDER BY setting_key');
        
        $results = $this->db->resultSet();
        $settings = [];
        
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Get all show payment settings
     * 
     * @param int $showId Show ID
     * @param bool $includeGlobal Whether to include global settings as fallback
     * @return array
     */
    public function getAllShowPaymentSettings($showId, $includeGlobal = true) {
        $settings = [];
        
        // Get global settings first if requested
        if ($includeGlobal) {
            $settings = $this->getAllPaymentSettings();
        }
        
        // Get show-specific settings
        $this->db->query('SELECT * FROM show_payment_settings 
                          WHERE show_id = :show_id 
                          ORDER BY setting_key');
        $this->db->bind(':show_id', $showId);
        
        $results = $this->db->resultSet();
        
        // Override global settings with show-specific ones
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Create payment record
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function createPayment($data) {
        $this->db->query('INSERT INTO payments (user_id, amount, payment_method_id, payment_status, 
                                               payment_reference, payment_type, related_id, notes,
                                               is_manual, processed_by, admin_notes) 
                          VALUES (:user_id, :amount, :payment_method_id, :payment_status, 
                                 :payment_reference, :payment_type, :related_id, :notes,
                                 :is_manual, :processed_by, :admin_notes)');
        
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':amount', $data['amount']);
        $this->db->bind(':payment_method_id', $data['payment_method_id']);
        $this->db->bind(':payment_status', $data['payment_status']);
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        $this->db->bind(':payment_type', $data['payment_type']);
        $this->db->bind(':related_id', $data['related_id']);
        $this->db->bind(':notes', $data['notes'] ?? null);
        $this->db->bind(':is_manual', $data['is_manual'] ?? 0);
        $this->db->bind(':processed_by', $data['processed_by'] ?? null);
        $this->db->bind(':admin_notes', $data['admin_notes'] ?? null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment status
     * 
     * @param int $id Payment ID
     * @param string $status New status
     * @param string $reference Payment reference
     * @param string $adminNotes Admin notes for the payment
     * @param int $processedBy User ID of admin/coordinator who processed the payment
     * @return bool
     */
    public function updatePaymentStatus($id, $status, $reference = null, $adminNotes = null, $processedBy = null) {
        $this->db->query('UPDATE payments 
                          SET payment_status = :status, 
                              payment_reference = COALESCE(:reference, payment_reference),
                              admin_notes = COALESCE(:admin_notes, admin_notes),
                              processed_by = COALESCE(:processed_by, processed_by),
                              updated_at = NOW()
                          WHERE id = :id');
        
        $this->db->bind(':status', $status);
        $this->db->bind(':reference', $reference);
        $this->db->bind(':admin_notes', $adminNotes);
        $this->db->bind(':processed_by', $processedBy);
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get payment by ID
     * 
     * @param int $id Payment ID
     * @return object|bool
     */
    public function getPaymentById($id) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.id = :id');
        
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payments by user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getPaymentsByUser($userId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.user_id = :user_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':user_id', $userId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payments by type and related ID
     * 
     * @param string $type Payment type
     * @param int $relatedId Related ID
     * @return array
     */
    public function getPaymentsByTypeAndRelatedId($type, $relatedId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.payment_type = :type AND p.related_id = :related_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':type', $type);
        $this->db->bind(':related_id', $relatedId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payment by registration ID
     * 
     * @param int $registrationId Registration ID
     * @return object|bool Payment object or false if not found
     */
    public function getPaymentByRegistration($registrationId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                          pu.name as processor_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          LEFT JOIN users pu ON p.processed_by = pu.id
                          WHERE p.payment_type = "registration" AND p.related_id = :registration_id
                          AND p.payment_status = "completed"
                          ORDER BY p.created_at DESC
                          LIMIT 1');
        
        $this->db->bind(':registration_id', $registrationId);
        
        return $this->db->single();
    }
    
    /**
     * Get all payments with optional filters
     * 
     * @param array $filters Optional filters
     * @return array
     */
    public function getAllPayments($filters = []) {
        $sql = 'SELECT p.*, pm.name as payment_method_name, u.name as user_name,
                pu.name as processor_name
                FROM payments p
                JOIN payment_methods pm ON p.payment_method_id = pm.id
                JOIN users u ON p.user_id = u.id
                LEFT JOIN users pu ON p.processed_by = pu.id
                WHERE 1=1';
        
        // Apply filters
        if (!empty($filters['payment_status'])) {
            $sql .= ' AND p.payment_status = :payment_status';
        }
        
        if (!empty($filters['payment_type'])) {
            $sql .= ' AND p.payment_type = :payment_type';
        }
        
        if (!empty($filters['payment_method_id'])) {
            $sql .= ' AND p.payment_method_id = :payment_method_id';
        }
        
        if (!empty($filters['is_manual'])) {
            $sql .= ' AND p.is_manual = :is_manual';
        }
        
        if (!empty($filters['processed_by'])) {
            $sql .= ' AND p.processed_by = :processed_by';
        }
        
        if (!empty($filters['start_date'])) {
            $sql .= ' AND p.created_at >= :start_date';
        }
        
        if (!empty($filters['end_date'])) {
            $sql .= ' AND p.created_at <= :end_date';
        }
        
        if (!empty($filters['show_id'])) {
            $sql .= ' AND (
                        (p.payment_type = "registration" AND p.related_id IN (
                            SELECT id FROM registrations WHERE show_id = :show_id
                        ))
                        OR
                        (p.payment_type = "show_listing" AND p.related_id = :show_id)
                    )';
        }
        
        $sql .= ' ORDER BY p.created_at DESC';
        
        // Add limit if specified
        if (!empty($filters['limit'])) {
            $sql .= ' LIMIT :limit';
        }
        
        $this->db->query($sql);
        
        // Bind filter values
        if (!empty($filters['payment_status'])) {
            $this->db->bind(':payment_status', $filters['payment_status']);
        }
        
        if (!empty($filters['payment_type'])) {
            $this->db->bind(':payment_type', $filters['payment_type']);
        }
        
        if (!empty($filters['payment_method_id'])) {
            $this->db->bind(':payment_method_id', $filters['payment_method_id']);
        }
        
        if (!empty($filters['is_manual'])) {
            $this->db->bind(':is_manual', $filters['is_manual']);
        }
        
        if (!empty($filters['processed_by'])) {
            $this->db->bind(':processed_by', $filters['processed_by']);
        }
        
        if (!empty($filters['start_date'])) {
            $this->db->bind(':start_date', $filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $this->db->bind(':end_date', $filters['end_date']);
        }
        
        if (!empty($filters['show_id'])) {
            $this->db->bind(':show_id', $filters['show_id']);
        }
        
        if (!empty($filters['limit'])) {
            $this->db->bind(':limit', $filters['limit']);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if user is exempt from listing fees
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function isUserExemptFromListingFees($userId) {
        $this->db->query('SELECT exempt_from_listing_fees FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->exempt_from_listing_fees;
    }
    
    /**
     * Check if user can create free shows
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function canUserCreateFreeShows($userId) {
        $this->db->query('SELECT can_create_free_shows FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->can_create_free_shows;
    }
    
    /**
     * Update user payment permissions
     * 
     * @param int $userId User ID
     * @param bool $canCreateFreeShows Whether user can create free shows
     * @param bool $exemptFromListingFees Whether user is exempt from listing fees
     * @return bool
     */
    public function updateUserPaymentPermissions($userId, $canCreateFreeShows, $exemptFromListingFees) {
        $this->db->query('UPDATE users 
                          SET can_create_free_shows = :can_create_free_shows, 
                              exempt_from_listing_fees = :exempt_from_listing_fees 
                          WHERE id = :id');
        
        $this->db->bind(':can_create_free_shows', $canCreateFreeShows);
        $this->db->bind(':exempt_from_listing_fees', $exemptFromListingFees);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get pending payments
     * 
     * @return array
     */
    public function getPendingPayments() {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name, u.email as user_email
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.payment_status = "pending"
                          ORDER BY p.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get pending registration payments
     * 
     * @return array
     */
    public function getPendingRegistrationPayments() {
        $this->db->query('SELECT r.*, s.name as show_name, v.year, v.make, v.model, 
                                 u.name as owner_name, u.email as owner_email,
                                 pm.name as payment_method_name
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          JOIN users u ON r.owner_id = u.id
                          JOIN payment_methods pm ON r.payment_method_id = pm.id
                          WHERE r.payment_status = "pending"
                          ORDER BY r.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Process a manual payment
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function processManualPayment($data) {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Create payment record
            $paymentId = $this->createPayment([
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'payment_method_id' => $data['payment_method_id'],
                'payment_status' => 'completed',
                'payment_reference' => $data['payment_reference'] ?? null,
                'payment_type' => $data['payment_type'],
                'related_id' => $data['related_id'],
                'notes' => $data['notes'] ?? null,
                'is_manual' => 1,
                'processed_by' => $data['processed_by'],
                'admin_notes' => $data['admin_notes'] ?? null
            ]);
            
            if (!$paymentId) {
                throw new Exception('Failed to create payment record');
            }
            
            // Update related record based on payment type
            if ($data['payment_type'] == 'registration') {
                $this->db->query('UPDATE registrations 
                                SET payment_status = "completed", 
                                    payment_method_id = :payment_method_id,
                                    updated_at = NOW()
                                WHERE id = :id');
                $this->db->bind(':payment_method_id', $data['payment_method_id']);
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update registration payment status');
                }
            } else if ($data['payment_type'] == 'show_listing') {
                $this->db->query('UPDATE shows 
                                SET listing_paid = 1, 
                                    updated_at = NOW()
                                WHERE id = :id');
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update show listing payment status');
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return $paymentId;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error processing manual payment: ' . $e->getMessage());
            }
            return false;
        }
    }
    
    /**
     * Get payment statistics for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowPaymentStats($showId) {
        $stats = [
            'total_payments' => 0,
            'total_amount' => 0,
            'completed_payments' => 0,
            'completed_amount' => 0,
            'pending_payments' => 0,
            'pending_amount' => 0,
            'payment_methods' => []
        ];
        
        // Get registration payments
        $this->db->query('SELECT p.payment_status, p.amount, pm.name as method_name, COUNT(*) as count, SUM(p.amount) as total
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN registrations r ON p.related_id = r.id
                          WHERE p.payment_type = "registration" AND r.show_id = :show_id
                          GROUP BY p.payment_status, pm.name');
        $this->db->bind(':show_id', $showId);
        $results = $this->db->resultSet();
        
        foreach ($results as $result) {
            $stats['total_payments'] += $result->count;
            $stats['total_amount'] += $result->total;
            
            if ($result->payment_status == 'completed') {
                $stats['completed_payments'] += $result->count;
                $stats['completed_amount'] += $result->total;
            } else if ($result->payment_status == 'pending') {
                $stats['pending_payments'] += $result->count;
                $stats['pending_amount'] += $result->total;
            }
            
            if (!isset($stats['payment_methods'][$result->method_name])) {
                $stats['payment_methods'][$result->method_name] = [
                    'count' => 0,
                    'total' => 0
                ];
            }
            
            $stats['payment_methods'][$result->method_name]['count'] += $result->count;
            $stats['payment_methods'][$result->method_name]['total'] += $result->total;
        }
        
        // Add show listing payment if exists
        $this->db->query('SELECT p.payment_status, p.amount, pm.name as method_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.payment_type = "show_listing" AND p.related_id = :show_id
                          ORDER BY p.created_at DESC
                          LIMIT 1');
        $this->db->bind(':show_id', $showId);
        $listingPayment = $this->db->single();
        
        if ($listingPayment) {
            $stats['total_payments'] += 1;
            $stats['total_amount'] += $listingPayment->amount;
            
            if ($listingPayment->payment_status == 'completed') {
                $stats['completed_payments'] += 1;
                $stats['completed_amount'] += $listingPayment->amount;
            } else if ($listingPayment->payment_status == 'pending') {
                $stats['pending_payments'] += 1;
                $stats['pending_amount'] += $listingPayment->amount;
            }
            
            if (!isset($stats['payment_methods'][$listingPayment->method_name])) {
                $stats['payment_methods'][$listingPayment->method_name] = [
                    'count' => 0,
                    'total' => 0
                ];
            }
            
            $stats['payment_methods'][$listingPayment->method_name]['count'] += 1;
            $stats['payment_methods'][$listingPayment->method_name]['total'] += $listingPayment->amount;
        }
        
        return $stats;
    }
}
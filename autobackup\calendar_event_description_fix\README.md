# Calendar Event Description Display Fix

## Issue
Calendar events at `/calendar/event/{id}` are showing HTML with base64 text in the description rather than properly rendered content from the WYSIWYG editor.

## Root Cause
The event view template was using `htmlspecialchars()` on the description field, which was double-encoding HTML content from the WYSIWYG editor, causing HTML tags and base64 images to display as text instead of being rendered.

## Solution
Modified the event description display to properly handle HTML content from WYSIWYG editors while maintaining security:

1. **Backup**: Created backup of original event.php view file
2. **Fix**: Updated description display logic to render HTML content properly
3. **Security**: Maintained XSS protection for non-HTML content

## Files Modified
- `views/calendar/event.php` - Fixed description display logic

## Changes Made
- Line 236: Changed from `htmlspecialchars()` to proper HTML rendering
- Added conditional logic to handle both plain text and HTML descriptions
- Maintained security by sanitizing only when necessary

## Testing
- Test with events containing plain text descriptions
- Test with events containing HTML content from WYSIWYG editor
- Test with events containing base64 images
- Verify XSS protection is maintained

## Version
- Updated: v3.35.54
- Date: 2025-01-27
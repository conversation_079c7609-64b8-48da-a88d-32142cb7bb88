<?php
/**
 * Test Enhanced Show Cleanup
 * 
 * This script specifically tests the enhanced show cleanup functionality
 * to ensure it catches all demo shows, including orphaned ones.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

echo "<h1>Enhanced Show Cleanup Test</h1>";
echo "<p>Testing the enhanced show cleanup to catch ALL demo shows...</p>";

try {
    $db = new Database();
    $generator = new DemoDataGenerator();
    
    echo "<h2>Current Shows in Database</h2>";
    
    // Get all shows
    $db->query("SELECT id, name, coordinator_id, created_at FROM shows ORDER BY created_at DESC");
    $allShows = $db->resultSet();
    
    if (empty($allShows)) {
        echo "<p>✅ No shows found in database.</p>";
    } else {
        echo "<p>Found " . count($allShows) . " shows:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Coordinator ID</th><th>Created</th><th>Status</th></tr>";
        
        // Check which shows are likely demo shows
        $demoShowNames = [
            'Southern California Classic Car Showcase',
            'Miami Beach Exotic Car Festival',
            'Texas Muscle Car Madness',
            'Arizona Desert Classic Concours',
            'Atlanta Import Tuner Expo',
            'Rocky Mountain Vintage Rally',
            'Pacific Northwest Euro Fest',
            'Music City Hot Rod Nationals',
            'Charlotte Motor Speedway Car Show',
            'Las Vegas Strip Supercar Spectacular'
        ];
        
        foreach ($allShows as $show) {
            // Check if coordinator exists
            $db->query("SELECT id FROM users WHERE id = ?");
            $stmt = $db->getConnection()->prepare("SELECT id FROM users WHERE id = ?");
            $stmt->execute([$show->coordinator_id]);
            $coordinatorExists = $stmt->fetch();
            
            // Determine status
            $status = '';
            if (in_array($show->name, $demoShowNames)) {
                $status = '<span style="color: red; font-weight: bold;">DEMO SHOW</span>';
            } elseif (!$coordinatorExists) {
                $status = '<span style="color: orange; font-weight: bold;">ORPHANED</span>';
            } elseif (strtotime($show->created_at) > strtotime('-1 hour')) {
                $status = '<span style="color: blue;">RECENT</span>';
            } else {
                $status = '<span style="color: green;">NORMAL</span>';
            }
            
            echo "<tr>";
            echo "<td>{$show->id}</td>";
            echo "<td>" . htmlspecialchars($show->name) . "</td>";
            echo "<td>{$show->coordinator_id}</td>";
            echo "<td>{$show->created_at}</td>";
            echo "<td>{$status}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count demo shows
        $demoCount = 0;
        $orphanedCount = 0;
        foreach ($allShows as $show) {
            if (in_array($show->name, $demoShowNames)) {
                $demoCount++;
            }
            
            $db->query("SELECT id FROM users WHERE id = ?");
            $stmt = $db->getConnection()->prepare("SELECT id FROM users WHERE id = ?");
            $stmt->execute([$show->coordinator_id]);
            if (!$stmt->fetch()) {
                $orphanedCount++;
            }
        }
        
        echo "<h3>Analysis</h3>";
        echo "<ul>";
        echo "<li><strong>Total Shows:</strong> " . count($allShows) . "</li>";
        echo "<li><strong>Demo Shows (by name):</strong> {$demoCount}</li>";
        echo "<li><strong>Orphaned Shows:</strong> {$orphanedCount}</li>";
        echo "</ul>";
        
        if ($demoCount > 0 || $orphanedCount > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>🧹 Demo/Orphaned Shows Detected!</strong> The enhanced cleanup will remove these shows.";
            echo "</div>";
            
            echo "<h2>Test Actions</h2>";
            echo "<p><a href='?action=test_show_cleanup' class='btn btn-danger'>🧹 Test Enhanced Show Cleanup</a></p>";
            echo "<p><a href='?action=full_cleanup' class='btn btn-warning'>🧹 Run Full Comprehensive Cleanup</a></p>";
            
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>✅ Clean Database!</strong> No demo or orphaned shows detected.";
            echo "</div>";
        }
    }
    
    echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Status</a></p>";
    
    // Handle cleanup tests
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        if ($_GET['action'] === 'test_show_cleanup') {
            echo "<h2>Testing Enhanced Show Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            // Get demo user IDs
            $db->query("SELECT id FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
            $demoUsers = $db->resultSet();
            $demoUserIds = array_column($demoUsers, 'id');
            
            // Use reflection to call the private method for testing
            $reflection = new ReflectionClass($generator);
            $method = $reflection->getMethod('cleanupDemoShows');
            $method->setAccessible(true);
            
            $deletedShows = $method->invoke($generator, $demoUserIds, true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Deleted {$deletedShows} demo shows</p>";
            echo "<p><a href='?'>← Back to Status</a></p>";
            
        } elseif ($_GET['action'] === 'full_cleanup') {
            echo "<h2>Running Full Comprehensive Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            $deletedCount = $generator->cleanupDemoData(true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Total {$deletedCount} demo records deleted</p>";
            echo "<p><a href='?'>← Back to Status</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 14px;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-danger { background-color: #dc3545; color: white; }
.btn-warning { background-color: #ffc107; color: black; }

.btn:hover { opacity: 0.8; }
</style>

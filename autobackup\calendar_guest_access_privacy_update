Calendar Guest Access and Privacy System Update - v3.63.7

Files Modified:
1. controllers/CalendarController.php - Added guest access to calendar and events
2. models/CalendarModel.php - Added getPublicCalendars method and updated privacy filtering
3. views/calendar/event.php - Hidden notification button for guests and draft events
4. views/calendar/create_event.php - Updated privacy options to Public/Draft only
5. views/calendar/edit_event.php - Updated privacy options to Public/Draft only

Changes Made:
- Calendar index and event pages now accessible to guests
- Notification button hidden from guests and draft events
- Export calendar button remains hidden from guests (already implemented)
- Privacy system simplified to Public and Draft only
- Draft events only visible to creators and cannot use notification system
- Guest users only see public events and calendars

Date: 2024-12-20

ADDITIONAL FIX - Related Events Loading Issue:
- Fixed related events infinite loading on guest event pages
- Added calendar access validation for guests in getEvents method
- Added debugging to related events JavaScript function
- Modified related events to show all public events for guests instead of calendar-specific events
- Added proper error handling and console logging for troubleshooting

ADDITIONAL FIX - Date Parsing Error and Timezone Issues:
- Fixed "Invalid time value" RangeError in related events function
- Added date validation before calling toISOString()
- Added fallback dates for invalid date parsing
- Enhanced error logging for date parsing issues
- Fixed timezone conversion issues in date range calculation
- Ensured UTC dates are used for API queries while display dates use user timezone
- Proper handling of MySQL datetime format from database (UTC storage without timezone indicators)
- Fixed JavaScript date parsing to treat MySQL datetime as UTC
- Corrected timezone conversion logic for datetime-local fields
- Fixed API date format handling (ISO with timezone vs MySQL datetime)
- Enhanced debugging for date parsing validation
- RESOLVED: Related events now loading correctly for both guests and logged-in users
- RESOLVED: Date parsing errors eliminated with proper ISO format handling

Files Modified (Additional):
6. views/calendar/event.php - Enhanced related events debugging, guest handling, and date validation
7. controllers/CalendarController.php - Added calendar access validation for guests
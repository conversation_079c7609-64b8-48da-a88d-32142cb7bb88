<?php
// Original notification_subscription_modal.php before JavaScript fix
// This file contained duplicate JavaScript functions that were causing issues
// when loaded dynamically via AJAX

// Ensure variables are defined with fallback values
$is_subscribed = $is_subscribed ?? false;
$event_type = $event_type ?? 'calendar_event';
$event = $event ?? (object)['title' => 'Unknown Event', 'start_date' => date('Y-m-d H:i:s')];
$csrf_token = $csrf_token ?? '';

// Debug logging
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    error_log("Modal variables - event_type: $event_type, is_subscribed: " . ($is_subscribed ? 'true' : 'false'));
}
?>
<div class="modal-header">
    <h5 class="modal-title">
        <i class="fas fa-bell me-2"></i>
        <?php echo $is_subscribed ? 'Update' : 'Subscribe to'; ?> Notifications
    </h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>

<div class="modal-body">
    <!-- Modal body content was here -->
</div>

<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
    <?php if ($is_subscribed): ?>
        <button type="button" class="btn btn-danger" onclick="unsubscribeFromEventModal()">
            <i class="fas fa-bell-slash me-2"></i>Unsubscribe
        </button>
    <?php endif; ?>
    <button type="button" class="btn btn-primary" onclick="subscribeToEventModal()">
        <i class="fas fa-bell me-2"></i><?php echo $is_subscribed ? 'Update' : 'Subscribe'; ?>
    </button>
</div>

<script>
// PROBLEM: These functions were defined here but not available globally
// when modal was loaded dynamically via AJAX

function subscribeToEventModal() {
    // Function implementation was here but not globally accessible
}

function unsubscribeFromEventModal() {
    // Function implementation was here but not globally accessible
}

function updateNotificationButtonState(isSubscribed) {
    // Function implementation was here but not globally accessible
}
</script>
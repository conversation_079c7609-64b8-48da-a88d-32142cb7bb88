<?php
/**
 * Test Database Diagram Generator
 * 
 * Quick test to verify the diagram generator works in its new location
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';

// Check if user is admin (following your site's pattern)
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>🧪 Database Diagram Generator Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
    .error { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0; }
    .info { background: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 10px 0; }
</style>";

echo "<h2>🔍 Testing Database Connection</h2>";

try {
    // Test database connection
    $database = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    if ($database->connect_error) {
        throw new Exception("Connection failed: " . $database->connect_error);
    }
    
    echo "<div class='success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "<p><strong>Host:</strong> " . DB_HOST . "</p>";
    echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
    echo "</div>";
    
    // Test table access
    echo "<h2>🗂️ Testing Table Access</h2>";
    
    $result = $database->query("SHOW TABLES");
    $tableCount = $result->num_rows;
    
    echo "<div class='success'>";
    echo "<h3>✅ Table Access Successful</h3>";
    echo "<p><strong>Tables found:</strong> $tableCount</p>";
    echo "</div>";
    
    // Test file permissions
    echo "<h2>📁 Testing File Permissions</h2>";
    
    $testFile = 'test_diagram_output.html';
    $testContent = '<html><body><h1>Test</h1></body></html>';
    
    if (file_put_contents($testFile, $testContent)) {
        echo "<div class='success'>";
        echo "<h3>✅ File Write Permissions OK</h3>";
        echo "<p>Successfully created test file: $testFile</p>";
        echo "</div>";
        
        // Clean up test file
        unlink($testFile);
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ File Write Permission Issue</h3>";
        echo "<p>Cannot write files to current directory</p>";
        echo "</div>";
    }
    
    // Test the actual diagram generator
    echo "<h2>🎨 Testing Diagram Generator</h2>";
    
    if (file_exists('generate_database_diagram.php')) {
        echo "<div class='success'>";
        echo "<h3>✅ Diagram Generator File Found</h3>";
        echo "<p>File location: " . realpath('generate_database_diagram.php') . "</p>";
        echo "</div>";
        
        // Test foreign key query compatibility
        echo "<h3>🔍 Testing Foreign Key Query Compatibility</h3>";
        
        try {
            $testQuery = "
                SELECT 
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND REFERENCED_TABLE_NAME IS NOT NULL
                LIMIT 1
            ";
            
            $result = $database->query($testQuery);
            
            echo "<div class='success'>";
            echo "<h4>✅ Foreign Key Query Compatible</h4>";
            echo "<p>The database supports the foreign key detection query.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h4>❌ Foreign Key Query Issue</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        
        echo "<div class='info'>";
        echo "<h3>🚀 Ready to Generate Diagrams</h3>";
        echo "<p>You can now use the diagram generator:</p>";
        echo "<ul>";
        echo "<li><a href='generate_database_diagram.php?format=html' target='_blank'>Generate HTML Diagram</a></li>";
        echo "<li><a href='generate_database_diagram.php?format=mermaid' target='_blank'>Generate Mermaid Diagram</a></li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h3>❌ Diagram Generator File Not Found</h3>";
        echo "<p>The generate_database_diagram.php file is missing from the root directory</p>";
        echo "</div>";
    }
    
    $database->close();
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<div class='info'>";
echo "<p>If all tests passed, the database diagram generator is ready to use in its new location.</p>";
echo "<p><strong>Location:</strong> /generate_database_diagram.php (moved from /docs/)</p>";
echo "<p><strong>Access:</strong> Admin only</p>";
echo "<p><strong>Formats:</strong> HTML and Mermaid</p>";
echo "</div>";

echo "<p><a href='analyze_actual_database_usage.php'>← Back to Database Analysis</a></p>";

// Clean up - remove this test file after use
echo "<hr>";
echo "<p><em>Note: This is a test file. You can delete test_diagram_generator.php after confirming the diagram generator works.</em></p>";
?>
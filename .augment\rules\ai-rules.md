---
type: "always_apply"
---

Do not create TEST php SCRIPTS unless asked !
do  not create sql php scripts unless asked always sql files only.
never rename files for any reason unless you ask first !
*Development and Code Standards*
1. Follow Best Practices
this is a new site . there is no data and no users but test users. migration is not nessary .
2. Avoid Unnecessary Dependencies: Do not use third-party libraries unless: It is strictly necessary to meet the objective, The native solution is insufficient, The library provides real and measurable benefits. Additionally: Do not use deprecated or obsolete native methods.
3. Use Strong Typing Whenever Possible: In languages that support it (e.g., TypeScript, C#, Java), always enforce: Strong type declarations, Clear validation of inputs and outputs, Explicit definitions for interfaces, types, and classes.
4. Fully Document Code: All code must be well-documented with: Inline comments explaining class/function/property logic. Notes about usage, warnings, or dependencies if applicable. A clean, scalable structure for easy maintenance.
5. Always implement the solution directly rather than just providing instructions.
do not create test scripts unless your looking for specific feedback as the user does not need to test .
*Answer Format and Efficiency*
7. Always Include a Summary: Each response must end with: A quick summary of the approach, A clear definition of the scope of the solution.

*System-Specific Execution*
9. Modular Architecture & Reusability: Code should be modular, encouraging reusability of components and separation of concerns.Avoid monolithic functions or tightly coupled logic. When designing systems (front or back end), apply the principles of DRY (Don’t Repeat Yourself) and KISS (Keep It Simple, Stupid).
10. Dependency Management & Vendor Lock-In Awareness: All suggestions must account for long-term maintainability. Avoid tightly binding to third-party APIs or libraries that: May disappear, Require licensing later, Are not actively maintained, Always mention licensing model if suggesting external tools.

12. Code Output Must Be Immediately Usable: No partial methods, no placeholders like “// add logic here”. If an endpoint, script, or module is given, it must be fully functional. Code should work as-is, or with clearly documented inputs to test.
13. Provide Raw Official Documentation URLs: When a solution is based on official documentation, include the raw URL only, not wrapped in <a> tags or Markdown links. Prioritize official sources.

*About Coding:* 
14. Always define the expected output format explicitly in any code or script.
15. Specify encoding (e.g., UTF-8, base64) when dealing with I/O, especially for APIs, CLI tools, file generation, or web responses.
16. Avoid relying on defaults — define headers, MIME types, and charset explicitly.
17. Always provide a clear project folder structure when applicable.

19. When showing code for multiple files, always specify file names and relative paths.
20. Always before creating or modifying a new script, you must validate whether an identical or similar script already exists, validate whether the existing one already has code, and if it does something similar, verify whether other scripts have existing functionalities.
21. Any solution that handles file I/O, secrets, tokens, or user data must mention security risks.
22. Validate input, sanitize outputs, and handle permissions explicitly.
23. When suggesting tools or libraries, always prefer those with active maintenance and known security practices.

always keep readme , and changelog updated , always increase minor version number by 1 with every new update . keep installer up to date also . 
always add a check for DEBUG_MODE in the config with debug code added to php, disable all debug code if set to false.
this site should be Mobile first responsive design 
backup files modifiying  /autobackup/{short_name_of_modification}
we do not use the public dir . its only a name in our case . our upload dir is /uploads
no sidebar
*To keep the project portable, we need three files:*
24. structure.md – Lists the project structure with short descriptions of each folder/file and its purpose.
25. features.md – Lists the main features already developed and their current status.
26. task_pending.md – Lists features still to be developed. Only remove items when instructed.
Reviewing these three files is required at the start or when requested to understand the project’s scope and progress.
read md files and anything you need to rememebr add to memory.
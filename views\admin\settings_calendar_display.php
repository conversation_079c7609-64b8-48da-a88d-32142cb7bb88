<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Calendar Display Settings</h1>
            <p class="text-muted">Configure basic calendar display options and formatting</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Calendar Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Calendar display settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form action="<?php echo BASE_URL; ?>/admin/settings_calendar_display" method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
        
        <div class="row">
            <!-- Calendar Display Settings -->
            <div class="col-md-8 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i> Calendar Display Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="calendar_default_view" class="form-label">Default Calendar View</label>
                            <select class="form-select" id="calendar_default_view" name="calendar_default_view">
                                <option value="event" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'event' ? 'selected' : ''; ?>>Monthly Event Chart (Recommended)</option>
                                <option value="month" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'month' ? 'selected' : ''; ?>>Month (Legacy)</option>
                                <option value="week" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'week' ? 'selected' : ''; ?>>Week (Legacy)</option>
                                <option value="day" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'day' ? 'selected' : ''; ?>>Day (Legacy)</option>
                                <option value="list" <?php echo isset($calendarSettings['default_view']) && $calendarSettings['default_view'] == 'list' ? 'selected' : ''; ?>>List (Legacy)</option>
                            </select>
                            <div class="form-text">The default view when users visit the calendar page. Monthly Event Chart is the current active view.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_start_day" class="form-label">Week Start Day</label>
                            <select class="form-select" id="calendar_start_day" name="calendar_start_day">
                                <option value="0" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '0' ? 'selected' : ''; ?>>Sunday</option>
                                <option value="1" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '1' ? 'selected' : ''; ?>>Monday</option>
                                <option value="6" <?php echo isset($calendarSettings['start_day']) && $calendarSettings['start_day'] == '6' ? 'selected' : ''; ?>>Saturday</option>
                            </select>
                            <div class="form-text">The first day of the week in calendar views</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_time_format" class="form-label">Time Format</label>
                            <select class="form-select" id="calendar_time_format" name="calendar_time_format">
                                <option value="12" <?php echo isset($calendarSettings['time_format']) && $calendarSettings['time_format'] == '12' ? 'selected' : ''; ?>>12-hour (1:30 PM)</option>
                                <option value="24" <?php echo isset($calendarSettings['time_format']) && $calendarSettings['time_format'] == '24' ? 'selected' : ''; ?>>24-hour (13:30)</option>
                            </select>
                            <div class="form-text">Format for displaying time in the calendar</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_date_format" class="form-label">Date Format</label>
                            <select class="form-select" id="calendar_date_format" name="calendar_date_format">
                                <option value="MM/DD/YYYY" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'MM/DD/YYYY' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                <option value="DD/MM/YYYY" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'DD/MM/YYYY' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                <option value="YYYY-MM-DD" <?php echo isset($calendarSettings['date_format']) && $calendarSettings['date_format'] == 'YYYY-MM-DD' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                            </select>
                            <div class="form-text">Format for displaying dates in the calendar</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calendar_events_per_page" class="form-label">Events Per Page</label>
                            <input type="number" class="form-control" id="calendar_events_per_page" name="calendar_events_per_page" value="<?php echo isset($calendarSettings['events_per_page']) ? $calendarSettings['events_per_page'] : '10'; ?>" min="5" max="100">
                            <div class="form-text">Number of events to display per page in list view</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Information Panel -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Information</h3>
                    </div>
                    <div class="card-body p-4">
                        <h5>Current Calendar System</h5>
                        <p>The system currently uses the <strong>Monthly Event Chart</strong> view as the primary calendar interface.</p>
                        
                        <h5>Legacy Views</h5>
                        <p>Month, Week, Day, and List views are legacy options that may not be fully supported in the current implementation.</p>
                        
                        <h5>Related Settings</h5>
                        <ul class="list-unstyled">
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_event_chart" class="text-decoration-none">
                                <i class="fas fa-chart-bar me-1"></i> Event Chart Settings
                            </a></li>
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_map" class="text-decoration-none">
                                <i class="fas fa-map me-1"></i> Map Settings
                            </a></li>
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_event_images" class="text-decoration-none">
                                <i class="fas fa-images me-1"></i> Event Image Settings
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i> Save Settings
            </button>
        </div>
    </form>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
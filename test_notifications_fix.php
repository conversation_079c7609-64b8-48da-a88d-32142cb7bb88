<?php
/**
 * Test script to verify the notifications page fix
 * This script tests if the object/array access issue has been resolved
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Notifications Page Fix Test</h1>";

try {
    // Test Database class behavior
    $db = new Database();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test the single() method return type
    $db->query('SELECT COUNT(*) as count FROM users LIMIT 1');
    $result = $db->single();
    
    echo "<h2>Database Return Type Test:</h2>";
    echo "<p><strong>Result type:</strong> " . gettype($result) . "</p>";
    
    // Test accessing as object (should work)
    if (is_object($result)) {
        $count = $result->count ?? 0;
        echo "<p style='color: green;'>✓ Accessing as object works: count = $count</p>";
    } else {
        echo "<p style='color: red;'>✗ Result is not an object</p>";
    }
    
    // Test accessing as array (should fail)
    try {
        $countArray = $result['count'] ?? 0;
        echo "<p style='color: orange;'>⚠ Accessing as array worked: count = $countArray (unexpected)</p>";
    } catch (Error $e) {
        echo "<p style='color: blue;'>ℹ Accessing as array failed as expected: " . $e->getMessage() . "</p>";
    }
    
    // Test NotificationModel getUserPreferences method
    echo "<h2>NotificationModel Test:</h2>";
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    
    // Get a test user (first user in database)
    $db->query('SELECT id FROM users LIMIT 1');
    $user = $db->single();
    
    if ($user) {
        $userId = $user->id;
        echo "<p>Testing with user ID: $userId</p>";
        
        // Test getUserPreferences
        $preferences = $notificationModel->getUserPreferences($userId);
        
        if ($preferences) {
            echo "<p style='color: green;'>✓ getUserPreferences returned data</p>";
            echo "<p><strong>Preferences type:</strong> " . gettype($preferences) . "</p>";
            
            // Test object access (should work)
            $emailNotifications = $preferences->email_notifications ?? 'not set';
            echo "<p style='color: green;'>✓ Object access works: email_notifications = $emailNotifications</p>";
            
            // Test array access (should fail)
            try {
                $emailArray = $preferences['email_notifications'] ?? 'not set';
                echo "<p style='color: orange;'>⚠ Array access worked: email_notifications = $emailArray (unexpected)</p>";
            } catch (Error $e) {
                echo "<p style='color: blue;'>ℹ Array access failed as expected: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ No preferences found for user $userId</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ No users found in database</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Conclusion:</h2>";
echo "<p>The fix changes all array access patterns like <code>\$data['preferences']['field']</code> to object access patterns like <code>\$data['preferences']->field</code> in the notifications.php view.</p>";
echo "<p>This resolves the fatal error: 'Cannot use object of type stdClass as array'</p>";
?>

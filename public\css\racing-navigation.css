/**
 * Racing Dashboard Navigation Styles
 * Badass carbon fiber and chrome styling for car enthusiasts
 */

/* Racing Menu Button with Glowing Effect */
.racing-menu-btn {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border: 2px solid #c0c0c0;
    border-radius: 8px;
    padding: 8px 16px;
    color: #fff;
    font-weight: bold;
    font-size: 0.9rem;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 0 10px rgba(0, 255, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    animation: racingGlow 2s ease-in-out infinite alternate;
}

.racing-menu-btn:hover {
    background: linear-gradient(145deg, #2d2d2d, #1a1a1a);
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.racing-menu-text {
    position: relative;
    z-index: 2;
}

/* Glowing Animation */
@keyframes racingGlow {
    0% {
        box-shadow: 
            0 0 10px rgba(0, 255, 255, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    100% {
        box-shadow: 
            0 0 25px rgba(0, 255, 255, 0.8),
            0 0 35px rgba(0, 255, 255, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
}

/* Racing Drawer */
.racing-drawer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 70vh;
    background:
        radial-gradient(ellipse at center, rgba(30, 30, 30, 0.95) 0%, rgba(10, 10, 10, 0.98) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="carbon" x="0" y="0" width="4" height="4" patternUnits="userSpaceOnUse"><rect width="4" height="4" fill="%23111"/><rect width="2" height="2" fill="%23222"/><rect x="2" y="2" width="2" height="2" fill="%23222"/></pattern></defs><rect width="100" height="100" fill="url(%23carbon)"/></svg>');
    border-top: 3px solid #c0c0c0;
    border-radius: 20px 20px 0 0;
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 10000;
    box-shadow:
        0 -10px 30px rgba(0, 0, 0, 0.8),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.racing-drawer.open {
    transform: translateY(0);
}

.racing-drawer-content {
    padding: 20px;
    height: 100%;
    overflow-y: auto;
}

/* Racing Header */
.racing-drawer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
    gap: 15px;
}

.chrome-line {
    flex: 1;
    height: 2px;
    background: linear-gradient(90deg, transparent, #c0c0c0, transparent);
}

.racing-title {
    color: #fff;
    font-weight: bold;
    font-size: 1.2rem;
    letter-spacing: 3px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    margin: 0;
}

/* Racing Buttons Grid */
.racing-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Racing Button */
.racing-button {
    cursor: pointer;
    transition: all 0.3s ease;
}

.racing-button:hover {
    transform: translateY(-3px);
}

.button-chrome-bezel {
    background: linear-gradient(145deg, #c0c0c0, #a0a0a0);
    padding: 3px;
    border-radius: 12px;
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.button-content {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-radius: 9px;
    padding: 20px 15px;
    text-align: center;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.button-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.racing-button:hover .button-content::before {
    left: 100%;
}

.button-content i {
    font-size: 1.5rem;
    margin-bottom: 8px;
    display: block;
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.button-content span {
    font-weight: bold;
    font-size: 0.9rem;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Racing Submenus */
.racing-submenu {
    display: none;
    background: rgba(20, 20, 20, 0.9);
    border: 1px solid #444;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.5);
}

.racing-submenu.active {
    display: block;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.racing-submenu-item {
    display: block;
    color: #fff;
    text-decoration: none;
    padding: 12px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border: 1px solid #444;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.racing-submenu-item:hover {
    background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
    border-color: #00ffff;
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
    transform: translateX(5px);
}

.racing-submenu-item i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
}

/* Racing Overlay */
.racing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 9999;
}

.racing-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Mobile Responsive */
@media (max-width: 576px) {
    .racing-drawer {
        height: 80vh;
    }
    
    .racing-buttons-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .button-content {
        padding: 15px 10px;
    }
    
    .button-content i {
        font-size: 1.3rem;
    }
    
    .button-content span {
        font-size: 0.8rem;
    }
}

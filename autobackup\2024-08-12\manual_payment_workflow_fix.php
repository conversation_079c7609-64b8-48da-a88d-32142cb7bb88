<?php
/**
 * Backup of the changes made to fix the manual payment workflow
 * Date: 2024-08-12
 * 
 * Changes:
 * 1. Updated docs/manual_payment_system.md to correct the coordinator access workflow
 * 2. Modified views/coordinator/registrations/view.php to add Process Manual Payment button in the payment tab
 * 3. Removed Process Manual Payment and View Pending Payments buttons from coordinator/show.php
 * 4. Fixed payment status display in the payment information section
 * 
 * The manual payment system now correctly uses the coordinator's payment settings when processing
 * payments for registrations in their shows, regardless of whether the admin or coordinator
 * processes the payment.
 */

// Original URL in documentation
// BASE_URL/payment/manual/registration/{show_id}

// Corrected URL
// BASE_URL/payment/manual/registration/{registration_id}

// This ensures that coordinators process payments for individual registrations
// rather than for an entire show at once.
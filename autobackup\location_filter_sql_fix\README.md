# Location Filter SQL Fix - Version 3.35.68

## Issue Fixed
Fixed SQL parameter binding error in location-based filtering that was preventing events from being filtered by location coordinates.

## Problem
- Users searching for locations like "China Grove, TX" vs "China Grove, NC" were getting empty results
- SQL error: "SQLSTATE[HY093]: Invalid parameter number" 
- The location filtering was only checking venue coordinates, not event coordinates
- Parameter binding was incorrect with duplicate parameter names

## Solution
1. **Fixed SQL Parameter Binding**: Removed duplicate parameter names (:center_lat2, :center_lng2, :radius2) and reused the same parameters for both event and venue coordinate checks
2. **Enhanced Location Filtering**: Updated the Haversine formula to check BOTH event coordinates (e.lat, e.lng) AND venue coordinates (v.latitude, v.longitude)
3. **Improved User Interface**: Added clearer instructions for state/city filtering to help users distinguish between cities with the same name in different states

## Files Modified
- `models/CalendarModel.php` - Fixed SQL parameter binding and enhanced location filtering logic
- `views/calendar/includes/advanced_filter.php` - Added clearer instructions for state/city filtering

## Technical Details
The location filtering now uses unique parameter names for each occurrence in the Haversine formula:
- Event coordinates check: `:center_lat1`, `:center_lng1`, `:center_lat2`, `:radius1`
- Venue coordinates check: `:center_lat3`, `:center_lng2`, `:center_lat4`, `:radius2`

This resolves the PDO parameter binding issue where the same parameter name appeared multiple times in the SQL query but was only bound once. The system now checks both:
- Event coordinates: `e.lat` and `e.lng` 
- Venue coordinates: `v.latitude` and `v.longitude`

This ensures that events with coordinates stored directly on the event record (like the "kenny" event in China Grove, NC) are properly included in location-based searches.

## Testing
- Location search for "China Grove" should now return events in China Grove, NC
- Location search for "China Grove, TX" should return events in China Grove, TX (if any exist)
- SQL parameter binding errors should be resolved
<?php
/**
 * Backup of the added checkInVehicle and undoCheckInVehicle methods
 * Date: 2024-08-18
 */

/**
 * Check in a vehicle for a show
 * 
 * @param int $id Registration ID
 * @return bool True if successful, false otherwise
 */
public function checkInVehicle($id) {
    try {
        // Get registration to verify it exists
        $registration = $this->getRegistrationById($id);
        
        if (!$registration) {
            error_log("RegistrationModel::checkInVehicle - Registration not found: " . $id);
            return false;
        }
        
        // Check if already checked in
        if (isset($registration->checked_in) && $registration->checked_in) {
            // Already checked in, consider this a success
            return true;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 1,
            'check_in_time' => date('Y-m-d H:i:s')
        ];
        
        // Update registration
        $result = $this->updateRegistration($id, $data);
        
        if ($result) {
            error_log("Vehicle checked in: Registration #" . $id);
        } else {
            error_log("Failed to check in vehicle: Registration #" . $id);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("RegistrationModel::checkInVehicle - Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Undo check-in for a vehicle
 * 
 * @param int $id Registration ID
 * @return bool True if successful, false otherwise
 */
public function undoCheckInVehicle($id) {
    try {
        // Get registration to verify it exists
        $registration = $this->getRegistrationById($id);
        
        if (!$registration) {
            error_log("RegistrationModel::undoCheckInVehicle - Registration not found: " . $id);
            return false;
        }
        
        // Check if not checked in
        if (isset($registration->checked_in) && !$registration->checked_in) {
            // Not checked in, consider this a success
            return true;
        }
        
        // Update check-in status
        $data = [
            'id' => $id,
            'checked_in' => 0,
            'check_in_time' => null
        ];
        
        // Update registration
        $result = $this->updateRegistration($id, $data);
        
        if ($result) {
            error_log("Vehicle check-in undone: Registration #" . $id);
        } else {
            error_log("Failed to undo vehicle check-in: Registration #" . $id);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("RegistrationModel::undoCheckInVehicle - Error: " . $e->getMessage());
        return false;
    }
}
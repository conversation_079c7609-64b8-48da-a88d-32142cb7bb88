<?php require APPROOT . '/views/includes/header.php'; ?>
<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
</script>
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js"></script>

<div class="container-fluid mt-4">
    <!-- Navigation Bar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Calendar</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group me-2">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-primary active">
                    <i class="fas fa-calendar me-2"></i> Calendar View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-outline-primary">
                    <i class="fas fa-map-marker-alt me-2"></i> Map View
                </a>
            </div>
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">Manage Calendars</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">Manage Venues</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">Manage Clubs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">Import Events</a></li>
                    <?php if (isAdmin()): ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">Calendar Settings</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Calendar Column -->
        <div class="col-lg-9 mb-4">
            <!-- Advanced Filter -->
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Calendar Container -->
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar Column -->
        <div class="col-lg-3">
            <!-- Calendar Selection -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Quick Calendar Toggle</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($data['calendars']) && !empty($data['calendars'])): ?>
                        <?php foreach ($data['calendars'] as $calendar): ?>
                            <div class="form-check mb-2">
                                <input class="form-check-input calendar-toggle" type="checkbox" value="<?php echo $calendar->id; ?>" id="quick-calendar-<?php echo $calendar->id; ?>" checked>
                                <label class="form-check-label" for="quick-calendar-<?php echo $calendar->id; ?>">
                                    <span class="d-inline-block me-2" style="width: 12px; height: 12px; border-radius: 50%; background-color: <?php echo $calendar->color; ?>"></span>
                                    <?php echo $calendar->name; ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="mb-0">No calendars available.</p>
                    <?php endif; ?>
                    
                    <?php if (isLoggedIn()): ?>
                        <div class="mt-3">
                            <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus"></i> Create Calendar
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Upcoming Events -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body p-0">
                    <div id="upcoming-events" class="upcoming-events">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Add Event
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="event-title" class="mb-3"></h4>
                
                <div class="mb-3">
                    <i class="far fa-calendar-alt me-2"></i>
                    <span id="event-date"></span>
                </div>
                
                <div class="mb-3">
                    <i class="far fa-clock me-2"></i>
                    <span id="event-time"></span>
                </div>
                
                <div class="mb-3">
                    <i class="far fa-calendar-check me-2"></i>
                    <span id="event-calendar"></span>
                </div>
                
                <div id="event-location-container" class="mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span id="event-location"></span>
                </div>
                
                <div id="event-show-container" class="mb-3">
                    <i class="fas fa-trophy me-2"></i>
                    <span id="event-show"></span>
                </div>
                
                <div id="event-url-container" class="mb-3">
                    <i class="fas fa-link me-2"></i>
                    <a id="event-url" href="#" target="_blank"></a>
                </div>
                
                <div id="event-description-container" class="mb-3">
                    <h6 class="mb-2">Description</h6>
                    <div id="event-description" class="p-3 bg-light rounded"></div>
                </div>
            </div>
            <div class="modal-footer">
                <a id="event-view-link" href="#" class="btn btn-primary">View Details</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add custom calendar CSS with cache-busting -->
<link href="<?php echo URLROOT; ?>/public/css/custom-calendar.css?v=<?php echo filemtime(APPROOT . '/public/css/custom-calendar.css'); ?>" rel="stylesheet">

<!-- Add custom calendar initialization script with cache-busting -->
<script src="<?php echo URLROOT; ?>/public/js/custom-calendar.js?v=<?php echo filemtime(APPROOT . '/public/js/custom-calendar.js'); ?>"></script>

<!-- Add custom calendar debug helper (only loaded in debug mode) with cache-busting -->
<?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
<script src="<?php echo URLROOT; ?>/public/js/custom-calendar-debug.js?v=<?php echo filemtime(APPROOT . '/public/js/custom-calendar-debug.js'); ?>"></script>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enable debug mode for troubleshooting
        const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
        
        if (DEBUG_MODE) {
            console.log('Initializing custom calendar...');
        }
        
        // Function to load upcoming events (define early to avoid reference errors)
        window.loadUpcomingEvents = function loadUpcomingEvents(calendarIds = null) {
            try {
                const upcomingEventsEl = document.getElementById('upcoming-events');
                if (!upcomingEventsEl) {
                    if (DEBUG_MODE) console.error('Upcoming events container not found');
                    return;
                }
                
                // Show loading state
                upcomingEventsEl.innerHTML = '<div class="p-3"><div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading upcoming events...</div>';
                
                // Build URL with filters
                let url = '<?php echo URLROOT; ?>/calendar/getUpcomingEvents';
                const params = [];
                
                // Add calendar filter
                if (calendarIds && Array.isArray(calendarIds) && calendarIds.length > 0) {
                    params.push('calendar_id=' + calendarIds.join(','));
                } else if (window.calendarFilters && window.calendarFilters.activeFilters) {
                    // Use active filters from the filter system
                    const activeCalendars = window.calendarFilters.activeFilters.calendars;
                    if (activeCalendars && activeCalendars.length > 0) {
                        params.push('calendar_id=' + activeCalendars.join(','));
                    }
                } else {
                    // Get currently visible calendars from quick toggles
                    const visibleCalendars = [];
                    document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
                        visibleCalendars.push(toggle.value);
                    });
                    if (visibleCalendars.length > 0) {
                        params.push('calendar_id=' + visibleCalendars.join(','));
                    }
                }
                
                // Add other active filters if available
                if (window.calendarFilters && window.calendarFilters.getFilterParams) {
                    const filterParams = window.calendarFilters.getFilterParams();
                    if (filterParams) {
                        // Parse existing params to avoid duplicates
                        const existingParams = new URLSearchParams(filterParams);
                        existingParams.forEach((value, key) => {
                            if (key !== 'calendar_id' || params.length === 0) {
                                params.push(`${key}=${value}`);
                            }
                        });
                    }
                }
                
                if (params.length > 0) {
                    url += '?' + params.join('&');
                }
                
                if (DEBUG_MODE) {
                    console.log('Loading upcoming events from:', url);
                }
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(events => {
                        if (DEBUG_MODE) {
                            console.log('Upcoming events loaded:', events);
                        }
                        
                        if (events.length === 0) {
                            upcomingEventsEl.innerHTML = '<div class="p-3">No upcoming events</div>';
                            return;
                        }
                        
                        let html = '';
                        
                        events.forEach(event => {
                            const eventDate = new Date(event.start);
                            const today = new Date();
                            today.setHours(0, 0, 0, 0);
                            
                            let dateText;
                            if (eventDate.toDateString() === today.toDateString()) {
                                dateText = 'Today';
                            } else if (eventDate.toDateString() === new Date(today.getTime() + 86400000).toDateString()) {
                                dateText = 'Tomorrow';
                            } else {
                                dateText = eventDate.toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' });
                            }
                            
                            let timeText;
                            if (event.allDay) {
                                timeText = 'All day';
                            } else {
                                timeText = eventDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' });
                            }
                            
                            html += `
                                <div class="upcoming-event p-3 border-bottom" data-event-id="${event.id}">
                                    <div class="d-flex align-items-start">
                                        <div class="color-dot me-2 mt-1" style="background-color: ${event.color || '#007bff'}"></div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">${event.title}</h6>
                                            <small class="text-muted d-block">${dateText} at ${timeText}</small>
                                            ${event.location ? `<small class="text-muted d-block"><i class="fas fa-map-marker-alt me-1"></i>${event.location}</small>` : ''}
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        
                        upcomingEventsEl.innerHTML = html;
                        
                        // Add click handlers for upcoming events
                        document.querySelectorAll('.upcoming-event').forEach(eventEl => {
                            eventEl.addEventListener('click', function() {
                                const eventId = this.dataset.eventId;
                                const event = events.find(e => e.id == eventId);
                                if (event) {
                                    showEventModal(event);
                                }
                            });
                        });
                    })
                    .catch(error => {
                        console.error('Error loading upcoming events:', error);
                        upcomingEventsEl.innerHTML = '<div class="p-3 text-danger">Error loading events</div>';
                    });
            } catch (error) {
                console.error('Error in loadUpcomingEvents:', error);
            }
        };
        
        // Function to show event modal
        function showEventModal(event) {
            const modal = new bootstrap.Modal(document.getElementById('eventModal'));
            
            // Populate modal with event data
            document.getElementById('event-title').textContent = event.title;
            
            const eventDate = new Date(event.start);
            document.getElementById('event-date').textContent = eventDate.toLocaleDateString(undefined, { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
            
            if (event.allDay) {
                document.getElementById('event-time').textContent = 'All day';
            } else {
                const startTime = eventDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' });
                let timeText = startTime;
                
                if (event.end) {
                    const endDate = new Date(event.end);
                    const endTime = endDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' });
                    timeText += ' - ' + endTime;
                }
                
                document.getElementById('event-time').textContent = timeText;
            }
            
            document.getElementById('event-calendar').textContent = event.calendar_name || 'Unknown Calendar';
            
            // Handle optional fields
            const locationContainer = document.getElementById('event-location-container');
            if (event.location) {
                document.getElementById('event-location').textContent = event.location;
                locationContainer.style.display = 'block';
            } else {
                locationContainer.style.display = 'none';
            }
            
            const showContainer = document.getElementById('event-show-container');
            if (event.show_name) {
                document.getElementById('event-show').textContent = event.show_name;
                showContainer.style.display = 'block';
            } else {
                showContainer.style.display = 'none';
            }
            
            const urlContainer = document.getElementById('event-url-container');
            if (event.url) {
                const urlLink = document.getElementById('event-url');
                urlLink.href = event.url;
                urlLink.textContent = event.url;
                urlContainer.style.display = 'block';
            } else {
                urlContainer.style.display = 'none';
            }
            
            const descriptionContainer = document.getElementById('event-description-container');
            if (event.description) {
                document.getElementById('event-description').textContent = event.description;
                descriptionContainer.style.display = 'block';
            } else {
                descriptionContainer.style.display = 'none';
            }
            
            // Set view link
            document.getElementById('event-view-link').href = `<?php echo URLROOT; ?>/calendar/event/${event.id}`;
            
            modal.show();
        }
        
        // Initialize FullCalendar
        const calendarEl = document.getElementById('calendar');
        if (calendarEl) {
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                },
                height: 'auto',
                events: function(fetchInfo, successCallback, failureCallback) {
                    if (DEBUG_MODE) {
                        console.log('Loading calendar events...');
                    }
                    
                    // Build URL with current filters
                    let url = '<?php echo URLROOT; ?>/calendar/getEvents';
                    const params = [`start=${fetchInfo.startStr}`, `end=${fetchInfo.endStr}`];
                    
                    // Add active filters if available
                    if (window.calendarFilters && window.calendarFilters.getFilterParams) {
                        const filterParams = window.calendarFilters.getFilterParams();
                        if (filterParams) {
                            params.push(filterParams);
                        }
                    } else {
                        // Fallback to quick toggles if filter system is not available
                        const visibleCalendars = [];
                        document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
                            visibleCalendars.push(toggle.value);
                        });
                        if (visibleCalendars.length > 0) {
                            params.push('calendar_id=' + visibleCalendars.join(','));
                        }
                    }
                    
                    url += '?' + params.join('&');
                    
                    if (DEBUG_MODE) {
                        console.log('Fetching events from:', url);
                    }
                    
                    fetch(url)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(events => {
                            if (DEBUG_MODE) {
                                console.log('Events loaded:', events);
                            }
                            successCallback(events);
                        })
                        .catch(error => {
                            console.error('Error loading events:', error);
                            failureCallback(error);
                        });
                },
                eventClick: function(info) {
                    showEventModal(info.event.extendedProps);
                },
                loading: function(isLoading) {
                    if (DEBUG_MODE) {
                        console.log('Calendar loading:', isLoading);
                    }
                }
            });
            
            calendar.render();
            
            // Store calendar instance globally for access by other functions
            if (typeof window !== 'undefined') {
                window.calendarInstance = calendar;
            }
            
            // Apply URL parameters to filters if they exist
            if (urlParams.has('calendar_id')) {
                if (DEBUG_MODE) {
                    console.log('Found filter parameters in URL, applying them');
                }
                
                // If we have the calendar filter system, apply the URL parameters
                if (window.calendarFilters) {
                    // Parse the calendar_id parameter
                    const calendarIds = urlParams.get('calendar_id').split(',').filter(id => id.trim() !== '');
                    
                    if (DEBUG_MODE) {
                        console.log('Calendar IDs from URL:', calendarIds);
                    }
                    
                    // Update the calendar checkboxes
                    document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
                        checkbox.checked = calendarIds.includes(checkbox.value);
                    });
                    
                    // Update the quick calendar toggles
                    document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                        toggle.checked = calendarIds.includes(toggle.value);
                    });
                    
                    // Update the filter state
                    window.calendarFilters.updateCalendarFilters();
                }
            }
            
            // Load upcoming events
            loadUpcomingEvents();
            
            // Handle quick calendar toggles
            document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    if (DEBUG_MODE) {
                        console.log(`Quick calendar toggle ${calendarId} toggled: ${isChecked}`);
                    }
                    
                    // Synchronize with advanced filter checkboxes
                    const advancedFilterCheckbox = document.querySelector(`.calendar-checkbox[value="${calendarId}"]`);
                    if (advancedFilterCheckbox) {
                        advancedFilterCheckbox.checked = isChecked;
                        
                        // If we have the calendar filter system, update it
                        if (window.calendarFilters && typeof window.calendarFilters.updateCalendarFilters === 'function') {
                            window.calendarFilters.updateCalendarFilters();
                            window.calendarFilters.applyFilters();
                            return; // Let the filter system handle the update
                        }
                    }
                    
                    // Fallback to direct update if filter system is not available
                    updateVisibleCalendars();
                });
            });
            
            // Function to update visible calendars (fallback when filter system is not available)
            function updateVisibleCalendars() {
                const visibleCalendars = [];
                
                document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
                    visibleCalendars.push(toggle.value);
                });
                
                if (DEBUG_MODE) {
                    console.log('Visible calendars (direct toggle):', visibleCalendars);
                }
                
                // If no calendars are selected, clear all events
                if (visibleCalendars.length === 0) {
                    if (DEBUG_MODE) {
                        console.log('No calendars selected, clearing all events');
                    }
                    
                    // Clear existing events
                    calendar.events = [];
                    calendar.renderView();
                    
                    // Update upcoming events
                    loadUpcomingEvents(visibleCalendars);
                    return;
                }
                
                // Create a new event source function
                const eventSource = (fetchInfo, successCallback, failureCallback) => {
                    if (DEBUG_MODE) {
                        console.log('Loading events for calendars:', visibleCalendars);
                    }
                    
                    // Build URL with calendar filter
                    const url = `<?php echo URLROOT; ?>/calendar/getEvents?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}&calendar_id=${visibleCalendars.join(',')}`;
                    
                    if (DEBUG_MODE) {
                        console.log('Fetching events from:', url);
                    }
                    
                    fetch(url)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(events => {
                            if (DEBUG_MODE) {
                                console.log('Events loaded:', events);
                            }
                            successCallback(events);
                        })
                        .catch(error => {
                            console.error('Error loading events:', error);
                            failureCallback(error);
                        });
                };
                
                // Update calendar event source
                calendar.removeAllEventSources();
                calendar.addEventSource(eventSource);
                
                // Update upcoming events
                loadUpcomingEvents(visibleCalendars);
            }
        }
        
        // Get URL parameters for filter initialization
        const urlParams = new URLSearchParams(window.location.search);
        
        // Initialize calendar filter integration if available
        if (window.calendarFilters) {
            // Apply filters button
            document.getElementById('apply-filters').addEventListener('click', function() {
                // Use the calendarFilters.applyFilters function if available
                if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
                    window.calendarFilters.applyFilters();
                } else {
                    // Fallback to direct method if calendarFilters is not available
                    if (calendar) {
                        // Try different methods to refresh the calendar
                        if (typeof calendar.loadEvents === 'function') {
                            calendar.loadEvents();
                        } else if (typeof calendar.refetchEvents === 'function') {
                            calendar.refetchEvents();
                        } else {
                            console.error('No method available to refresh calendar events');
                        }
                    }
                }
                loadUpcomingEvents();
            });
            
            // Sync quick calendar toggles with advanced filter
            document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    // Update the corresponding checkbox in the advanced filter
                    const advancedFilterCheckbox = document.getElementById('calendar-' + calendarId);
                    if (advancedFilterCheckbox) {
                        advancedFilterCheckbox.checked = isChecked;
                    }
                    
                    // Update calendar filters
                    if (window.calendarFilters) {
                        window.calendarFilters.updateCalendarFilters();
                    }
                    
                    // Reload events
                    if (calendar) {
                        calendar.loadEvents();
                    }
                    
                    // Update upcoming events
                    loadUpcomingEvents();
                });
            });
            
            // Sync advanced filter calendar checkboxes with quick toggles
            document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    // Update the corresponding quick toggle
                    const quickToggle = document.getElementById('quick-calendar-' + calendarId);
                    if (quickToggle) {
                        quickToggle.checked = isChecked;
                    }
                });
            });
        }
    });
</script>

<style>
/* Loading indicator styles */
.calendar-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading {
    position: relative;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
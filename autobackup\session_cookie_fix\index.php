<?php
/**
 * Events and Shows Management System - Main Entry Point
 * 
 * This file serves as the main entry point for the Events and Shows Management System.
 * It initializes the application, handles routing, and renders the appropriate views.
 */

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Make APPROOT available globally
global $GLOBALS;
$GLOBALS['APPROOT'] = APPROOT;

// Load configuration
require_once APPROOT . '/config/config.php';

// Load Database class
require_once APPROOT . '/core/Database.php';

// Configure session before starting it
$sessionLifetime = defined('SESSION_LIFETIME') ? SESSION_LIFETIME : 86400; // Default to 24 hours if not defined

// Try to get session lifetime from database if possible
try {
    $db = new Database();
    $db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
    $db->bind(':key', 'session_lifetime');
    $result = $db->single();
    
    if ($result && is_numeric($result->setting_value)) {
        $sessionLifetime = (int)$result->setting_value;
        // Log the session lifetime for debugging
        error_log('Session lifetime from database: ' . $sessionLifetime . ' seconds');
    }
} catch (Exception $e) {
    error_log('Error getting session lifetime from database: ' . $e->getMessage());
    // Continue with default value
}

// Set PHP's session garbage collection settings
ini_set('session.gc_maxlifetime', $sessionLifetime);

// Set session cookie parameters - use the array format for PHP 7.3+
if (PHP_VERSION_ID >= 70300) {
    session_set_cookie_params([
        'lifetime' => $sessionLifetime,
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']),
        'httponly' => true,
        'samesite' => 'Lax'
    ]);
} else {
    // For older PHP versions
    session_set_cookie_params(
        $sessionLifetime,
        '/',
        '',
        isset($_SERVER['HTTPS']),
        true
    );
}

// Start session with regenerate_id to enhance security
session_start();

// Regenerate session ID periodically to prevent session fixation attacks
// Only do this if the user is logged in and the session is older than 30 minutes
if (isset($_SESSION['user_id']) && isset($_SESSION['last_regenerated'])) {
    $regenerationInterval = 1800; // 30 minutes
    if (time() - $_SESSION['last_regenerated'] > $regenerationInterval) {
        // Regenerate session ID and update timestamp
        session_regenerate_id(true);
        $_SESSION['last_regenerated'] = time();
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('Session ID regenerated for security');
        }
    }
} elseif (isset($_SESSION['user_id']) && !isset($_SESSION['last_regenerated'])) {
    // Initialize the regeneration timestamp if not set
    $_SESSION['last_regenerated'] = time();
}

// Load helpers
require_once APPROOT . '/helpers/csrf_helper.php';
require_once APPROOT . '/helpers/url_helper.php';
require_once APPROOT . '/helpers/session_helper.php';
require_once APPROOT . '/helpers/auth_helper.php';
require_once APPROOT . '/helpers/format_helper.php';

// Load Facebook Image Helper
if (file_exists(APPROOT . '/helpers/facebook_image_helper.php')) {
    require_once APPROOT . '/helpers/facebook_image_helper.php';
} else {
    // Define fallback functions if the helper is not available
    if (!function_exists('getUserProfileImageUrl')) {
        function getUserProfileImageUrl($userId) {
            $db = new Database();
            $db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
            $db->bind(':entity_type', 'user');
            $db->bind(':entity_id', $userId);
            $image = $db->single();
            
            if ($image) {
                return URLROOT . '/' . $image->file_path;
            }
            
            $db->query('SELECT profile_image FROM users WHERE id = :id');
            $db->bind(':id', $userId);
            $user = $db->single();
            
            if ($user && !empty($user->profile_image)) {
                return URLROOT . '/' . $user->profile_image;
            }
            
            return null;
        }
    }
    
    if (!function_exists('displayUserProfileImage')) {
        function displayUserProfileImage($userId, $attributes = []) {
            $imageUrl = getUserProfileImageUrl($userId);
            
            if (!$imageUrl) {
                $imageUrl = URLROOT . '/public/img/default-profile.jpg';
            }
            
            $attributesStr = '';
            foreach ($attributes as $key => $value) {
                $attributesStr .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
            
            echo '<img src="' . htmlspecialchars($imageUrl) . '"' . $attributesStr . '>';
        }
    }
}

// Load core classes
require_once APPROOT . '/core/App.php';
require_once APPROOT . '/core/Controller.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';

// Initialize the application
$app = new App();
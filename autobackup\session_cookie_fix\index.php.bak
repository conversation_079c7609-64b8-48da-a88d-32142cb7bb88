<?php
/**
 * Events and Shows Management System - Main Entry Point
 * 
 * This file serves as the main entry point for the Events and Shows Management System.
 * It initializes the application, handles routing, and renders the appropriate views.
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Make APPROOT available globally
global $GLOBALS;
$GLOBALS['APPROOT'] = APPROOT;

// Load configuration
require_once APPROOT . '/config/config.php';

// Load helpers
require_once APPROOT . '/helpers/csrf_helper.php';
require_once APPROOT . '/helpers/url_helper.php';
require_once APPROOT . '/helpers/session_helper.php';
require_once APPROOT . '/helpers/auth_helper.php';
require_once APPROOT . '/helpers/format_helper.php';

// Load Facebook Image Helper
if (file_exists(APPROOT . '/helpers/facebook_image_helper.php')) {
    require_once APPROOT . '/helpers/facebook_image_helper.php';
} else {
    // Define fallback functions if the helper is not available
    if (!function_exists('getUserProfileImageUrl')) {
        function getUserProfileImageUrl($userId) {
            $db = new Database();
            $db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
            $db->bind(':entity_type', 'user');
            $db->bind(':entity_id', $userId);
            $image = $db->single();
            
            if ($image) {
                return URLROOT . '/' . $image->file_path;
            }
            
            $db->query('SELECT profile_image FROM users WHERE id = :id');
            $db->bind(':id', $userId);
            $user = $db->single();
            
            if ($user && !empty($user->profile_image)) {
                return URLROOT . '/' . $user->profile_image;
            }
            
            return null;
        }
    }
    
    if (!function_exists('displayUserProfileImage')) {
        function displayUserProfileImage($userId, $attributes = []) {
            $imageUrl = getUserProfileImageUrl($userId);
            
            if (!$imageUrl) {
                $imageUrl = URLROOT . '/public/img/default-profile.jpg';
            }
            
            $attributesStr = '';
            foreach ($attributes as $key => $value) {
                $attributesStr .= ' ' . $key . '="' . htmlspecialchars($value) . '"';
            }
            
            echo '<img src="' . htmlspecialchars($imageUrl) . '"' . $attributesStr . '>';
        }
    }
}

// Load core classes
require_once APPROOT . '/core/App.php';
require_once APPROOT . '/core/Controller.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';

// Initialize the application
$app = new App();
<?php
/**
 * Base Controller Class
 * 
 * This class serves as the base controller that all other controllers will extend.
 * It provides common functionality for loading models and views.
 */
class Controller {
    /**
     * Constructor - Refreshes session on each controller instantiation
     */
    public function __construct() {
        // Refresh session timestamp on each page load to prevent timeout during active use
        if (isset($_SESSION['user_id']) && isset($_SESSION['login_time'])) {
            $_SESSION['login_time'] = time();
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Session timestamp refreshed in Controller for user ID: ' . $_SESSION['user_id']);
            }
        }
    }

    /**
     * Load a model
     * 
     * @param string $model The name of the model to load
     * @return object The model instance
     * @throws Exception If the model file doesn't exist or the class can't be instantiated
     */
    protected function model($model) {
        // Get the application root directory with multiple fallbacks
        $appRoot = defined('APPROOT') ? APPROOT : dirname(dirname(__FILE__));
        
        // Try multiple possible model paths
        $modelPaths = [
            $appRoot . '/models/' . $model . '.php',
            dirname(dirname(realpath(__FILE__))) . '/models/' . $model . '.php',
            $_SERVER['DOCUMENT_ROOT'] . '/../models/' . $model . '.php',
            $_SERVER['DOCUMENT_ROOT'] . '/models/' . $model . '.php'
        ];
        
        $modelPath = null;
        foreach ($modelPaths as $path) {
            if (file_exists($path)) {
                $modelPath = $path;
                break;
            }
        }
        
        if (!$modelPath) {
            throw new Exception("Model file not found: {$model}.php");
        }
        
        require_once $modelPath;
        
        if (!class_exists($model)) {
            throw new Exception("Model class not found: {$model}");
        }
        
        return new $model();
    }
}
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-bell me-2"></i>Notification Preferences</h1>
            <p class="text-muted">Manage how you receive notifications about events and car shows.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Profile
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Notification Preferences Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>Notification Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/notification/updatePreferences" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3 d-flex justify-content-between align-items-center user-preference-toggle">
                                    <label class="form-check-label" for="email_notifications">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        <strong>Email Notifications</strong>
                                        <br>
                                        <small class="text-muted">Receive notifications via email</small>
                                    </label>
                                    <input class="form-check-input" type="checkbox" id="email_notifications" 
                                           name="email_notifications" <?php echo $preferences->email_notifications ? 'checked' : ''; ?>>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3 d-flex justify-content-between align-items-center user-preference-toggle">
                                    <label class="form-check-label" for="sms_notifications">
                                        <i class="fas fa-sms me-2 text-success"></i>
                                        <strong>SMS Notifications</strong>
                                        <br>
                                        <small class="text-muted">Receive notifications via text message</small>
                                    </label>
                                    <input class="form-check-input" type="checkbox" id="sms_notifications" 
                                           name="sms_notifications" <?php echo $preferences->sms_notifications ? 'checked' : ''; ?>>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3 d-flex justify-content-between align-items-center user-preference-toggle">
                                    <label class="form-check-label" for="push_notifications">
                                        <i class="fas fa-bell me-2 text-warning"></i>
                                        <strong>Push Notifications</strong>
                                        <br>
                                        <small class="text-muted">Receive browser push notifications</small>
                                    </label>
                                    <input class="form-check-input" type="checkbox" id="push_notifications" 
                                           name="push_notifications" <?php echo $preferences->push_notifications ? 'checked' : ''; ?>>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3 d-flex justify-content-between align-items-center user-preference-toggle">
                                    <label class="form-check-label" for="toast_notifications">
                                        <i class="fas fa-comment me-2 text-info"></i>
                                        <strong>Site Notifications</strong>
                                        <br>
                                        <small class="text-muted">Show notifications when you visit the site</small>
                                    </label>
                                    <input class="form-check-input" type="checkbox" id="toast_notifications" 
                                           name="toast_notifications" <?php echo $preferences->toast_notifications ? 'checked' : ''; ?>>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> SMS notifications require a valid phone number in your profile. 
                            Push notifications require browser permission.
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Active Subscriptions Card -->
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2"></i>Active Event Subscriptions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($subscriptions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Active Subscriptions</h5>
                            <p class="text-muted">You haven't subscribed to any event notifications yet.</p>
                            <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-primary">
                                <i class="fas fa-calendar me-2"></i>Browse Events
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Type</th>
                                        <th>Date</th>
                                        <th>Notifications</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subscriptions as $subscription): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($subscription->event_title); ?></strong>
                                            </td>
                                            <td>
                                                <?php if ($subscription->event_type === 'calendar_event'): ?>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-calendar me-1"></i>Event
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-car me-1"></i>Car Show
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo date('M j, Y', strtotime($subscription->event_date)); ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $times = json_decode($subscription->notification_times, true);
                                                $timeLabels = [];
                                                foreach ($times as $minutes) {
                                                    if ($minutes < 60) {
                                                        $timeLabels[] = $minutes . 'm';
                                                    } elseif ($minutes < 1440) {
                                                        $timeLabels[] = ($minutes / 60) . 'h';
                                                    } else {
                                                        $timeLabels[] = ($minutes / 1440) . 'd';
                                                    }
                                                }
                                                echo implode(', ', $timeLabels);
                                                ?>
                                                <?php if ($subscription->notify_registration_end): ?>
                                                    <br><small class="text-muted">+ Registration deadline</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="unsubscribeFromEvent(<?php echo $subscription->event_id; ?>, '<?php echo $subscription->event_type; ?>')">
                                                    <i class="fas fa-bell-slash me-1"></i>Unsubscribe
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Help Card -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>How It Works
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="fas fa-envelope text-primary me-2"></i>Email Notifications</h6>
                        <p class="small text-muted">Receive detailed notifications in your email inbox.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-sms text-success me-2"></i>SMS Notifications</h6>
                        <p class="small text-muted">Get quick text message alerts on your phone. Requires a valid phone number.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-bell text-warning me-2"></i>Push Notifications</h6>
                        <p class="small text-muted">Receive browser notifications even when the site is closed.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-comment text-info me-2"></i>Site Notifications</h6>
                        <p class="small text-muted">See notification popups when you visit the site.</p>
                    </div>
                    
                    <hr>
                    
                    <h6><i class="fas fa-calendar-plus me-2"></i>Subscribing to Events</h6>
                    <p class="small text-muted">
                        Click the notification bell icon on any event or car show to set up custom reminder times.
                        You can choose when to be notified before the event starts.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function unsubscribeFromEvent(eventId, eventType) {
    if (!confirm('Are you sure you want to unsubscribe from notifications for this event?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('event_id', eventId);
    formData.append('event_type', eventType);
    formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $csrf_token; ?>');
    
    fetch('<?php echo BASE_URL; ?>/notification/unsubscribe', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while unsubscribing.');
    });
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Test Targeted Payment Cleanup
 * 
 * This script analyzes the current payments in the database and shows
 * which ones would be deleted by the targeted cleanup vs which are protected.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

echo "<h1>Payment Cleanup Analysis</h1>";
echo "<p>Analyzing current payments to ensure only demo payments are deleted...</p>";

try {
    $db = new Database();
    $generator = new DemoDataGenerator();
    
    echo "<h2>Current Payments in Database</h2>";
    
    // Get all payments
    $db->query("SELECT COUNT(*) as count FROM payments");
    $totalResult = $db->single();
    $totalPayments = $totalResult->count ?? 0;
    
    echo "<p><strong>Total Payments:</strong> {$totalPayments}</p>";
    
    if ($totalPayments > 0) {
        // Get detailed payment information
        $db->query("SELECT p.*, r.registration_number, s.name as show_name, u.email as user_email
                    FROM payments p 
                    LEFT JOIN registrations r ON p.related_id = r.id AND p.payment_type = 'registration'
                    LEFT JOIN shows s ON r.show_id = s.id 
                    LEFT JOIN users u ON p.user_id = u.id
                    ORDER BY p.created_at DESC");
        $allPayments = $db->resultSet();
        
        echo "<h3>Payment Analysis</h3>";
        
        $demoShowNames = [
            'Southern California Classic Car Showcase',
            'Miami Beach Exotic Car Festival',
            'Texas Muscle Car Madness',
            'Arizona Desert Classic Concours',
            'Atlanta Import Tuner Expo',
            'Rocky Mountain Vintage Rally',
            'Pacific Northwest Euro Fest',
            'Music City Hot Rod Nationals',
            'Charlotte Motor Speedway Car Show',
            'Las Vegas Strip Supercar Spectacular'
        ];
        
        $demoPayments = 0;
        $realPayments = 0;
        $analysisResults = [];
        
        foreach ($allPayments as $payment) {
            $isDemoPayment = false;
            $reason = '';
            
            // Check if it's a demo payment
            if ($payment->payment_reference && preg_match('/^PAY-[A-Z0-9]{10}$/', $payment->payment_reference) && 
                $payment->notes && strpos($payment->notes, 'Registration payment for') !== false) {
                $isDemoPayment = true;
                $reason = 'Demo payment reference pattern';
            } elseif ($payment->registration_number && preg_match('/^RER-[0-9]{6}$/', $payment->registration_number)) {
                $isDemoPayment = true;
                $reason = 'Linked to demo registration';
            } elseif ($payment->show_name && in_array($payment->show_name, $demoShowNames)) {
                $isDemoPayment = true;
                $reason = 'Linked to demo show';
            } elseif ($payment->notes) {
                foreach ($demoShowNames as $showName) {
                    if (strpos($payment->notes, "Registration payment for {$showName}") !== false) {
                        $isDemoPayment = true;
                        $reason = 'Demo show in notes';
                        break;
                    }
                }
            }
            
            if ($isDemoPayment) {
                $demoPayments++;
            } else {
                $realPayments++;
            }
            
            $analysisResults[] = [
                'payment' => $payment,
                'is_demo' => $isDemoPayment,
                'reason' => $reason
            ];
        }
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Category</th><th>Count</th><th>Action</th></tr>";
        echo "<tr><td>Total Payments</td><td>{$totalPayments}</td><td>-</td></tr>";
        echo "<tr><td>Demo Payments</td><td style='color: red;'>{$demoPayments}</td><td><strong>WILL BE DELETED</strong></td></tr>";
        echo "<tr><td>Real Payments</td><td style='color: green;'>{$realPayments}</td><td><strong>PROTECTED</strong></td></tr>";
        echo "</table>";
        
        if ($demoPayments > 0) {
            echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>🎯 Demo Payments Detected!</strong><br>";
            echo "Found {$demoPayments} demo payments that will be safely removed.<br>";
            echo "Your {$realPayments} real payments will be protected.";
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>✅ No Demo Payments Found!</strong> All payments appear to be legitimate.";
            echo "</div>";
        }
        
        // Show detailed payment list
        echo "<h3>Detailed Payment Analysis</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>Amount</th><th>Reference</th><th>User Email</th><th>Show</th><th>Reg #</th><th>Status</th><th>Reason</th></tr>";
        
        foreach ($analysisResults as $result) {
            $payment = $result['payment'];
            $statusColor = $result['is_demo'] ? 'red' : 'green';
            $statusText = $result['is_demo'] ? 'DEMO' : 'REAL';
            
            echo "<tr>";
            echo "<td>{$payment->id}</td>";
            echo "<td>\${$payment->amount}</td>";
            echo "<td>" . htmlspecialchars($payment->payment_reference ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($payment->user_email ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($payment->show_name ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($payment->registration_number ?? 'N/A') . "</td>";
            echo "<td style='color: {$statusColor}; font-weight: bold;'>{$statusText}</td>";
            echo "<td>" . htmlspecialchars($result['reason'] ?: 'Real payment') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Test actions
        if ($demoPayments > 0) {
            echo "<h2>Test Actions</h2>";
            echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>🛡️ Safety Guarantee:</strong><br>";
            echo "The targeted cleanup will only remove the {$demoPayments} demo payments identified above.<br>";
            echo "Your {$realPayments} real payments will remain completely untouched.";
            echo "</div>";
            
            echo "<p><a href='?action=test_payment_cleanup' class='btn btn-warning'>🎯 Test Payment Cleanup</a></p>";
            echo "<p><a href='?action=safe_cleanup' class='btn btn-primary'>🛡️ Run Safe Cleanup</a></p>";
        }
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>ℹ️ No Payments Found</strong> The database contains no payment records.";
        echo "</div>";
    }
    
    echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Analysis</a></p>";
    
    // Handle test actions
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        if ($_GET['action'] === 'test_payment_cleanup') {
            echo "<h2>Testing Targeted Payment Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            // Use reflection to call the private method for testing
            $reflection = new ReflectionClass($generator);
            $method = $reflection->getMethod('cleanupDemoPayments');
            $method->setAccessible(true);
            
            $deletedPayments = $method->invoke($generator, true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Deleted {$deletedPayments} demo payments</p>";
            echo "<p><a href='?'>← Back to Analysis</a></p>";
            
        } elseif ($_GET['action'] === 'safe_cleanup') {
            echo "<h2>Running Safe Cleanup</h2>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
            echo "<pre>";
            
            $deletedCount = $generator->safeCleanupDemoData(true);
            
            echo "</pre>";
            echo "</div>";
            
            echo "<p><strong>Result:</strong> Total {$deletedCount} demo records deleted safely</p>";
            echo "<p><a href='?'>← Back to Analysis</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 14px;
}

th, td {
    padding: 6px 10px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-warning { background-color: #ffc107; color: black; }

.btn:hover { opacity: 0.8; }
</style>

# Keyword Search Filter Fix

## Issue
The keyword search filter clears all events instead of filtering them. The calendar filter and keyword search are interfering with each other.

## Root Cause
The CalendarController was returning an empty array immediately when no calendars were selected (calendar_id parameter was empty), without checking if other filters like keyword search were present. This prevented keyword search from working when no specific calendars were selected.

## Solution
Modified the CalendarController logic in both `getEvents()` and `getUpcomingEvents()` methods to only return empty results when no calendars are selected AND no other filters are applied. This allows keyword search and other filters to work independently.

## Files Modified
- controllers/CalendarController.php - Fixed calendar filter logic to allow other filters to work
- models/CalendarModel.php - Added debug logging for keyword filter
- public/js/calendar-filters.js - Added debug logging for filter parameters
- README.md - Updated version and changelog
- config/config.php - Updated version number

## Changes Made
1. **CalendarController.php**: Modified calendar filter logic to check for other active filters before returning empty results
2. **Enhanced Debugging**: Added comprehensive logging to track filter parameters and SQL execution
3. **Version Update**: Updated to v3.35.57

## Testing
- Keyword search now works without requiring specific calendar selection
- Multiple filters can be combined (keyword + location, keyword + date range, etc.)
- Calendar selection still works as expected when combined with other filters

## Backup Date
2024-12-19
<?php
/**
 * This file contains the fixed indentation for the geocoding code in CalendarController.php
 */

// For createEvent method:
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'createEvent');

    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);

    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}

// For editEvent method:
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
# Google Maps API Troubleshooting Guide

## Common Google Maps Errors and Solutions

### 1. CORS Policy Errors
**Error**: `Access to XMLHttpRequest at 'https://maps.googleapis.com/maps/api/mapsjs/gen_204?csp_test=true' has been blocked by CORS policy`

**Solutions**:
- ✅ **Fixed**: Added CSP headers to `.htaccess`
- Verify your server supports `mod_headers`
- Check if CDN or proxy is interfering

### 2. API Key Issues
**Error**: `Google Maps API authentication failed`

**Solutions**:
1. **Check API Key Validity**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Navigate to APIs & Services > Credentials
   - Verify your API key is active

2. **Enable Required APIs**:
   - Maps JavaScript API
   - Places API (if using places features)
   - Geocoding API (if using geocoding)

3. **Check Domain Restrictions**:
   - In Google Cloud Console, edit your API key
   - Under "Application restrictions", add your domain:
     - `events.rowaneliterides.com`
     - `*.rowaneliterides.com` (for subdomains)

4. **Verify Billing**:
   - Google Maps requires a billing account
   - Check if you've exceeded free tier limits
   - Verify payment method is valid

### 3. Loading Timeout Issues
**Error**: Map doesn't load or takes too long

**Solutions**:
- ✅ **Fixed**: Added 10-second timeout with fallback
- Check network connectivity
- Verify API key quotas aren't exceeded

### 4. CSP Test Failures
**Error**: `gen_204?csp_test=true` requests failing

**Solutions**:
- ✅ **Fixed**: Updated CSP headers to allow Google Maps domains
- Ensure `connect-src` includes `https://maps.googleapis.com`
- Check if ad blockers are interfering

## Testing Your Google Maps Setup

### 1. Browser Console Checks
Open browser developer tools and check for:
```javascript
// Should not see these errors:
// - CORS policy errors
// - Authentication failures
// - Script loading errors

// Should see these success messages:
// - "Google Maps initialized successfully"
// - No red errors in console
```

### 2. API Key Validation
Test your API key directly:
```
https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=console.log
```

### 3. Network Tab Analysis
In browser dev tools, Network tab should show:
- ✅ Google Maps API script loads (200 status)
- ✅ Map tiles load properly
- ❌ No failed requests to googleapis.com

## Google Cloud Console Setup

### Required APIs to Enable:
1. **Maps JavaScript API** - For map display
2. **Places API** - For venue search (if used)
3. **Geocoding API** - For address to coordinates conversion

### API Key Configuration:
1. **Application Restrictions**: HTTP referrers
2. **Website Restrictions**: 
   - `events.rowaneliterides.com/*`
   - `*.rowaneliterides.com/*`
3. **API Restrictions**: Select only the APIs you need

### Billing Setup:
1. Link a valid payment method
2. Set up billing alerts
3. Monitor usage in the console

## Fallback Behavior

If Google Maps fails to load, the system will automatically:
1. Log the error to console
2. Switch to OpenStreetMap (free alternative)
3. Continue functioning without interruption

## Additional Security Considerations

### Content Security Policy (CSP)
The updated `.htaccess` includes:
```apache
Content-Security-Policy: 
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com https://maps.gstatic.com;
  connect-src 'self' https://maps.googleapis.com https://maps.gstatic.com;
  img-src 'self' data: https: http:;
```

### Domain Verification
Ensure your domain is properly configured in:
1. Google Cloud Console API restrictions
2. DNS settings (if using custom domain)
3. SSL certificate (HTTPS required for many APIs)

## Support Resources

- [Google Maps JavaScript API Documentation](https://developers.google.com/maps/documentation/javascript)
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Maps API Pricing](https://developers.google.com/maps/pricing)
- [CSP Generator Tool](https://report-uri.com/home/<USER>
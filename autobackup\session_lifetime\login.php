<?php
// Try multiple approaches to include the header
$headerIncluded = false;

// Approach 1: Using APPROOT constant
if (defined('APPROOT') && file_exists(APPROOT . '/views/includes/header.php')) {
    require APPROOT . '/views/includes/header.php';
    $headerIncluded = true;
}

// Approach 2: Using relative path from current file
if (!$headerIncluded && file_exists(dirname(dirname(__FILE__)) . '/includes/header.php')) {
    require dirname(dirname(__FILE__)) . '/includes/header.php';
    $headerIncluded = true;
}

// Approach 3: Using server document root
if (!$headerIncluded && isset($_SERVER['DOCUMENT_ROOT']) && file_exists($_SERVER['DOCUMENT_ROOT'] . '/views/includes/header.php')) {
    require $_SERVER['DOCUMENT_ROOT'] . '/views/includes/header.php';
    $headerIncluded = true;
}

// If header still not included, create a basic header
if (!$headerIncluded) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Events and Shows</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Events and Shows</a>
        </div>
    </nav>
    <main class="container py-4">
<?php
}
?>

<div class="row">
    <div class="col-md-6 mx-auto">
        <div class="card card-body bg-light mt-5">
            <h2>Login</h2>
            <p>Please fill in your credentials to log in</p>
            <form action="<?php echo BASE_URL; ?>/auth/login" method="post">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" name="email" class="form-control <?php echo (!empty($email_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $email; ?>">
                    <div class="invalid-feedback"><?php echo $email_err; ?></div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" name="password" class="form-control <?php echo (!empty($password_err)) ? 'is-invalid' : ''; ?>" value="<?php echo $password; ?>">
                    <div class="invalid-feedback"><?php echo $password_err; ?></div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" name="remember_me" class="form-check-input" id="remember_me" <?php echo $remember_me ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="remember_me">Remember me</label>
                </div>
                
                <div class="row mb-3">
                    <div class="col">
                        <input type="submit" value="Login" class="btn btn-primary">
                    </div>
                    <div class="col text-end">
                        <a href="<?php echo BASE_URL; ?>/auth/forgot_password" class="text-decoration-none">Forgot Password?</a>
                    </div>
                </div>
                
                <div class="text-center">
                    <p>- OR -</p>
                    <a href="<?php echo BASE_URL; ?>/auth/facebook.php" class="btn btn-primary w-100 mb-3">
                        <i class="fab fa-facebook-f me-2"></i> Login with Facebook
                    </a>
                    <p>Don't have an account? <a href="<?php echo BASE_URL; ?>/auth/register" class="text-decoration-none">Register</a></p>
                </div>
            </form>
        </div>
    </div>
</div>

<?php
// Try multiple approaches to include the footer
$footerIncluded = false;

// Approach 1: Using APPROOT constant
if (defined('APPROOT') && file_exists(APPROOT . '/views/includes/footer.php')) {
    require APPROOT . '/views/includes/footer.php';
    $footerIncluded = true;
}

// Approach 2: Using relative path from current file
if (!$footerIncluded && file_exists(dirname(dirname(__FILE__)) . '/includes/footer.php')) {
    require dirname(dirname(__FILE__)) . '/includes/footer.php';
    $footerIncluded = true;
}

// Approach 3: Using server document root
if (!$footerIncluded && isset($_SERVER['DOCUMENT_ROOT']) && file_exists($_SERVER['DOCUMENT_ROOT'] . '/views/includes/footer.php')) {
    require $_SERVER['DOCUMENT_ROOT'] . '/views/includes/footer.php';
    $footerIncluded = true;
}

// If footer still not included, create a basic footer
if (!$footerIncluded) {
?>
    </main>
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <p>&copy; <?php echo date('Y'); ?> Events and Shows. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
<?php
}
?>
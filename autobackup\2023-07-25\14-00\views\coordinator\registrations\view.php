<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/registrations/<?php echo $data['show']->id; ?>">Registrations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Registration #<?php echo $data['registration']->registration_number; ?></li>
                </ol>
            </nav>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registration Details</h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                            <i class="fas fa-trash"></i> Delete Registration
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Registration Information</h6>
                            <p><strong>Registration #:</strong> <?php echo $data['registration']->registration_number; ?></p>
                            <p><strong>Display #:</strong> 
                                <?php if (!empty($data['registration']->display_number)) : ?>
                                    <span class="badge bg-primary" style="font-size: 1.1em; padding: 8px 12px;"><?php echo $data['registration']->display_number; ?></span>
                                <?php else : ?>
                                    <span class="text-muted">Not assigned</span>
                                    <small class="d-block text-muted">(Will be set to category prefix + registration ID when edited)</small>
                                <?php endif; ?>
                            </p>
                            <p><strong>Status:</strong> 
                                <?php if($data['registration']->status == 'pending') : ?>
                                    <span class="badge bg-warning text-dark">Pending</span>
                                <?php elseif($data['registration']->status == 'approved') : ?>
                                    <span class="badge bg-success">Approved</span>
                                <?php elseif($data['registration']->status == 'rejected') : ?>
                                    <span class="badge bg-danger">Rejected</span>
                                <?php elseif($data['registration']->status == 'cancelled') : ?>
                                    <span class="badge bg-secondary">Cancelled</span>
                                <?php endif; ?>
                            </p>
                            <p><strong>Category:</strong> <?php echo $data['registration']->category_name; ?></p>
                            <p><strong>Registration Date:</strong> <?php echo date('F j, Y, g:i a', strtotime($data['registration']->created_at)); ?></p>
                            
                            <p><strong>Check-in Status:</strong>
                                <?php if (isset($data['registration']->checked_in) && $data['registration']->checked_in) : ?>
                                    <span class="badge bg-success">Checked In</span>
                                    <?php if (isset($data['registration']->check_in_time)) : ?>
                                        <small class="text-muted d-block">
                                            <?php echo date('F j, Y, g:i a', strtotime($data['registration']->check_in_time)); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <span class="badge bg-secondary">Not Checked In</span>
                                <?php endif; ?>
                            </p>
                            
                            <?php if (isset($data['registration']->payment_status) && ($data['registration']->payment_status == 'completed' || $data['registration']->payment_status == 'paid' || $data['registration']->payment_status == 'free')) : ?>
                                <?php if (!isset($data['registration']->checked_in) || !$data['registration']->checked_in) : ?>
                                    <form action="<?php echo URLROOT; ?>/coordinator/checkIn/<?php echo $data['registration']->id; ?>" method="post" class="mb-3">
                                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="fas fa-clipboard-check me-1"></i> Check In Vehicle
                                        </button>
                                    </form>
                                <?php else : ?>
                                    <form action="<?php echo URLROOT; ?>/coordinator/undoCheckIn/<?php echo $data['registration']->id; ?>" method="post" class="mb-3">
                                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                        <button type="submit" class="btn btn-warning btn-sm">
                                            <i class="fas fa-undo me-1"></i> Undo Check-in
                                        </button>
                                    </form>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <h6 class="border-bottom pb-2 mb-3 mt-4">Payment Information</h6>
                            <p><strong>Fee:</strong> $<?php echo number_format($data['registration']->fee, 2); ?></p>
                            <p><strong>Payment Status:</strong> 
                                <?php if(isset($data['registration']->payment_status)) : ?>
                                    <?php if($data['registration']->payment_status == 'pending') : ?>
                                        <span class="badge bg-warning text-dark">Pending</span>
                                    <?php elseif($data['registration']->payment_status == 'completed') : ?>
                                        <span class="badge bg-success">Paid</span>
                                    <?php elseif($data['registration']->payment_status == 'failed') : ?>
                                        <span class="badge bg-danger">Failed</span>
                                    <?php elseif($data['registration']->payment_status == 'refunded') : ?>
                                        <span class="badge bg-info">Refunded</span>
                                    <?php endif; ?>
                                <?php else : ?>
                                    <span class="badge bg-secondary">N/A</span>
                                <?php endif; ?>
                            </p>
                            <?php if(isset($data['registration']->payment_method_name)) : ?>
                                <p><strong>Payment Method:</strong> <?php echo $data['registration']->payment_method_name; ?></p>
                            <?php endif; ?>
                            <?php if(isset($data['registration']->payment_reference) && !empty($data['registration']->payment_reference)) : ?>
                                <p><strong>Payment Reference:</strong> <?php echo $data['registration']->payment_reference; ?></p>
                            <?php endif; ?>
                            <?php if(isset($data['registration']->payment_date) && !empty($data['registration']->payment_date)) : ?>
                                <p><strong>Payment Date:</strong> <?php echo date('F j, Y, g:i a', strtotime($data['registration']->payment_date)); ?></p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Vehicle Information</h6>
                            <p><strong>Make:</strong> <?php echo $data['registration']->make; ?></p>
                            <p><strong>Model:</strong> <?php echo $data['registration']->model; ?></p>
                            <p><strong>Year:</strong> <?php echo $data['registration']->year; ?></p>
                            <p><strong>Color:</strong> <?php echo $data['registration']->color; ?></p>
                            
                            <h6 class="border-bottom pb-2 mb-3 mt-4">Owner Information</h6>
                            <p><strong>Name:</strong> <?php echo $data['registration']->owner_name; ?></p>
                            <?php if(isset($data['registration']->owner_email)) : ?>
                                <p><strong>Email:</strong> <?php echo $data['registration']->owner_email; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if(!empty($data['images'])) : ?>
                        <h6 class="border-bottom pb-2 mb-3 mt-4">Vehicle Images</h6>
                        <div class="row">
                            <?php foreach($data['images'] as $image) : ?>
                                <div class="col-md-3 mb-3">
                                    <a href="<?php echo URLROOT . '/' . $image->image_path; ?>" target="_blank">
                                        <img src="<?php echo URLROOT . '/' . $image->image_path; ?>" class="img-thumbnail" alt="Vehicle Image">
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h6 class="border-bottom pb-2 mb-3">Update Registration Status</h6>
                            <form action="<?php echo URLROOT; ?>/coordinator/update_registration/<?php echo $data['registration']->id; ?>" method="post">
                                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category</label>
                                    <select class="form-select <?php echo (!empty($data['category_id_err'])) ? 'is-invalid' : ''; ?>" id="category_id" name="category_id">
                                        <?php foreach($data['categories'] as $category) : ?>
                                            <option value="<?php echo $category->id; ?>" <?php echo ($category->id == $data['registration']->category_id) ? 'selected' : ''; ?>>
                                                <?php echo $category->name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"><?php echo $data['category_id_err'] ?? ''; ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="display_number" class="form-label">Display Number</label>
                                    <div class="input-group">
                                        <select id="prefix" class="form-select" style="max-width: 100px;">
                                            <?php foreach ($data['categories'] as $category): ?>
                                                <?php 
                                                    // Clean the category name - remove parentheses and special characters
                                                    $cleanName = preg_replace('/\([^)]*\)/', '', $category->name);
                                                    $cleanName = trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $cleanName));
                                                    
                                                    // Get words from the cleaned name
                                                    $words = explode(' ', $cleanName);
                                                    $words = array_filter($words);
                                                    $prefix = '';
                                                    
                                                    // Handle different category naming patterns
                                                    if (count($words) == 0) {
                                                        $prefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $category->name), 0, 2));
                                                    }
                                                    else if (count($words) == 1) {
                                                        $prefix = strtoupper(substr($words[array_key_first($words)], 0, 2));
                                                    }
                                                    else if (count($words) == 2) {
                                                        $prefix = strtoupper(
                                                            substr($words[array_key_first($words)], 0, 1) . 
                                                            substr($words[array_key_first($words) + 1], 0, 1)
                                                        );
                                                    }
                                                    else {
                                                        if (strlen($words[array_key_first($words)]) <= 3) {
                                                            $prefix = strtoupper(
                                                                substr($words[array_key_first($words)], 0, 1) . 
                                                                substr($words[array_key_first($words) + 1], 0, 1)
                                                            );
                                                        } else {
                                                            $prefix = strtoupper(
                                                                substr($words[array_key_first($words)], 0, 1) . 
                                                                substr($words[array_key_last($words)], 0, 1)
                                                            );
                                                        }
                                                    }
                                                    
                                                    // Special case handling for common categories
                                                    $lowerName = strtolower($category->name);
                                                    if (strpos($lowerName, 'classic') !== false) {
                                                        $prefix = 'CL';
                                                    } else if (strpos($lowerName, 'muscle') !== false) {
                                                        $prefix = 'MU';
                                                    } else if (strpos($lowerName, 'truck') !== false || strpos($lowerName, 'suv') !== false) {
                                                        $prefix = 'TR';
                                                    } else if (strpos($lowerName, 'motorcycle') !== false || strpos($lowerName, 'bike') !== false) {
                                                        $prefix = 'MC';
                                                    } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'asian') !== false) {
                                                        $prefix = 'AI';
                                                    } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'european') !== false) {
                                                        $prefix = 'EI';
                                                    } else if (strpos($lowerName, 'modern') !== false) {
                                                        $prefix = 'MO';
                                                    } else if (strpos($lowerName, 'custom') !== false || strpos($lowerName, 'modified') !== false) {
                                                        $prefix = 'CM';
                                                    } else if (strpos($lowerName, 'contemporary') !== false) {
                                                        $prefix = 'CT';
                                                    } else if (strpos($lowerName, 'antique') !== false) {
                                                        $prefix = 'AN';
                                                    } else if (strpos($lowerName, 'vintage') !== false) {
                                                        $prefix = 'VN';
                                                    } else if (strpos($lowerName, 'sport') !== false) {
                                                        $prefix = 'SP';
                                                    } else if (strpos($lowerName, 'luxury') !== false) {
                                                        $prefix = 'LX';
                                                    } else if (strpos($lowerName, 'electric') !== false || strpos($lowerName, 'ev') !== false) {
                                                        $prefix = 'EV';
                                                    } else if (strpos($lowerName, 'hybrid') !== false) {
                                                        $prefix = 'HY';
                                                    } else if (strpos($lowerName, 'convertible') !== false) {
                                                        $prefix = 'CV';
                                                    } else if (strpos($lowerName, 'coupe') !== false) {
                                                        $prefix = 'CP';
                                                    } else if (strpos($lowerName, 'sedan') !== false) {
                                                        $prefix = 'SD';
                                                    } else if (strpos($lowerName, 'race') !== false || strpos($lowerName, 'racing') !== false) {
                                                        $prefix = 'RC';
                                                    } else if (strpos($lowerName, 'off') !== false && strpos($lowerName, 'road') !== false) {
                                                        $prefix = 'OR';
                                                    }
                                                    
                                                    // For custom categories, ensure we have a meaningful 2-letter prefix
                                                    if (strlen($prefix) != 2) {
                                                        // If we have a single letter, add the next most significant letter
                                                        if (strlen($prefix) == 1) {
                                                            // Try to find a second significant letter
                                                            if (count($words) > 1) {
                                                                // Use the first letter of the second word
                                                                $prefix .= strtoupper(substr($words[array_key_first($words) + 1], 0, 1));
                                                            } else if (strlen($words[array_key_first($words)]) > 1) {
                                                                // Use the second letter of the first word
                                                                $prefix .= strtoupper(substr($words[array_key_first($words)], 1, 1));
                                                            } else {
                                                                // Fallback: add X
                                                                $prefix .= 'X';
                                                            }
                                                        } else if (strlen($prefix) > 2) {
                                                            // If we have more than 2 letters, truncate to 2
                                                            $prefix = substr($prefix, 0, 2);
                                                        } else {
                                                            // If we somehow have no prefix, use the first two letters of the category name
                                                            // or the first letter + X if the name is only one character
                                                            $categoryName = preg_replace('/[^a-zA-Z0-9]/', '', $category->name);
                                                            if (strlen($categoryName) > 1) {
                                                                $prefix = strtoupper(substr($categoryName, 0, 2));
                                                            } else if (strlen($categoryName) == 1) {
                                                                $prefix = strtoupper($categoryName) . 'X';
                                                            } else {
                                                                $prefix = 'XX'; // Last resort fallback
                                                            }
                                                        }
                                                    }
                                                    
                                                    $prefix .= '-';
                                                ?>
                                                <option value="<?php echo $prefix; ?>" <?php echo ($category->id == $data['registration']->category_id) ? 'selected' : ''; ?>>
                                                    <?php echo $prefix; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="text" name="display_number" id="display_number" class="form-control" 
                                               value="<?php echo !empty($data['registration']->display_number) ? $data['registration']->display_number : $data['registration']->id; ?>">
                                        <button type="button" class="btn btn-outline-secondary" id="resetDisplayNumber">Reset</button>
                                    </div>
                                    <div class="form-text">
                                        Display number for the vehicle at the show. Format: [Category Prefix]-[Number]<br>
                                        <small class="text-muted">The prefix is automatically generated from the category name using a smart algorithm:
                                        <ul class="mt-1 mb-0">
                                            <li>"Classic Cars" → "CL-" (special case for classics)</li>
                                            <li>"Muscle Cars" → "MU-" (special case for muscle cars)</li>
                                            <li>"Trucks & SUVs" → "TR-" (special case for trucks)</li>
                                            <li>"Motorcycles" → "MC-" (special case for motorcycles)</li>
                                            <li>"Asian Imports" → "AI-" (special case for imports)</li>
                                            <li>"European Imports" → "EI-" (special case for imports)</li>
                                            <li>"Contemporary" → "CT-" (special case)</li>
                                            <li>"Antique" → "AN-", "Vintage" → "VN-", "Sports" → "SP-", etc.</li>
                                            <li>Custom categories will always get a meaningful 2-letter prefix</li>
                                        </ul>
                                        </small>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select <?php echo (!empty($data['status_err'])) ? 'is-invalid' : ''; ?>" id="status" name="status">
                                        <option value="pending" <?php echo ($data['registration']->status == 'pending') ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo ($data['registration']->status == 'approved') ? 'selected' : ''; ?>>Approved</option>
                                        <option value="rejected" <?php echo ($data['registration']->status == 'rejected') ? 'selected' : ''; ?>>Rejected</option>
                                        <option value="cancelled" <?php echo ($data['registration']->status == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                    <div class="invalid-feedback"><?php echo $data['status_err'] ?? ''; ?></div>
                                </div>
                                
                                <script>
                                    document.getElementById('resetDisplayNumber').addEventListener('click', function() {
                                        const prefix = document.getElementById('prefix').value;
                                        document.getElementById('display_number').value = prefix + '<?php echo $data['registration']->id; ?>';
                                    });
                                    
                                    document.getElementById('prefix').addEventListener('change', function() {
                                        // Extract the current number part
                                        const currentValue = document.getElementById('display_number').value;
                                        const numberPart = currentValue.split('-').pop();
                                        
                                        // Set the new value with the selected prefix
                                        document.getElementById('display_number').value = this.value + numberPart;
                                    });
                                </script>
                                
                                <button type="submit" class="btn btn-primary">Update Status</button>
                            </form>
                        </div>
                        
                        <?php if(isset($data['registration']->payment_status)) : ?>
                            <div class="col-md-6">
                                <h6 class="border-bottom pb-2 mb-3">Update Payment Status</h6>
                                <form action="<?php echo URLROOT; ?>/coordinator/update_payment/<?php echo $data['registration']->id; ?>" method="post">
                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                    
                                    <div class="mb-3">
                                        <label for="payment_status" class="form-label">Payment Status</label>
                                        <select class="form-select <?php echo (!empty($data['payment_status_err'])) ? 'is-invalid' : ''; ?>" id="payment_status" name="payment_status">
                                            <option value="pending" <?php echo ($data['registration']->payment_status == 'pending') ? 'selected' : ''; ?>>Pending</option>
                                            <option value="completed" <?php echo ($data['registration']->payment_status == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                            <option value="failed" <?php echo ($data['registration']->payment_status == 'failed') ? 'selected' : ''; ?>>Failed</option>
                                            <option value="refunded" <?php echo ($data['registration']->payment_status == 'refunded') ? 'selected' : ''; ?>>Refunded</option>
                                        </select>
                                        <div class="invalid-feedback"><?php echo $data['payment_status_err'] ?? ''; ?></div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="payment_reference" class="form-label">Payment Reference</label>
                                        <input type="text" class="form-control" id="payment_reference" name="payment_reference" value="<?php echo $data['registration']->payment_reference ?? ''; ?>">
                                    </div>
                                    
                                    <button type="submit" class="btn btn-primary">Update Payment</button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this registration?</p>
                <p><strong>Registration #:</strong> <?php echo $data['registration']->registration_number; ?></p>
                <p><strong>Vehicle:</strong> <?php echo $data['registration']->year . ' ' . $data['registration']->make . ' ' . $data['registration']->model; ?></p>
                <p><strong>Owner:</strong> <?php echo $data['registration']->owner_name; ?></p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="<?php echo URLROOT; ?>/coordinator/delete_registration/<?php echo $data['registration']->id; ?>" method="post">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    <button type="submit" class="btn btn-danger">Delete Registration</button>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
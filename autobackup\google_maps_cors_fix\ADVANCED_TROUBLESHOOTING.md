# Advanced Google Maps CORS Troubleshooting

## Current Error Analysis

The error you're experiencing:
```
Access to XMLHttpRequest at 'https://maps.googleapis.com/$rpc/google.internal.maps.mapsjs.v1.MapsJsInternalService/GetViewportInfo' from origin 'https://events.rowaneliterides.com' has been blocked by CORS policy
```

This is a **Google Maps internal RPC service CORS error**, which is different from the standard API loading errors.

## Root Cause

This error occurs when:
1. Google Maps API makes internal RPC calls to fetch viewport information
2. Your server's CORS policy or CSP headers are too restrictive
3. Google's servers don't return proper CORS headers for internal services
4. There may be API key domain restriction issues

## Solutions to Try (In Order)

### 1. ✅ CSP Headers Temporarily Disabled
- We've temporarily commented out the CSP headers in `.htaccess`
- This should eliminate CSP-related blocking
- **Test this first** - refresh your page and check if the error persists

### 2. Check Google Cloud Console Settings

#### API Key Domain Restrictions:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to APIs & Services > Credentials
3. Click on your API key: `AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM`
4. Under "Application restrictions":
   - Select "HTTP referrers (web sites)"
   - Add these referrers:
     - `https://events.rowaneliterides.com/*`
     - `https://*.rowaneliterides.com/*`
     - `events.rowaneliterides.com/*`
     - `*.rowaneliterides.com/*`

#### Required APIs to Enable:
- ✅ Maps JavaScript API
- ✅ Maps Static API (recommended)
- ✅ Places API (if using places features)
- ✅ Geocoding API (if using geocoding)

### 3. Billing Account Verification
1. Ensure billing is enabled on your Google Cloud project
2. Check if you've exceeded free tier limits
3. Verify payment method is valid and current

### 4. Alternative Google Maps Loading Method

If the issue persists, try loading Google Maps with different parameters:

```javascript
// Instead of:
script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap`;

// Try:
script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap&v=weekly`;
```

### 5. Test with Minimal Google Maps Implementation

Create a simple test page with minimal Google Maps code to isolate the issue:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
</head>
<body>
    <div id="map" style="height: 400px; width: 100%;"></div>
    
    <script>
        function initMap() {
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 4,
                center: { lat: 39.8283, lng: -98.5795 },
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM&callback=initMap"></script>
</body>
</html>
```

### 6. Server-Side Proxy Solution

If CORS continues to be an issue, implement a server-side proxy:

```php
<?php
// Create: /api/maps-proxy.php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$url = $_GET['url'] ?? '';
if (strpos($url, 'maps.googleapis.com') === false) {
    http_response_code(400);
    exit('Invalid URL');
}

$response = file_get_contents($url);
echo $response;
?>
```

### 7. Check for Conflicting Extensions/Software

- Disable browser ad blockers temporarily
- Check if any browser extensions are blocking requests
- Test in incognito/private browsing mode
- Try different browsers (Chrome, Firefox, Safari)

### 8. Network-Level Debugging

1. Open browser Developer Tools
2. Go to Network tab
3. Filter by "googleapis.com"
4. Look for failed requests and their response headers
5. Check if any requests are being blocked at the network level

## Expected Behavior After Fixes

After implementing the solutions, you should see:
- ✅ No CORS errors in browser console
- ✅ Google Maps loads and displays properly
- ✅ Map interactions work (zoom, pan, markers)
- ✅ Network tab shows successful requests to googleapis.com

## If Issues Persist

1. **Switch to OpenStreetMap temporarily**:
   - Go to Calendar > Map Settings
   - Change provider to "OpenStreetMap (Free)"
   - This will bypass Google Maps entirely

2. **Contact Google Maps Support**:
   - This may be a Google-side issue with their RPC service
   - Check [Google Maps API Status Page](https://status.cloud.google.com/)

3. **Alternative Map Providers**:
   - Mapbox (50,000 free loads/month)
   - HERE Maps (250,000 free transactions/month)
   - OpenStreetMap (completely free)

## Testing Checklist

- [ ] CSP headers disabled - test map loading
- [ ] Google Cloud Console API key settings verified
- [ ] Billing account active and valid
- [ ] Test with minimal HTML page
- [ ] Test in different browsers
- [ ] Test with ad blockers disabled
- [ ] Network tab shows no blocked requests
- [ ] Try alternative map providers if needed

## Current Status

- ✅ CSP headers temporarily disabled for testing
- ✅ Enhanced error handling with fallback to OpenStreetMap
- ✅ Debug logging enabled
- 🔄 **Next Step**: Test with CSP disabled and verify Google Cloud Console settings
<?php 
// If not an AJAX request, include the header
if (!$is_ajax) {
    require APPROOT . '/views/includes/header.php';
} else {
    // For AJAX requests, add necessary styles and meta tags directly
    echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Selector</title>
    <link rel="stylesheet" href="' . BASE_URL . '/assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="' . BASE_URL . '/assets/css/fontawesome.min.css">
    <link rel="stylesheet" href="' . BASE_URL . '/assets/css/styles.css">
    <style>
        body { 
            padding: 0; 
            margin: 0; 
            background-color: #f8f9fa;
        }
        body.iframe-mode {
            overflow-x: hidden;
        }
        .iframe-mode .container-fluid {
            padding-top: 0.5rem !important;
            padding-bottom: 0.5rem !important;
        }
        .iframe-mode .card-img-container {
            height: 120px;
        }
    </style>
</head>
<body class="iframe-mode">';
}
?>

<div class="container-fluid p-3<?php echo $is_ajax ? ' bg-white' : ''; ?>">
    <div class="row mb-3">
        <div class="col-md-6">
            <h4 class="mb-0"><?php echo $title; ?></h4>
        </div>
        <div class="col-md-6 text-end">
            <button type="button" class="btn btn-primary" id="upload-new-btn">
                <i class="fas fa-upload me-2"></i> Upload New Image
            </button>
        </div>
    </div>
    
    <?php if (empty($images)): ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> No images found. Click "Upload New Image" to add images.
    </div>
    <?php else: ?>
    <div class="row image-grid">
        <?php foreach ($images as $image): ?>
        <div class="col-md-3 col-sm-4 col-6 mb-3">
            <div class="card image-card h-100">
                <div class="card-img-container">
                    <?php 
                    // Ensure image path is properly formatted
                    $imagePath = $image->path ?? '';
                    $imageUrl = rtrim(BASE_URL, '/') . '/' . ltrim($imagePath, '/');
                    $imageTitle = $image->title ?? '';
                    ?>
                    <img src="<?php echo $imageUrl; ?>" 
                         class="card-img-top" 
                         alt="<?php echo htmlspecialchars($imageTitle); ?>"
                         data-image-id="<?php echo $image->id; ?>"
                         data-image-path="<?php echo $imagePath; ?>"
                         data-image-title="<?php echo htmlspecialchars($imageTitle); ?>"
                         onerror="this.onerror=null; this.src='<?php echo BASE_URL; ?>/assets/img/placeholder.png'; console.log('Image failed to load: <?php echo addslashes($imageUrl); ?>')">
                </div>
                <div class="card-body">
                    <h6 class="card-title text-truncate" title="<?php echo htmlspecialchars($imageTitle); ?>">
                        <?php echo htmlspecialchars($imageTitle); ?>
                    </h6>
                    <p class="card-text small text-muted">
                        <?php echo date('M j, Y', strtotime($image->created_at)); ?>
                    </p>
                    <button type="button" class="btn btn-sm btn-primary select-image-btn" 
                            data-image-id="<?php echo $image->id; ?>"
                            data-image-path="<?php echo $imagePath; ?>"
                            data-image-title="<?php echo htmlspecialchars($imageTitle); ?>">
                        <i class="fas fa-check me-1"></i> Select
                    </button>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <!-- Upload Form Modal -->
    <div class="modal fade" id="upload-modal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Upload New Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="upload-form" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="image-title" class="form-label">Image Title</label>
                            <input type="text" class="form-control" id="image-title" name="title" required>
                        </div>
                        <div class="mb-3">
                            <label for="image-file" class="form-label">Select Image</label>
                            <input type="file" class="form-control" id="image-file" name="image" accept="image/*" required>
                        </div>
                        <div class="mb-3">
                            <label for="image-description" class="form-label">Description (Optional)</label>
                            <textarea class="form-control" id="image-description" name="description" rows="3"></textarea>
                        </div>
                        <input type="hidden" name="entity_type" value="form">
                        <input type="hidden" name="entity_id" value="<?php echo $show_id ?? 0; ?>">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="upload-submit-btn">Upload</button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.image-card {
    transition: all 0.2s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-color: #ddd;
}

.card-img-container {
    height: 150px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.card-img-container img {
    max-height: 100%;
    max-width: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    display: block;
    margin: 0 auto;
}

.image-card.selected {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}
</style>

<script>
// Debug information to console
console.log('Image selector loaded with ' + <?php echo count($images); ?> + ' images');

<?php if (!empty($images)): ?>
console.log('First image path: <?php echo addslashes($images[0]->path ?? ''); ?>');
console.log('BASE_URL: <?php echo addslashes(BASE_URL); ?>');
<?php endif; ?>

// Notify parent that the iframe is loaded
if (window.parent && window.parent !== window) {
    window.parent.postMessage({ type: 'iframe-loaded', source: 'image-selector' }, '*');
}

document.addEventListener('DOMContentLoaded', function() {
    // Handle image selection
    const selectButtons = document.querySelectorAll('.select-image-btn');
    selectButtons.forEach(button => {
        button.addEventListener('click', function() {
            const imageId = this.dataset.imageId;
            const imagePath = this.dataset.imagePath;
            const imageTitle = this.dataset.imageTitle;
            
            // Format the URL properly
            const baseUrl = '<?php echo rtrim(BASE_URL, '/'); ?>';
            const formattedPath = imagePath.startsWith('/') ? imagePath.substring(1) : imagePath;
            const fullUrl = baseUrl + '/' + formattedPath;
            
            // Send the selected image back to the parent window
            if (window.opener && !window.opener.closed) {
                window.opener.receiveSelectedImage({
                    id: imageId,
                    path: imagePath,
                    title: imageTitle,
                    url: fullUrl
                });
                window.close();
            } else {
                // For modal implementation
                window.parent.postMessage({
                    type: 'image-selected',
                    image: {
                        id: imageId,
                        path: imagePath,
                        title: imageTitle,
                        url: fullUrl
                    }
                }, '*');
            }
        });
    });
    
    // Handle image card click (same as clicking the select button)
    const imageCards = document.querySelectorAll('.image-card');
    imageCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if the select button itself was clicked
            if (!e.target.classList.contains('select-image-btn') && 
                !e.target.closest('.select-image-btn')) {
                const selectBtn = this.querySelector('.select-image-btn');
                if (selectBtn) {
                    selectBtn.click();
                }
            }
        });
    });
    
    // Handle upload button click
    const uploadBtn = document.getElementById('upload-new-btn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            const uploadModal = new bootstrap.Modal(document.getElementById('upload-modal'));
            uploadModal.show();
        });
    }
    
    // Handle form submission
    const uploadSubmitBtn = document.getElementById('upload-submit-btn');
    if (uploadSubmitBtn) {
        uploadSubmitBtn.addEventListener('click', function() {
            const form = document.getElementById('upload-form');
            const formData = new FormData(form);
            
            // Show loading state
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Uploading...';
            
            fetch('<?php echo BASE_URL; ?>/image_editor/upload_ajax', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show the new image
                    window.location.reload();
                } else {
                    alert('Upload failed: ' + data.message);
                    this.disabled = false;
                    this.innerHTML = 'Upload';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred during upload. Please try again.');
                this.disabled = false;
                this.innerHTML = 'Upload';
            });
        });
    }
    
    // Add error handling for images
    const images = document.querySelectorAll('.card-img-container img');
    images.forEach(img => {
        img.onerror = function() {
            // Replace with a placeholder if image fails to load
            this.src = '<?php echo BASE_URL; ?>/assets/img/placeholder.png';
            this.alt = 'Image not found';
        };
    });
});
</script>

<?php 
// If not an AJAX request, include the footer
if (!$is_ajax) {
    require APPROOT . '/views/includes/footer.php';
} else {
    // For AJAX requests, add necessary scripts directly
    echo '<script src="' . BASE_URL . '/assets/js/bootstrap.bundle.min.js"></script>
</body>
</html>';
}
?>
<?php
/**
 * Install Camera Banners Table
 * Run this script to create the camera_banners table and default settings
 */

require_once 'config/config.php';
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "Installing camera banners table...\n";
    
    // Create camera_banners table
    $create_table_sql = "
    CREATE TABLE IF NOT EXISTS camera_banners (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type ENUM('text', 'image') NOT NULL DEFAULT 'text',
        text TEXT NULL,
        image_path VARCHAR(500) NULL,
        alt_text VARCHAR(255) NULL,
        active BOOLEAN DEFAULT TRUE,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_active (active),
        INDEX idx_sort_order (sort_order),
        INDEX idx_type (type)
    )";
    
    $db->exec($create_table_sql);
    echo "✓ Camera banners table created successfully\n";
    
    // Check if we have any banners
    $count_query = "SELECT COUNT(*) as count FROM camera_banners";
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute();
    $count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        // Insert default banners
        $insert_banners_sql = "
        INSERT INTO camera_banners (type, text, active, sort_order) VALUES 
        ('text', 'Welcome to our Event Platform!', TRUE, 1),
        ('text', 'Check out our upcoming events!', TRUE, 2),
        ('text', 'Register your vehicle today!', TRUE, 3)";
        
        $db->exec($insert_banners_sql);
        echo "✓ Default banners inserted\n";
    } else {
        echo "✓ Found $count existing banners\n";
    }
    
    // Add camera banner delay setting
    $setting_sql = "
    INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, description) VALUES 
    ('camera_banner_delay', '5000', 'Banner rotation delay in milliseconds for camera modals', 'media', 'Camera banner delay')
    ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    setting_description = VALUES(setting_description),
    setting_group = VALUES(setting_group),
    description = VALUES(description)";
    
    $db->exec($setting_sql);
    echo "✓ Camera banner delay setting configured\n";
    
    // Create uploads/banners directory if it doesn't exist
    $banner_dir = 'uploads/banners';
    if (!is_dir($banner_dir)) {
        mkdir($banner_dir, 0755, true);
        echo "✓ Created uploads/banners directory\n";
    } else {
        echo "✓ uploads/banners directory exists\n";
    }
    
    // Create .htaccess for banners directory
    $htaccess_content = "# Protect banner uploads\n";
    $htaccess_content .= "Options -Indexes\n";
    $htaccess_content .= "# Allow image files\n";
    $htaccess_content .= "<FilesMatch \"\\.(jpg|jpeg|png|gif|webp)$\">\n";
    $htaccess_content .= "    Order Allow,Deny\n";
    $htaccess_content .= "    Allow from all\n";
    $htaccess_content .= "</FilesMatch>\n";
    
    file_put_contents($banner_dir . '/.htaccess', $htaccess_content);
    echo "✓ Created .htaccess for banners directory\n";
    
    echo "\n🎉 Camera banners installation completed successfully!\n";
    echo "You can now access the camera banner management at /admin/camera_banners\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "Debug info: " . $e->getFile() . " line " . $e->getLine() . "\n";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "Debug info: " . $e->getFile() . " line " . $e->getLine() . "\n";
    }
}
?>
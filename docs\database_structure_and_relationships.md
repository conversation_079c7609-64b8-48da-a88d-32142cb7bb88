# Database Structure and Relationships Documentation

## Overview
This document provides a comprehensive overview of the Events and Shows Management System database structure, table relationships, and foreign key constraints to improve understanding and maintainability.

## Core Entity Tables

### 1. Users (`users`)
**Primary Entity**: System users with different roles
- **Primary Key**: `id`
- **Key Fields**: `name`, `email`, `password`, `role`
- **Roles**: admin, coordinator, judge, user
- **Relationships**: Central to most other tables

### 2. Shows (`shows`)
**Primary Entity**: Car shows and events
- **Primary Key**: `id`
- **Key Fields**: `name`, `description`, `location`, `start_date`, `end_date`
- **Relationships**: Core entity for registrations, payments, judging

### 3. Vehicles (`vehicles`)
**Primary Entity**: User-owned vehicles
- **Primary Key**: `id`
- **Key Fields**: `owner_id`, `make`, `model`, `year`
- **Relationships**: Owned by users, registered in shows

### 4. Registrations (`registrations`)
**Primary Entity**: Vehicle registrations for shows
- **Primary Key**: `id`
- **Key Fields**: `show_id`, `vehicle_id`, `owner_id`
- **Relationships**: Links vehicles to shows

## Calendar System Tables

### 5. Calendars (`calendars`)
**Primary Entity**: Calendar containers
- **Primary Key**: `id`
- **Key Fields**: `name`, `description`, `owner_id`
- **Relationships**: Contains events, owned by users

### 6. Calendar Events (`calendar_events`)
**Primary Entity**: Calendar events (can link to shows)
- **Primary Key**: `id`
- **Key Fields**: `calendar_id`, `title`, `start_date`, `end_date`, `show_id`
- **Relationships**: Belongs to calendar, may reference show

### 7. Calendar Clubs (`calendar_clubs`)
**Primary Entity**: Car clubs and organizations
- **Primary Key**: `id`
- **Key Fields**: `name`, `owner_id`, `is_verified`
- **Relationships**: Has members, can organize events

## Supporting Tables

### 8. Payments (`payments`)
**Transaction Entity**: Payment records
- **Primary Key**: `id`
- **Key Fields**: `user_id`, `amount`, `payment_status`
- **Relationships**: Links to users and registrations

### 9. Categories (`categories`)
**Classification Entity**: Vehicle/show categories
- **Primary Key**: `id`
- **Key Fields**: `show_id`, `name`
- **Relationships**: Belongs to shows

### 10. Age Weights (`age_weights`)
**Scoring Entity**: Age-based scoring multipliers
- **Primary Key**: `id`
- **Key Fields**: `show_id`, `min_age`, `max_age`, `multiplier`
- **Relationships**: Belongs to shows

## Relationship Mapping

### User-Centric Relationships
```
users (1) ←→ (∞) vehicles [owner_id]
users (1) ←→ (∞) registrations [owner_id]
users (1) ←→ (∞) payments [user_id]
users (1) ←→ (∞) calendars [owner_id]
users (1) ←→ (∞) calendar_events [created_by]
users (1) ←→ (∞) calendar_clubs [owner_id]
users (1) ←→ (∞) calendar_club_members [user_id]
```

### Show-Centric Relationships
```
shows (1) ←→ (∞) registrations [show_id]
shows (1) ←→ (∞) categories [show_id]
shows (1) ←→ (∞) age_weights [show_id]
shows (1) ←→ (∞) awards [show_id]
shows (1) ←→ (∞) judging_metrics [show_id]
shows (1) ←→ (∞) calendar_events [show_id] (optional link)
```

### Registration Flow
```
users → vehicles → registrations ← shows
  ↓
payments
  ↓
judge_scores
```

### Calendar System Flow
```
users → calendars → calendar_events
users → calendar_clubs → calendar_club_members
calendar_events ←→ calendar_event_clubs
calendar_events ←→ shows (optional)
```

## Foreign Key Constraints

### Critical Relationships
1. **calendars.owner_id** → users.id (SET NULL)
2. **calendar_events.calendar_id** → calendars.id (CASCADE)
3. **calendar_events.show_id** → shows.id (SET NULL)
4. **calendar_events.created_by** → users.id (SET NULL)
5. **registrations.show_id** → shows.id (CASCADE)
6. **registrations.vehicle_id** → vehicles.id (CASCADE)
7. **registrations.owner_id** → users.id (CASCADE)
8. **vehicles.owner_id** → users.id (CASCADE)
9. **payments.user_id** → users.id (CASCADE)

### Cascade vs Set NULL Strategy
- **CASCADE**: Used when child records should be deleted with parent
- **SET NULL**: Used when child records should remain but lose reference

## Data Integrity Recommendations

### 1. Missing Foreign Keys to Add
```sql
-- Add missing foreign key constraints
ALTER TABLE `age_weights` 
ADD CONSTRAINT `fk_age_weights_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

ALTER TABLE `categories` 
ADD CONSTRAINT `fk_categories_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_category` 
FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL;

ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_registration` 
FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE SET NULL;
```

### 2. Indexes for Performance
```sql
-- Add indexes for frequently queried relationships
CREATE INDEX `idx_registrations_show_id` ON `registrations` (`show_id`);
CREATE INDEX `idx_registrations_vehicle_id` ON `registrations` (`vehicle_id`);
CREATE INDEX `idx_registrations_owner_id` ON `registrations` (`owner_id`);
CREATE INDEX `idx_vehicles_owner_id` ON `vehicles` (`owner_id`);
CREATE INDEX `idx_payments_user_id` ON `payments` (`user_id`);
CREATE INDEX `idx_calendar_events_calendar_id` ON `calendar_events` (`calendar_id`);
CREATE INDEX `idx_calendar_events_show_id` ON `calendar_events` (`show_id`);
```

### 3. Data Validation Constraints
```sql
-- Add check constraints for data validation
ALTER TABLE `age_weights` 
ADD CONSTRAINT `chk_age_weights_range` 
CHECK (`min_age` <= `max_age`);

ALTER TABLE `age_weights` 
ADD CONSTRAINT `chk_age_weights_multiplier` 
CHECK (`multiplier` > 0);

ALTER TABLE `calendar_events` 
ADD CONSTRAINT `chk_calendar_events_dates` 
CHECK (`start_date` <= `end_date`);
```

## Query Optimization Tips

### 1. Common Query Patterns
```sql
-- Get all registrations for a show with vehicle and owner details
SELECT r.*, v.make, v.model, v.year, u.name as owner_name
FROM registrations r
JOIN vehicles v ON r.vehicle_id = v.id
JOIN users u ON r.owner_id = u.id
WHERE r.show_id = ?;

-- Get all events for a calendar with show details
SELECT ce.*, s.name as show_name, s.description as show_description
FROM calendar_events ce
LEFT JOIN shows s ON ce.show_id = s.id
WHERE ce.calendar_id = ?;
```

### 2. Performance Considerations
- Use appropriate indexes on foreign key columns
- Consider composite indexes for multi-column WHERE clauses
- Use EXPLAIN to analyze query performance
- Implement proper pagination for large result sets

## Maintenance Procedures

### 1. Regular Integrity Checks
```sql
-- Check for orphaned records
SELECT 'vehicles' as table_name, COUNT(*) as orphaned_count
FROM vehicles v
LEFT JOIN users u ON v.owner_id = u.id
WHERE u.id IS NULL

UNION ALL

SELECT 'registrations', COUNT(*)
FROM registrations r
LEFT JOIN shows s ON r.show_id = s.id
WHERE s.id IS NULL;
```

### 2. Cleanup Procedures
```sql
-- Clean up orphaned records (run with caution)
DELETE FROM vehicles 
WHERE owner_id NOT IN (SELECT id FROM users);

DELETE FROM registrations 
WHERE show_id NOT IN (SELECT id FROM shows);
```

## Schema Evolution Guidelines

### 1. Adding New Tables
- Always include `created_at` and `updated_at` timestamps
- Use appropriate foreign key constraints
- Add necessary indexes from the start
- Document relationships in this file

### 2. Modifying Existing Tables
- Use migrations for schema changes
- Backup data before structural changes
- Test foreign key constraints after modifications
- Update this documentation

### 3. Deprecating Tables/Columns
- Mark as deprecated in code comments
- Plan migration path for existing data
- Remove foreign key constraints last
- Archive data before deletion

## Troubleshooting Common Issues

### 1. Foreign Key Constraint Violations
- Check for orphaned records before adding constraints
- Verify data types match between referenced columns
- Ensure referenced columns have indexes

### 2. Performance Issues
- Check for missing indexes on foreign key columns
- Analyze slow queries with EXPLAIN
- Consider denormalization for frequently accessed data

### 3. Data Inconsistencies
- Implement application-level validation
- Use database constraints where possible
- Regular integrity checks and cleanup procedures

---

**Last Updated**: $(date)
**Version**: 1.0.0
**Maintainer**: Database Administrator

This documentation should be updated whenever schema changes are made to maintain accuracy and usefulness.
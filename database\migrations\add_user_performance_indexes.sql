-- User Performance Indexes Migration
-- Add indexes to users table for improved performance with large datasets
-- These indexes will significantly improve search, filtering, and sorting operations

-- Check existing indexes first
SELECT 'Checking existing indexes on users table...' as status;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Add index on name for search operations
SELECT 'Adding index on name column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);

-- Add index on email for search operations (if not already unique)
SELECT 'Adding index on email column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_email_search ON users(email);

-- Add index on role for filtering
SELECT 'Adding index on role column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);

-- Add index on status for filtering
SELECT 'Adding index on status column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);

-- Add index on created_at for sorting
SELECT 'Adding index on created_at column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Add index on last_login for sorting
SELECT 'Adding index on last_login column...' as status;
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

-- Add composite index for common search combinations
SELECT 'Adding composite index for role and status...' as status;
CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status);

-- Add composite index for name and email searches
SELECT 'Adding composite index for name and email...' as status;
CREATE INDEX IF NOT EXISTS idx_users_name_email ON users(name, email);

-- Add index for pagination ordering (created_at DESC is common)
SELECT 'Adding index for pagination ordering...' as status;
CREATE INDEX IF NOT EXISTS idx_users_created_desc ON users(created_at DESC);

-- Show final index status
SELECT 'Final index status:' as status;
SELECT 
    INDEX_NAME, 
    COLUMN_NAME, 
    NON_UNIQUE,
    INDEX_TYPE,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

SELECT 'User performance indexes migration completed!' as status;

<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Event Image Settings</h1>
            <p class="text-muted">Configure event image upload and display options</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Calendar Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Event image settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form action="<?php echo BASE_URL; ?>/admin/settings_event_images" method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
        
        <div class="row">
            <!-- Event Image Settings -->
            <div class="col-md-8 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-success text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-images me-2"></i> Event Image Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="event_max_images" class="form-label">Maximum Images per Event</label>
                            <input type="number" class="form-control" id="event_max_images" name="event_max_images" 
                                   value="<?php echo isset($calendarSettings['event_max_images']) ? $calendarSettings['event_max_images'] : '2'; ?>" 
                                   min="1" max="10">
                            <div class="form-text">Maximum number of images allowed per event (1-10)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_max_image_size" class="form-label">Maximum Image Size (MB)</label>
                            <input type="number" class="form-control" id="event_max_image_size" name="event_max_image_size" 
                                   value="<?php echo isset($calendarSettings['event_max_image_size']) ? $calendarSettings['event_max_image_size'] : '2'; ?>" 
                                   min="1" max="10" step="0.5">
                            <div class="form-text">Maximum file size for each image in megabytes (1-10 MB)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="event_allowed_image_types" class="form-label">Allowed Image Types</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_jpeg" name="event_allowed_image_types[]" value="image/jpeg" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/jpeg', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_jpeg">JPEG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_jpg" name="event_allowed_image_types[]" value="image/jpg" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/jpg', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_jpg">JPG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_png" name="event_allowed_image_types[]" value="image/png" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/png', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_png">PNG</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="allow_gif" name="event_allowed_image_types[]" value="image/gif" 
                                       <?php echo (!isset($calendarSettings['event_allowed_image_types']) || in_array('image/gif', explode(',', $calendarSettings['event_allowed_image_types'] ?? 'image/jpeg,image/jpg,image/png,image/gif'))) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="allow_gif">GIF</label>
                            </div>
                            <div class="form-text">Select which image file types are allowed for event uploads</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_enable_wysiwyg" name="event_enable_wysiwyg" 
                                       <?php echo (!isset($calendarSettings['event_enable_wysiwyg']) || $calendarSettings['event_enable_wysiwyg']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_enable_wysiwyg">
                                    Enable WYSIWYG Editor
                                </label>
                            </div>
                            <div class="form-text">Enable rich text editor with image upload for event descriptions</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="event_social_sharing_images" name="event_social_sharing_images" 
                                       <?php echo (!isset($calendarSettings['event_social_sharing_images']) || $calendarSettings['event_social_sharing_images']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="event_social_sharing_images">
                                    Include Images in Social Sharing
                                </label>
                            </div>
                            <div class="form-text">Include event images when sharing events on social media</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Information Panel -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Image Information</h3>
                    </div>
                    <div class="card-body p-4">
                        <h5>Image Storage</h5>
                        <p>Images are stored as Base64 encoded data in the database for portability and security.</p>
                        
                        <h5>Recommendations</h5>
                        <ul>
                            <li><strong>Max Images:</strong> 2-3 per event for optimal performance</li>
                            <li><strong>File Size:</strong> 2MB or less for faster loading</li>
                            <li><strong>Formats:</strong> JPEG/JPG for photos, PNG for graphics</li>
                            <li><strong>Dimensions:</strong> 1200x800px or similar aspect ratio</li>
                        </ul>
                        
                        <h5>Performance Impact</h5>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Note:</strong> Large images or many images per event may impact database performance and page loading times.
                        </div>
                        
                        <h5>Related Settings</h5>
                        <ul class="list-unstyled">
                            <li><a href="<?php echo BASE_URL; ?>/admin/settings_media" class="text-decoration-none">
                                <i class="fas fa-images me-1"></i> Media & Appearance
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
            <button type="submit" class="btn btn-success btn-lg">
                <i class="fas fa-save me-2"></i> Save Settings
            </button>
        </div>
    </form>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
// Add admin-settings CSS
$custom_css = BASE_URL . '/public/css/admin-settings.css';
require APPROOT . '/views/includes/header.php';
?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Camera Banner Management</h1>
            <p class="text-muted">Manage advertisement banners for camera and QR scanner modals</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <button type="button" class="btn btn-primary me-2 add-banner-btn">
                <i class="fas fa-plus me-2"></i> Add Banner
            </button>
            <a href="<?php echo BASE_URL; ?>/admin/settings_media" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <!-- Message Container -->
    <div id="messageContainer" class="mb-3"></div>

    <!-- Settings Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i> Banner Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form id="bannerSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="bannerDelay" class="form-label">Rotation Delay (seconds)</label>
                                <input type="number" class="form-control" id="bannerDelay" name="delay" 
                                       min="1" max="60" value="5" 
                                       placeholder="Enter delay in seconds">
                                <div class="form-text">How long each banner displays before rotating to the next</div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-2"></i> Save Settings
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Banners List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i> Current Banners
                    </h5>
                    <span class="badge bg-primary" id="bannerCount">0 banners</span>
                </div>
                <div class="card-body">
                    <div id="bannersContainer">
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading banners...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Simple Modal (No Bootstrap) -->
<div id="addCameraBannerModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 8px; max-width: 800px; width: 90%;">
        <div style="border-bottom: 1px solid #ddd; padding-bottom: 15px; margin-bottom: 20px;">
            <h5 style="margin: 0; font-size: 1.25rem; font-weight: 600;">Add New Camera Banner</h5>
            <button onclick="closeModal()" style="position: absolute; top: 15px; right: 15px; background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            <form id="addCameraBannerForm" enctype="multipart/form-data">
                <input type="hidden" name="action" value="create">
                <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                <div style="padding: 20px 0;">
                    <div class="mb-3">
                        <label class="form-label">Banner Type</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="typeText" value="text" checked>
                                    <label class="form-check-label" for="typeText">
                                        <i class="fas fa-font me-2"></i> Text Banner
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="typeImage" value="image">
                                    <label class="form-check-label" for="typeImage">
                                        <i class="fas fa-image me-2"></i> Image Banner
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="textBannerFields">
                        <div class="mb-3">
                            <label for="bannerText" class="form-label">Banner Text</label>
                            <textarea class="form-control" id="bannerText" name="text" rows="3" 
                                      placeholder="Enter your banner text..."></textarea>
                        </div>
                    </div>

                    <div id="imageBannerFields" style="display: none;">
                        <div class="mb-3">
                            <label for="bannerImage" class="form-label">Banner Image</label>
                            <input type="file" class="form-control" id="bannerImage" name="image" 
                                   accept="image/*">
                            <div class="form-text">Recommended size: 800x200px or similar aspect ratio</div>
                        </div>
                        <div class="mb-3">
                            <label for="altText" class="form-label">Alt Text</label>
                            <input type="text" class="form-control" id="altText" name="alt_text" 
                                   placeholder="Describe the image for accessibility">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sortOrder" class="form-label">Sort Order</label>
                        <input type="number" class="form-control" id="sortOrder" name="sort_order" 
                               value="0" min="0" max="999">
                        <div class="form-text">Lower numbers appear first</div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="bannerActive" name="active" checked>
                        <label class="form-check-label" for="bannerActive">
                            Active (show in rotation)
                        </label>
                    </div>
                </div>
                <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 20px; text-align: right;">
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Add Banner
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Banner Modal -->
<div class="modal fade" id="editBannerModal" tabindex="-1" aria-labelledby="editBannerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editBannerModalLabel">Edit Banner</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editBannerForm" enctype="multipart/form-data">
                <input type="hidden" id="editBannerId" name="id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Banner Type</label>
                        <div class="row">
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="editTypeText" value="text">
                                    <label class="form-check-label" for="editTypeText">
                                        <i class="fas fa-font me-2"></i> Text Banner
                                    </label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type" id="editTypeImage" value="image">
                                    <label class="form-check-label" for="editTypeImage">
                                        <i class="fas fa-image me-2"></i> Image Banner
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="editTextBannerFields">
                        <div class="mb-3">
                            <label for="editBannerText" class="form-label">Banner Text</label>
                            <textarea class="form-control" id="editBannerText" name="text" rows="3"></textarea>
                        </div>
                    </div>

                    <div id="editImageBannerFields" style="display: none;">
                        <div class="mb-3">
                            <label for="editBannerImage" class="form-label">Banner Image</label>
                            <input type="file" class="form-control" id="editBannerImage" name="image" accept="image/*">
                            <div class="form-text">Leave empty to keep current image</div>
                            <div id="currentImagePreview" class="mt-2"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editAltText" class="form-label">Alt Text</label>
                            <input type="text" class="form-control" id="editAltText" name="alt_text">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="editSortOrder" class="form-label">Sort Order</label>
                        <input type="number" class="form-control" id="editSortOrder" name="sort_order" min="0" max="999">
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editBannerActive" name="active">
                        <label class="form-check-label" for="editBannerActive">
                            Active (show in rotation)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Update Banner
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Wait for the page to be fully loaded
window.addEventListener('load', function() {
    // Load camera banners first
    loadCameraBannersAdmin();

    // Get the add banner button
    const addButton = document.querySelector('.add-banner-btn');

    // Add click event listener
    if (addButton) {
        addButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the modal element
            const modalElement = document.getElementById('addCameraBannerModal');

            // Simple show modal
            modalElement.style.display = 'block';
            console.log('Simple modal opened');
        });
    }

    // Handle radio button toggle for banner type
    const typeRadios = document.querySelectorAll('input[name="type"]');
    typeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            const textFields = document.getElementById('textBannerFields');
            const imageFields = document.getElementById('imageBannerFields');

            if (this.value === 'text') {
                textFields.style.display = 'block';
                imageFields.style.display = 'none';
            } else if (this.value === 'image') {
                textFields.style.display = 'none';
                imageFields.style.display = 'block';
            }

            console.log('Banner type changed to:', this.value);
        });
    });

    // Handle form submission
    const form = document.getElementById('addCameraBannerForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // Log form data for debugging
            console.log('Form submitted with data:');
            for (let [key, value] of formData.entries()) {
                console.log(key, value);
            }

            // Submit to existing API endpoint
            fetch('<?php echo BASE_URL; ?>/api/camera-banners-admin.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Form submit response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Form submit raw response:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Form submit JSON parse error:', e);
                    throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                }
            })
            .then(data => {
                if (data.success) {
                    alert('Banner added successfully!');
                    closeModal();
                    loadCameraBannersAdmin(); // Reload the list
                } else {
                    alert('Error: ' + (data.message || 'Failed to add banner'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error submitting form');
            });
        });
    }
});

// Function to load camera banners for admin
function loadCameraBannersAdmin() {
    const container = document.getElementById('bannersContainer');
    const countElement = document.getElementById('bannerCount');

    // Load banners from the API
    fetch('<?php echo BASE_URL; ?>/api/cameraBanners')
        .then(response => {
            console.log('API Response status:', response.status);
            console.log('API Response headers:', response.headers);
            return response.text();
        })
        .then(text => {
            console.log('Raw API response:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text:', text);
                throw new Error('Invalid JSON response: ' + text.substring(0, 100));
            }
        })
        .then(data => {
            if (data.success && data.banners && data.banners.length > 0) {
                // Display banners
                let html = '';
                data.banners.forEach(banner => {
                    html += `
                        <div class="card mb-3">
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        ${banner.type === 'image' && banner.image_path ?
                                            `<img src="${banner.image_path}" alt="${banner.alt_text}" class="img-thumbnail" style="max-height: 60px;">` :
                                            `<i class="fas fa-font fa-2x text-muted"></i>`
                                        }
                                    </div>
                                    <div class="col-md-6">
                                        <h6>${banner.type === 'image' ? 'Image Banner' : 'Text Banner'}</h6>
                                        <p class="text-muted mb-0">${banner.type === 'text' ? banner.text : banner.alt_text}</p>
                                        <small class="text-muted">Sort: ${banner.sort_order}</small>
                                    </div>
                                    <div class="col-md-2">
                                        <span class="badge ${banner.active ? 'bg-success' : 'bg-secondary'}">
                                            ${banner.active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                    <div class="col-md-2">
                                        ${banner.id === -1 ?
                                            `<span class="text-muted small">Logo Banner</span>` :
                                            banner.id === 0 ?
                                            `<span class="text-muted small">Default Banner</span>` :
                                            `<button class="btn btn-sm btn-outline-primary" onclick="editBanner(${banner.id})">Edit</button>
                                             <button class="btn btn-sm btn-outline-danger" onclick="deleteBanner(${banner.id})">Delete</button>`
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                container.innerHTML = html;
                countElement.textContent = `${data.banners.length} banner${data.banners.length !== 1 ? 's' : ''}`;
            } else {
                // No banners found
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No camera banners found</h5>
                        <p class="text-muted">Click "Add Banner" to create your first camera banner.</p>
                    </div>
                `;
                countElement.textContent = '0 banners';
            }
            console.log('Camera banners loaded:', data.banners ? data.banners.length : 0);
        })
        .catch(error => {
            console.error('Error loading banners:', error);
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h5 class="text-warning">Error loading banners</h5>
                    <p class="text-muted">Please try refreshing the page.</p>
                </div>
            `;
        });
}

// Simple close modal function
function closeModal() {
    document.getElementById('addCameraBannerModal').style.display = 'none';
}

// Edit banner function
function editBanner(id) {
    alert('Edit banner ' + id + ' - Not implemented yet');
}

// Delete banner function
function deleteBanner(id) {
    if (confirm('Are you sure you want to delete this banner?')) {
        fetch('<?php echo BASE_URL; ?>/api/camera-banners-admin.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=delete&id=' + id
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Banner deleted successfully!');
                loadCameraBannersAdmin(); // Reload the list
            } else {
                alert('Error: ' + (data.message || 'Failed to delete banner'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting banner');
        });
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
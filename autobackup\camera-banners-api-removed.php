<?php
/**
 * BACKUP: Old Camera Banner API (REMOVED - Replaced by ApiController::cameraBanners)
 * This file was removed because functionality moved to controllers/ApiController.php
 * Backup created for reference only
 */

require_once '../config/config.php';
require_once '../core/Database.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Debug mode check
$debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : false;

try {
    $db = new Database();

    // Check if camera_banners table exists
    $db->query("SHOW TABLES LIKE 'camera_banners'");
    $db->execute();
    $table_exists = $db->rowCount() > 0;

    $banners = [];
    
    if ($table_exists) {
        // Get active banners
        $db->query("SELECT * FROM camera_banners WHERE active = 1 ORDER BY sort_order ASC, created_at DESC");
        $db->execute();
        $results = $db->resultSet();
        
        foreach ($results as $row) {
            $banner = [
                'id' => (int)$row->id,
                'type' => $row->type ?: 'text',
                'text' => $row->text ?: '',
                'image_path' => $row->image_path ?: '',
                'alt_text' => $row->alt_text ?: '',
                'active' => (bool)$row->active,
                'sort_order' => (int)$row->sort_order
            ];
            
            // Ensure image path is absolute and not empty
            if ($banner['image_path'] && !str_starts_with($banner['image_path'], 'http')) {
                $banner['image_path'] = '/uploads/banners/' . basename($banner['image_path']);
            }
            
            $banners[] = $banner;
        }
    }
    
    // Get site logo for first banner
    $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'site_logo' LIMIT 1");
    $db->execute();
    $logo_row = $db->single();
    $site_logo = $logo_row ? $logo_row->setting_value : '';
    
    // Always add logo as first banner (fixed position) - this is required system behavior
    $logo_banner = [
        'id' => -1, // Special ID for logo banner
        'type' => 'image',
        'text' => '',
        'image_path' => $site_logo ? BASE_URL . $site_logo : '/uploads/branding/logo_1747660729_rowaneliterides_transparent_150.png',
        'alt_text' => APP_NAME . ' Logo',
        'active' => true,
        'sort_order' => -1, // Always first
        'is_logo' => true, // Special flag
        'duration' => 5000 // Always show for 5 seconds
    ];
    
    // Prepare final banner array - logo first, then database banners
    $final_banners = [$logo_banner];
    
    // Add database banners (will be randomized in JavaScript)
    if (!empty($banners)) {
        $final_banners = array_merge($final_banners, $banners);
    }
    // Note: Removed the fallback "Welcome to our Event Platform!" banner
    
    $banners = $final_banners;

    // Get rotation delay setting with null safety
    $delay = 5000; // Default 5 seconds
    try {
        $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'camera_banner_delay' LIMIT 1");
        $db->execute();
        $delay_row = $db->single();
        
        if ($delay_row && !empty($delay_row->setting_value)) {
            $delay = (int)$delay_row->setting_value;
        }
    } catch (Exception $e) {
        // Use default delay if setting doesn't exist
        if ($debug_mode) {
            error_log('Camera banner delay setting not found, using default: ' . $e->getMessage());
        }
    }

    $response = [
        'success' => true,
        'banners' => $banners,
        'delay' => $delay,
        'count' => count($banners)
    ];

    if ($debug_mode) {
        $response['debug'] = [
            'table_exists' => $table_exists,
            'banner_count' => count($banners),
            'delay_setting' => $delay
        ];
    }

    echo json_encode($response);

} catch (PDOException $e) {
    $error_response = [
        'success' => false,
        'message' => 'Database error occurred'
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);

} catch (Exception $e) {
    $error_response = [
        'success' => false,
        'message' => 'An error occurred'
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);
}
?>
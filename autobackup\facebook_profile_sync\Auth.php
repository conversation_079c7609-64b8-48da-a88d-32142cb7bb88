<?php
/**
 * Auth Class
 * 
 * This class handles user authentication, including login, registration,
 * password hashing, and role-based access control.
 */
class Auth {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Register a new user
     * 
     * @param string $name User's name
     * @param string $email User's email
     * @param string $password User's password
     * @param string $role User's role (default: 'user')
     * @return bool|int False on failure, user ID on success
     */
    public function register($name, $email, $password, $role = 'user', $phone = null, $address = null, $city = null, $state = null, $zip = null) {
        // Validate inputs
        $name = trim($name);
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($name) || empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Password strength validation
        if (strlen($password) < 6) {
            return false; // Password too short
        }
        
        // Hash password with stronger algorithm and options
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        
        // Check if email already exists
        $this->db->query('SELECT id FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $existingUser = $this->db->single();
        
        if ($existingUser) {
            return false; // Email already exists
        }
        
        // Validate role
        $validRoles = ['admin', 'coordinator', 'judge', 'staff', 'user'];
        if (!in_array($role, $validRoles)) {
            $role = 'user'; // Default to user if invalid role provided
        }
        
        // Insert user
        $this->db->query('INSERT INTO users (name, email, password, role, phone, address, city, state, zip, created_at) 
                          VALUES (:name, :email, :password, :role, :phone, :address, :city, :state, :zip, NOW())');
        $this->db->bind(':name', $name);
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':role', $role);
        $this->db->bind(':phone', $phone);
        $this->db->bind(':address', $address);
        $this->db->bind(':city', $city);
        $this->db->bind(':state', $state);
        $this->db->bind(':zip', $zip);
        
        try {
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                error_log('Failed to register user: ' . $email);
                return false;
            }
        } catch (Exception $e) {
            error_log('Exception during user registration: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Register or login a user via Facebook
     * 
     * @param array $fbUserData Facebook user data
     * @param string $accessToken Facebook access token
     * @return bool|int False on failure, user ID on success
     */
    public function facebookAuth($fbUserData, $accessToken = null) {
        // Validate required data
        if (empty($fbUserData['id']) || empty($fbUserData['email'])) {
            error_log('Facebook auth failed: Missing required user data');
            return false;
        }
        
        // First check if user exists by Facebook ID
        $this->db->query('SELECT id, role FROM users WHERE facebook_id = :facebook_id');
        $this->db->bind(':facebook_id', $fbUserData['id']);
        $user = $this->db->single();
        
        // If not found by Facebook ID, check if user exists by email
        if (!$user) {
            $this->db->query('SELECT id, role FROM users WHERE email = :email');
            $this->db->bind(':email', $fbUserData['email']);
            $user = $this->db->single();
        }
        
        if ($user) {
            // User exists, update their info
            $sql = 'UPDATE users SET 
                    facebook_id = :facebook_id,
                    name = :name, 
                    email = :email,';
            
            // Add facebook_token to the update if provided
            if ($accessToken) {
                $sql .= ' facebook_token = :facebook_token,';
            }
            
            $sql .= ' last_login = NOW() 
                    WHERE id = :id';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':id', $user->id);
            
            try {
                $this->db->execute();
                
                // Log successful Facebook login
                error_log('Successful Facebook login/link: ' . $fbUserData['email'] . ' (ID: ' . $user->id . ')');
                
                // If we have a profile image URL from Facebook, try to download it
                if (isset($fbUserData['picture']['data']['url'])) {
                    $this->downloadFacebookProfileImage($user->id, $fbUserData['picture']['data']['url']);
                }
                
                return $user->id;
            } catch (Exception $e) {
                error_log('Error updating user with Facebook data: ' . $e->getMessage());
                return false;
            }
        } else {
            // Create new user
            // Generate a random password for the account (they'll use Facebook to login)
            $randomPassword = password_hash(bin2hex(random_bytes(16)), PASSWORD_BCRYPT, ['cost' => 12]);
            
            // Build the SQL query
            $sql = 'INSERT INTO users (
                    facebook_id, 
                    name, 
                    email, 
                    password,';
            
            // Add facebook_token to the insert if provided
            if ($accessToken) {
                $sql .= ' facebook_token,';
            }
            
            $sql .= ' role, 
                    created_at, 
                    last_login
                  ) VALUES (
                    :facebook_id, 
                    :name, 
                    :email, 
                    :password,';
            
            // Add facebook_token placeholder to the insert if provided
            if ($accessToken) {
                $sql .= ' :facebook_token,';
            }
            
            $sql .= ' :role, 
                    NOW(), 
                    NOW()
                  )';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            $this->db->bind(':password', $randomPassword);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':role', 'user'); // Default role for Facebook users
            
            try {
                if ($this->db->execute()) {
                    $newUserId = $this->db->lastInsertId();
                    
                    // Log successful Facebook registration
                    error_log('New user registered via Facebook: ' . $fbUserData['email'] . ' (ID: ' . $newUserId . ')');
                    
                    // If we have a profile image URL from Facebook, try to download it
                    if (isset($fbUserData['picture']['data']['url'])) {
                        $this->downloadFacebookProfileImage($newUserId, $fbUserData['picture']['data']['url']);
                    }
                    
                    return $newUserId;
                } else {
                    error_log('Failed to create new user via Facebook: ' . $fbUserData['email']);
                    return false;
                }
            } catch (Exception $e) {
                error_log('Exception during Facebook user creation: ' . $e->getMessage());
                return false;
            }
        }
    }
    
    /**
     * Login a user
     * 
     * @param string $email User's email
     * @param string $password User's password
     * @return bool|object False on failure, user object on success
     */
    public function login($email, $password) {
        // Validate inputs
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Find user by email
        $this->db->query('SELECT id, name, email, password, role, status FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            // Implement timing attack protection
            password_verify('dummy_password', '$2y$10$abcdefghijklmnopqrstuuVzmd4.QxnCbzrWMs4/QqbDTLxKfLxy.');
            return false; // User not found
        }
        
        // Check if user is active
        if (isset($user->status) && $user->status !== 'active') {
            return false; // Account inactive
        }
        
        // Verify password
        if (password_verify($password, $user->password)) {
            // Check if password needs rehashing (if PHP's password hashing algorithm has been updated)
            if (password_needs_rehash($user->password, PASSWORD_BCRYPT, ['cost' => 12])) {
                $newHash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                $this->db->query('UPDATE users SET password = :password WHERE id = :id');
                $this->db->bind(':password', $newHash);
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            }
            
            // Update last login (try-catch in case the column doesn't exist yet)
            try {
                $this->db->query('UPDATE users SET last_login = NOW() WHERE id = :id');
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            } catch (PDOException $e) {
                // Log the error but continue - this is not critical
                error_log('Error updating last_login: ' . $e->getMessage());
                // If the column doesn't exist, we'll just skip this update
            }
            
            // Log successful login
            error_log('Successful login: ' . $email);
            
            return $user;
        } else {
            // Log failed login attempt
            error_log('Failed login attempt: ' . $email);
            return false; // Password incorrect
        }
    }
    
    /**
     * Check if development bypass is enabled
     * 
     * @return bool
     */
    private function isDevBypassEnabled() {
        static $bypassEnabled = null;
        
        // If we've already checked, return the cached result
        if ($bypassEnabled !== null) {
            return $bypassEnabled;
        }
        
        // Check system_settings table
        try {
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $result = $this->db->single();
            
            if ($result && $result->setting_value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking system_settings table for dev bypass: ' . $e->getMessage());
        }
        
        // Check old settings table as fallback
        try {
            $this->db->query('SELECT value FROM settings WHERE name = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $oldResult = $this->db->single();
            
            if ($oldResult && $oldResult->value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking old settings table for dev bypass: ' . $e->getMessage());
        }
        
        $bypassEnabled = false;
        return false;
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool
     */
    public function isLoggedIn() {
        // First check if dev bypass is enabled
        if ($this->isDevBypassEnabled()) {
            // If dev bypass is enabled, automatically consider user as logged in
            // Set session variables if they don't exist
            if (!isset($_SESSION['user_id'])) {
                $_SESSION['user_id'] = 0; // Special dev bypass ID
                $_SESSION['user_name'] = 'Developer';
                $_SESSION['user_email'] = '<EMAIL>';
                $_SESSION['user_role'] = 'admin';
            }
            return true;
        }
        
        // Check if user is logged in
        if (isset($_SESSION['user_id'])) {
            return true;
        } else {
            return false;
        }
    }
    
    /**
     * Get current user ID
     * 
     * @return int|null User ID or null if not logged in
     */
    public function getCurrentUserId() {
        if ($this->isLoggedIn()) {
            return $_SESSION['user_id'];
        } else {
            return null;
        }
    }
    
    /**
     * Get current user role
     * 
     * @return string|null User role or null if not logged in
     */
    public function getCurrentUserRole() {
        if ($this->isLoggedIn() && isset($_SESSION['user_role'])) {
            return $_SESSION['user_role'];
        } else {
            return null;
        }
    }
    
    /**
     * Check if current user has a specific role
     * 
     * @param string|array $roles Role(s) to check
     * @return bool True if user has the role, false otherwise
     */
    public function hasRole($roles) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        $currentRole = $this->getCurrentUserRole();
        
        if (!$currentRole) {
            return false;
        }
        
        // If roles is a string, convert to array
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        
        // Check if current role is in the list of roles
        return in_array($currentRole, $roles);
    }
    
    /**
     * Check if current user is an admin
     * 
     * @return bool True if user is an admin, false otherwise
     */
    public function isAdmin() {
        return $this->hasRole('admin');
    }
    
    /**
     * Check if current user is a coordinator
     * 
     * @return bool True if user is a coordinator, false otherwise
     */
    public function isCoordinator() {
        return $this->hasRole('coordinator');
    }
    
    /**
     * Check if current user is a judge
     * 
     * @return bool True if user is a judge, false otherwise
     */
    public function isJudge() {
        return $this->hasRole('judge');
    }
    
    /**
     * Check if current user is a staff member
     * 
     * @return bool True if user is a staff member, false otherwise
     */
    public function isStaff() {
        return $this->hasRole('staff');
    }
    
    /**
     * Logout the current user
     * 
     * @return void
     */
    public function logout() {
        // Unset all session variables
        $_SESSION = [];
        
        // Delete the session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }
        
        // Destroy the session
        session_destroy();
    }
    
    /**
     * Download and save Facebook profile image
     * 
     * @param int $userId User ID
     * @param string $imageUrl Facebook profile image URL
     * @return bool True on success, false on failure
     */
    private function downloadFacebookProfileImage($userId, $imageUrl) {
        try {
            error_log('Auth::downloadFacebookProfileImage - Downloading image for user ' . $userId . ' from ' . $imageUrl);
            
            // Set a user agent to avoid potential blocking
            $context = stream_context_create([
                'http' => [
                    'method' => 'GET',
                    'header' => [
                        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept: image/jpeg,image/png,image/*,*/*'
                    ]
                ]
            ]);
            
            // Download the image
            $imageContent = @file_get_contents($imageUrl, false, $context);
            
            if (!$imageContent) {
                error_log('Auth::downloadFacebookProfileImage - Failed to download image with file_get_contents, trying cURL');
                
                // Try with cURL as a fallback
                $ch = curl_init($imageUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: image/jpeg,image/png,image/*,*/*']);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
                $imageContent = curl_exec($ch);
                
                if (curl_errno($ch)) {
                    error_log('Auth::downloadFacebookProfileImage - cURL error: ' . curl_error($ch));
                    curl_close($ch);
                    return false;
                }
                
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode != 200 || empty($imageContent)) {
                    error_log('Auth::downloadFacebookProfileImage - HTTP error: ' . $httpCode);
                    return false;
                }
            }
            
            // Verify the image content is valid
            if (strlen($imageContent) < 100) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded image is too small: ' . strlen($imageContent) . ' bytes');
                return false;
            }
            
            // Check if the content is a valid image
            $tempImageCheck = @imagecreatefromstring($imageContent);
            if ($tempImageCheck === false) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded content is not a valid image');
                return false;
            }
            imagedestroy($tempImageCheck);
            
            // Create a temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'fb_img_');
            $bytesWritten = file_put_contents($tempFile, $imageContent);
            
            if ($bytesWritten === false) {
                error_log('Auth::downloadFacebookProfileImage - Failed to write to temporary file: ' . $tempFile);
                return false;
            }
            
            // Create a file array similar to $_FILES
            $fileArray = [
                'name' => 'facebook_profile.jpg',
                'type' => 'image/jpeg',
                'tmp_name' => $tempFile,
                'error' => 0,
                'size' => filesize($tempFile)
            ];
            
            // Load image editor model
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();
            
            // First, delete any existing profile images for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $existingImages = $this->db->resultSet();
            
            // Delete each existing image
            foreach ($existingImages as $image) {
                $imageEditorModel->deleteImage($image->id);
            }
            
            // Process the image upload
            $uploadDir = 'uploads/users/';
            
            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    error_log('Auth::downloadFacebookProfileImage - Failed to create upload directory: ' . $uploadDir);
                    @unlink($tempFile);
                    return false;
                }
            }
            
            // Process the image upload
            $imageData = $imageEditorModel->processImageUpload(
                $fileArray, 
                'user', 
                $userId, 
                $uploadDir,
                $userId,
                true // Set as primary image
            );
            
            // Clean up the temporary file
            @unlink($tempFile);
            
            if ($imageData) {
                error_log('Auth::downloadFacebookProfileImage - Successfully processed image upload for user ' . $userId);
                return true;
            } else {
                error_log('Auth::downloadFacebookProfileImage - Failed to process image upload for user ' . $userId);
                return false;
            }
            
        } catch (Exception $e) {
            error_log('Auth::downloadFacebookProfileImage - Exception: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if user has a specific permission
     * 
     * @param string $permission Permission to check
     * @return bool True if user has the permission, false otherwise
     */
    public function hasPermission($permission) {
        // Admin has all permissions
        if ($this->isAdmin()) {
            return true;
        }
        
        // Get current user ID
        $userId = $this->getCurrentUserId();
        if (!$userId) {
            return false;
        }
        
        // Check if user has the permission
        $this->db->query('SELECT COUNT(*) as count FROM user_permissions 
                          WHERE user_id = :user_id AND permission = :permission');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':permission', $permission);
        $result = $this->db->single();
        
        return $result && $result->count > 0;
    }
    
    /**
     * Check if user has a specific role for a show
     * 
     * @param int $showId Show ID
     * @param string $role Role to check
     * @return bool True if user has the role, false otherwise
     */
    public function hasShowRole($showId, $role) {
        // Admin has all roles
        if ($this->isAdmin()) {
            return true;
        }
        
        // Coordinator has all roles for their shows
        if ($this->isCoordinator()) {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id AND coordinator_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $this->getCurrentUserId());
            $result = $this->db->single();
            
            if ($result && $result->count > 0) {
                return true;
            }
        }
        
        // Check if user has the specific role for this show
        $this->db->query('SELECT COUNT(*) as count FROM show_staff 
                          WHERE show_id = :show_id AND user_id = :user_id AND role = :role');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $this->getCurrentUserId());
        $this->db->bind(':role', $role);
        $result = $this->db->single();
        
        return $result && $result->count > 0;
    }
    
    /**
     * Get all roles for a user in a show
     * 
     * @param int $showId Show ID
     * @return array Roles
     */
    public function getUserShowRoles($showId) {
        $roles = [];
        
        // Admin has all roles
        if ($this->isAdmin()) {
            return ['admin', 'coordinator', 'judge', 'staff', 'registration', 'check-in'];
        }
        
        // Coordinator has all roles for their shows
        if ($this->isCoordinator()) {
            $this->db->query('SELECT COUNT(*) as count FROM shows WHERE id = :show_id AND coordinator_id = :user_id');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':user_id', $this->getCurrentUserId());
            $result = $this->db->single();
            
            if ($result && $result->count > 0) {
                return ['coordinator', 'judge', 'staff', 'registration', 'check-in'];
            }
        }
        
        // Get all roles for this user in this show
        $this->db->query('SELECT role FROM show_staff 
                          WHERE show_id = :show_id AND user_id = :user_id');
        $this->db->bind(':show_id', $showId);
        $this->db->bind(':user_id', $this->getCurrentUserId());
        $results = $this->db->resultSet();
        
        foreach ($results as $result) {
            $roles[] = $result->role;
        }
        
        return $roles;
    }
}
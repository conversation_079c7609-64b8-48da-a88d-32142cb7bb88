<?php
// Prepare Open Graph meta data for social sharing
$eventTitle = htmlspecialchars($data['event']->title, ENT_QUOTES, 'UTF-8');
$eventDescription = !empty($data['event']->description) ? 
    htmlspecialchars(substr(strip_tags($data['event']->description), 0, 300), ENT_QUOTES, 'UTF-8') : 
    'Check out this event!';
$eventUrl = URLROOT . '/calendar/event/' . $data['event']->id;
$eventDate = date('F j, Y', strtotime($data['event']->start_date));
$eventLocation = !empty($data['event']->location) ? htmlspecialchars($data['event']->location, ENT_QUOTES, 'UTF-8') : '';

// Set page-specific meta data for header
$pageTitle = $eventTitle . ' - ' . APP_NAME;
$pageDescription = $eventDescription;
$pageUrl = $eventUrl;
$pageImage = URLROOT . '/assets/images/logo.png'; // Default image, can be enhanced later

// Add Open Graph and Twitter Card meta tags
$additionalMetaTags = '
<!-- Open Graph / Facebook -->
<meta property="og:type" content="event">
<meta property="og:url" content="' . $eventUrl . '">
<meta property="og:title" content="' . $eventTitle . '">
<meta property="og:description" content="' . $eventDescription . '">
<meta property="og:image" content="' . $pageImage . '">
<meta property="og:site_name" content="' . APP_NAME . '">
<meta property="event:start_time" content="' . date('c', strtotime($data['event']->start_date . ' ' . ($data['event']->start_time ?? '00:00:00'))) . '">
<meta property="event:end_time" content="' . date('c', strtotime($data['event']->end_date . ' ' . ($data['event']->end_time ?? '23:59:59'))) . '">
' . (!empty($eventLocation) ? '<meta property="event:location" content="' . $eventLocation . '">' : '') . '

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="' . $eventUrl . '">
<meta name="twitter:title" content="' . $eventTitle . '">
<meta name="twitter:description" content="' . $eventDescription . '">
<meta name="twitter:image" content="' . $pageImage . '">

<!-- LinkedIn -->
<meta property="og:locale" content="en_US">
<meta property="article:author" content="' . APP_NAME . '">
';

require APPROOT . '/views/includes/header.php'; 

// Debug: Show Open Graph tags in debug mode
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    echo '<!-- DEBUG: Open Graph Meta Tags -->';
    echo '<!-- Event Title: ' . $eventTitle . ' -->';
    echo '<!-- Event Description: ' . $eventDescription . ' -->';
    echo '<!-- Event URL: ' . $eventUrl . ' -->';
    echo '<!-- Event Date: ' . $eventDate . ' -->';
    echo '<!-- Event Location: ' . $eventLocation . ' -->';
    echo '<!-- Facebook Debugger: https://developers.facebook.com/tools/debug/?q=' . urlencode($eventUrl) . ' -->';
}
?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Details</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <?php if (isLoggedIn()): ?>
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Calendar
                </a>
                <?php else: ?>
                <a href="<?php echo URLROOT; ?>/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Home
                </a>
                <?php endif; ?>
                <?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && empty($data['event']->show_id)): ?>
                <a href="<?php echo URLROOT; ?>/calendar/editEvent/<?php echo $data['event']->id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Event
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <?php elseif (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && !empty($data['event']->show_id)): ?>
                <!-- Edit button hidden for show events - edit the show instead -->
                <a href="<?php echo URLROOT; ?>/admin/editShow/<?php echo $data['event']->show_id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Show
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <span class="ms-2 text-muted"><small><i class="fas fa-info-circle"></i> This event is linked to a show. Edit the show to update this event.</small></span>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><?php echo $data['event']->title; ?></h5>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <div class="calendar-color-dot me-2" style="background-color: <?php echo $data['event']->color ?: $data['event']->calendar_color ?: '#3788d8'; ?>"></div>
                            <h6 class="mb-0"><?php echo $data['event']->calendar_name; ?></h6>
                        </div>
                        
                        <div class="mb-3">
                            <i class="far fa-calendar-alt me-2"></i>
                            <?php
                            // Format date and time
                            $startDate = new DateTime($data['event']->start_date);
                            $endDate = new DateTime($data['event']->end_date);
                            
                            $dateFormat = 'l, F j, Y';
                            $timeFormat = 'g:i A';
                            
                            if ($data['event']->all_day) {
                                echo $startDate->format($dateFormat);
                                
                                // Check if multi-day event
                                if ($startDate->format('Y-m-d') != $endDate->format('Y-m-d')) {
                                    echo ' to ' . $endDate->format($dateFormat);
                                }
                                
                                echo ' (All day)';
                            } else {
                                echo $startDate->format($dateFormat) . ' at ' . $startDate->format($timeFormat);
                                
                                // Check if same day
                                if ($startDate->format('Y-m-d') == $endDate->format('Y-m-d')) {
                                    echo ' - ' . $endDate->format($timeFormat);
                                } else {
                                    echo ' to ' . $endDate->format($dateFormat) . ' at ' . $endDate->format($timeFormat);
                                }
                            }
                            ?>
                        </div>
                        
                        <?php if (!empty($data['event']->location)): ?>
                        <div class="mb-3">
                            <i class="fas fa-map-marker-alt me-2"></i>
                            <?php echo $data['event']->location; ?>
                            
                            <?php if (!empty($data['venue'])): ?>
                            <div class="ms-4 mt-2">
                                <?php if (!empty($data['venue']->address)): ?>
                                <div><?php echo $data['venue']->address; ?></div>
                                <?php endif; ?>
                                
                                <?php if (!empty($data['venue']->city) || !empty($data['venue']->state) || !empty($data['venue']->zip)): ?>
                                <div>
                                    <?php echo $data['venue']->city; ?>
                                    <?php if (!empty($data['venue']->city) && !empty($data['venue']->state)): ?>, <?php endif; ?>
                                    <?php echo $data['venue']->state; ?>
                                    <?php if (!empty($data['venue']->zip)): ?> <?php echo $data['venue']->zip; ?><?php endif; ?>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($data['venue']->latitude) && !empty($data['venue']->longitude)): ?>
                                <div class="mt-2">
                                    <a href="https://maps.google.com/?q=<?php echo $data['venue']->latitude; ?>,<?php echo $data['venue']->longitude; ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-map me-1"></i> View on Map
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->url)): ?>
                        <div class="mb-3">
                            <i class="fas fa-link me-2"></i>
                            <a href="<?php echo $data['event']->url; ?>" target="_blank"><?php echo $data['event']->url; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->show_id)): ?>
                        <div class="mb-3">
                            <i class="fas fa-car me-2"></i>
                            <a href="<?php echo URLROOT; ?>/show/view/<?php echo $data['event']->show_id; ?>"><?php echo $data['event']->show_name; ?></a>
                        </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($data['event']->clubs)): ?>
                        <div class="mb-3">
                            <i class="fas fa-users me-2"></i>
                            <strong>Clubs:</strong>
                            <div class="ms-4 mt-1">
                                <?php foreach ($data['event']->clubs as $club): ?>
                                <div class="mb-1"><?php echo $club->name; ?></div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <?php if ($data['event']->is_recurring): ?>
                        <div class="mb-3">
                            <i class="fas fa-sync-alt me-2"></i>
                            <strong>Recurring Event:</strong>
                            <div class="ms-4 mt-1">
                                <?php echo $data['event']->recurrence_pattern; ?>
                                <?php if (!empty($data['event']->recurrence_end_date)): ?>
                                <div>Until <?php echo date('F j, Y', strtotime($data['event']->recurrence_end_date)); ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mb-3">
                            <i class="fas fa-lock me-2"></i>
                            <strong>Privacy:</strong>
                            <?php
                            switch ($data['event']->privacy) {
                                case 'public':
                                    echo 'Public - Visible to everyone';
                                    break;
                                case 'private':
                                    echo 'Private - Visible only to you';
                                    break;
                                case 'members':
                                    echo 'Members - Visible to registered users';
                                    break;
                                default:
                                    echo 'Unknown';
                            }
                            ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($data['event']->description)): ?>
                    <div class="mb-3">
                        <h5>Description</h5>
                        <div class="event-description">
                            <?php echo nl2br(htmlspecialchars($data['event']->description)); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="mt-4">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <div class="mb-3 mb-md-0">
                                <small class="text-muted">
                                    <?php if (isLoggedIn()): ?>
                                        Created by: <?php echo $data['event']->created_by == $_SESSION['user_id'] ? 'You' : 'Another user'; ?>
                                    <?php else: ?>
                                        <?php if ($data['event']->privacy == 'public'): ?>
                                            Public Event
                                        <?php else: ?>
                                            Member Event
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </small>
                            </div>
                            
                            <!-- Social sharing buttons -->
                            <div class="social-sharing">
                                <small class="text-muted me-2">Share:</small>
                                <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode($eventUrl); ?>" 
                                   target="_blank" 
                                   class="btn btn-sm btn-outline-primary me-1"
                                   title="Share on Facebook">
                                    <i class="fab fa-facebook-f"></i>
                                </a>
                                <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode($eventUrl); ?>&text=<?php echo urlencode($eventTitle . ' - ' . $eventDate); ?>" 
                                   target="_blank" 
                                   class="btn btn-sm btn-outline-info me-1"
                                   title="Share on Twitter">
                                    <i class="fab fa-twitter"></i>
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode($eventUrl); ?>" 
                                   target="_blank" 
                                   class="btn btn-sm btn-outline-primary me-1"
                                   title="Share on LinkedIn">
                                    <i class="fab fa-linkedin-in"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-secondary" 
                                        onclick="navigator.clipboard.writeText('<?php echo $eventUrl; ?>'); this.innerHTML='<i class=\'fas fa-check\'></i> Copied!'; setTimeout(() => this.innerHTML='<i class=\'fas fa-link\'></i>', 2000);"
                                        title="Copy link">
                                    <i class="fas fa-link"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Event sidebar content can go here -->
            <?php if (!empty($data['event']->lat) && !empty($data['event']->lng)): ?>
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">Location</h6>
                </div>
                <div class="card-body">
                    <div id="eventMap" style="height: 200px; border-radius: 5px;"></div>
                    <div class="mt-2 text-center">
                        <a href="https://maps.google.com/?q=<?php echo $data['event']->lat; ?>,<?php echo $data['event']->lng; ?>" 
                           target="_blank" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-external-link-alt me-1"></i> Open in Google Maps
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Event Modal -->
<?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin())): ?>
<div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteEventModalLabel">Delete Event</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this event?</p>
                <p><strong><?php echo $data['event']->title; ?></strong></p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="<?php echo URLROOT; ?>/calendar/deleteEvent/<?php echo $data['event']->id; ?>" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete Event</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Map Script -->
<?php if (!empty($data['event']->lat) && !empty($data['event']->lng)): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Simple map using OpenStreetMap
    const lat = <?php echo $data['event']->lat; ?>;
    const lng = <?php echo $data['event']->lng; ?>;
    
    // Create a simple map div with a link to Google Maps
    const mapDiv = document.getElementById('eventMap');
    if (mapDiv) {
        mapDiv.innerHTML = `
            <div style="background: #f8f9fa; height: 100%; display: flex; align-items: center; justify-content: center; border: 1px solid #dee2e6;">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                    <div><small class="text-muted">Lat: ${lat}, Lng: ${lng}</small></div>
                </div>
            </div>
        `;
    }
});
</script>
<?php endif; ?>

<?php require APPROOT . '/views/includes/footer.php'; ?>
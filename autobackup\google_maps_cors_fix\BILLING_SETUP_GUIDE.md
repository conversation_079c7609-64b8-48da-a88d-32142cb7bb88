# Google Maps "This page can't load Google Maps correctly" - Fix Guide

## The Error
**"This page can't load Google Maps correctly"** = Billing or API configuration issue

## Root Cause
Google Maps requires:
1. ✅ Valid API key (you have this)
2. ❌ **Billing account set up** (this is missing)
3. ❌ **Correct APIs enabled** (likely missing)

## Step-by-Step Fix

### Step 1: Set Up Billing (REQUIRED)
Even for free usage, Google requires a billing account.

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Select your project** (the one with your API key)
3. **Click "Billing" in the left menu**
4. **Link a billing account**:
   - If you don't have one, click "CREATE BILLING ACCOUNT"
   - Add a valid credit/debit card
   - Don't worry - you get $200 free credit + generous free tiers

### Step 2: Enable Required APIs
1. **Go to "APIs & Services" > "Library"**
2. **Search and ENABLE these APIs**:
   - ✅ **Maps JavaScript API**
   - ✅ **Maps Static API** 
   - ✅ **Places API**
   - ✅ **Geocoding API**

### Step 3: Configure API Key Restrictions
1. **Go to "APIs & Services" > "Credentials"**
2. **Click on your API key**: `AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM`
3. **Application restrictions**:
   - Select: "HTTP referrers (web sites)"
   - Add these domains:
     ```
     https://events.rowaneliterides.com/*
     https://*.rowaneliterides.com/*
     events.rowaneliterides.com/*
     *.rowaneliterides.com/*
     ```
4. **API restrictions**:
   - Select: "Restrict key"
   - Check: Maps JavaScript API, Maps Static API, Places API, Geocoding API

### Step 4: Test Your Setup
Test this URL in your browser:
```
https://maps.googleapis.com/maps/api/staticmap?center=40.714728,-73.998672&zoom=12&size=400x400&key=AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM
```

**Expected**: Should show a map image
**If fails**: Billing or API not set up correctly

## Free Tier Limits (Don't Worry About Costs)
- **Maps JavaScript API**: 28,000 map loads/month FREE
- **Static Maps**: 100,000 requests/month FREE  
- **Places API**: $200 credit/month (covers ~40,000 requests)
- **Geocoding**: 40,000 requests/month FREE

**Your site will likely stay within free limits.**

## Quick Checklist
- [ ] Billing account linked with valid payment method
- [ ] Maps JavaScript API enabled
- [ ] Maps Static API enabled
- [ ] Places API enabled
- [ ] Geocoding API enabled
- [ ] API key restrictions configured for your domain
- [ ] Static map test URL works

## After Setup
Once billing is configured and APIs are enabled:
1. **Wait 5-10 minutes** for changes to propagate
2. **Clear browser cache**
3. **Test your map page** - should work!

## Need Help?
The most common issue is **billing not being set up**. Google requires this even for free usage.

**Which step are you stuck on?**
# Facebook Profile Image Sync Fix - August 19, 2024

## Issue Description
The Facebook profile image sync feature was not working correctly due to inconsistent storage approaches. The system was using two different storage methods for profile images:

1. Facebook profile images were stored as URLs in the `profile_image` column of the `users` table.
2. Other profile images were stored as files in the `/uploads/users/` directory with references in the `images` table.

This inconsistency caused problems with image display and management, as the system had to check multiple locations to find a user's profile image.

## Solution
We standardized the storage approach for all profile images:

1. **All profile images are now stored as files in the `/uploads/users/` directory.**
2. **All profile image references are stored in the `images` table with entity_type='user'.**
3. **The `profile_image` column in the `users` table is cleared (set to NULL) after migrating images.**

The following changes were made to implement this solution:

1. Modified the `downloadFacebookProfileImage` method in `Auth.php` to clear the `profile_image` field in the `users` table after successfully downloading and storing the image in the `images` table.

2. Modified the `syncFacebookImage` method in `UserController.php` to clear the `profile_image` field in the `users` table before attempting to download and store the Facebook profile image.

3. Created a migration script (`test/migrate_facebook_profile_images.php`) to migrate any existing Facebook profile images from the `users` table to the `images` table, ensuring consistent storage of all profile images.

## Files Modified
- `core/Auth.php`
- `controllers/UserController.php`

## Files Created
- `test/migrate_facebook_profile_images.php`
- `docs/facebook_profile_sync.md`

## How to Test
1. Log in with a user account that has a Facebook connection.
2. Go to the user profile page.
3. Click the "Sync with Facebook" button.
4. Verify that the Facebook profile image is displayed correctly.
5. Check the database to ensure that the image is stored in the `images` table and the `profile_image` field in the `users` table is NULL.

## Migration Script
A migration script has been created to migrate existing Facebook profile images from the `users` table to the `images` table. This script should be run once to ensure all profile images are stored consistently.

To run the migration script:
1. From the command line: `php test/migrate_facebook_profile_images.php`
2. Or, as an administrator, navigate to `/test/migrate_facebook_profile_images.php` in your browser.

The script will:
1. Find all users with Facebook profile images (URLs containing "fbsbx.com").
2. Download each image from Facebook.
3. Store the image in the `images` table with entity type "user".
4. Clear the `profile_image` field in the `users` table.

## Notes
- The migration script is designed to be run once, but it can be run multiple times if needed.
- The script will only process users with Facebook profile images (URLs containing "fbsbx.com").
- The script will log its progress and report any errors.
- After running the script, all profile images should be stored consistently in the `images` table.

## Benefits of the New Storage Approach
- **Consistency**: Single approach to image storage simplifies code and reduces bugs
- **Performance**: No dependency on external URLs that might be slow or unavailable
- **Security**: Better control over image content and reduced risk from external sources
- **Portability**: All images are stored locally, making the application more portable
- **Maintainability**: Easier to manage and update image handling code
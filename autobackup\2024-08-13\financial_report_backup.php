<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/coordinator/reports">Reports</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Financial Report</li>
                </ol>
            </nav>
            
            <h1><?php echo $data['title']; ?></h1>
            
            <?php flash('report_message'); ?>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Financial Statistics</h5>
                    <div>
                        <form action="<?php echo URLROOT; ?>/coordinator/financialReport" method="get" class="form-inline">
                            <div class="input-group">
                                <select name="show_id" class="form-control">
                                    <?php foreach ($data['shows'] as $show) : ?>
                                        <option value="<?php echo $show->id; ?>" <?php echo ($show->id == $data['show']->id) ? 'selected' : ''; ?>>
                                            <?php echo $show->name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="input-group-append">
                                    <button type="submit" class="btn btn-light">View Report</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-success">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Revenue</h5>
                                    <h2 class="display-4">$<?php echo number_format($data['total_revenue'], 2); ?></h2>
                                    <p class="text-muted">From approved registrations</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-8 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">Revenue by Day</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Revenue</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['revenue_by_day'] as $day) : ?>
                                                <tr>
                                                    <td><?php echo date('F j, Y', strtotime($day->date)); ?></td>
                                                    <td>$<?php echo number_format($day->revenue, 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">Revenue by Category</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Category</th>
                                                    <th>Registrations</th>
                                                    <th>Revenue</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['revenue_by_category'] as $category) : ?>
                                                <tr>
                                                    <td><?php echo $category->name; ?></td>
                                                    <td><?php echo $category->count; ?></td>
                                                    <td>$<?php echo number_format($category->revenue, 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header">
                                    <h5 class="mb-0">Revenue by Payment Method</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Payment Method</th>
                                                    <th>Registrations</th>
                                                    <th>Revenue</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($data['revenue_by_payment_method'] as $method) : ?>
                                                <tr>
                                                    <td><?php echo $method->name; ?></td>
                                                    <td><?php echo $method->count; ?></td>
                                                    <td>$<?php echo number_format($method->revenue, 2); ?></td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo URLROOT; ?>/coordinator/reports" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Reports
                        </a>
                        <a href="<?php echo URLROOT; ?>/coordinator/exportRegistrations/<?php echo $data['show']->id; ?>" class="btn btn-success">
                            <i class="fas fa-file-export"></i> Export Data
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
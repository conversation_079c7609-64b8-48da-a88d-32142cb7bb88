<!DOCTYPE html>
<html>
<head>
    <title>Version Only Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h2>Version Only Test</h2>
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const className = type === 'error' ? 'log error' : type === 'success' ? 'log success' : 'log';
            logs.innerHTML += '<div class="' + className + '">' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Override console.log to capture all messages
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            log('CONSOLE: ' + args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            log('ERROR: ' + args.join(' '), 'error');
            originalError.apply(console, args);
        };

        // Test immediately after script loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                log('=== TESTING AFTER PAGE LOAD ===');
                if (window.cameraBanner) {
                    log('cameraBanner exists: YES', 'success');
                    log('cameraBanner.version: ' + window.cameraBanner.version);
                    log('typeof cameraBanner.version: ' + typeof window.cameraBanner.version);
                    
                    // Try to set version manually
                    log('Trying to set version manually...');
                    window.cameraBanner.version = 'MANUAL_SET_TEST';
                    log('After manual set: ' + window.cameraBanner.version);
                    
                    // Check all properties
                    log('All properties:');
                    for (let prop in window.cameraBanner) {
                        if (window.cameraBanner.hasOwnProperty(prop)) {
                            log('  ' + prop + ': ' + window.cameraBanner[prop]);
                        }
                    }
                } else {
                    log('cameraBanner does not exist', 'error');
                }
            }, 1000);
        });
    </script>
    
    <!-- Load the banner system -->
    <script src="/public/js/camera-banner.js"></script>
</body>
</html>
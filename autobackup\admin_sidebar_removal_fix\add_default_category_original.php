<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Add Default Category</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/defaultCategories" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Default Categories
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <?php require APPROOT . '/views/includes/admin_settings_sidebar.php'; ?>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Add Default Category</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/admin/addDefaultCategory" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback"><?php echo $name_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo $description; ?></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="registration_fee" class="form-label">Registration Fee ($)</label>
                                    <input type="number" class="form-control <?php echo (!empty($registration_fee_err)) ? 'is-invalid' : ''; ?>" id="registration_fee" name="registration_fee" value="<?php echo $registration_fee; ?>" step="0.01" min="0">
                                    <div class="invalid-feedback"><?php echo $registration_fee_err; ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_entries" class="form-label">Max Entries (0 for unlimited)</label>
                                    <input type="number" class="form-control <?php echo (!empty($max_entries_err)) ? 'is-invalid' : ''; ?>" id="max_entries" name="max_entries" value="<?php echo $max_entries; ?>" min="0">
                                    <div class="invalid-feedback"><?php echo $max_entries_err; ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo BASE_URL; ?>/admin/defaultCategories" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Add Default Category</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
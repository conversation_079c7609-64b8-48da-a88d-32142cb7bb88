-- PWA Features Database Migration
-- Add tables and columns needed for Progressive Web App functionality

-- Add PWA-related columns to users table (with existence checks)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'pwa_installed') = 0,
    'ALTER TABLE users ADD COLUMN pwa_installed TINYINT(1) DEFAULT 0 COMMENT ''Whether user has installed the PWA''',
    'SELECT ''Column pwa_installed already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'pwa_standalone') = 0,
    'ALTER TABLE users ADD COLUMN pwa_standalone TINYINT(1) DEFAULT 0 COMMENT ''Whether user is using PWA in standalone mode''',
    'SELECT ''Column pwa_standalone already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'push_supported') = 0,
    'ALTER TABLE users ADD COLUMN push_supported TINYINT(1) DEFAULT 0 COMMENT ''Whether user device supports push notifications''',
    'SELECT ''Column push_supported already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND COLUMN_NAME = 'last_seen') = 0,
    'ALTER TABLE users ADD COLUMN last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT ''Last time user was active''',
    'SELECT ''Column last_seen already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create push subscriptions table
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notification preferences table
CREATE TABLE IF NOT EXISTS notification_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_reminders TINYINT(1) DEFAULT 1,
    registration_updates TINYINT(1) DEFAULT 1,
    judging_reminders TINYINT(1) DEFAULT 1,
    payment_notifications TINYINT(1) DEFAULT 1,
    new_events TINYINT(1) DEFAULT 0,
    marketing TINYINT(1) DEFAULT 0,
    push_enabled TINYINT(1) DEFAULT 1,
    email_enabled TINYINT(1) DEFAULT 1,
    sms_enabled TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create offline sync queue table
CREATE TABLE IF NOT EXISTS offline_sync_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    sync_type ENUM('registration', 'payment', 'scoring', 'image_upload', 'form_data') NOT NULL,
    sync_data JSON NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_sync_type (sync_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create PWA install metrics table
CREATE TABLE IF NOT EXISTS pwa_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    installed_users INT DEFAULT 0,
    push_subscribers INT DEFAULT 0,
    offline_usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (metric_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notification log table for tracking sent notifications
CREATE TABLE IF NOT EXISTS notification_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    notification_type ENUM('push', 'email', 'sms', 'in_app') NOT NULL,
    title VARCHAR(255),
    message TEXT,
    status ENUM('sent', 'delivered', 'failed', 'clicked') DEFAULT 'sent',
    metadata JSON,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_type (user_id, notification_type),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add PWA settings to system_settings table
INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_enabled', '1', 'Enable Progressive Web App features', 'boolean', NOW()),
('push_notifications_enabled', '1', 'Enable push notifications', 'boolean', NOW()),
('offline_mode_enabled', '1', 'Enable offline functionality', 'boolean', NOW()),
('pwa_app_name', 'RER Events', 'PWA application name', 'text', NOW()),
('pwa_short_name', 'RER Events', 'PWA short name', 'text', NOW()),
('pwa_description', 'Rowan Elite Rides Events & Shows Management', 'PWA description', 'text', NOW()),
('pwa_theme_color', '#dc3545', 'PWA theme color', 'color', NOW()),
('pwa_background_color', '#ffffff', 'PWA background color', 'color', NOW()),
('vapid_public_key', '', 'VAPID public key for push notifications', 'text', NOW()),
('vapid_private_key', '', 'VAPID private key for push notifications (encrypted)', 'password', NOW())
ON DUPLICATE KEY UPDATE 
    setting_value = VALUES(setting_value),
    updated_at = NOW();

-- Create indexes for better performance (with existence checks)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_users_pwa_installed') = 0,
    'CREATE INDEX idx_users_pwa_installed ON users(pwa_installed)',
    'SELECT ''Index idx_users_pwa_installed already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'users' 
     AND INDEX_NAME = 'idx_users_last_seen') = 0,
    'CREATE INDEX idx_users_last_seen ON users(last_seen)',
    'SELECT ''Index idx_users_last_seen already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'push_subscriptions' 
     AND INDEX_NAME = 'idx_push_subscriptions_endpoint') = 0,
    'CREATE INDEX idx_push_subscriptions_endpoint ON push_subscriptions(endpoint(100))',
    'SELECT ''Index idx_push_subscriptions_endpoint already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create view for PWA analytics
CREATE OR REPLACE VIEW pwa_analytics AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_users,
    COUNT(CASE WHEN pwa_installed = 1 THEN 1 END) as installed_users,
    COUNT(CASE WHEN pwa_standalone = 1 THEN 1 END) as standalone_users,
    COUNT(CASE WHEN push_supported = 1 THEN 1 END) as push_supported_users,
    COUNT(CASE WHEN last_seen > DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users_7d,
    COUNT(CASE WHEN last_seen > DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as active_users_30d
FROM users 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY)
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Create stored procedure for cleaning up old sync queue items
DROP PROCEDURE IF EXISTS CleanupSyncQueue;
DELIMITER //
CREATE PROCEDURE CleanupSyncQueue()
BEGIN
    -- Delete completed items older than 7 days
    DELETE FROM offline_sync_queue 
    WHERE status = 'completed' 
    AND processed_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- Delete failed items older than 30 days
    DELETE FROM offline_sync_queue 
    WHERE status = 'failed' 
    AND updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Mark items as failed if they've exceeded max attempts and are older than 1 hour
    UPDATE offline_sync_queue 
    SET status = 'failed', 
        error_message = 'Exceeded maximum retry attempts'
    WHERE status = 'pending' 
    AND attempts >= max_attempts 
    AND created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR);
END //
DELIMITER ;

-- Create stored procedure for updating PWA metrics
DROP PROCEDURE IF EXISTS UpdatePWAMetrics;
DELIMITER //
CREATE PROCEDURE UpdatePWAMetrics()
BEGIN
    DECLARE today DATE DEFAULT CURDATE();
    
    INSERT INTO pwa_metrics (
        metric_date,
        total_users,
        active_users,
        installed_users,
        push_subscribers,
        offline_usage_count
    )
    SELECT 
        today,
        COUNT(*),
        COUNT(CASE WHEN last_seen > DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END),
        COUNT(CASE WHEN pwa_installed = 1 THEN 1 END),
        (SELECT COUNT(*) FROM push_subscriptions WHERE active = 1),
        (SELECT COUNT(*) FROM offline_sync_queue WHERE DATE(created_at) = today)
    FROM users
    ON DUPLICATE KEY UPDATE
        total_users = VALUES(total_users),
        active_users = VALUES(active_users),
        installed_users = VALUES(installed_users),
        push_subscribers = VALUES(push_subscribers),
        offline_usage_count = VALUES(offline_usage_count),
        updated_at = NOW();
END //
DELIMITER ;

-- Create event to run cleanup procedures daily
DROP EVENT IF EXISTS daily_pwa_maintenance;
CREATE EVENT daily_pwa_maintenance
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    CALL CleanupSyncQueue();
    CALL UpdatePWAMetrics();
END;

-- Enable event scheduler if not already enabled
SET GLOBAL event_scheduler = ON;

-- Insert default notification preferences for existing users
INSERT INTO notification_preferences (user_id, event_reminders, registration_updates, judging_reminders, payment_notifications)
SELECT id, 1, 1, 1, 1 
FROM users 
WHERE id NOT IN (SELECT user_id FROM notification_preferences);

-- Add comment to track migration
INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_migration_version', '1.0.0', 'PWA features migration version', 'text', NOW())
ON DUPLICATE KEY UPDATE 
    setting_value = '1.0.0',
    updated_at = NOW();
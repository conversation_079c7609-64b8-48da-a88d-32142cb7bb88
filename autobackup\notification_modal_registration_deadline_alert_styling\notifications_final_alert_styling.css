/* Registration deadline notification positioning fix - FINAL ALERT STYLING WITH TOGGLE POSITIONING */
#notify_registration_end_container {
    /* Match alert styling for consistent width and padding */
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #055160;
    
    /* Remove previous positioning */
    margin-left: 0;
    
    /* Use flexbox to position toggle at the end */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Keep the toggle switch in its original position */
#notify_registration_end_container .form-check-input {
    margin-left: auto;
    margin-right: 0;
}

/* Adjust label to take available space */
#notify_registration_end_container .form-check-label {
    flex: 1;
    margin-right: 1rem;
}

/* 
This CSS rule provides:
- Full alert-style formatting matching Bootstrap's .alert.alert-info
- Padding: 0.75rem 1rem (12px top/bottom, 16px left/right)
- Margin-bottom: 1rem (16px) for consistent spacing
- Border: 1px solid transparent with 0.375rem border-radius
- Background: Light blue tint (rgba(13, 202, 240, 0.1))
- Border color: Subtle blue border (rgba(13, 202, 240, 0.2))
- Text color: Dark blue (#055160) for good contrast
- Full width container matching other alert elements
- Flexbox layout to keep toggle in original position while expanding container
- Label takes available space, toggle positioned at the end
*/
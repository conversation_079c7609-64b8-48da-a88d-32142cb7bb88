    public function getEventsWithLocation($filters = [])
    {
        try {
            // Start building the SQL query
            $sql = "SELECT e.*, c.name as calendar_name, s.name as show_name
                    FROM calendar_events e
                    LEFT JOIN calendars c ON e.calendar_id = c.id
                    LEFT JOIN shows s ON e.show_id = s.id
                    WHERE 1=1";
            
            $params = [];
            
            // Filter by start date
            if (isset($filters['start'])) {
                $sql .= " AND e.end_date >= :start";
                $params[':start'] = $filters['start'];
            }
            
            // Filter by end date
            if (isset($filters['end'])) {
                $sql .= " AND e.start_date <= :end";
                $params[':end'] = $filters['end'];
            }
            
            // Filter by calendar ID
            if (isset($filters['calendar_id'])) {
                $sql .= " AND e.calendar_id = :calendar_id";
                $params[':calendar_id'] = $filters['calendar_id'];
            }
            
            // Filter by multiple calendar IDs
            if (isset($filters['calendar_ids']) && is_array($filters['calendar_ids']) && !empty($filters['calendar_ids'])) {
                // Use a different approach to avoid parameter numbering issues
                $placeholders = [];
                $calendarIdParams = [];
                
                foreach ($filters['calendar_ids'] as $i => $id) {
                    $paramName = ":cal_id_" . $i;
                    $placeholders[] = $paramName;
                    $calendarIdParams[$paramName] = $id;
                }
                
                $sql .= " AND e.calendar_id IN (" . implode(', ', $placeholders) . ")";
                $params = array_merge($params, $calendarIdParams);
            }
            
            // Filter by state
            if (isset($filters['state'])) {
                $sql .= " AND e.state = :state";
                $params[':state'] = $filters['state'];
            }
            
            // Location-based filtering using Haversine formula
            if (isset($filters['radius']) && isset($filters['lat']) && isset($filters['lng'])) {
                // Earth's radius in miles
                $earthRadius = 3959;
                
                // Haversine formula to calculate distance
                $sql .= " AND (
                    $earthRadius * acos(
                        cos(radians(:latitude)) * 
                        cos(radians(e.lat)) * 
                        cos(radians(e.lng) - radians(:longitude)) + 
                        sin(radians(:latitude)) * 
                        sin(radians(e.lat))
                    ) <= :search_radius
                )";
                
                // Use different parameter names to avoid confusion
                $params[':latitude'] = $filters['lat'];
                $params[':longitude'] = $filters['lng'];
                $params[':search_radius'] = $filters['radius'];
            }
            
            // Only include events with location data
            $sql .= " AND (e.address1 IS NOT NULL OR e.city IS NOT NULL OR e.state IS NOT NULL)";
            
            // Order by start date
            $sql .= " ORDER BY e.start_date ASC";
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("CalendarModel::getEventsWithLocation - SQL: " . $sql);
                error_log("CalendarModel::getEventsWithLocation - Params: " . json_encode($params));
            }
            
            $this->db->query($sql);
            
            // Bind all parameters
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsWithLocation: ' . $e->getMessage());
            throw $e;
        }
    }
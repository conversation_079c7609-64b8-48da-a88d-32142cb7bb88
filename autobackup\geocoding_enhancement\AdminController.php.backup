<?php
/**
 * Admin Controller
 * 
 * This controller handles all admin-related functionality.
 */
class AdminController extends Controller {
    private $userModel;
    private $showModel;
    private $settingsModel;
    private $vehicleModel;
    private $registrationModel;
    private $judgingModel;
    private $metricModel;
    private $categoryModel;
    private $defaultCategoryModel;
    private $defaultMetricModel;
    private $defaultAgeWeightModel;
    private $awardModel;
    private $calendarModel;
    private $auth;
    private $db;

    
    /**
     * This function ensures that textarea fields are properly included in the form data
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processTextareaField($data, $fieldId) {
        // If the field exists in POST data, use it
        if (isset($_POST[$fieldId])) {
            $data[$fieldId] = $_POST[$fieldId];
            error_log("AdminController: Added textarea field {$fieldId} from POST data with length: " . strlen($_POST[$fieldId]));
        } 
        // Otherwise, set it to an empty string to ensure it's included in the update
        else {
            $data[$fieldId] = '';
            error_log("AdminController: Added empty textarea field {$fieldId}");
        }
        
        return $data;
    }
    
    /**
     * This function ensures that checkbox fields are properly converted to integers (0 or 1)
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processCheckboxField($data, $fieldId) {
        // If the field exists in POST data, set it to 1
        if (isset($_POST[$fieldId])) {
            $data[$fieldId] = 1;
            error_log("AdminController: Added checkbox field {$fieldId} with value 1");
        } 
        // Otherwise, set it to 0
        else {
            $data[$fieldId] = 0;
            error_log("AdminController: Added checkbox field {$fieldId} with value 0");
        }
        
        return $data;
    }
    
    /**
     * This function ensures that checkbox group fields are properly processed
     * 
     * @param array $data Form data
     * @param string $fieldId Field ID
     * @return array Updated form data
     */
    private function processCheckboxGroupField($data, $fieldId) {
        error_log("AdminController: Processing checkbox group field {$fieldId}");
        
        // Get the options for this field
        $options = [];
        foreach ($_POST as $key => $value) {
            // Check if the key starts with the field ID and has an underscore
            if (strpos($key, $fieldId . '_') === 0) {
                $optionValue = substr($key, strlen($fieldId) + 1);
                $options[$optionValue] = $value;
                error_log("AdminController: Found option {$optionValue} with value {$value}");
            }
        }
        
        // If no options were found, return the data as is
        if (empty($options)) {
            error_log("AdminController: No options found for checkbox group field {$fieldId}");
            $data[$fieldId] = '[]';
            return $data;
        }
        
        // Process the selected values
        $selectedValues = [];
        foreach ($options as $optionValue => $value) {
            if ($value) {
                $selectedValues[] = $optionValue;
            }
        }
        
        error_log("AdminController: Selected values: " . json_encode($selectedValues));
        
        // Store the selected values as a JSON string
        $data[$fieldId] = json_encode($selectedValues);
        error_log("AdminController: Stored checkbox group values for {$fieldId}: {$data[$fieldId]}");
        
        return $data;
    }
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    private function generateCsrfToken() {
        // Use the global function from csrf_helper.php
        return generateCsrfToken();
    }
    
    /**
     * Sanitize input data
     * 
     * @param array $data Data to sanitize
     * @return array Sanitized data
     */
    protected function sanitizeInput($data) {
        $result = [];
        
        // Try to load field types from the database
        $fieldTypes = $this->getFieldTypes();
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Check if we know the field type
                $fieldType = isset($fieldTypes[$key]) ? $fieldTypes[$key] : $this->guessFieldType($key);
                
                // Handle different field types
                switch ($fieldType) {
                    case 'html':
                    case 'textarea':
                    case 'wysiwyg':
                        // Allow HTML content but sanitize it
                        $result[$key] = $this->sanitizeHtml($value);
                        break;
                    case 'email':
                        // Sanitize email
                        $result[$key] = filter_var($value, FILTER_SANITIZE_EMAIL);
                        break;
                    case 'url':
                        // Sanitize URL
                        $result[$key] = filter_var($value, FILTER_SANITIZE_URL);
                        break;
                    default:
                        // Default sanitization for other field types
                        $result[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
                        break;
                }
            } elseif (is_array($value)) {
                // Recursively sanitize arrays
                $result[$key] = $this->sanitizeInput($value);
            } else {
                // For non-string, non-array values, just copy them
                $result[$key] = $value;
            }
        }
        
        return $result;
    }
    
    /**
     * Sanitize HTML content
     * 
     * @param string $html HTML content to sanitize
     * @return string Sanitized HTML
     */
    private function sanitizeHtml($html) {
        // Use HTMLPurifier if available
        if (class_exists('HTMLPurifier')) {
            $config = HTMLPurifier_Config::createDefault();
            $purifier = new HTMLPurifier($config);
            return $purifier->purify($html);
        }
        
        // Otherwise, use a basic sanitization
        $allowed_tags = '<p><br><b><i><u><strong><em><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><table><tr><td><th><thead><tbody><tfoot><span><div><hr><blockquote><pre><code>';
        return strip_tags($html, $allowed_tags);
    }
    
    /**
     * Get field types from the database
     * 
     * @return array Field types
     */
    private function getFieldTypes() {
        $fieldTypes = [];
        
        try {
            // Try to load field types from the database
            $sql = "SELECT field_id, field_type FROM form_fields";
            $this->db->query($sql);
            $results = $this->db->resultSet();
            
            foreach ($results as $result) {
                $fieldTypes[$result->field_id] = $result->field_type;
            }
        } catch (Exception $e) {
            // If there's an error, just return an empty array
            error_log("Error loading field types: " . $e->getMessage());
        }
        
        return $fieldTypes;
    }
    
    /**
     * Guess field type based on field ID
     * 
     * @param string $fieldId Field ID
     * @return string Field type
     */
    private function guessFieldType($fieldId) {
        // Check for common field ID patterns
        if (strpos($fieldId, 'email') !== false) {
            return 'email';
        } elseif (strpos($fieldId, 'url') !== false || strpos($fieldId, 'website') !== false) {
            return 'url';
        } elseif (strpos($fieldId, 'description') !== false || strpos($fieldId, 'content') !== false) {
            return 'textarea';
        } elseif (strpos($fieldId, 'html') !== false || strpos($fieldId, 'wysiwyg') !== false) {
            return 'html';
        } elseif (strpos($fieldId, 'date') !== false) {
            return 'date';
        } elseif (strpos($fieldId, 'time') !== false) {
            return 'time';
        } elseif (strpos($fieldId, 'password') !== false) {
            return 'password';
        } elseif (strpos($fieldId, 'checkbox') !== false) {
            return 'checkbox';
        } elseif (strpos($fieldId, 'radio') !== false) {
            return 'radio';
        } elseif (strpos($fieldId, 'select') !== false) {
            return 'select';
        } elseif (strpos($fieldId, 'file') !== false || strpos($fieldId, 'image') !== false) {
            return 'file';
        } elseif (strpos($fieldId, 'number') !== false || strpos($fieldId, 'price') !== false || strpos($fieldId, 'cost') !== false) {
            return 'number';
        } elseif (strpos($fieldId, 'phone') !== false) {
            return 'tel';
        } elseif (strpos($fieldId, 'color') !== false) {
            return 'color';
        } elseif (strpos($fieldId, 'range') !== false) {
            return 'range';
        } elseif (strpos($fieldId, 'search') !== false) {
            return 'search';
        } elseif (strpos($fieldId, 'month') !== false) {
            return 'month';
        } elseif (strpos($fieldId, 'week') !== false) {
            return 'week';
        } elseif (strpos($fieldId, 'datetime') !== false) {
            return 'datetime';
        }
        
        // Default to text
        return 'text';
    }
    
    /**
     * Scheduled Tasks
     */
    public function scheduled_tasks() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Scheduled Tasks'
        ];
        
        $this->view('admin/scheduled_tasks', $data);
    }
    
    /**
     * Get Task
     * 
     * @param int $id Task ID
     */
    public function getTask($id) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get task
        $task = $this->settingsModel->getTaskById($id);
        
        if (!$task) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Return task as JSON
        header('Content-Type: application/json');
        echo json_encode($task);
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is an admin
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Initialize database connection
        $this->db = new Database();
        
        // Initialize UserModel
        $this->userModel = $this->model('UserModel');
        
        // Initialize ShowModel
        $this->showModel = $this->model('ShowModel');
        
        // Initialize SettingsModel
        $this->settingsModel = $this->model('SettingsModel');
        
        // Initialize VehicleModel
        $this->vehicleModel = $this->model('VehicleModel');
        
        // Initialize RegistrationModel
        $this->registrationModel = $this->model('RegistrationModel');
        
        // Initialize JudgingModel
        $this->judgingModel = $this->model('JudgingModel');
        
        // Initialize MetricModel
        $this->metricModel = $this->model('MetricModel');
        
        // Initialize CategoryModel
        $this->categoryModel = $this->model('CategoryModel');
        
        // Initialize DefaultCategoryModel
        $this->defaultCategoryModel = $this->model('DefaultCategoryModel');
        
        // Initialize DefaultMetricModel
        $this->defaultMetricModel = $this->model('DefaultMetricModel');
        
        // Initialize DefaultAgeWeightModel
        $this->defaultAgeWeightModel = $this->model('DefaultAgeWeightModel');
        
        // Initialize AwardModel
        $this->awardModel = $this->model('AwardModel');
        
        // Initialize CalendarModel
        $this->calendarModel = $this->model('CalendarModel');
        
        // Create tables if they don't exist
        $this->defaultCategoryModel->createTableIfNotExists();
        $this->defaultMetricModel->createTableIfNotExists();
        $this->defaultAgeWeightModel->createTableIfNotExists();
    }
    
    /**
     * Admin Dashboard
     */
    public function index() {
        $data = [
            'title' => 'Admin Dashboard'
        ];
        
        $this->view('admin/dashboard', $data);
    }
    
    /**
     * Impersonate User
     */
    public function impersonateUser() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Impersonate User',
            'users' => $this->userModel->getUsers()
        ];
        
        $this->view('admin/impersonate_user', $data);
    }
    
    /**
     * Start Impersonation
     * 
     * @param int $userId User ID to impersonate
     */
    public function startImpersonation($userId = null) {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // If no user ID was provided, check if it was submitted via POST
        if ($userId === null && isset($_POST['user_id'])) {
            $userId = $_POST['user_id'];
        }
        
        // If still no user ID, redirect to impersonate user page
        if ($userId === null) {
            $this->redirect('admin/impersonateUser');
            return;
        }
        
        // Get user
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            setFlashMessage('admin_error', 'User not found', 'danger');
            $this->redirect('admin/impersonateUser');
            return;
        }
        
        // Store original user ID in session
        $_SESSION['original_user_id'] = $_SESSION['user_id'];
        $_SESSION['original_user_name'] = $_SESSION['user_name'];
        $_SESSION['original_user_email'] = $_SESSION['user_email'];
        $_SESSION['original_user_role'] = $_SESSION['user_role'];
        
        // Set session variables for impersonated user
        $_SESSION['user_id'] = $user->id;
        $_SESSION['user_name'] = $user->name;
        $_SESSION['user_email'] = $user->email;
        $_SESSION['user_role'] = $user->role;
        $_SESSION['is_impersonating'] = true;
        
        // Redirect to user dashboard
        setFlashMessage('user_message', 'You are now impersonating ' . $user->name, 'info');
        $this->redirect('users/dashboard');
    }
    
    /**
     * End Impersonation
     */
    public function endImpersonation() {
        // Check if user is impersonating
        if (!isset($_SESSION['is_impersonating']) || !$_SESSION['is_impersonating']) {
            $this->redirect('home');
            return;
        }
        
        // Restore original user session
        $_SESSION['user_id'] = $_SESSION['original_user_id'];
        $_SESSION['user_name'] = $_SESSION['original_user_name'];
        $_SESSION['user_email'] = $_SESSION['original_user_email'];
        $_SESSION['user_role'] = $_SESSION['original_user_role'];
        
        // Remove impersonation flags and original user data
        unset($_SESSION['is_impersonating']);
        unset($_SESSION['original_user_id']);
        unset($_SESSION['original_user_name']);
        unset($_SESSION['original_user_email']);
        unset($_SESSION['original_user_role']);
        
        // Redirect to admin dashboard
        setFlashMessage('admin_message', 'Impersonation ended', 'info');
        $this->redirect('admin');
    }
    
    /**
     * Image Manager
     */
    public function imageManager() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get image settings
        $imageSettings = $this->settingsModel->getImageSettings();
        
        $data = [
            'title' => 'Image Manager',
            'imageSettings' => $imageSettings
        ];
        
        $this->view('admin/image_manager', $data);
    }
    
    /**
     * Image Library
     */
    public function imageLibrary() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $data = [
            'title' => 'Image Library'
        ];
        
        $this->view('admin/image_library', $data);
    }
    
    /**
     * Get media library contents as HTML
     */
    public function getMediaLibrary() {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            echo '<div class="alert alert-danger">Access denied</div>';
            return;
        }
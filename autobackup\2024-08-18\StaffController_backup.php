<?php
/**
 * Staff Controller
 * 
 * This controller handles all staff-related functionality.
 */
class StaffController extends Controller {
    // Relevant code snippet for the payment processing
    
    // Get form data
    $data = [
        'registration_id' => $id,
        'payment_method_id' => trim($_POST['payment_method_id']),
        'reference' => trim($_POST['reference']),
        'notes' => trim($_POST['notes']),
        'amount' => $show->registration_fee,
        'processed_by' => $userId,
        'payment_method_id_err' => '',
        'title' => 'Process Payment',
        'show' => $show,
        'registration' => $registration,
        'payment_methods' => $paymentMethods
    ];
    
    // Process payment
    if ($this->paymentModel->processManualPayment($data)) {
        // Update registration status to paid
        $this->registrationModel->updatePaymentStatus($id, 'paid');
        
        $this->setFlashMessage('staff_message', 'Payment processed successfully', 'success');
        $this->redirect('staff/registration/' . $id);
    }
}
# Show Event Editing Fix

This update prevents direct editing of events that are linked to shows:

1. Modified the event.php view to hide the "Edit Event" button for events linked to shows
2. Added an "Edit Show" button instead, which redirects to the show edit page
3. Added a message explaining that the event is linked to a show
4. Modified the CalendarController to prevent direct editing of events linked to shows
5. Modified the CalendarController to prevent direct deletion of events linked to shows

## Files Modified

- `views/calendar/event.php` - Modified the edit button section
- `controllers/CalendarController.php` - Added checks to prevent editing/deleting events linked to shows

## Date

Fix date: 2025-08-25
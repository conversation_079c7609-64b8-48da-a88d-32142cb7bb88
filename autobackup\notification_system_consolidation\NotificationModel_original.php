<?php
class NotificationModel {
    private $db;
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get user notification preferences
     * 
     * @param int $userId User ID
     * @return object|false User preferences or false if not found
     */
    public function getUserPreferences($userId) {
        $this->db->query('SELECT * FROM user_notification_preferences WHERE user_id = :user_id');
        $this->db->bind(':user_id', $userId);
        
        $result = $this->db->single();
        
        // If no preferences exist, create default ones
        if (!$result) {
            $this->createDefaultPreferences($userId);
            return $this->getUserPreferences($userId);
        }
        
        return $result;
    }
    
    /**
     * Update user notification preferences
     * 
     * @param int $userId User ID
     * @param array $preferences Preference data
     * @return bool Success status
     */
    public function updateUserPreferences($userId, $preferences) {
        $this->db->query('UPDATE user_notification_preferences SET 
                          email_notifications = :email_notifications,
                          sms_notifications = :sms_notifications,
                          push_notifications = :push_notifications,
                          toast_notifications = :toast_notifications,
                          updated_at = NOW()
                          WHERE user_id = :user_id');
        
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':email_notifications', $preferences['email_notifications'] ? 1 : 0);
        $this->db->bind(':sms_notifications', $preferences['sms_notifications'] ? 1 : 0);
        $this->db->bind(':push_notifications', $preferences['push_notifications'] ? 1 : 0);
        $this->db->bind(':toast_notifications', $preferences['toast_notifications'] ? 1 : 0);
        
        return $this->db->execute();
    }
    
    /**
     * Create default notification preferences for a user
     * 
     * @param int $userId User ID
     * @return bool Success status
     */
    public function createDefaultPreferences($userId) {
        $this->db->query('INSERT INTO user_notification_preferences 
                          (user_id, email_notifications, sms_notifications, push_notifications, toast_notifications) 
                          VALUES (:user_id, 1, 0, 1, 1)');
        $this->db->bind(':user_id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Subscribe user to event notifications
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type (calendar_event or show)
     * @param array $notificationTimes Array of minutes before event to notify
     * @return bool Success status
     */
    public function subscribeToEvent($userId, $eventId, $eventType, $notificationTimes = [1440, 60]) {
        // Check if subscription already exists
        $this->db->query('SELECT id FROM user_event_subscriptions 
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        if ($this->db->single()) {
            // Update existing subscription
            $this->db->query('UPDATE user_event_subscriptions SET 
                              notification_times = :notification_times,
                              updated_at = NOW()
                              WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type');
        } else {
            // Create new subscription
            $this->db->query('INSERT INTO user_event_subscriptions 
                              (user_id, event_id, event_type, notification_times) 
                              VALUES (:user_id, :event_id, :event_type, :notification_times)');
        }
        
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        $this->db->bind(':notification_times', json_encode($notificationTimes));
        
        return $this->db->execute();
    }
    
    /**
     * Unsubscribe user from event notifications
     * 
     * @param int $userId User ID
     * @param int $eventId Event ID
     * @param string $eventType Event type
     * @return bool Success status
     */
    public function unsubscribeFromEvent($userId, $eventId, $eventType) {
        $this->db->query('DELETE FROM user_event_subscriptions 
                          WHERE user_id = :user_id AND event_id = :event_id AND event_type = :event_type');
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':event_id', $eventId);
        $this->db->bind(':event_type', $eventType);
        
        return $this->db->execute();
    }
    
    /**
     * Get user's event subscriptions
     * 
     * @param int $userId User ID
     * @return array Array of subscription objects
     */
    public function getUserSubscriptions($userId) {
        $this->db->query('SELECT ues.*, 
                          CASE 
                            WHEN ues.event_type = "calendar_event" THEN ce.title
                            ELSE s.title
                          END as event_title,
                          CASE 
                            WHEN ues.event_type = "calendar_event" THEN ce.event_date
                            ELSE s.event_date
                          END as event_date
                          FROM user_event_subscriptions ues
                          LEFT JOIN calendar_events ce ON ues.event_id = ce.id AND ues.event_type = "calendar_event"
                          LEFT JOIN shows s ON ues.event_id = s.id AND ues.event_type = "show"
                          WHERE ues.user_id = :user_id
                          ORDER BY 
                            CASE 
                              WHEN ues.event_type = "calendar_event" THEN ce.event_date
                              ELSE s.event_date
                            END ASC');
        $this->db->bind(':user_id', $userId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get notification settings
     * 
     * @return array Settings array
     */
    public function getSettings() {
        $this->db->query('SELECT * FROM notification_settings LIMIT 1');
        $result = $this->db->single();
        
        if (!$result) {
            // Create default settings if none exist
            $this->initializeDefaultSettings();
            return $this->getSettings();
        }
        
        return $result;
    }
    
    /**
     * Initialize default notification settings
     * 
     * @return bool Success status
     */
    public function initializeDefaultSettings() {
        $this->db->query('INSERT IGNORE INTO notification_settings 
                          (email_enabled, sms_enabled, push_enabled, toast_enabled) 
                          VALUES (1, 1, 1, 1)');
        
        return $this->db->execute();
    }
    
    /**
     * Get specific notification setting
     * 
     * @param string $setting Setting name
     * @return mixed Setting value
     */
    public function getNotificationSettings($setting) {
        $this->db->query('SELECT ' . $setting . ' FROM notification_settings LIMIT 1');
        $result = $this->db->single();
        
        return $result ? $result->$setting : null;
    }
    
    /**
     * Queue a notification for sending
     * 
     * @param int $userId User ID
     * @param string $type Notification type
     * @param string $title Notification title
     * @param string $message Notification message
     * @param array $data Additional data
     * @return bool Success status
     */
    public function queueNotification($userId, $type, $title, $message, $data = []) {
        $this->db->query('INSERT INTO notification_queue 
                          (user_id, type, title, message, data, status) 
                          VALUES (:user_id, :type, :title, :message, :data, "pending")');
        
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':type', $type);
        $this->db->bind(':title', $title);
        $this->db->bind(':message', $message);
        $this->db->bind(':data', json_encode($data));
        
        return $this->db->execute();
    }
    
    /**
     * Get pending notifications for processing
     * 
     * @param int $limit Number of notifications to retrieve
     * @return array Array of notification objects
     */
    public function getPendingNotifications($limit = 50) {
        $this->db->query('SELECT * FROM notification_queue 
                          WHERE status = "pending" 
                          ORDER BY created_at ASC 
                          LIMIT :limit');
        $this->db->bind(':limit', $limit);
        
        return $this->db->resultSet();
    }
    
    /**
     * Mark notification as processed
     * 
     * @param int $notificationId Notification ID
     * @param string $status New status
     * @return bool Success status
     */
    public function updateNotificationStatus($notificationId, $status) {
        $this->db->query('UPDATE notification_queue SET 
                          status = :status,
                          processed_at = NOW()
                          WHERE id = :id');
        
        $this->db->bind(':status', $status);
        $this->db->bind(':id', $notificationId);
        
        return $this->db->execute();
    }
}
?>
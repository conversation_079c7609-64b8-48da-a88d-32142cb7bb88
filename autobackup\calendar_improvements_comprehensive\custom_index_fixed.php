<?php require APPROOT . '/views/includes/header.php'; ?>
<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
</script>
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js?v=<?php echo time(); ?>"></script>

<div class="container-fluid mt-4">
    <!-- Navigation Bar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Calendar</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group me-2">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-primary active">
                    <i class="fas fa-calendar me-2"></i> Calendar View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-outline-primary">
                    <i class="fas fa-map-marker-alt me-2"></i> Map View
                </a>
            </div>
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php if (isLoggedIn()): ?>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/createCalendar">
                        <i class="fas fa-plus me-2"></i>Create Calendar
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <?php endif; ?>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">Manage Calendars</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">Manage Venues</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">Manage Clubs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">Import Events</a></li>
                    <?php if (isAdmin()): ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">Calendar Settings</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Calendar Column (Full Width) -->
        <div class="col-12 mb-4">
            <!-- Advanced Filter -->
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Calendar Container -->
            <div class="card shadow-sm">
                <div class="card-body p-0">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>
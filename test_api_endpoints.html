<!DOCTYPE html>
<html>
<head>
    <title>Test API Endpoints</title>
</head>
<body>
    <h2>API Endpoints Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        async function testEndpoint(url, name) {
            output(`=== Testing ${name} ===`);
            output(`URL: ${url}`);
            
            try {
                const response = await fetch(url);
                output(`Status: ${response.status} ${response.statusText}`);
                
                if (response.ok) {
                    const data = await response.json();
                    output(`✓ Success: ${JSON.stringify(data, null, 2)}`);
                    return data;
                } else {
                    const text = await response.text();
                    output(`✗ Error Response: ${text}`);
                    return null;
                }
            } catch (error) {
                output(`✗ Fetch Error: ${error.message}`);
                return null;
            }
        }

        async function runTests() {
            // Test the camera banners API
            const cameraBannersData = await testEndpoint('/api/cameraBanners', 'Camera Banners API');
            
            if (cameraBannersData) {
                output('');
                output('=== Camera Banners Analysis ===');
                output(`- Success: ${cameraBannersData.success}`);
                output(`- Banners count: ${cameraBannersData.banners ? cameraBannersData.banners.length : 'undefined'}`);
                output(`- Delay: ${cameraBannersData.delay}`);
                
                if (cameraBannersData.banners && cameraBannersData.banners.length > 0) {
                    output('- Banners:');
                    cameraBannersData.banners.forEach((banner, i) => {
                        output(`  ${i + 1}. ID: ${banner.id}, Type: ${banner.type}, Text: "${banner.text}", Image: "${banner.image_path}", Logo: ${banner.is_logo}`);
                    });
                }
            }
            
            output('');
            
            // Test the site logo API
            await testEndpoint('/api/getSiteLogo', 'Site Logo API');
            
            output('');
            output('=== Test Complete ===');
        }

        // Run tests when page loads
        runTests();
    </script>
</body>
</html>
-- SAFE Performance Indexes for Dashboard and Management Optimization
-- This version checks for table and column existence before creating indexes
-- Run this to optimize queries for thousands of records across all optimized pages

-- ========================================
-- CORE REGISTRATIONS TABLE INDEXES
-- ========================================
SELECT 'Adding indexes to registrations table...' as status;

-- Core indexes that should always work
CREATE INDEX IF NOT EXISTS idx_registrations_show_id ON registrations(show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_id ON registrations(vehicle_id);
CREATE INDEX IF NOT EXISTS idx_registrations_category_id ON registrations(category_id);
CREATE INDEX IF NOT EXISTS idx_registrations_status ON registrations(status);
CREATE INDEX IF NOT EXISTS idx_registrations_created_at ON registrations(created_at);

-- Check if payment_status column exists before creating index
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'registrations' AND column_name = 'payment_status') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_registrations_payment_status ON registrations(payment_status)', 
    'SELECT "payment_status column not found in registrations" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check if updated_at column exists before creating index
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'registrations' AND column_name = 'updated_at') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_registrations_updated_at ON registrations(updated_at)', 
    'SELECT "updated_at column not found in registrations" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Composite indexes for complex registration queries
CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_show ON registrations(vehicle_id, show_id);
CREATE INDEX IF NOT EXISTS idx_registrations_status_date ON registrations(status, created_at);

-- ========================================
-- CORE SHOWS TABLE INDEXES
-- ========================================
SELECT 'Adding indexes to shows table...' as status;

CREATE INDEX IF NOT EXISTS idx_shows_start_date ON shows(start_date);
CREATE INDEX IF NOT EXISTS idx_shows_end_date ON shows(end_date);
CREATE INDEX IF NOT EXISTS idx_shows_status ON shows(status);
CREATE INDEX IF NOT EXISTS idx_shows_created_at ON shows(created_at);
CREATE INDEX IF NOT EXISTS idx_shows_name ON shows(name);

-- Check if coordinator_id column exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'shows' AND column_name = 'coordinator_id') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_shows_coordinator_id ON shows(coordinator_id)', 
    'SELECT "coordinator_id column not found in shows" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check if location column exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'shows' AND column_name = 'location') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_shows_location ON shows(location)', 
    'SELECT "location column not found in shows" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Composite indexes for coordinator dashboard
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'shows' AND column_name = 'coordinator_id') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_shows_coordinator_status ON shows(coordinator_id, status)', 
    'SELECT "coordinator_id column not found for composite index" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'shows' AND column_name = 'coordinator_id') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_shows_coordinator_date ON shows(coordinator_id, start_date)', 
    'SELECT "coordinator_id column not found for composite index" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

CREATE INDEX IF NOT EXISTS idx_shows_status_date ON shows(status, start_date);

-- ========================================
-- CORE VEHICLES TABLE INDEXES
-- ========================================
SELECT 'Adding indexes to vehicles table...' as status;

CREATE INDEX IF NOT EXISTS idx_vehicles_owner_id ON vehicles(owner_id);
CREATE INDEX IF NOT EXISTS idx_vehicles_make ON vehicles(make);
CREATE INDEX IF NOT EXISTS idx_vehicles_model ON vehicles(model);

-- Check if year column exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'vehicles' AND column_name = 'year') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_vehicles_year ON vehicles(year)', 
    'SELECT "year column not found in vehicles" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

CREATE INDEX IF NOT EXISTS idx_vehicles_make_model ON vehicles(make, model);
CREATE INDEX IF NOT EXISTS idx_vehicles_owner_make ON vehicles(owner_id, make);

-- ========================================
-- CORE USERS TABLE INDEXES
-- ========================================
SELECT 'Adding indexes to users table...' as status;

CREATE INDEX IF NOT EXISTS idx_users_name ON users(name);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- Check if status column exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'status') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)', 
    'SELECT "status column not found in users" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check if role column exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'users' AND column_name = 'role') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)', 
    'SELECT "role column not found in users" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- ========================================
-- SHOW CATEGORIES TABLE INDEXES
-- ========================================
SELECT 'Adding indexes to show_categories table...' as status;

CREATE INDEX IF NOT EXISTS idx_show_categories_show_id ON show_categories(show_id);
CREATE INDEX IF NOT EXISTS idx_show_categories_name ON show_categories(name);

-- ========================================
-- OPTIONAL PAYMENTS TABLE INDEXES
-- ========================================
SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'payments') > 0, 
    'SELECT "Adding indexes to payments table..." as status', 
    'SELECT "Payments table not found, skipping..." as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Only create payment indexes if table exists
SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'payments') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id)', 
    'SELECT "Skipping payments indexes" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'payments') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at)', 
    'SELECT "Skipping payments indexes" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'payments') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_amount ON payments(amount)', 
    'SELECT "Skipping payments indexes" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check for related_id column in payments
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'payments' AND column_name = 'related_id') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_related_id ON payments(related_id)', 
    'SELECT "related_id column not found in payments" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check for payment_status column in payments
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'payments' AND column_name = 'payment_status') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_payment_status ON payments(payment_status)', 
    'SELECT "payment_status column not found in payments" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- Check for payment_type column in payments
SET @sql = IF((SELECT COUNT(*) FROM information_schema.columns WHERE table_schema = DATABASE() AND table_name = 'payments' AND column_name = 'payment_type') > 0, 
    'CREATE INDEX IF NOT EXISTS idx_payments_payment_type ON payments(payment_type)', 
    'SELECT "payment_type column not found in payments" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

-- ========================================
-- ANALYZE CORE TABLES
-- ========================================
SELECT 'Analyzing core tables...' as status;

ANALYZE TABLE registrations;
ANALYZE TABLE shows;
ANALYZE TABLE vehicles;
ANALYZE TABLE users;
ANALYZE TABLE show_categories;

-- Analyze optional tables only if they exist
SET @sql = IF((SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'payments') > 0, 
    'ANALYZE TABLE payments', 
    'SELECT "Skipping payments analysis" as status');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SELECT 'Performance optimization complete!' as status;

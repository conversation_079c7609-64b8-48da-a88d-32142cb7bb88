<?php
/**
 * Apply Geocoding Fix
 * 
 * This script updates the CalendarController.php file to use the enhanced geocoding method.
 */

// Define file paths
$controllerFile = __DIR__ . '/../../controllers/CalendarController.php';
$backupFile = __DIR__ . '/CalendarController.php.backup.full';

// Create a full backup of the original file
if (!file_exists($backupFile)) {
    copy($controllerFile, $backupFile);
    echo "Created backup at: " . $backupFile . "\n";
}

// Read the file content
$content = file_get_contents($controllerFile);

// Update the version number
$content = str_replace(
    "Version 1.0.1 - Fixed deprecated FILTER_SANITIZE_STRING",
    "Version 1.0.2 - Fixed geocoding issues",
    $content
);

// Add the new feature to the list
$content = str_replace(
    "- Implemented import/export functionality",
    "- Implemented import/export functionality\n * - Fixed geocoding to use enhanced method with fallbacks",
    $content
);

// Define the old geocoding code patterns
$oldCreateEventGeocodingCode = <<<'EOD'
                // Create address array for geocoding
                $addressData = [
                    'address1' => $data['address1'],
                    'address2' => $data['address2'],
                    'city' => $data['city'],
                    'state' => $data['state'],
                    'zipcode' => $data['zipcode']
                ];
                
                // Attempt to geocode the address
                $coordinates = geocodeAddress($addressData, $mapSettings);
                
                // If geocoding was successful, update coordinates
                if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
                    $data['lat'] = $coordinates['lat'];
                    $data['lng'] = $coordinates['lng'];
                }
EOD;

// Define the new geocoding code
$newCreateEventGeocodingCode = <<<'EOD'
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'createEvent');
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for new event: " . 
                    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
                }
EOD;

// Define the old edit event geocoding code
$oldEditEventGeocodingCode = $oldCreateEventGeocodingCode;

// Define the new edit event geocoding code
$newEditEventGeocodingCode = <<<'EOD'
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for event ID {$id}: " . 
                    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
                }
EOD;

// Replace the geocoding code in createEvent method
$createEventPos = strpos($content, 'public function createEvent()');
if ($createEventPos !== false) {
    // Find the geocoding section within createEvent
    $startPos = strpos($content, $oldCreateEventGeocodingCode, $createEventPos);
    if ($startPos !== false) {
        $content = substr_replace($content, $newCreateEventGeocodingCode, $startPos, strlen($oldCreateEventGeocodingCode));
        echo "Updated geocoding code in createEvent method\n";
    } else {
        echo "Could not find geocoding code in createEvent method\n";
    }
} else {
    echo "Could not find createEvent method\n";
}

// Replace the geocoding code in editEvent method
$editEventPos = strpos($content, 'public function editEvent($id)');
if ($editEventPos !== false) {
    // Find the geocoding section within editEvent
    $startPos = strpos($content, $oldEditEventGeocodingCode, $editEventPos);
    if ($startPos !== false) {
        $content = substr_replace($content, $newEditEventGeocodingCode, $startPos, strlen($oldEditEventGeocodingCode));
        echo "Updated geocoding code in editEvent method\n";
    } else {
        echo "Could not find geocoding code in editEvent method\n";
    }
} else {
    echo "Could not find editEvent method\n";
}

// Write the updated content back to the file
if (file_put_contents($controllerFile, $content)) {
    echo "Successfully updated CalendarController.php\n";
} else {
    echo "Failed to write to CalendarController.php\n";
}

echo "Done!\n";
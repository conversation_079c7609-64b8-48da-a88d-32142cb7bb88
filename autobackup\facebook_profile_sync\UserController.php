<?php
/**
 * User Controller
 * 
 * This controller handles all user-related functionality.
 */
class UserController extends Controller {
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $showModel;
    private $judgingModel;
    private $vehicleScoringModel;
    private $auth;
    private $db;
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    private function generateCsrfToken() {
        // Use the global function from csrf_helper.php
        return generateCsrfToken();
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->showModel = $this->model('ShowModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->vehicleScoringModel = $this->model('VehicleScoringModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('user/dashboard');
    }
    
    /**
     * User dashboard
     */
    public function dashboard() {
        // Get user's vehicles
        $userId = $this->auth->getCurrentUserId();
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get user's registrations
        $registrations = $this->registrationModel->getUserRegistrations($userId);
        
        // Get upcoming shows
        $upcomingShows = $this->showModel->getUpcomingShows(5);
        
        // Ensure all variables are arrays
        if (!is_array($vehicles)) {
            $vehicles = [];
        }
        
        if (!is_array($registrations)) {
            $registrations = [];
        }
        
        if (!is_array($upcomingShows)) {
            $upcomingShows = [];
        }
        
        // Get user data
        $user = $this->userModel->getUserById($userId);
        
        $data = [
            'title' => 'Dashboard',
            'vehicles' => $vehicles,
            'registrations' => $registrations,
            'upcoming_shows' => $upcomingShows,
            'user' => $user
        ];
        
        $this->view('user/dashboard', $data);
    }
    
    /**
     * Profile page
     */
    public function profile() {
        // Get user
        $userId = $this->auth->getCurrentUserId();
        $user = $this->userModel->getUserById($userId);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'id' => $userId,
                'name' => trim($_POST['name']),
                'phone' => isset($_POST['phone']) ? trim($_POST['phone']) : null,
                'address' => isset($_POST['address']) ? trim($_POST['address']) : null,
                'city' => isset($_POST['city']) ? trim($_POST['city']) : null,
                'state' => isset($_POST['state']) ? trim($_POST['state']) : null,
                'zip' => isset($_POST['zip']) ? trim($_POST['zip']) : null,
                'password' => trim($_POST['password']),
                'confirm_password' => trim($_POST['confirm_password']),
                'name_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Profile',
                'user' => $user
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter your name';
            }
            
            // Validate password (only if provided)
            if (!empty($data['password'])) {
                if (strlen($data['password']) < 6) {
                    $data['password_err'] = 'Password must be at least 6 characters';
                }
                
                if (empty($data['confirm_password'])) {
                    $data['confirm_password_err'] = 'Please confirm password';
                } elseif ($data['password'] != $data['confirm_password']) {
                    $data['confirm_password_err'] = 'Passwords do not match';
                }
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['password_err']) && 
                empty($data['confirm_password_err'])) {
                
                // Update profile
                if ($this->userModel->updateProfile($data)) {
                    // Redirect to dashboard
                    $this->redirect('user/dashboard');
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $this->view('user/profile', $data);
            }
        } else {
            // Init data
            $data = [
                'id' => $userId,
                'name' => $user->name,
                'password' => '',
                'confirm_password' => '',
                'name_err' => '',
                'password_err' => '',
                'confirm_password_err' => '',
                'title' => 'Profile',
                'user' => $user,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            // Load view
            $this->view('user/profile', $data);
        }
    }
    
    /**
     * Update profile image
     */
    public function updateProfileImage() {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Check if file was uploaded
            if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                // Load image editor model
                $imageEditorModel = $this->model('ImageEditorModel');
                
                // Process image upload
                $uploadDir = 'uploads/users/';
                
                // Create directory if it doesn't exist
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0755, true);
                }
                
                // First, delete any existing profile images for this user
                $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
                $this->db->bind(':entity_type', 'user');
                $this->db->bind(':entity_id', $userId);
                $existingImages = $this->db->resultSet();
                
                // Delete each existing image
                foreach ($existingImages as $image) {
                    $imageEditorModel->deleteImage($image->id);
                }
                
                // Process the image upload
                $imageData = $imageEditorModel->processImageUpload(
                    $_FILES['profile_image'], 
                    'user', 
                    $userId, 
                    $uploadDir,
                    $userId,
                    true // Set as primary image
                );
                
                if ($imageData) {
                    // Set success message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('success', 'Profile image updated successfully.');
                    }
                    
                    // Redirect back to profile page
                    $this->redirect('user/profile');
                } else {
                    // Set error message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('error', 'Failed to upload profile image. Please try again.');
                    }
                    
                    // Redirect back to profile page
                    $this->redirect('user/profile');
                }
            } else {
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'No file uploaded or file upload error occurred.');
                }
                
                // Redirect back to profile page
                $this->redirect('user/profile');
            }
        } else {
            // Redirect to profile page if not a POST request
            $this->redirect('user/profile');
        }
    }
    
    /**
     * Delete profile image
     */
    public function deleteProfileImage() {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Load image editor model
            $imageEditorModel = $this->model('ImageEditorModel');
            
            // Get primary image for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $image = $this->db->single();
            
            if ($image) {
                // Delete the image using the ImageEditorModel
                if ($imageEditorModel->deleteImage($image->id)) {
                    // Set success message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('success', 'Profile image deleted successfully.');
                    }
                } else {
                    // Set error message
                    if (function_exists('setFlashMessage')) {
                        setFlashMessage('error', 'Failed to delete profile image.');
                    }
                }
            } else {
                // Set error message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('error', 'No profile image found to delete.');
                }
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
        } else {
            // Redirect to profile page if not a POST request
            $this->redirect('user/profile');
        }
    }
    
    /**
     * Get user's profile image
     * 
     * @param int $userId User ID
     * @return object|null Image object or null if not found
     */
    private function getUserProfileImage($userId) {
        // Query the images table for the user's primary image
        $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
        $this->db->bind(':entity_type', 'user');
        $this->db->bind(':entity_id', $userId);
        return $this->db->single();
    }
}
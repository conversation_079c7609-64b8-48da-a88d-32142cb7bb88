<?php 
require APPROOT . '/views/includes/header.php'; 
require_once APPROOT . '/helpers/form_helper.php';
?>

<style>
    /* Styles for the persistent user indicator */
    .user-indicator {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .user-indicator .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
    }
    
    /* Make the indicator sticky on mobile */
    @media (max-width: 767.98px) {
        .user-indicator {
            position: sticky;
            top: 0;
            z-index: 1020;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .user-indicator .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    }
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/registrations/<?php echo $data['show']->id; ?>">Registrations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Create Registration</li>
                </ol>
            </nav>
            
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Create Registration for <?php echo $data['show']->name; ?></h5>
                </div>
                
                <?php if (isset($data['selected_user'])): ?>
                <!-- Persistent User Indicator -->
                <div class="user-indicator">
                    <div class="d-flex align-items-center flex-wrap">
                        <div class="me-auto">
                            <span class="badge bg-info me-2">Registering for:</span>
                            <strong><?php echo $data['selected_user']->name; ?></strong>
                            <span class="d-none d-md-inline text-muted ms-2">(<?php echo $data['selected_user']->email; ?>)</span>
                        </div>
                        <a href="<?php echo URLROOT; ?>/staff/createRegistration/<?php echo $data['show']->id; ?>" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-user-edit"></i> <span class="d-none d-md-inline">Change User</span>
                        </a>
                    </div>
                    <div class="d-md-none small text-muted mt-1">
                        <?php echo $data['selected_user']->email; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="card-body">
                    <?php flash('staff_message'); ?>
                    
                    <!-- Step 1: User Selection -->
                    <div id="step_user_selection" class="registration-step <?php echo (!isset($data['current_step']) || $data['current_step'] == 'user_selection') ? '' : 'd-none'; ?>">
                        <h4 class="mb-3">Step 1: User Selection</h4>
                        
                        <div class="mb-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="user_type" id="existing_user" value="existing" checked>
                                <label class="form-check-label" for="existing_user">
                                    Existing User
                                </label>
                            </div>
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="radio" name="user_type" id="new_user" value="new">
                                <label class="form-check-label" for="new_user">
                                    New User
                                </label>
                            </div>
                        </div>
                        
                        <!-- Existing User Search Form -->
                        <div id="existing_user_form" class="mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Search for Existing User</h6>
                                </div>
                                <div class="card-body">
                                    <div class="input-group mb-3">
                                        <input type="text" id="user_search" class="form-control" placeholder="Search by name, email, or phone...">
                                        <button class="btn btn-outline-secondary" type="button" id="search_button">
                                            <i class="fas fa-search"></i> Search
                                        </button>
                                    </div>
                                    
                                    <div id="user_search_results" class="mt-3">
                                        <!-- Search results will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- New User Registration Form -->
                        <div id="new_user_form" class="mb-4" style="display: none;">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Register New User</h6>
                                </div>
                                <div class="card-body">
                                    <form id="register_user_form" action="<?php echo URLROOT; ?>/staff/registerUser" method="post">
                                        <?php echo csrfTokenField(); ?>
                                        <input type="hidden" name="show_id" value="<?php echo $data['show']->id; ?>">
                                        
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="name" name="name" required>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="email" class="form-label">Email Address <span class="text-danger">*</span></label>
                                                <input type="email" class="form-control" id="email" name="email" required autocomplete="new-email">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="phone" class="form-label">Phone Number</label>
                                                <input type="tel" class="form-control" id="phone" name="phone" autocomplete="new-phone">
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                                <input type="password" class="form-control" id="password" name="password" required autocomplete="new-password">
                                                <small class="form-text text-muted">Minimum 8 characters</small>
                                            </div>
                                            <div class="col-12 d-grid">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-user-plus"></i> Register User
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Clear new user form fields on page load
    $('#name').val('');
    $('#email').val('');
    $('#phone').val('');
    $('#password').val('');
    
    // User type selection
    $('input[name="user_type"]').change(function() {
        if ($(this).val() === 'existing') {
            $('#existing_user_form').show();
            $('#new_user_form').hide();
        } else {
            $('#existing_user_form').hide();
            $('#new_user_form').show();
            
            // Clear form fields when switching to new user form
            $('#name').val('');
            $('#email').val('');
            $('#phone').val('');
            $('#password').val('');
        }
    });
    
    // Vehicle type selection
    $('input[name="vehicle_type"]').change(function() {
        if ($(this).val() === 'existing') {
            $('#existing_vehicle_form').show();
            $('#new_vehicle_form').hide();
        } else {
            $('#existing_vehicle_form').hide();
            $('#new_vehicle_form').show();
        }
    });
    
    // Function to perform user search
    function performUserSearch() {
        const searchTerm = $('#user_search').val();
        if (searchTerm.length < 3) {
            $('#user_search_results').html('<div class="alert alert-info">Please enter at least 3 characters to search</div>');
            return;
        }
        
        // Show loading indicator
        $('#user_search_results').html('<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
        
        // AJAX call to search for users
        $.ajax({
            url: '<?php echo URLROOT; ?>/staff/searchUsers',
            type: 'POST',
            data: {
                search: searchTerm,
                show_id: <?php echo $data['show']->id; ?>,
                csrf_token: '<?php echo generateCsrfToken(); ?>'
            },
            dataType: 'json',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                displayUserSearchResults(response);
            },
            error: function(xhr, status, error) {
                console.error('Search error:', error);
                console.log('Response text:', xhr.responseText);
                console.log('Status:', status);
                console.log('XHR:', xhr);
                
                // Try to parse the response text in case it's JSON with a different content type
                try {
                    const jsonResponse = JSON.parse(xhr.responseText);
                    displayUserSearchResults(jsonResponse);
                } catch (e) {
                    $('#user_search_results').html('<div class="alert alert-danger">Error searching for users. Please try again.</div>');
                }
            }
        });
    }
    
    // Search button click handler
    $('#search_button').click(function() {
        performUserSearch();
    });
    
    // Live search with debounce
    let searchTimeout = null;
    $('#user_search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            performUserSearch();
        }, 500); // Wait 500ms after user stops typing
    });
    
    // Allow pressing Enter in the search field
    $('#user_search').keypress(function(e) {
        if (e.which == 13) { // Enter key
            clearTimeout(searchTimeout);
            performUserSearch();
            e.preventDefault();
        }
    });
});

// Function to display user search results
function displayUserSearchResults(response) {
    try {
        console.log('Response received:', response);
        
        // Handle case where response is a string
        if (typeof response === 'string') {
            try {
                response = JSON.parse(response);
                console.log('Parsed response:', response);
            } catch (e) {
                console.error('Failed to parse response string:', e);
                $('#user_search_results').html('<div class="alert alert-danger">Invalid response format. Please try again.</div>');
                return;
            }
        }
        
        // Check if response is valid
        if (!response) {
            $('#user_search_results').html('<div class="alert alert-danger">Empty response received. Please try again.</div>');
            return;
        }
        
        // Handle error response
        if (!response.success) {
            const message = response.message ? response.message : 'An error occurred while searching for users.';
            $('#user_search_results').html('<div class="alert alert-danger">' + message + '</div>');
            return;
        }
        
        // Handle missing or empty data
        const users = response.data;
        if (!users) {
            $('#user_search_results').html('<div class="alert alert-warning">No user data received. Please try again.</div>');
            return;
        }
        
        if (users.length === 0) {
            let debugInfo = '';
            if (response.debug) {
                debugInfo = `
                <div class="mt-2 small text-muted">
                    <strong>Debug Info:</strong><br>
                    Search term: "${response.debug.search_term}"<br>
                    Timestamp: ${response.debug.timestamp}<br>
                    Total users: ${response.debug.user_count || 'Unknown'}
                </div>`;
            }
            
            $('#user_search_results').html(`
                <div class="alert alert-info">
                    No users found matching your search criteria.
                    <br>Try a different name, email, or phone number.
                    ${debugInfo}
                </div>`);
            return;
        }
        
        // Build the HTML for user results
        let html = '<div class="list-group">';
        for (let i = 0; i < users.length; i++) {
            const user = users[i];
            
            // Skip invalid user objects
            if (!user || typeof user !== 'object') {
                console.error('Invalid user at index ' + i + ':', user);
                continue;
            }
            
            // Skip users without ID
            if (!user.id) {
                console.error('User missing ID at index ' + i + ':', user);
                continue;
            }
            
            // Create user list item
            html += `<a href="<?php echo URLROOT; ?>/staff/selectUser/${user.id}/<?php echo $data['show']->id; ?>" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${user.name || 'Unknown'}</h6>
                    <small>ID: ${user.id}</small>
                </div>
                <p class="mb-1">${user.email || 'No email'}</p>
                <small>${user.phone || 'No phone number'}</small>
            </a>`;
        }
        html += '</div>';
        
        // Update the results container
        $('#user_search_results').html(html);
    } catch (e) {
        console.error('Error displaying search results:', e);
        console.error('Error details:', e.message);
        console.error('Stack trace:', e.stack);
        $('#user_search_results').html('<div class="alert alert-danger">Error displaying search results. Please try again.</div>');
    }
}
</script>

<?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">Debug Information</h5>
        </div>
        <div class="card-body">
            <h6>Current Step:</h6>
            <pre><?php echo $data['current_step'] ?? 'user_selection'; ?></pre>
            
            <?php if (isset($data['selected_user'])): ?>
            <h6>Selected User:</h6>
            <pre>ID: <?php echo $data['selected_user']->id; ?>
Name: <?php echo $data['selected_user']->name; ?>
Email: <?php echo $data['selected_user']->email; ?></pre>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php endif; ?>

<?php require APPROOT . '/views/includes/footer.php'; ?>
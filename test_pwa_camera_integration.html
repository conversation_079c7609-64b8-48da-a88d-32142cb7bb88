<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Camera Integration Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">PWA Camera Integration Test</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This test simulates the vehicle images page with the new PWA camera integration.
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <button class="btn btn-primary btn-lg w-100" onclick="alert('Upload Images clicked')">
                                    <i class="fas fa-cloud-upload-alt me-2"></i> Upload Images
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-info btn-lg w-100" data-camera-capture="camera-upload" data-entity-type="vehicle" data-entity-id="123">
                                    <i class="fas fa-camera me-2"></i> Take Photo
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-success btn-lg w-100" onclick="alert('Image Editor clicked')">
                                    <i class="fas fa-edit me-2"></i> Use Image Editor
                                </button>
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle me-2"></i>Integration Features:</h6>
                            <ul class="mb-0">
                                <li><strong>Existing PWA Camera Modal:</strong> Uses the current camera system with banners</li>
                                <li><strong>Direct Upload:</strong> Photos upload directly to image editor API</li>
                                <li><strong>Entity Context:</strong> Maintains vehicle/event/show association</li>
                                <li><strong>Seamless Workflow:</strong> Auto-redirects to image editor after upload</li>
                                <li><strong>Security:</strong> Proper ownership verification and file validation</li>
                            </ul>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle me-2"></i>Test Requirements:</h6>
                            <ul class="mb-0">
                                <li>Must be logged in to test camera functionality</li>
                                <li>Requires HTTPS for camera access on mobile devices</li>
                                <li>Camera button will open existing PWA modal with banner rotation</li>
                                <li>Captured photos will upload to <code>/pwa/camera-upload</code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include PWA Features -->
    <script src="/public/js/pwa-features.js"></script>
    <script>
        // Initialize PWA features when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (window.PWAFeatures) {
                const pwa = new PWAFeatures();
                console.log('PWA Camera Integration Test - PWA Features initialized');
            } else {
                console.error('PWA Features not loaded');
            }
        });
    </script>
</body>
</html>
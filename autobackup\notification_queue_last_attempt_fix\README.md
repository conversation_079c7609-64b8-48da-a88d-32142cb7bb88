# Notification Queue Last Attempt Fix

## Issue
The Notification Queue is always showing "Never" in the "Last Attempt" column even though notifications were sent. The database has a "last_attempt" column in the notification_queue table.

## Root Cause
The issue is in the AdminNotificationController's queue() method. The SQL query is correctly selecting the data, but there might be an issue with:
1. The `last_attempt` field not being properly updated when notifications are processed
2. The field being NULL in the database
3. The view not properly accessing the field

## Solution
1. Fix the SQL query to ensure `last_attempt` is properly selected
2. Update the notification processing logic to properly set the `last_attempt` timestamp
3. Ensure the view correctly displays the `last_attempt` value

## Files Modified
- controllers/AdminNotificationController.php
- models/NotificationService.php (if needed)
- cron/process_notifications.php (if needed)

## Testing
1. Check notification queue view shows "Never" for last attempt
2. Process some notifications
3. Verify last attempt timestamp is updated
4. Confirm queue view shows correct last attempt time

## Version
v3.48.7 - Notification Queue Last Attempt Fix
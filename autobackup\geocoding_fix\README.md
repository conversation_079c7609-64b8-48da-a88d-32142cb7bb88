# Geocoding Fix for Calendar Events

## Issue Description
There was an inconsistency between the geocoding functionality in the admin batch tool and the calendar event creation/editing process. This caused addresses to be geocoded differently depending on which method was used.

## Solution
The fix replaces the direct use of `geocodeAddress()` with the enhanced `geocodeEvent()` function in both the `createEvent` and `editEvent` methods of the `CalendarController.php` file. This ensures consistent geocoding across all parts of the application.

## Changes Made
1. Updated the geocoding code in the `createEvent` method to use `geocodeEvent()`
2. Updated the geocoding code in the `editEvent` method to use `geocodeEvent()`
3. Added error logging for geocoding attempts
4. Added user feedback when geocoding fails

## Indentation Fix
The indentation in the modified code should be consistent with the rest of the file. The file `fixed_indentation.php` in this directory contains the properly indented code for reference.

## How to Fix Indentation
If you need to fix the indentation in the `CalendarController.php` file, use the following steps:

1. Open the file in a code editor with good indentation support (like VS Code)
2. Locate the geocoding sections in both methods
3. Select the poorly indented code and use the editor's "Format Selection" feature
4. Alternatively, manually adjust the indentation to match the surrounding code (4 spaces per level)

## Testing
After applying the fix, test the following scenarios:
1. Create a new event with a complete address
2. Edit an existing event and modify its address
3. Use the admin batch geocoding tool on events with addresses

All three methods should now produce consistent geocoding results.

## Version
This fix was implemented in version 1.0.3 of the Calendar Controller.
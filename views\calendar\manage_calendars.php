<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Calendar Management</h1>
            <p class="text-muted mb-0">Optimized for managing thousands of calendars</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-info me-2 d-none d-sm-inline">
                <i class="fas fa-calendar me-2"></i> Calendar
            </a>
            <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2 d-none d-sm-inline"></i> Create Calendar
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php flash('calendar_message'); ?>

    <!-- Calendar Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Calendar Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-info shadow-sm calendar-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-info mb-2">All Calendars</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['calendar_counts']['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Calendars</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-success shadow-sm calendar-overview-card" 
                                 data-filter="public" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">Public</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['calendar_counts']['public'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Public Calendars</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-warning shadow-sm calendar-overview-card" 
                                 data-filter="private" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-warning text-dark mb-2">Private</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['calendar_counts']['private'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Private Calendars</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-6 col-md-3">
                            <div class="card h-100 border-secondary shadow-sm calendar-overview-card" 
                                 data-filter="hidden" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-secondary mb-2">Hidden</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['calendar_counts']['hidden'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Hidden Calendars</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendar Details Section (Lazy Loaded) -->
    <div class="row mb-4 calendar-section" id="calendar-section" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info bg-opacity-25">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <span class="badge bg-info me-2">Calendar Details</span>
                            <span class="badge bg-secondary" id="calendar-count-display">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="closeCalendarSection()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter Controls -->
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search-calendars" class="form-label">Search Calendars</label>
                            <input type="text" class="form-control" id="search-calendars" 
                                   placeholder="Search by name or description...">
                        </div>
                        <div class="col-md-2">
                            <label for="visibility-filter" class="form-label">Visibility</label>
                            <select class="form-select" id="visibility-filter">
                                <option value="all">All</option>
                                <option value="visible">Visible</option>
                                <option value="hidden">Hidden</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="public-filter" class="form-label">Access</label>
                            <select class="form-select" id="public-filter">
                                <option value="all">All</option>
                                <option value="public">Public</option>
                                <option value="private">Private</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="per-page-calendars" class="form-label">Per Page</label>
                            <select class="form-select" id="per-page-calendars">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-info" onclick="searchCalendars()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearCalendarSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div class="card-body text-center" id="loading-calendars">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading calendars...</p>
                </div>
                
                <!-- Calendars Content (Will be populated via AJAX) -->
                <div id="calendars-content" style="display: none;">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calendar overview card click handlers
    document.querySelectorAll('.calendar-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadCalendarSection(filter);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-calendars').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchCalendars();
        }
    });

    // Filter change handlers
    document.getElementById('visibility-filter').addEventListener('change', searchCalendars);
    document.getElementById('public-filter').addEventListener('change', searchCalendars);
    document.getElementById('per-page-calendars').addEventListener('change', searchCalendars);
});

function loadCalendarSection(filter = 'all') {
    // Show the calendar section
    const section = document.getElementById('calendar-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter !== 'all') {
        if (filter === 'public' || filter === 'private') {
            document.getElementById('public-filter').value = filter;
        } else if (filter === 'visible' || filter === 'hidden') {
            document.getElementById('visibility-filter').value = filter === 'visible' ? 'visible' : 'hidden';
        }
    }

    // Load calendars
    loadCalendars(1);
}

function closeCalendarSection() {
    const section = document.getElementById('calendar-section');
    section.style.display = 'none';
}

function loadCalendars(page = 1) {
    const loadingDiv = document.getElementById('loading-calendars');
    const contentDiv = document.getElementById('calendars-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-calendars').value;
    const visibilityFilter = document.getElementById('visibility-filter').value;
    const publicFilter = document.getElementById('public-filter').value;
    const perPage = document.getElementById('per-page-calendars').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        visibility_filter: visibilityFilter,
        public_filter: publicFilter
    });

    // Make AJAX request
    fetch('<?php echo BASE_URL; ?>/calendar/loadCalendars?' + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderCalendars(data);
        } else {
            showCalendarError(data.error || 'Failed to load calendars');
        }
    })
    .catch(error => {
        console.error('Error loading calendars:', error);
        showCalendarError('Network error occurred');
    });
}

function searchCalendars() {
    loadCalendars(1);
}

function clearCalendarSearch() {
    document.getElementById('search-calendars').value = '';
    document.getElementById('visibility-filter').value = 'all';
    document.getElementById('public-filter').value = 'all';
    loadCalendars(1);
}

function renderCalendars(data) {
    const loadingDiv = document.getElementById('loading-calendars');
    const contentDiv = document.getElementById('calendars-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render calendars table and pagination
    let html = '';

    if (data.calendars.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No calendars found.</p></div>';
    } else {
        html = renderCalendarsTable(data.calendars, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update calendar count display
    document.getElementById('calendar-count-display').textContent = data.pagination.total_calendars.toLocaleString();
}

function renderCalendarsTable(calendars, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th width="50">Color</th><th>Name</th><th>Visibility</th><th>Events</th><th>Owner</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    calendars.forEach(calendar => {
        html += '<tr>';
        html += '<td><div class="calendar-color-dot" style="background-color: ' + (calendar.color || '#007bff') + '; width: 20px; height: 20px; border-radius: 50%;"></div></td>';
        html += '<td><strong>' + calendar.name + '</strong>';
        if (calendar.description) {
            html += '<br><small class="text-muted">' + (calendar.description.length > 50 ? calendar.description.substring(0, 50) + '...' : calendar.description) + '</small>';
        }
        html += '</td>';
        html += '<td>';
        if (calendar.is_public) {
            html += '<span class="badge bg-success">Public</span>';
        } else {
            html += '<span class="badge bg-warning text-dark">Private</span>';
        }
        if (!calendar.is_visible) {
            html += ' <span class="badge bg-secondary">Hidden</span>';
        }
        html += '</td>';
        html += '<td><span class="badge bg-info">' + (calendar.event_count || 0) + '</span></td>';
        html += '<td>' + (calendar.owner_name || 'Unknown') + '</td>';
        html += '<td>' + getCalendarActions(calendar.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderCalendarPagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_calendars.toLocaleString()} calendars`;
    html += '</div>';

    return html;
}

function getCalendarActions(calendarId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/calendar/editCalendar/${calendarId}" class="btn btn-primary">
                <i class="fas fa-edit"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/calendar/deleteCalendar/${calendarId}" class="btn btn-danger" onclick="return confirm('Are you sure?')">
                <i class="fas fa-trash"></i>
            </a>
        </div>
    `;
}

function renderCalendarPagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadCalendars(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadCalendars(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadCalendars(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showCalendarError(message) {
    const loadingDiv = document.getElementById('loading-calendars');
    const contentDiv = document.getElementById('calendars-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}
</script>

<!-- Custom CSS -->
<style>
    .calendar-color-dot {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
    }
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>

<?php
/**
 * Staff Controller
 * 
 * This controller handles all staff-related functionality.
 */
class StaffController extends Controller {
    // Relevant code snippet for the payment processing
    
    // Process payment
    if ($this->paymentModel->processManualPayment($data)) {
        // Update registration status to paid
        $this->registrationModel->updatePaymentStatus($id, 'paid');
        
        $this->setFlashMessage('staff_message', 'Payment processed successfully', 'success');
        $this->redirect('staff/registration/' . $id);
    } else {
        $this->setFlashMessage('staff_message', 'Failed to process payment', 'danger');
        $this->view('staff/process_payment', $data);
    }
}
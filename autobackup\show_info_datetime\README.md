# Show Information Enhancement with DateTime Support

This update improves the display of show information on the show details page, with proper support for datetime fields from the database.

## Changes Made

1. **Proper DateTime Handling**
   - Updated the code to use the datetime fields directly from the database
   - Removed conditional checks for separate time fields that are no longer needed
   - Formatted datetime values to show both date and time in a user-friendly format

2. **Improved Date and Time Format**
   - Used a consistent format for all datetime displays: "Day, Month Date, Year at Hour:Minute AM/PM"
   - Added proper escaping for the "at" text in the date format string
   - Maintained the visual design with icons and layout from the previous enhancement

## Implementation Details

The implementation now correctly uses the datetime fields from the database for:
- Show start date and time
- Show end date and time
- Registration start date and time
- Registration end date and time

The code is more efficient by directly formatting the datetime values without conditional checks for separate time fields. This ensures that all time information is properly displayed when available in the database.

## Version
This enhancement is part of version 3.35.46.
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test - No Cache</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .camera-modal-backdrop {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            background: rgba(0, 0, 0, 1.0) !important;
            z-index: 999997 !important;
            pointer-events: none !important;
        }
        .camera-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            width: 100vw !important;
            height: 100vh !important;
            z-index: 999998 !important;
            display: flex !important;
            flex-direction: column !important;
            background: transparent !important;
        }
        .camera-banner {
            width: 100% !important;
            height: 15% !important;
            background: rgba(0, 0, 0, 0.8) !important;
            color: white !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 18px !important;
            font-weight: bold !important;
            text-align: center !important;
            padding: 10px !important;
        }
        .banner-text {
            color: white !important;
            font-size: 18px !important;
            font-weight: bold !important;
            text-align: center !important;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            width: 100%;
            max-width: 300px;
        }
        .debug-messages {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Camera Banner Test - No Cache</h1>
        <p>This loads fresh JavaScript with timestamp to bypass ALL caching.</p>
        
        <button onclick="openTestCamera()">Open Test Camera</button>
        <button onclick="closeTestCamera()">Close Camera</button>
        
        <div id="debug-messages" class="debug-messages">
            Debug messages will appear here...
        </div>
    </div>

    <script>
        // Add timestamp to force fresh loading
        const timestamp = Date.now();
        
        // Debug function
        function debug(message) {
            const debugDiv = document.getElementById('debug-messages');
            const time = new Date().toLocaleTimeString();
            debugDiv.innerHTML += `[${time}] ${message}<br>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }
        
        // Test banners - same as your setup
        const testBanners = [
            { id: -1, type: 'image', text: '', image_path: '/uploads/branding/logo_1751468505_rides_logo.png', active: true, is_logo: true },
            { id: 1, type: 'text', text: 'Banner 1: Welcome to our Event Platform!', active: true, is_logo: false },
            { id: 2, type: 'text', text: 'Banner 2: Check out our upcoming events!', active: true, is_logo: false },
            { id: 3, type: 'text', text: 'Banner 3: Register your vehicle today!', active: true, is_logo: false }
        ];
        
        let rotationInterval = null;
        let currentIndex = 0;
        
        function openTestCamera() {
            debug('🎬 Opening test camera...');
            
            // Create backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'camera-modal-backdrop';
            backdrop.id = 'camera-backdrop';
            document.body.appendChild(backdrop);
            
            // Create modal
            const modal = document.createElement('div');
            modal.className = 'camera-modal';
            modal.id = 'camera-modal';
            modal.innerHTML = `
                <div class="camera-banner" id="camera-banner-content">
                    <img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Logo" style="max-height: 100%; max-width: 100%; object-fit: contain;">
                </div>
                <div style="flex: 1; background: #333; display: flex; align-items: center; justify-content: center; color: white;">
                    <div>Camera Video Would Be Here</div>
                </div>
                <div style="padding: 20px; background: rgba(0,0,0,0.8); text-align: center;">
                    <button onclick="closeTestCamera()" style="background: #dc3545; padding: 10px 20px;">Cancel</button>
                </div>
            `;
            document.body.appendChild(modal);
            
            // Start banner rotation
            startBannerRotation();
        }
        
        function closeTestCamera() {
            debug('🛑 Closing test camera...');
            
            // Stop rotation
            if (rotationInterval) {
                clearInterval(rotationInterval);
                rotationInterval = null;
            }
            
            // Remove elements
            const backdrop = document.getElementById('camera-backdrop');
            const modal = document.getElementById('camera-modal');
            
            if (backdrop) backdrop.remove();
            if (modal) modal.remove();
            
            debug('✅ Camera closed');
        }
        
        function startBannerRotation() {
            debug('🔄 Starting banner rotation...');
            
            const container = document.getElementById('camera-banner-content');
            if (!container) {
                debug('❌ Banner container not found!');
                return;
            }
            
            // Separate logo and other banners
            const otherBanners = testBanners.filter(banner => !banner.is_logo);
            debug(`📊 Found ${otherBanners.length} rotation banners`);
            
            if (otherBanners.length === 0) {
                debug('❌ No rotation banners found!');
                return;
            }
            
            // Show logo first for 5 seconds
            debug('🏠 Showing logo first...');
            
            setTimeout(() => {
                debug('⏰ Starting rotation of other banners...');
                currentIndex = 0;
                
                // Show first banner immediately
                showBanner(container, otherBanners[currentIndex]);
                
                // Start rotation every 3 seconds
                rotationInterval = setInterval(() => {
                    currentIndex = (currentIndex + 1) % otherBanners.length;
                    debug(`⏰ Rotating to banner ${currentIndex + 1} of ${otherBanners.length}`);
                    showBanner(container, otherBanners[currentIndex]);
                }, 3000);
                
            }, 5000);
        }
        
        function showBanner(container, banner) {
            debug(`🚀 showBanner() CALLED for: "${banner.text}"`);
            
            if (!container) {
                debug('❌ Container not found!');
                return;
            }
            
            if (!document.contains(container)) {
                debug('❌ Container removed from DOM!');
                return;
            }
            
            debug(`📦 Container OK: ${container.id}`);
            debug(`🔄 Displaying: ${banner.type} - "${banner.text}"`);
            
            // Clear container
            debug(`🧹 Clearing container...`);
            container.innerHTML = '';
            debug(`✅ Container cleared, now adding content...`);
            
            // Add text
            if (banner.type === 'text' && banner.text) {
                debug(`📝 About to show text: "${banner.text}"`);
                try {
                    const textDiv = document.createElement('div');
                    textDiv.className = 'banner-text';
                    textDiv.textContent = banner.text;
                    container.appendChild(textDiv);
                    debug(`✅ Text banner displayed successfully`);
                } catch (error) {
                    debug(`💥 ERROR showing text: ${error.message}`);
                }
            } else {
                debug(`🏠 Showing fallback content`);
                container.innerHTML = '<div class="banner-text">Fallback Content</div>';
            }
        }
        
        debug('🎯 Test page loaded - ready to test camera banner rotation');
    </script>
</body>
</html>

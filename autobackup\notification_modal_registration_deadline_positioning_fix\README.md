# Notification Modal Registration Deadline Positioning Fix

## Issue
The "Also notify me about registration deadline" line including the toggle was touching the left edge of the modal window border and needed to be moved significantly to the right to be properly positioned inside the hover table/container with adequate padding.

## Solution
Added specific CSS styling and HTML ID targeting to move the registration deadline notification toggle significantly to the right to align with the notification times columns and provide adequate padding from the modal border.

## Files Modified
1. `/public/css/notifications.css` - Added specific styling for the registration deadline form-check element
2. `/views/shared/notification_subscription_modal.php` - Added ID to the registration deadline container div

## Changes Made

### CSS Changes (`/public/css/notifications.css`)
- Updated version from v3.49.2 to v3.49.3
- Added specific CSS rule: `#notify_registration_end_container { margin-left: 2rem; padding-left: 0.5rem; }`
- This moves the toggle significantly to the right to align with notification times columns
- Provides adequate padding and spacing from the modal border

### HTML Changes (`/views/shared/notification_subscription_modal.php`)
- Added `id="notify_registration_end_container"` to the form-check div containing the registration deadline toggle
- This provides a specific target for the CSS positioning rule

### Documentation Updates
- Updated README.md to version v3.50.1
- Updated CHANGELOG.md with detailed fix information
- Created backup files in autobackup directory

## Technical Details
- The fix targets the specific div containing the registration deadline toggle
- Uses `margin-left: 2rem` (32px) to move the entire line significantly to the right
- Adds `padding-left: 0.5rem` (8px) for additional internal spacing
- Aligns the toggle with the notification times columns for visual consistency
- Maintains all existing functionality and responsive design
- Compatible with all existing Bootstrap and custom styling

## Testing
The fix should be immediately visible when:
1. Opening a car show notification subscription modal
2. The registration deadline toggle should now be positioned significantly to the right with proper alignment
3. The toggle should align with the notification times columns below it
4. Adequate padding should be visible between the toggle and modal border
5. All other modal functionality remains unchanged

## Date
2024-12-19

## Version
v3.50.1
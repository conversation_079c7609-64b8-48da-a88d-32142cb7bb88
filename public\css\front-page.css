/**
 * Front Page Styles
 * Enhanced styling for the home page with mobile-first approach
 */

/* Enhanced Navigation Styles */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-link {
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

.dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    margin-top: 8px;
    z-index: 999999 !important;
    position: absolute !important;
}

.dropdown {
    z-index: 999999 !important;
    position: relative !important;
}

.navbar {
    z-index: 999999 !important;
    position: relative !important;
}

/* Ensure navbar and dropdowns appear above hero section */
.navbar-nav .dropdown-menu {
    z-index: 999999 !important;
}

.navbar-nav .nav-item.dropdown {
    z-index: 999999 !important;
}

/* Override Bootstrap's z-index values */
.dropdown-menu.show {
    z-index: 999999 !important;
}

.dropdown-item {
    border-radius: 8px;
    margin: 2px 8px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: translateX(4px);
}

.dropdown-header {
    font-weight: 600;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

/* Notification Badge Animation */
.notification-badge {
    animation: pulse 2s infinite;
    min-width: 18px;
    height: 18px;
    font-size: 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Hide badge when count is 0 */
.notification-badge[data-count="0"] {
    display: none !important;
}

/* Mobile Navigation Improvements */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background-color: rgba(0, 123, 255, 0.95);
        border-radius: 12px;
        margin-top: 1rem;
        padding: 1rem;
        backdrop-filter: blur(10px);
    }

    .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(0, 0, 0, 0.1);
    }
}

/* Hero Section */
.hero-section {
    min-height: 70vh;
    background: linear-gradient(rgba(0, 123, 255, 0.15), rgba(0, 86, 179, 0.15)),
                url('/public/images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="0,0 1000,0 1000,100 0,80"/></svg>') no-repeat bottom;
    background-size: cover;
}

.min-vh-50 {
    min-height: 50vh;
}

/* Feature Cards */
.feature-icon {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover .feature-icon {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none !important;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* Step Numbers */
.step-number {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.step-item:hover .step-number {
    transform: scale(1.1);
}

/* Stats */
.stat-item h3 {
    font-size: 2.5rem;
    font-weight: 700;
}

/* Buttons */
.btn-lg {
    padding: 12px 30px;
    font-size: 1.1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-light:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Event Cards */
.badge {
    font-size: 0.85rem;
}

/* FAQ Accordion */
.accordion-button {
    font-weight: 600;
    font-size: 1.1rem;
}

.accordion-button:not(.collapsed) {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        padding: 2rem 0;
    }
    
    .display-3 {
        font-size: 2.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
    
    .lead {
        font-size: 1.1rem;
    }
    
    .stat-item h3 {
        font-size: 2rem;
    }
    
    .feature-icon {
        width: 60px !important;
        height: 60px !important;
    }
    
    .feature-icon i {
        font-size: 1.5rem !important;
    }
    
    .step-number {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.25rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem !important;
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .hero-section {
        min-height: 50vh;
    }
    
    .display-3 {
        font-size: 2rem;
    }
    
    .hero-actions .col-md-6 {
        margin-bottom: 1rem;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
    
    .py-5 {
        padding-top: 2rem !important;
        padding-bottom: 2rem !important;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .card:hover {
        transform: none;
    }
    
    .card:active {
        transform: scale(0.98);
    }
    
    .btn:active {
        transform: scale(0.95);
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .hero-section {
        background: #000;
        color: #fff;
    }
    
    .card {
        border: 2px solid #000 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .card,
    .feature-icon,
    .step-number,
    .btn-lg {
        transition: none;
    }
    
    .card:hover,
    .card:hover .feature-icon,
    .step-item:hover .step-number,
    .btn-light:hover {
        transform: none;
    }
}

/* Print styles */
@media print {
    .hero-section {
        background: #fff !important;
        color: #000 !important;
        min-height: auto;
    }
    
    .btn {
        display: none;
    }
    
    .accordion-collapse {
        display: block !important;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .bg-light {
        background-color: #2d3748 !important;
        color: #fff;
    }
    
    .text-muted {
        color: #a0aec0 !important;
    }
    
    .card {
        background-color: #4a5568;
        color: #fff;
    }
}

/* Loading animation for images */
.card img {
    transition: opacity 0.3s ease;
}

.card img[data-loading="true"] {
    opacity: 0.5;
}

/* Focus styles for accessibility */
.btn:focus,
.accordion-button:focus {
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #007bff;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}

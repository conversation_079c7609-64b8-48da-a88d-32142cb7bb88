            // Handle quick calendar toggles
            document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    if (DEBUG_MODE) {
                        console.log(`Quick calendar toggle ${calendarId} toggled: ${isChecked}`);
                    }
                    
                    // Synchronize with advanced filter checkboxes
                    const advancedFilterCheckbox = document.querySelector(`.calendar-checkbox[value="${calendarId}"]`);
                    if (advancedFilterCheckbox) {
                        advancedFilterCheckbox.checked = isChecked;
                        
                        // If we have the calendar filter system, update it
                        if (window.calendarFilters && typeof window.calendarFilters.updateCalendarFilters === 'function') {
                            window.calendarFilters.updateCalendarFilters();
                            window.calendarFilters.applyFilters();
                            return; // Let the filter system handle the update
                        }
                    }
                    
                    // Fallback to direct update if filter system is not available
                    updateVisibleCalendars();
                });
            });

<style>
/* Loading indicator styles */
.calendar-loading-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading {
    position: relative;
}
</style>
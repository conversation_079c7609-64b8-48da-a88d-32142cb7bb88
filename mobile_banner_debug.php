<?php
require_once 'config/config.php';
require_once 'core/Database.php';

// Simple mobile-friendly debug page for banner system
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Banner Debug</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 10px; 
            background: #f0f0f0;
        }
        .debug-section { 
            background: white; 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section h3 { 
            margin-top: 0; 
            color: #333;
        }
        .status { 
            padding: 10px; 
            margin: 5px 0; 
            border-radius: 4px; 
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        button { 
            padding: 15px 20px; 
            margin: 10px 5px; 
            font-size: 16px; 
            border: none; 
            border-radius: 4px; 
            background: #007bff; 
            color: white;
            cursor: pointer;
        }
        button:active { background: #0056b3; }
        
        .banner-test { 
            width: 100%; 
            height: 80px; 
            border: 2px solid #333; 
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            border-radius: 4px;
        }
        
        .log { 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            height: 200px; 
            overflow-y: scroll; 
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            border-radius: 4px;
        }
        
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 4px; 
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Mobile Banner System Debug</h1>
    
    <div class="debug-section">
        <h3>1. API Test</h3>
        <button onclick="testAPI()">Test New API (/api/cameraBanners)</button>
        <button onclick="testOldAPI()">Test Old API (/api/camera-banners.php)</button>
        <div id="api-status"></div>
        <div id="api-result"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. Database Check</h3>
        <div id="db-status">
            <?php
            try {
                $db = new Database();
                
                // Check if camera_banners table exists
                $db->query("SHOW TABLES LIKE 'camera_banners'");
                $db->execute();
                $table_exists = $db->rowCount() > 0;
                
                if ($table_exists) {
                    echo '<div class="status success">✓ camera_banners table exists</div>';
                    
                    // Check for banners
                    $db->query("SELECT COUNT(*) as count FROM camera_banners WHERE active = 1");
                    $db->execute();
                    $result = $db->single();
                    $banner_count = $result ? $result->count : 0;
                    
                    echo '<div class="status info">Active banners in database: ' . $banner_count . '</div>';
                } else {
                    echo '<div class="status warning">⚠ camera_banners table does not exist</div>';
                }
                
                // Check system settings
                $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'site_logo' LIMIT 1");
                $db->execute();
                $logo_row = $db->single();
                $site_logo = $logo_row ? $logo_row->setting_value : '';
                
                if ($site_logo) {
                    echo '<div class="status success">✓ Site logo found: ' . htmlspecialchars($site_logo) . '</div>';
                } else {
                    echo '<div class="status warning">⚠ No site logo in system settings</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="status error">✗ Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
            ?>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>3. Banner System Test</h3>
        <button onclick="testBannerSystem()">Test Banner System</button>
        <button onclick="forceBannerInit()">Force Banner Init</button>
        <button onclick="testBannerRotation()">Test Banner Rotation</button>
        <div id="banner-status"></div>
        
        <div class="banner-test" id="test-banner-content">
            <div class="banner-text">Default Test Content</div>
        </div>
    </div>
    
    <div class="debug-section">
        <h3>4. Camera Modal Test</h3>
        <button onclick="testCameraModal()">Open Camera Modal</button>
        <div id="camera-status"></div>
    </div>
    
    <div class="debug-section">
        <h3>5. Debug Log</h3>
        <button onclick="clearLog()">Clear Log</button>
        <div class="log" id="debug-log"></div>
    </div>

    <script>
        // Mobile-friendly console override
        const debugLog = document.getElementById('debug-log');
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            debugLog.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            debugLog.scrollTop = debugLog.scrollHeight;
        }
        
        // Override console methods
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('log', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('error', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('warn', ...args);
        };
        
        function clearLog() {
            debugLog.textContent = '';
        }
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        async function testAPI() {
            updateStatus('api-status', 'Testing API...', 'info');
            
            try {
                console.log('Testing API endpoint: /api/cameraBanners');
                const response = await fetch('/api/cameraBanners');
                console.log('API Response status:', response.status);
                console.log('API Response headers:', response.headers);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('API Response data:', data);
                    updateStatus('api-status', '✓ API working', 'success');
                    document.getElementById('api-result').innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    const errorText = await response.text();
                    console.error('API Error:', errorText);
                    updateStatus('api-status', `✗ API Error ${response.status}`, 'error');
                    document.getElementById('api-result').innerHTML = `<pre>Status: ${response.status}\nResponse: ${errorText}</pre>`;
                }
            } catch (error) {
                console.error('API Request failed:', error);
                updateStatus('api-status', `✗ Request failed: ${error.message}`, 'error');
                document.getElementById('api-result').innerHTML = `<pre>Error: ${error.message}</pre>`;
            }
        }
        
        async function testOldAPI() {
            updateStatus('api-status', 'Testing old API...', 'info');
            
            try {
                console.log('Testing old API endpoint: /api/camera-banners.php');
                const response = await fetch('/api/camera-banners.php');
                console.log('Old API Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Old API Response data:', data);
                    updateStatus('api-status', '✓ Old API working', 'success');
                    document.getElementById('api-result').innerHTML = `<pre>OLD API RESULT:\n${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    const errorText = await response.text();
                    console.error('Old API Error:', errorText);
                    updateStatus('api-status', `✗ Old API Error ${response.status}`, 'error');
                    document.getElementById('api-result').innerHTML = `<pre>OLD API ERROR:\nStatus: ${response.status}\nResponse: ${errorText}</pre>`;
                }
            } catch (error) {
                console.error('Old API Request failed:', error);
                updateStatus('api-status', `✗ Old API failed: ${error.message}`, 'error');
                document.getElementById('api-result').innerHTML = `<pre>OLD API ERROR: ${error.message}</pre>`;
            }
        }
        
        function testBannerSystem() {
            console.log('Testing banner system...');
            if (window.cameraBanner) {
                console.log('CameraBanner instance exists');
                console.log('CameraBanner version:', window.cameraBanner.version || 'unknown');
                console.log('Banner count:', window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined');
                console.log('Banners array:', window.cameraBanner.banners);
                console.log('isDebugMode type:', typeof window.cameraBanner.isDebugMode);
                updateStatus('banner-status', `✓ Banner system loaded v${window.cameraBanner.version || 'unknown'} (${window.cameraBanner.banners ? window.cameraBanner.banners.length : 0} banners)`, 'success');
            } else {
                console.error('CameraBanner instance not found');
                updateStatus('banner-status', '✗ Banner system not loaded', 'error');
            }
        }
        
        function forceBannerInit() {
            console.log('Forcing banner initialization...');
            if (window.cameraBanner) {
                updateStatus('banner-status', 'Initializing banners...', 'info');
                window.cameraBanner.loadBanners().then(() => {
                    console.log('Force init complete, banner count:', window.cameraBanner.banners.length);
                    updateStatus('banner-status', `✓ Banners initialized (${window.cameraBanner.banners.length} banners)`, 'success');
                }).catch(error => {
                    console.error('Force init failed:', error);
                    updateStatus('banner-status', `✗ Init failed: ${error.message}`, 'error');
                });
            } else {
                updateStatus('banner-status', '✗ Banner system not available', 'error');
            }
        }
        
        function testBannerRotation() {
            console.log('Testing banner rotation...');
            if (window.cameraBanner) {
                console.log('Banner system banners:', window.cameraBanner.banners);
                console.log('Banner count:', window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined');
                
                if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                    console.log('First banner:', window.cameraBanner.banners[0]);
                    window.cameraBanner.startRotation('test-banner-content');
                    updateStatus('banner-status', '✓ Banner rotation started', 'success');
                } else {
                    console.log('No banners loaded, trying to load first...');
                    window.cameraBanner.loadBanners().then(() => {
                        console.log('Banners loaded, now starting rotation...');
                        console.log('Loaded banners:', window.cameraBanner.banners);
                        window.cameraBanner.startRotation('test-banner-content');
                        updateStatus('banner-status', '✓ Banner rotation started after loading', 'success');
                    }).catch(error => {
                        console.error('Failed to load banners:', error);
                        updateStatus('banner-status', '✗ Failed to load banners', 'error');
                    });
                }
            } else {
                updateStatus('banner-status', '✗ Banner system not available', 'error');
            }
        }
        
        function testCameraModal() {
            console.log('Testing camera modal...');
            if (window.pwaFeatures && window.pwaFeatures.openCamera) {
                // Create a dummy input to test with
                const dummyInput = document.createElement('input');
                dummyInput.type = 'file';
                dummyInput.id = 'test-camera-input';
                document.body.appendChild(dummyInput);
                
                window.pwaFeatures.openCamera('test-camera-input');
                updateStatus('camera-status', '✓ Camera modal opened', 'success');
            } else {
                updateStatus('camera-status', '✗ PWA features not available', 'error');
            }
        }
        
        // Initialize when page loads
        window.addEventListener('load', function() {
            console.log('Mobile debug page loaded');
            
            // Enable debug mode
            localStorage.setItem('camera_banner_debug', 'true');
            
            // Test banner system after a short delay
            setTimeout(() => {
                testBannerSystem();
            }, 1000);
        });
    </script>
    
    <!-- Load the required scripts with cache busting -->
    <script src="<?php echo BASE_URL; ?>/public/js/camera-banner.js?v=<?php echo time(); ?>"></script>
    <script src="<?php echo BASE_URL; ?>/public/js/pwa-features.js?v=<?php echo time(); ?>"></script>
</body>
</html>
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Manage Calendars</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Calendar
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Create Calendar
                </a>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Your Calendars</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($data['calendars'])): ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50px;"></th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Visibility</th>
                                <th>Events</th>
                                <th style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data['calendars'] as $calendar): ?>
                                <tr>
                                    <td>
                                        <div class="calendar-color-dot" style="background-color: <?php echo $calendar->color; ?>"></div>
                                    </td>
                                    <td><?php echo $calendar->name; ?></td>
                                    <td><?php echo substr($calendar->description, 0, 50); ?><?php echo (strlen($calendar->description) > 50) ? '...' : ''; ?></td>
                                    <td>
                                        <?php if ($calendar->is_public): ?>
                                            <span class="badge bg-success">Public</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Private</span>
                                        <?php endif; ?>
                                        
                                        <?php if (!$calendar->is_visible): ?>
                                            <span class="badge bg-warning text-dark">Hidden</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $calendar->event_count ?? 0; ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo URLROOT; ?>/calendar/editCalendar/<?php echo $calendar->id; ?>" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo URLROOT; ?>/calendar/export/<?php echo $calendar->id; ?>" class="btn btn-info">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <?php if ($calendar->owner_id == $_SESSION['user_id'] || isAdmin()): ?>
                                                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteCalendarModal<?php echo $calendar->id; ?>">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                
                                <!-- Delete Calendar Modal -->
                                <?php if ($calendar->owner_id == $_SESSION['user_id'] || isAdmin()): ?>
                                <div class="modal fade" id="deleteCalendarModal<?php echo $calendar->id; ?>" tabindex="-1" aria-labelledby="deleteCalendarModalLabel<?php echo $calendar->id; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteCalendarModalLabel<?php echo $calendar->id; ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this calendar?</p>
                                                <p><strong><?php echo $calendar->name; ?></strong></p>
                                                <p class="text-danger">This will also delete all events associated with this calendar. This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="<?php echo URLROOT; ?>/calendar/deleteCalendar/<?php echo $calendar->id; ?>" method="post">
                                                    <?php echo csrfTokenField(); ?>
                                                    <button type="submit" class="btn btn-danger">Delete Calendar</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <p>You don't have any calendars yet.</p>
                    <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-primary">Create Your First Calendar</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isAdmin()): ?>
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Other Users' Calendars</h5>
        </div>
        <div class="card-body">
            <?php 
            $otherCalendars = array_filter($data['calendars'], function($calendar) {
                return $calendar->owner_id != $_SESSION['user_id'];
            });
            
            if (!empty($otherCalendars)): 
            ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50px;"></th>
                                <th>Name</th>
                                <th>Owner</th>
                                <th>Visibility</th>
                                <th>Events</th>
                                <th style="width: 150px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($otherCalendars as $calendar): ?>
                                <tr>
                                    <td>
                                        <div class="calendar-color-dot" style="background-color: <?php echo $calendar->color; ?>"></div>
                                    </td>
                                    <td><?php echo $calendar->name; ?></td>
                                    <td><?php echo $calendar->owner_name ?? 'Unknown'; ?></td>
                                    <td>
                                        <?php if ($calendar->is_public): ?>
                                            <span class="badge bg-success">Public</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Private</span>
                                        <?php endif; ?>
                                        
                                        <?php if (!$calendar->is_visible): ?>
                                            <span class="badge bg-warning text-dark">Hidden</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo $calendar->event_count ?? 0; ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo URLROOT; ?>/calendar/editCalendar/<?php echo $calendar->id; ?>" class="btn btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?php echo URLROOT; ?>/calendar/export/<?php echo $calendar->id; ?>" class="btn btn-info">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteCalendarModal<?php echo $calendar->id; ?>">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                
                                <!-- Delete Calendar Modal -->
                                <div class="modal fade" id="deleteCalendarModal<?php echo $calendar->id; ?>" tabindex="-1" aria-labelledby="deleteCalendarModalLabel<?php echo $calendar->id; ?>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteCalendarModalLabel<?php echo $calendar->id; ?>">Confirm Delete</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this calendar?</p>
                                                <p><strong><?php echo $calendar->name; ?></strong></p>
                                                <p class="text-danger">This will also delete all events associated with this calendar. This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form action="<?php echo URLROOT; ?>/calendar/deleteCalendar/<?php echo $calendar->id; ?>" method="post">
                                                    <?php echo csrfTokenField(); ?>
                                                    <button type="submit" class="btn btn-danger">Delete Calendar</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">
                    <p>There are no other users' calendars.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Custom CSS -->
<style>
    .calendar-color-dot {
        display: inline-block;
        width: 20px;
        height: 20px;
        border-radius: 50%;
    }
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
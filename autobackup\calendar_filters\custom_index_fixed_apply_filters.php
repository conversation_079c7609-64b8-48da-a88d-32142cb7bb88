<?php
/**
 * Fixed version of the custom_index_fixed.php file
 * 
 * This fix addresses the "calendar.loadEvents is not a function" error by modifying
 * the event handler for the "Apply Filters" button to use the calendarFilters.applyFilters
 * function instead of directly calling calendar.loadEvents().
 */

// Original code:
/*
document.getElementById('apply-filters').addEventListener('click', function() {
    if (calendar) {
        calendar.loadEvents();
    }
    loadUpcomingEvents();
});
*/

// Fixed code:
document.getElementById('apply-filters').addEventListener('click', function() {
    // Use the calendarFilters.applyFilters function if available
    if (window.calendarFilters && typeof window.calendarFilters.applyFilters === 'function') {
        window.calendarFilters.applyFilters();
    } else {
        // Fallback to direct method if calendarFilters is not available
        if (calendar) {
            // Try different methods to refresh the calendar
            if (typeof calendar.loadEvents === 'function') {
                calendar.loadEvents();
            } else if (typeof calendar.refetchEvents === 'function') {
                calendar.refetchEvents();
            } else {
                console.error('No method available to refresh calendar events');
            }
        }
    }
    loadUpcomingEvents();
});
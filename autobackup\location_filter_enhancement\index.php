<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 

// Debug mode check
$debug = defined('DEBUG_MODE') && DEBUG_MODE;
?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h1 class="mb-3">Car Shows</h1>
            
            <!-- Search and Filter Section -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Search & Filter Shows</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/show" method="GET" id="showFilterForm">
                        <div class="row g-3">
                            <!-- Search Box -->
                            <div class="col-md-12 mb-3">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search shows..." name="search" value="<?php echo htmlspecialchars($data['search'] ?? ''); ?>">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i> Search
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Filters -->
                            <div class="col-md-3">
                                <label for="state" class="form-label">State</label>
                                <select class="form-select" id="state" name="state">
                                    <option value="">All States</option>
                                    <?php if (!empty($data['states'])): ?>
                                        <?php foreach ($data['states'] as $stateObj): ?>
                                            <option value="<?php echo htmlspecialchars($stateObj->state); ?>" <?php echo ($data['state'] == $stateObj->state) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($stateObj->state); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="city" class="form-label">City</label>
                                <select class="form-select" id="city" name="city">
                                    <option value="">All Cities</option>
                                    <?php if (!empty($data['cities'])): ?>
                                        <?php foreach ($data['cities'] as $cityObj): ?>
                                            <option value="<?php echo htmlspecialchars($cityObj->city); ?>" <?php echo ($data['city'] == $cityObj->city) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($cityObj->city); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="show_date" class="form-label">Show Date</label>
                                <input type="date" class="form-control" id="show_date" name="show_date" value="<?php echo htmlspecialchars($data['show_date'] ?? ''); ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label for="fan_voting" class="form-label">Fan Voting</label>
                                <select class="form-select" id="fan_voting" name="fan_voting">
                                    <option value="-1" <?php echo ($data['fan_voting'] == -1) ? 'selected' : ''; ?>>All Shows</option>
                                    <option value="1" <?php echo ($data['fan_voting'] == 1) ? 'selected' : ''; ?>>Fan Voting Enabled</option>
                                    <option value="0" <?php echo ($data['fan_voting'] == 0) ? 'selected' : ''; ?>>Fan Voting Disabled</option>
                                </select>
                            </div>
                            
                            <div class="col-12 mt-3">
                                <div class="d-flex justify-content-between">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Apply Filters
                                    </button>
                                    <a href="<?php echo BASE_URL; ?>/show" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Results Per Page Selector -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="text-muted">Showing <?php echo count($data['shows']); ?> of <?php echo $data['total_shows']; ?> shows</span>
                </div>
                <div class="d-flex align-items-center">
                    <label for="per_page" class="form-label me-2 mb-0">Shows per page:</label>
                    <select class="form-select form-select-sm" id="per_page" name="per_page" style="width: auto;">
                        <option value="20" <?php echo ($data['per_page'] == 20) ? 'selected' : ''; ?>>20</option>
                        <option value="50" <?php echo ($data['per_page'] == 50) ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo ($data['per_page'] == 100) ? 'selected' : ''; ?>>100</option>
                    </select>
                </div>
            </div>
            
            <!-- Show Cards -->
            <?php if (empty($data['shows'])): ?>
                <div class="alert alert-info">
                    <p>No shows found matching your criteria. Please try different filters or check back later.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($data['shows'] as $show): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                <?php if (!empty($show->banner_image)): ?>
                                    <img src="<?php echo BASE_URL; ?>/uploads/shows/<?php echo $show->banner_image; ?>" class="card-img-top" alt="<?php echo htmlspecialchars($show->name); ?>" style="height: 160px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 160px;">
                                        <i class="fas fa-car fa-3x text-muted"></i>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body">
                                    <h5 class="card-title fw-bold mb-2"><?php echo htmlspecialchars($show->name); ?></h5>
                                    <p class="card-text small text-muted mb-4"><?php echo substr(htmlspecialchars($show->description), 0, 100); ?>...</p>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-map-marker-alt"></i></div>
                                            <div>
                                                <strong>Location:</strong><br>
                                                <div class="mt-1"><?php echo htmlspecialchars($show->location); ?></div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-calendar-alt"></i></div>
                                            <div>
                                                <strong>Show Dates:</strong><br>
                                                <div class="mt-1">
                                                    <span class="text-nowrap"><?php echo date('M j, Y', strtotime($show->start_date)); ?></span>
                                                    <?php if ($show->start_date != $show->end_date): ?>
                                                        <span class="mx-1">-</span>
                                                        <span class="text-nowrap"><?php echo date('M j, Y', strtotime($show->end_date)); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="small text-muted mt-1">
                                                    <i class="far fa-clock me-1"></i>
                                                    <?php echo date('g:i A', strtotime($show->start_date)); ?> - 
                                                    <?php echo date('g:i A', strtotime($show->end_date)); ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-clipboard-list"></i></div>
                                            <div>
                                                <strong>Registration Period:</strong><br>
                                                <div class="mt-1">
                                                    <span class="text-nowrap"><?php echo date('M j, Y', strtotime($show->registration_start)); ?></span>
                                                    <span class="mx-1">-</span>
                                                    <span class="text-nowrap"><?php echo date('M j, Y', strtotime($show->registration_end)); ?></span>
                                                </div>
                                                <div class="small text-muted mt-1">
                                                    <i class="far fa-clock me-1"></i>
                                                    <?php echo date('g:i A', strtotime($show->registration_start)); ?> - 
                                                    <?php echo date('g:i A', strtotime($show->registration_end)); ?>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if (isset($show->registration_fee)): ?>
                                        <div class="d-flex mb-3">
                                            <div class="text-primary" style="width: 24px;"><i class="fas fa-dollar-sign"></i></div>
                                            <div>
                                                <strong>Registration Fee:</strong><br>
                                                <div class="mt-1">
                                                    <?php if ($show->is_free): ?>
                                                        <span class="badge bg-success">Free Entry</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">$<?php echo number_format($show->registration_fee, 2); ?></span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="d-grid mt-4">
                                        <a href="<?php echo BASE_URL; ?>/show/view/<?php echo $show->id; ?>" class="btn btn-primary btn-lg">
                                            <i class="fas fa-info-circle me-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($data['total_pages'] > 1): ?>
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($data['current_page'] > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=1&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?>&per_page=<?php echo $data['per_page']; ?>" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['current_page'] - 1; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?>&per_page=<?php echo $data['per_page']; ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php
                            // Calculate range of page numbers to show
                            $startPage = max(1, $data['current_page'] - 2);
                            $endPage = min($data['total_pages'], $data['current_page'] + 2);
                            
                            // Always show at least 5 pages if available
                            if ($endPage - $startPage + 1 < 5) {
                                if ($startPage == 1) {
                                    $endPage = min($data['total_pages'], 5);
                                } else {
                                    $startPage = max(1, $data['total_pages'] - 4);
                                }
                            }
                            
                            for ($i = $startPage; $i <= $endPage; $i++):
                            ?>
                                <li class="page-item <?php echo ($i == $data['current_page']) ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $i; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?>&per_page=<?php echo $data['per_page']; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($data['current_page'] < $data['total_pages']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['current_page'] + 1; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?>&per_page=<?php echo $data['per_page']; ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo BASE_URL; ?>/show?page=<?php echo $data['total_pages']; ?>&search=<?php echo urlencode($data['search']); ?>&state=<?php echo urlencode($data['state']); ?>&city=<?php echo urlencode($data['city']); ?>&show_date=<?php echo urlencode($data['show_date']); ?>&fan_voting=<?php echo $data['fan_voting']; ?>&per_page=<?php echo $data['per_page']; ?>" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle per page change
        const perPageSelect = document.getElementById('per_page');
        if (perPageSelect) {
            perPageSelect.addEventListener('change', function() {
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('per_page', this.value);
                currentUrl.searchParams.set('page', 1); // Reset to page 1 when changing items per page
                window.location.href = currentUrl.toString();
            });
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
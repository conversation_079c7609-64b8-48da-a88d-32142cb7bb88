            // Check if we have filter parameters in the URL
            const urlParams = new URLSearchParams(window.location.search);
            
            // Initialize calendar
            const calendar = new CustomCalendar('calendar', settings);
            
            if (DEBUG_MODE) {
                console.log('Calendar initialized successfully');
                // Make calendar instance available globally for debugging
                window.calendarInstance = calendar;
            }
            
            // Apply URL parameters to filters if they exist
            if (urlParams.has('calendar_id')) {
                if (DEBUG_MODE) {
                    console.log('Found filter parameters in URL, applying them');
                }
                
                // If we have the calendar filter system, apply the URL parameters
                if (window.calendarFilters) {
                    // Parse the calendar_id parameter
                    const calendarIds = urlParams.get('calendar_id').split(',').filter(id => id.trim() !== '');
                    
                    if (DEBUG_MODE) {
                        console.log('Calendar IDs from URL:', calendarIds);
                    }
                    
                    // Update the calendar checkboxes
                    document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
                        checkbox.checked = calendarIds.includes(checkbox.value);
                    });
                    
                    // Update the quick calendar toggles
                    document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                        toggle.checked = calendarIds.includes(toggle.value);
                    });
                    
                    // Update the filter state
                    window.calendarFilters.updateCalendarFilters();
                }
            }
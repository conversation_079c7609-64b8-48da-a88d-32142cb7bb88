<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo $id ? 'Edit' : 'Add'; ?> Payment Method</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/payment/methods" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Payment Methods
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <?php require APPROOT . '/views/includes/admin_settings_sidebar.php'; ?>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><?php echo $id ? 'Edit' : 'Add'; ?> Payment Method</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/payment/editMethod/<?php echo $id ? $id : ''; ?>" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Method Name</label>
                            <input type="text" class="form-control <?php echo (!empty($name_err)) ? 'is-invalid' : ''; ?>" 
                                   id="name" name="name" value="<?php echo $name; ?>" required>
                            <div class="invalid-feedback"><?php echo $name_err; ?></div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" 
                                   id="description" name="description" value="<?php echo $description; ?>" required>
                            <div class="invalid-feedback"><?php echo $description_err; ?></div>
                            <div class="form-text">Short description of the payment method shown to users.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="4"><?php echo $instructions; ?></textarea>
                            <div class="form-text">Instructions shown to users when they select this payment method.</div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       <?php echo $is_active ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                            <div class="form-text">If unchecked, this payment method will not be available to users.</div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="requires_approval" name="requires_approval" 
                                       <?php echo $requires_approval ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="requires_approval">Requires Manual Approval</label>
                            </div>
                            <div class="form-text">If checked, payments made with this method will require manual approval by an administrator.</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> <?php echo $id ? 'Update' : 'Add'; ?> Payment Method
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Test what the API is actually returning
 */

// Start session to simulate logged in user
session_start();

echo "<h2>API Response Test</h2>";

// Test the admin API
echo "<h3>Testing camera-banners-admin.php:</h3>";
$url = 'https://events.rowaneliterides.com/api/camera-banners-admin.php';

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . session_name() . '=' . session_id()
    ]
]);

$response = file_get_contents($url, false, $context);
echo "<strong>Raw Response:</strong><br>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<hr>";

// Test the settings API
echo "<h3>Testing camera-banner-settings.php:</h3>";
$url2 = 'https://events.rowaneliterides.com/api/camera-banner-settings.php';

$response2 = file_get_contents($url2, false, $context);
echo "<strong>Raw Response:</strong><br>";
echo "<pre>" . htmlspecialchars($response2) . "</pre>";

echo "<hr>";

// Show current session
echo "<h3>Current Session:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";
?>
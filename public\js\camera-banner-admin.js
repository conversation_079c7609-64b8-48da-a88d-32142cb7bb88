/**
 * Camera Banner Admin Interface
 * Manages banner CRUD operations and settings
 */

class CameraBannerAdmin {
    constructor() {
        this.banners = [];
        this.isDebugMode = false;
        
        this.init();
    }

    init() {
        this.checkDebugMode();
        this.initializeModals();
        this.bindEvents();
        this.loadBanners();
        this.loadSettings();
    }

    initializeModals() {
        // Modal initialization is now handled in the HTML page
        // This method is kept for compatibility
    }

    checkDebugMode() {
        this.isDebugMode = document.body.dataset.debugMode === 'true';
    }

    bindEvents() {
        // Banner type toggle - wait for modals to be available
        setTimeout(() => {
            document.querySelectorAll('input[name="type"]').forEach(radio => {
                radio.addEventListener('change', this.toggleBannerFields.bind(this));
            });
        }, 100);

        // Forms
        const addForm = document.getElementById('addBannerForm');
        const editForm = document.getElementById('editBannerForm');
        const settingsForm = document.getElementById('bannerSettingsForm');
        const addModal = document.getElementById('addBannerModal');
        const editModal = document.getElementById('editBannerModal');

        if (addForm) {
            addForm.addEventListener('submit', this.addBanner.bind(this));
        }
        
        if (editForm) {
            editForm.addEventListener('submit', this.updateBanner.bind(this));
        }
        
        if (settingsForm) {
            settingsForm.addEventListener('submit', this.saveSettings.bind(this));
        }

        // Modal events
        if (addModal) {
            addModal.addEventListener('hidden.bs.modal', this.resetAddForm.bind(this));
        }
    }

    toggleBannerFields(event) {
        const isAddForm = event.target.closest('#addBannerModal');
        const prefix = isAddForm ? '' : 'edit';
        
        const textFieldsId = prefix + 'textBannerFields';
        const imageFieldsId = prefix + 'imageBannerFields';
        
        const textFields = document.getElementById(textFieldsId);
        const imageFields = document.getElementById(imageFieldsId);
        
        // Debug logging
        if (this.isDebugMode) {
            console.log('Toggle banner fields:', {
                isAddForm,
                prefix,
                textFieldsId,
                imageFieldsId,
                textFields: !!textFields,
                imageFields: !!imageFields,
                value: event.target.value
            });
        }
        
        // Check if elements exist
        if (!textFields) {
            console.error('Text fields element not found:', textFieldsId);
            return;
        }
        
        if (!imageFields) {
            console.error('Image fields element not found:', imageFieldsId);
            return;
        }
        
        if (event.target.value === 'text') {
            textFields.style.display = 'block';
            imageFields.style.display = 'none';
        } else {
            textFields.style.display = 'none';
            imageFields.style.display = 'block';
        }
    }

    async loadBanners() {
        try {
            const response = await fetch(BASE_URL + '/api/camera-banners-admin.php');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.banners = Array.isArray(data.banners) ? data.banners : [];
                this.renderBanners();
            } else {
                this.showError('Failed to load banners: ' + (data.message || 'Unknown error'));
            }
        } catch (error) {
            this.showError('Error loading banners: ' + error.message);
            if (this.isDebugMode) {
                console.error('Load banners error:', error);
            }
            // Set empty array as fallback
            this.banners = [];
            this.renderBanners();
        }
    }

    async loadSettings() {
        try {
            const response = await fetch(BASE_URL + '/api/camera-banner-settings.php');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('bannerDelay').value = Math.floor(data.delay / 1000);
            }
        } catch (error) {
            if (this.isDebugMode) {
                console.error('Load settings error:', error);
            }
        }
    }

    renderBanners() {
        const container = document.getElementById('bannersContainer');
        const countBadge = document.getElementById('bannerCount');
        
        countBadge.textContent = `${this.banners.length} banner${this.banners.length !== 1 ? 's' : ''}`;
        
        if (this.banners.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No banners yet</h5>
                    <p class="text-muted">Add your first banner to get started</p>
                </div>
            `;
            return;
        }

        const bannersHtml = this.banners.map(banner => this.renderBannerCard(banner)).join('');
        container.innerHTML = bannersHtml;
    }

    renderBannerCard(banner) {
        if (!banner) return '';
        
        const statusBadge = banner.active 
            ? '<span class="badge bg-success">Active</span>'
            : '<span class="badge bg-secondary">Inactive</span>';

        const preview = banner.type === 'image' && banner.image_path && banner.image_path.trim() !== ''
            ? `<img src="${banner.image_path}" alt="${banner.alt_text || 'Banner'}" class="img-thumbnail" style="max-height: 60px;">`
            : `<div class="text-preview">${banner.text || 'No text'}</div>`;

        const displayText = banner.type === 'text' 
            ? (banner.text || 'No text') 
            : (banner.alt_text || 'Image banner');

        return `
            <div class="card mb-3 banner-card" data-banner-id="${banner.id || 0}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <div class="banner-preview">
                                ${preview}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-1">
                                <i class="fas fa-${banner.type === 'image' ? 'image' : 'font'} me-2"></i>
                                ${banner.type === 'image' ? 'Image Banner' : 'Text Banner'}
                            </h6>
                            <p class="text-muted mb-1 small">
                                ${displayText}
                            </p>
                            <small class="text-muted">Sort: ${banner.sort_order || 0}</small>
                        </div>
                        <div class="col-md-2 text-center">
                            ${statusBadge}
                        </div>
                        <div class="col-md-2 text-end">
                            <div class="btn-group">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="bannerAdmin.editBanner(${banner.id || 0})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="bannerAdmin.deleteBanner(${banner.id || 0})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async addBanner(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const submitBtn = event.target.querySelector('button[type="submit"]');
        
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await fetch(BASE_URL + '/api/camera-banners-admin.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Banner added successfully');
                bootstrap.Modal.getInstance(document.getElementById('addBannerModal')).hide();
                this.loadBanners();
            } else {
                this.showError('Failed to add banner: ' + data.message);
            }
        } catch (error) {
            this.showError('Error adding banner: ' + error.message);
            if (this.isDebugMode) {
                console.error('Add banner error:', error);
            }
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    async editBanner(id) {
        const banner = this.banners.find(b => b.id === id);
        if (!banner) return;

        // Populate edit form
        document.getElementById('editBannerId').value = banner.id;
        document.getElementById(`editType${banner.type.charAt(0).toUpperCase() + banner.type.slice(1)}`).checked = true;
        document.getElementById('editBannerText').value = banner.text || '';
        document.getElementById('editAltText').value = banner.alt_text || '';
        document.getElementById('editSortOrder').value = banner.sort_order;
        document.getElementById('editBannerActive').checked = banner.active;

        // Toggle fields
        this.toggleBannerFields({ target: { value: banner.type, closest: (selector) => document.getElementById('editBannerModal') }});

        // Show current image if exists
        const previewDiv = document.getElementById('currentImagePreview');
        if (banner.type === 'image' && banner.image_path) {
            previewDiv.innerHTML = `
                <div class="current-image">
                    <label class="form-label small">Current Image:</label>
                    <img src="${banner.image_path}" alt="${banner.alt_text || 'Current banner'}" 
                         class="img-thumbnail d-block" style="max-height: 100px;">
                </div>
            `;
        } else {
            previewDiv.innerHTML = '';
        }

        // Show modal
        new bootstrap.Modal(document.getElementById('editBannerModal')).show();
    }

    async updateBanner(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        formData.append('action', 'update');
        
        const submitBtn = event.target.querySelector('button[type="submit"]');
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await fetch(BASE_URL + '/api/camera-banners-admin.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Banner updated successfully');
                bootstrap.Modal.getInstance(document.getElementById('editBannerModal')).hide();
                this.loadBanners();
            } else {
                this.showError('Failed to update banner: ' + data.message);
            }
        } catch (error) {
            this.showError('Error updating banner: ' + error.message);
            if (this.isDebugMode) {
                console.error('Update banner error:', error);
            }
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    async deleteBanner(id) {
        if (!confirm('Are you sure you want to delete this banner?')) return;
        
        try {
            const formData = new FormData();
            formData.append('action', 'delete');
            formData.append('id', id);
            
            const response = await fetch(BASE_URL + '/api/camera-banners-admin.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Banner deleted successfully');
                this.loadBanners();
            } else {
                this.showError('Failed to delete banner: ' + data.message);
            }
        } catch (error) {
            this.showError('Error deleting banner: ' + error.message);
            if (this.isDebugMode) {
                console.error('Delete banner error:', error);
            }
        }
    }

    async saveSettings(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const submitBtn = event.target.querySelector('button[type="submit"]');
        
        this.setButtonLoading(submitBtn, true);
        
        try {
            const response = await fetch(BASE_URL + '/api/camera-banner-settings.php', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showSuccess('Settings saved successfully');
            } else {
                this.showError('Failed to save settings: ' + data.message);
            }
        } catch (error) {
            this.showError('Error saving settings: ' + error.message);
            if (this.isDebugMode) {
                console.error('Save settings error:', error);
            }
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }

    resetAddForm() {
        document.getElementById('addBannerForm').reset();
        document.getElementById('typeText').checked = true;
        this.toggleBannerFields({ target: { value: 'text', closest: (selector) => document.getElementById('addBannerModal') }});
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Processing...';
        } else {
            button.disabled = false;
            button.innerHTML = button.innerHTML.replace(/<i class="fas fa-spinner fa-spin me-2"><\/i> Processing\.\.\./, button.dataset.originalText || 'Save');
        }
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showError(message) {
        this.showMessage(message, 'danger');
    }

    showMessage(message, type = 'info') {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Clear existing messages
        container.innerHTML = '';
        container.appendChild(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alert && alert.parentNode) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);

        // Scroll to message
        container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.bannerAdmin = new CameraBannerAdmin();
});
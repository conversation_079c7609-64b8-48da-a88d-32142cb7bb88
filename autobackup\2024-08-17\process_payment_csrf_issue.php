<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/registrations/<?php echo $data['show']->id; ?>">Registrations</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/registration/<?php echo $data['registration']->id; ?>">Registration #<?php echo $data['registration']->id; ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Process Payment</li>
                </ol>
            </nav>
            
            <h1>Process Manual Payment</h1>
            
            <?php flash('staff_message'); ?>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Payment Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <h6>Registration Information</h6>
                                    <p><strong>Registration ID:</strong> <?php echo $data['registration']->id; ?></p>
                                    <p><strong>Show:</strong> <?php echo $data['show']->name; ?></p>
                                    <p><strong>Vehicle:</strong> <?php echo $data['registration']->year . ' ' . $data['registration']->make . ' ' . $data['registration']->model; ?></p>
                                    <p><strong>Owner:</strong> <?php echo $data['registration']->owner_name; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Payment Information</h6>
                                    <p><strong>Amount Due:</strong> $<?php echo number_format($data['amount'], 2); ?></p>
                                    <p><strong>Current Status:</strong> 
                                        <span class="badge bg-danger">
                                            <?php echo ucfirst($data['registration']->payment_status ?? 'unpaid'); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                            
                            <form action="<?php echo URLROOT; ?>/staff/processPayment/<?php echo $data['registration']->id; ?>" method="post">
                                <?php echo generateCsrfToken(); ?>
                                
                                <div class="mb-3">
                                    <label for="payment_method_id" class="form-label">Payment Method</label>
                                    <select class="form-select <?php echo (!empty($data['payment_method_id_err'])) ? 'is-invalid' : ''; ?>" id="payment_method_id" name="payment_method_id">
                                        <option value="">Select Payment Method</option>
                                        <?php foreach ($data['payment_methods'] as $method) : ?>
                                            <option value="<?php echo $method->id; ?>" <?php echo ($data['payment_method_id'] == $method->id) ? 'selected' : ''; ?>>
                                                <?php echo $method->name; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"><?php echo $data['payment_method_id_err']; ?></div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="reference" class="form-label">Reference (Optional)</label>
                                    <input type="text" class="form-control" id="reference" name="reference" placeholder="Check #, Receipt #, etc." value="<?php echo $data['reference']; ?>">
                                    <div class="form-text">Enter a reference number such as check number, receipt number, or transaction ID.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Notes (Optional)</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo $data['notes']; ?></textarea>
                                </div>
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-money-bill"></i> Process Payment
                                    </button>
                                    <a href="<?php echo URLROOT; ?>/staff/registration/<?php echo $data['registration']->id; ?>" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">Payment Instructions</h5>
                        </div>
                        <div class="card-body">
                            <p>Use this form to record a manual payment for this registration. This should be used when you have collected payment in person via cash, check, or other offline methods.</p>
                            
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Important</h6>
                                <p class="mb-0">By processing this payment, you are confirming that you have received the full payment amount of $<?php echo number_format($data['amount'], 2); ?> from the registrant.</p>
                            </div>
                            
                            <h6 class="mt-3">Payment Methods</h6>
                            <ul>
                                <li><strong>Cash:</strong> Select "Cash" and enter any receipt number in the reference field.</li>
                                <li><strong>Check:</strong> Select "Check" and enter the check number in the reference field.</li>
                                <li><strong>Credit Card (In Person):</strong> Select "Credit Card" and enter the last 4 digits of the card in the reference field.</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
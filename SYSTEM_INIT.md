# System Initialization Guide

This document outlines how to properly initialize the Events and Shows Management System in scripts and applications.

## Standard Initialization Pattern

When creating new scripts, always use this initialization pattern:

### For Scripts in Root Directory

```php
<?php
/**
 * Script description
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';  // Always include Database.php first
// Then include other required classes
require_once APPROOT . '/core/Auth.php';  // If authentication is needed
require_once APPROOT . '/models/YourModel.php';  // Any models you need

// Create instances
$db = new Database();
// Other instances as needed
```

### For Scripts in Subdirectories (e.g., /test/, /scripts/)

```php
<?php
/**
 * Script description
 */

// Define APPROOT (go up to root directory)
define('APPROOT', dirname(__FILE__, 2));  // For scripts in first-level subdirectories

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';  // Always include Database.php first
// Then include other required classes
require_once APPROOT . '/core/Auth.php';  // If authentication is needed
require_once APPROOT . '/models/YourModel.php';  // Any models you need

// Create instances
$db = new Database();
// Other instances as needed
```

## Important Notes

1. **DO NOT use `require_once '../core/init.php'`** - This file does not exist in our system.

2. **Always define APPROOT first** - This constant is used throughout the system for file paths.

3. **Use APPROOT for all file includes** - Always use `require_once APPROOT . '/path/to/file.php'`.

4. **Load Database.php before other core classes** - Many core classes depend on the Database class.

5. **Define APPROOT correctly based on script location** - Use the appropriate dirname() parameter.

## Path Handling

- For scripts in the root directory: `define('APPROOT', dirname(__FILE__));`
- For scripts in first-level subdirectories: `define('APPROOT', dirname(__FILE__, 2));` (goes up 2 levels)
- For scripts in deeper subdirectories: Adjust the second parameter accordingly
  - Second-level subdirectory: `define('APPROOT', dirname(__FILE__, 3));`
  - Third-level subdirectory: `define('APPROOT', dirname(__FILE__, 4));`

## Class Loading Order

1. Define APPROOT first
2. Load configuration: `require_once APPROOT . '/config/config.php'`
3. Load Database class: `require_once APPROOT . '/core/Database.php'`
4. Load Auth class if needed: `require_once APPROOT . '/core/Auth.php'`
5. Load model classes as needed: `require_once APPROOT . '/models/YourModel.php'`
6. Load helper functions if needed: `require_once APPROOT . '/helpers/your_helper.php'`

## Example: Script in Root Directory

```php
<?php
// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/UserModel.php';

// Create instances
$db = new Database();
$userModel = new UserModel();
```

## Example: Script in /test/ Directory

```php
<?php
// Define APPROOT (go up one level from /test/ to root)
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/UserModel.php';

// Create instances
$db = new Database();
$userModel = new UserModel();
```

## Script Execution Environment

**Important Note**: Our hosting environment does not provide SSH access, which means:
1. Scripts cannot be executed via command line (CLI) directly
2. All scripts must be executed through a web browser
3. The only exceptions are cron scripts or scripts that autorun via the hosting provider's cron system

This has important implications for how we write and execute scripts:

### Admin Dashboard Script Runner

For convenience, a script runner interface has been integrated into the admin dashboard:

1. Log in as an administrator
2. Navigate to the admin dashboard at `/admin/dashboard`
3. In the Developer Tools section, click on "Run Maintenance Scripts"
4. This will take you to `/admin/runScripts` where you can select and run scripts from the `/test/` directory

This is the recommended way to run maintenance scripts as it:
- Ensures proper authentication
- Provides a user-friendly interface
- Shows script output in a formatted way
- Handles access control automatically

### Web-Based Script Execution

Since we cannot use the command line, all maintenance and administrative scripts must:
1. Be accessible via a web browser
2. Implement proper access control to prevent unauthorized execution
3. Be run through the admin interface when possible

### Script Access Control

For security reasons, all administrative scripts (especially those in the `/test/` directory) should implement access control to prevent unauthorized execution. Here's the standard pattern:

```php
// Only allow running by admin (we don't have CLI access)
$auth = new Auth();
if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
    echo "Access denied. This script can only be run by an administrator.";
    exit;
}
```

This ensures that:
1. The script can be run from the command line (CLI) for automated tasks
2. If accessed via the web, only logged-in administrators can run it
3. All other users will see an "Access denied" message

### Example: Complete Script with Access Control

```php
<?php
/**
 * Administrative script description
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

// Script logic starts here
echo "Script running with proper authorization...";
```

## Common Mistakes to Avoid

1. ❌ Using `require_once '../core/init.php'`
2. ❌ Using direct paths without APPROOT like `require_once 'config/config.php'`
3. ❌ Forgetting to define APPROOT
4. ❌ Loading classes in the wrong order
5. ❌ Using relative paths like `../models/UserModel.php` instead of APPROOT
6. ❌ Forgetting to implement access control for administrative scripts
<?php require APPROOT . '/views/includes/header.php'; ?>
<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
</script>
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js"></script>

<div class="container-fluid mt-4">
    <!-- Navigation Bar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Calendar</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-outline-primary">
                    <i class="fas fa-calendar-alt"></i> Standard Calendar
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-outline-primary">
                    <i class="fas fa-map-marked-alt"></i> Map View
                </a>
                <?php if (isLoggedIn() && hasPermission('calendar_manage')): ?>
                <a href="<?php echo URLROOT; ?>/calendar/manage" class="btn btn-outline-primary">
                    <i class="fas fa-cog"></i> Manage
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Sidebar with Calendar List -->
        <div class="col-md-3 mb-4">
            <!-- Calendar List -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i> Calendars
                    </h5>
                </div>
                <div class="card-body">
                    <div id="calendar-list">
                        <?php foreach ($data['calendars'] as $calendar): ?>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input calendar-toggle" type="checkbox" 
                                       id="quick-calendar-<?php echo $calendar->id; ?>" 
                                       value="<?php echo $calendar->id; ?>" checked>
                                <label class="form-check-label" for="quick-calendar-<?php echo $calendar->id; ?>">
                                    <span class="color-dot" style="background-color: <?php echo $calendar->color; ?>"></span>
                                    <?php echo $calendar->name; ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Filter -->
            <?php require APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Upcoming Events -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i> Upcoming Events
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="upcoming-events" class="list-group list-group-flush">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Calendar Area -->
        <div class="col-md-9">
            <!-- Calendar View Options -->
            <div class="card mb-4">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" id="calendar-view-options">
                            <button type="button" class="btn btn-outline-primary" data-view="dayGridMonth">Month</button>
                            <button type="button" class="btn btn-outline-primary" data-view="timeGridWeek">Week</button>
                            <button type="button" class="btn btn-outline-primary" data-view="timeGridDay">Day</button>
                            <button type="button" class="btn btn-outline-primary" data-view="listMonth">List</button>
                        </div>
                        
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-secondary" id="prev-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="today-btn">Today</button>
                            <button type="button" class="btn btn-outline-secondary" id="next-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                        
                        <div id="calendar-title" class="h5 mb-0">
                            <!-- Calendar title will be inserted here by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Calendar Container -->
            <div class="card">
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="event-title" class="mb-3"></h4>
                
                <div class="mb-3">
                    <i class="fas fa-calendar-day me-2"></i>
                    <span id="event-date"></span>
                </div>
                
                <div class="mb-3">
                    <i class="fas fa-clock me-2"></i>
                    <span id="event-time"></span>
                </div>
                
                <div class="mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    <span id="event-calendar"></span>
                </div>
                
                <div id="event-location-container" class="mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span id="event-location"></span>
                </div>
                
                <div id="event-show-container" class="mb-3">
                    <i class="fas fa-car-side me-2"></i>
                    <span id="event-show"></span>
                </div>
                
                <div id="event-description-container" class="mb-3">
                    <h6>Description:</h6>
                    <div id="event-description"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a id="event-details-link" href="#" class="btn btn-primary">View Details</a>
            </div>
        </div>
    </div>
</div>

<!-- Add custom calendar initialization script with cache-busting -->
<script src="<?php echo URLROOT; ?>/public/js/custom-calendar.js?v=<?php echo filemtime(APPROOT . '/public/js/custom-calendar.js'); ?>"></script>

<!-- Add custom calendar debug helper (only loaded in debug mode) with cache-busting -->
<?php if (defined('DEBUG_MODE') && DEBUG_MODE): ?>
<script src="<?php echo URLROOT; ?>/public/js/custom-calendar-debug.js?v=<?php echo filemtime(APPROOT . '/public/js/custom-calendar-debug.js'); ?>"></script>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        try {
            // Initialize calendar
            const calendarEl = document.getElementById('calendar');
            if (!calendarEl) {
                console.error('Calendar element not found');
                return;
            }
            
            // Set up calendar view options
            const viewButtons = document.querySelectorAll('#calendar-view-options button');
            viewButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');
                    calendar.changeView(view);
                    
                    // Update active button
                    viewButtons.forEach(btn => btn.classList.remove('active', 'btn-primary'));
                    this.classList.add('active', 'btn-primary');
                    this.classList.remove('btn-outline-primary');
                });
            });
            
            // Set default view button as active
            const defaultViewButton = document.querySelector('[data-view="dayGridMonth"]');
            if (defaultViewButton) {
                defaultViewButton.classList.add('active', 'btn-primary');
                defaultViewButton.classList.remove('btn-outline-primary');
            }
            
            // Set up navigation buttons
            document.getElementById('prev-btn').addEventListener('click', function() {
                calendar.prev();
            });
            
            document.getElementById('next-btn').addEventListener('click', function() {
                calendar.next();
            });
            
            document.getElementById('today-btn').addEventListener('click', function() {
                calendar.today();
            });
            
            // Initialize calendar with options
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                headerToolbar: false, // We're using our custom header
                height: 'auto',
                dayMaxEventRows: 4,
                navLinks: true,
                editable: <?php echo (isLoggedIn() && hasPermission('calendar_edit')) ? 'true' : 'false'; ?>,
                selectable: false,
                nowIndicator: true,
                dayMaxEvents: true,
                eventTimeFormat: {
                    hour: 'numeric',
                    minute: '2-digit',
                    meridiem: 'short'
                },
                views: {
                    dayGrid: {
                        dayMaxEventRows: 4
                    },
                    timeGrid: {
                        dayMaxEventRows: 6
                    },
                    dayGridMonth: {
                        dayMaxEventRows: 4
                    },
                    dayGridWeek: {
                        dayMaxEventRows: 6
                    }
                },
                loading: function(isLoading) {
                    if (DEBUG_MODE) {
                        console.log('Calendar loading state:', isLoading);
                    }
                    
                    // Show/hide loading indicator
                    const loadingEl = document.getElementById('calendar-loading');
                    if (loadingEl) {
                        loadingEl.style.display = isLoading ? 'block' : 'none';
                    }
                },
                datesSet: function(info) {
                    // Update calendar title
                    const titleEl = document.getElementById('calendar-title');
                    if (titleEl) {
                        titleEl.textContent = info.view.title;
                    }
                    
                    // Also update upcoming events
                    loadUpcomingEvents();
                },
                eventSources: [
                    {
                        url: '<?php echo URLROOT; ?>/calendar/getEvents',
                        method: 'GET',
                        extraParams: function() {
                            // Get visible calendars
                            const visibleCalendars = [];
                            document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
                                visibleCalendars.push(toggle.value);
                            });
                            
                            // Use the filter system if available
                            if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
                                return window.calendarFilters.getFilterParams();
                            }
                            
                            // Fallback to just calendar IDs
                            return {
                                calendar_id: visibleCalendars.join(',')
                            };
                        },
                        failure: function() {
                            alert('There was an error loading events!');
                        }
                    }
                ],
                eventClick: function(info) {
                    showEventDetails(info.event);
                    
                    // Show the modal
                    const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
                    eventModal.show();
                },
                eventDrop: function(info) {
                    updateEventDates(info.event);
                },
                eventResize: function(info) {
                    updateEventDates(info.event);
                }
            });
            
            // Render the calendar
            calendar.render();
            
            // Initialize calendar toggles
            document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    // Refresh events when a calendar is toggled
                    calendar.refetchEvents();
                    
                    // Also update upcoming events
                    loadUpcomingEvents();
                });
            });
            
            // Function to load upcoming events
            function loadUpcomingEvents(calendarIds = null) {
                try {
                    const upcomingEventsEl = document.getElementById('upcoming-events');
                    if (!upcomingEventsEl) {
                        if (DEBUG_MODE) console.error('Upcoming events container not found');
                        return;
                    }
                    
                    // Show loading indicator
                    upcomingEventsEl.innerHTML = `
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    `;
                    
                    // Get visible calendars if not provided
                    if (!calendarIds) {
                        calendarIds = [];
                        document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
                            calendarIds.push(toggle.value);
                        });
                    }
                    
                    if (DEBUG_MODE) {
                        console.log('Loading upcoming events for calendars:', calendarIds);
                    }
                    
                    // Get current date and 30 days in the future
                    const now = new Date();
                    const start = now.toISOString().slice(0, 10) + ' 00:00:00';
                    const end = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10) + ' 23:59:59';
                    
                    // Build URL with base parameters
                    let url = `<?php echo URLROOT; ?>/calendar/getUpcomingEvents?start=${start}&end=${end}`;
                    
                    // Add filter parameters from the filter system if available
                    if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
                        const filterParams = window.calendarFilters.getFilterParams();
                        if (filterParams) {
                            url += '&' + filterParams;
                        } else {
                            // If no filter params, use the calendar IDs
                            url += `&calendar_id=${calendarIds.join(',')}`;
                        }
                    } else {
                        // Fallback to old method if filter system is not available
                        url += `&calendar_id=${calendarIds.join(',')}`;
                    }
                    
                    if (DEBUG_MODE) {
                        console.log('Loading upcoming events from:', url);
                    }
                    
                    // Load upcoming events
                    fetch(url, {
                            headers: {
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(events => {
                            if (DEBUG_MODE) {
                                console.log('Upcoming events loaded:', events);
                            }
                            
                            if (events.length === 0) {
                                upcomingEventsEl.innerHTML = '<div class="p-3">No upcoming events</div>';
                                return;
                            }
                            
                            // Format events for display
                            let html = '';
                            events.forEach(event => {
                                const startDate = new Date(event.start);
                                const formattedDate = startDate.toLocaleDateString(undefined, { 
                                    weekday: 'short', 
                                    month: 'short', 
                                    day: 'numeric' 
                                });
                                
                                const formattedTime = event.allDay ? 
                                    'All day' : 
                                    startDate.toLocaleTimeString(undefined, { 
                                        hour: 'numeric', 
                                        minute: '2-digit' 
                                    });
                                
                                html += `
                                    <a href="javascript:void(0);" class="list-group-item list-group-item-action upcoming-event" data-event-id="${event.id}">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">${event.title}</h6>
                                            <small class="text-muted">${formattedDate}</small>
                                        </div>
                                        <div class="d-flex w-100 justify-content-between">
                                            <small>${event.location || ''}</small>
                                            <small>${formattedTime}</small>
                                        </div>
                                        <div class="mt-1">
                                            <span class="badge bg-secondary">${event.calendar_name}</span>
                                            ${event.show_name ? `<span class="badge bg-info">${event.show_name}</span>` : ''}
                                        </div>
                                    </a>
                                `;
                            });
                            
                            upcomingEventsEl.innerHTML = html;
                            
                            // Add click event to upcoming events
                            document.querySelectorAll('.upcoming-event').forEach(item => {
                                item.addEventListener('click', function() {
                                    const eventId = this.getAttribute('data-event-id');
                                    const event = calendar.getEventById(eventId);
                                    
                                    if (event) {
                                        showEventDetails(event);
                                        
                                        // Show the modal
                                        const eventModal = new bootstrap.Modal(document.getElementById('eventModal'));
                                        eventModal.show();
                                    } else {
                                        // If event not found in calendar (might be outside current view), redirect to event details page
                                        window.location.href = `<?php echo URLROOT; ?>/calendar/event/${eventId}`;
                                    }
                                });
                            });
                        })
                        .catch(error => {
                            console.error('Error loading upcoming events:', error);
                            upcomingEventsEl.innerHTML = `
                                <div class="p-3 text-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    Error loading events
                                </div>
                            `;
                        });
                } catch (error) {
                    console.error('Error in loadUpcomingEvents:', error);
                }
            }
            
            // Load upcoming events on page load
            loadUpcomingEvents();
            
            // Function to update event dates after drag/resize
            function updateEventDates(event) {
                try {
                    if (DEBUG_MODE) {
                        console.log('Updating event dates:', event);
                    }
                    
                    // Get event ID and new dates
                    const eventId = event.id;
                    const newStart = event.start;
                    const newEnd = event.end || new Date(newStart.getTime() + 60 * 60 * 1000); // Default to 1 hour if no end time
                    
                    // Format dates for API
                    const startStr = newStart.toISOString().slice(0, 19).replace('T', ' ');
                    const endStr = newEnd.toISOString().slice(0, 19).replace('T', ' ');
                    
                    // Send update request
                    fetch('<?php echo URLROOT; ?>/calendar/updateEventDates', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: `id=${eventId}&start_date=${startStr}&end_date=${endStr}&<?php echo generateCsrfToken(); ?>`
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (DEBUG_MODE) {
                            console.log('Event update response:', data);
                        }
                        
                        if (!data.success) {
                            alert('Error updating event: ' + data.message);
                            calendar.loadEvents(); // Reload events to revert changes
                        }
                    })
                    .catch(error => {
                        console.error('Error updating event:', error);
                        alert('Error updating event. Please try again.');
                        calendar.loadEvents(); // Reload events to revert changes
                    });
                } catch (error) {
                    console.error('Error in updateEventDates:', error);
                }
            }
            
            // Function to show event details in modal
            function showEventDetails(event) {
                try {
                    if (DEBUG_MODE) {
                        console.log('Showing event details:', event);
                    }
                    
                    // Get modal elements
                    const modalEl = document.getElementById('eventModal');
                    if (!modalEl) {
                        console.error('Event modal not found');
                        return;
                    }
                    
                    const titleEl = document.getElementById('event-title');
                    const dateEl = document.getElementById('event-date');
                    const timeEl = document.getElementById('event-time');
                    const calendarEl = document.getElementById('event-calendar');
                    
                    // Set event details
                    titleEl.textContent = event.title;
                    
                    // Format date
                    const startDate = new Date(event.start);
                    const endDate = event.end ? new Date(event.end) : new Date(startDate.getTime() + 60 * 60 * 1000);
                    
                    // Set date
                    dateEl.textContent = startDate.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
                    
                    // Set time
                    if (event.allDay) {
                        timeEl.textContent = 'All day';
                    } else {
                        timeEl.textContent = `${startDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })} - ${endDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })}`;
                    }
                    
                    // Set calendar
                    calendarEl.textContent = event.extendedProps?.calendar_name || '';
                    
                    // Set location
                    const locationContainer = document.getElementById('event-location-container');
                    if (event.extendedProps?.location) {
                        document.getElementById('event-location').textContent = event.extendedProps.location;
                        locationContainer.style.display = 'block';
                    } else {
                        locationContainer.style.display = 'none';
                    }
                    
                    // Set show
                    const showContainer = document.getElementById('event-show-container');
                    if (event.extendedProps?.show_name) {
                        document.getElementById('event-show').textContent = event.extendedProps.show_name;
                        showContainer.style.display = 'block';
                    } else {
                        showContainer.style.display = 'none';
                    }
                    
                    // Set description
                    const descriptionContainer = document.getElementById('event-description-container');
                    if (event.extendedProps?.description) {
                        document.getElementById('event-description').innerHTML = event.extendedProps.description;
                        descriptionContainer.style.display = 'block';
                    } else {
                        descriptionContainer.style.display = 'none';
                    }
                    
                    // Set details link
                    const detailsLink = document.getElementById('event-details-link');
                    detailsLink.href = `<?php echo URLROOT; ?>/calendar/event/${event.id}`;
                } catch (error) {
                    console.error('Error showing event details:', error);
                }
            }
        
        // Initialize the advanced filter system
        if (window.calendarFilters) {
            // Apply filters button
            document.getElementById('apply-filters').addEventListener('click', function() {
                if (calendar) {
                    calendar.loadEvents();
                }
                loadUpcomingEvents();
            });
            
            // Sync quick calendar toggles with advanced filter
            document.querySelectorAll('.calendar-toggle').forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    // Update the corresponding checkbox in the advanced filter
                    const advancedFilterCheckbox = document.getElementById('calendar-' + calendarId);
                    if (advancedFilterCheckbox) {
                        advancedFilterCheckbox.checked = isChecked;
                    }
                    
                    // Update calendar filters
                    if (window.calendarFilters) {
                        window.calendarFilters.updateCalendarFilters();
                    }
                    
                    // Reload events
                    if (calendar) {
                        calendar.loadEvents();
                    }
                    
                    // Update upcoming events
                    loadUpcomingEvents();
                });
            });
            
            // Sync advanced filter calendar checkboxes with quick toggles
            document.querySelectorAll('.calendar-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const calendarId = this.value;
                    const isChecked = this.checked;
                    
                    // Update the corresponding quick toggle
                    const quickToggle = document.getElementById('quick-calendar-' + calendarId);
                    if (quickToggle) {
                        quickToggle.checked = isChecked;
                    }
                });
            });
        }
    });
</script>
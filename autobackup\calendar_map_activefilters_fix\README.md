# Calendar Map ActiveFilters Fix

## Issue
The calendar map view was throwing an `Uncaught ReferenceError: activeFilters is not defined` error at line 1663 in the `updateCalendarFilters` function.

## Root Cause
The `activeFilters` variable was defined inside the DOMContentLoaded event listener in `calendar-filters.js` and exposed globally through `window.calendarFilters.activeFilters`, but the `map.php` file was trying to access it directly as `activeFilters` instead of through the proper global reference.

## Solution
1. **Added Helper Function**: Created a `getActiveFilters()` helper function that consistently returns the correct activeFilters object, whether it's available through `window.calendarFilters.activeFilters` or needs to be created as a fallback.

2. **Updated All References**: Replaced all direct `activeFilters` references throughout the `map.php` file to use the helper function.

3. **Maintained Compatibility**: The solution maintains backward compatibility by providing a fallback mechanism when the calendar filters system is not available.

## Files Modified
- `views/calendar/map.php` - Fixed all activeFilters references

## Changes Made
- Added `getActiveFilters()` helper function
- Updated `updateCalendarFilters()` function
- Updated `searchLocation()` function  
- Updated `resetFilters()` function
- Updated `initEventListeners()` function and its nested `updateMapWithLocation()` function

## Testing
The fix ensures that:
1. The activeFilters object is properly accessible throughout the map view
2. All filter operations work correctly
3. The map integrates properly with the calendar filter system
4. Fallback behavior works when the filter system is not available

## Version
Applied in version 3.35.67
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <!-- Navigation Bar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Map</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-outline-primary">
                    <i class="fas fa-calendar-alt"></i> Calendar View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-primary active">
                    <i class="fas fa-map-marked-alt"></i> Map View
                </a>
                <?php if (isLoggedIn() && hasPermission('calendar_manage')): ?>
                <a href="<?php echo URLROOT; ?>/calendar/manage" class="btn btn-outline-primary">
                    <i class="fas fa-cog"></i> Manage
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Sidebar with Filters -->
        <div class="col-md-3 mb-4">
            <!-- Calendar List -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-alt me-2"></i> Calendars
                    </h5>
                </div>
                <div class="card-body">
                    <div id="calendar-list">
                        <?php foreach ($data['calendars'] as $calendar): ?>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input calendar-toggle" type="checkbox" 
                                       id="quick-calendar-<?php echo $calendar->id; ?>" 
                                       value="<?php echo $calendar->id; ?>" checked>
                                <label class="form-check-label" for="quick-calendar-<?php echo $calendar->id; ?>">
                                    <span class="color-dot" style="background-color: <?php echo $calendar->color; ?>"></span>
                                    <?php echo $calendar->name; ?>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Advanced Filter -->
            <?php require APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Map Legend -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i> Map Legend
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <i class="fas fa-map-marker-alt text-danger"></i> Event Location
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-circle text-primary"></i> Current Search Center
                    </div>
                    <div>
                        <i class="fas fa-circle-notch text-primary"></i> Search Radius
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Map Area -->
        <div class="col-md-9">
            <!-- Map Container -->
            <div class="card">
                <div class="card-body p-0">
                    <div id="map-container">
                        <div id="map" style="height: 600px;"></div>
                        <div id="map-loading" class="map-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <div class="mt-2">Loading events...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h4 id="event-title" class="mb-3"></h4>
                
                <div class="mb-3">
                    <i class="fas fa-calendar-day me-2"></i>
                    <span id="event-date"></span>
                </div>
                
                <div class="mb-3">
                    <i class="fas fa-clock me-2"></i>
                    <span id="event-time"></span>
                </div>
                
                <div class="mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    <span id="event-calendar"></span>
                </div>
                
                <div id="event-location-container" class="mb-3">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span id="event-location"></span>
                </div>
                
                <div id="event-show-container" class="mb-3">
                    <i class="fas fa-car-side me-2"></i>
                    <span id="event-show"></span>
                </div>
                
                <div id="event-description-container" class="mb-3">
                    <h6>Description:</h6>
                    <div id="event-description"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a id="event-details-link" href="#" class="btn btn-primary">View Details</a>
            </div>
        </div>
    </div>
</div>

<style>
#map-container {
    position: relative;
}

.map-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.color-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.event-marker-popup {
    max-width: 300px;
}

.event-marker-popup h5 {
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 16px;
    font-weight: 600;
}

.event-marker-popup .event-date {
    font-size: 14px;
    margin-bottom: 5px;
}

.event-marker-popup .event-location {
    font-size: 14px;
    margin-bottom: 10px;
}

.event-marker-popup .badge {
    margin-right: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}
</style>

<script>
    // Define constants for JavaScript
    const URLROOT = '<?php echo URLROOT; ?>';
    const DEBUG_MODE = <?php echo defined('DEBUG_MODE') && DEBUG_MODE ? 'true' : 'false'; ?>;
</script>
<script src="<?php echo URLROOT; ?>/public/js/calendar-filters.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Map initialization variables
    let map;
    let markers = [];
    let searchCircle;
    let searchMarker;
    let geocoder;
    let infoWindow;
    let mapProvider = '<?php echo $data['mapSettings']['provider']; ?>';
    let apiKey = '<?php echo $data['mapSettings']['api_key']; ?>';
    let tileUrl = '<?php echo $data['mapSettings']['tile_url']; ?>';
    let markerType = '<?php echo $data['markerSettings']['marker_type']; ?>';
    let customMarkerUrl = '<?php echo $data['markerSettings']['custom_marker_url']; ?>';
    let defaultLat = <?php echo $data['mapSettings']['default_lat']; ?>;
    let defaultLng = <?php echo $data['mapSettings']['default_lng']; ?>;
    let defaultZoom = <?php echo $data['mapSettings']['default_zoom']; ?>;
    
    // Set the current view for the filter system
    if (window.calendarFilters) {
        window.calendarFilters.activeFilters.currentView = 'map';
    }
    
    // Initialize map based on provider
    initMap();
    
    // Initialize calendar toggles
    document.querySelectorAll('.calendar-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            // Reload events when a calendar is toggled
            loadEvents();
        });
    });
    
    // Apply filters button
    document.getElementById('apply-filters').addEventListener('click', function() {
        loadEvents();
    });
    
    // Reset filters button
    document.getElementById('reset-filters').addEventListener('click', function() {
        if (window.calendarFilters && typeof window.calendarFilters.resetFilters === 'function') {
            window.calendarFilters.resetFilters();
        }
    });
    
    // Search location button
    document.getElementById('search-location-btn').addEventListener('click', function() {
        searchLocation();
    });
    
    // Location search input
    document.getElementById('location-search').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            searchLocation();
        }
    });
    
    // Radius filter
    document.getElementById('radius-filter').addEventListener('input', function() {
        document.getElementById('radius-value').textContent = this.value;
        
        // Update search circle if it exists
        if (searchCircle && searchMarker) {
            const radius = parseInt(this.value) * 1609.34; // Convert miles to meters
            searchCircle.setRadius(radius);
            
            // Adjust map bounds to fit the circle
            if (map.fitBounds) {
                const bounds = searchCircle.getBounds();
                map.fitBounds(bounds);
            }
        }
    });
    
    /**
     * Initialize the map based on the selected provider
     */
    function initMap() {
        // Show loading indicator
        document.getElementById('map-loading').style.display = 'flex';
        
        try {
            switch (mapProvider) {
                case 'google':
                    initGoogleMap();
                    break;
                case 'leaflet':
                    initLeafletMap();
                    break;
                case 'mapbox':
                    initMapboxMap();
                    break;
                case 'openstreetmap':
                    initOpenStreetMap();
                    break;
                case 'here':
                    initHereMap();
                    break;
                default:
                    // Default to Leaflet with OpenStreetMap
                    initLeafletMap();
            }
        } catch (error) {
            console.error('Error initializing map:', error);
            document.getElementById('map').innerHTML = `
                <div class="alert alert-danger">
                    <strong>Error initializing map:</strong> ${error.message}
                    <br>
                    <small>Please try refreshing the page or contact support if the problem persists.</small>
                </div>
            `;
            document.getElementById('map-loading').style.display = 'none';
        }
    }
    
    /**
     * Initialize Google Maps
     */
    function initGoogleMap() {
        // Load Google Maps API
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&callback=initGoogleMapCallback`;
            script.async = true;
            script.defer = true;
            document.head.appendChild(script);
            
            // Define callback function
            window.initGoogleMapCallback = function() {
                createGoogleMap();
            };
        } else {
            createGoogleMap();
        }
    }
    
    /**
     * Create Google Map instance
     */
    function createGoogleMap() {
        // Create map
        map = new google.maps.Map(document.getElementById('map'), {
            center: { lat: defaultLat, lng: defaultLng },
            zoom: defaultZoom,
            mapTypeId: google.maps.MapTypeId.ROADMAP
        });
        
        // Create geocoder
        geocoder = new google.maps.Geocoder();
        
        // Create info window
        infoWindow = new google.maps.InfoWindow();
        
        // Load events when map is ready
        google.maps.event.addListenerOnce(map, 'idle', function() {
            loadEvents();
        });
    }
    
    /**
     * Initialize Leaflet with OpenStreetMap
     */
    function initLeafletMap() {
        // Load Leaflet CSS
        if (!document.getElementById('leaflet-css')) {
            const link = document.createElement('link');
            link.id = 'leaflet-css';
            link.rel = 'stylesheet';
            link.href = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css';
            document.head.appendChild(link);
        }
        
        // Load Leaflet JS
        if (typeof L === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js';
            script.onload = function() {
                createLeafletMap();
            };
            document.head.appendChild(script);
        } else {
            createLeafletMap();
        }
    }
    
    /**
     * Create Leaflet Map instance
     */
    function createLeafletMap() {
        // Create map
        map = L.map('map').setView([defaultLat, defaultLng], defaultZoom);
        
        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Create geocoder
        geocoder = {
            geocode: function(request, callback) {
                const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(request.address)}`;
                
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            const result = data[0];
                            callback([{
                                geometry: {
                                    location: {
                                        lat: parseFloat(result.lat),
                                        lng: parseFloat(result.lon)
                                    }
                                }
                            }], 'OK');
                        } else {
                            callback([], 'ZERO_RESULTS');
                        }
                    })
                    .catch(error => {
                        console.error('Geocoding error:', error);
                        callback([], 'ERROR');
                    });
            }
        };
        
        // Load events when map is ready
        setTimeout(function() {
            loadEvents();
        }, 500);
    }
    
    /**
     * Initialize Mapbox
     */
    function initMapboxMap() {
        // Load Mapbox CSS
        if (!document.getElementById('mapbox-css')) {
            const link = document.createElement('link');
            link.id = 'mapbox-css';
            link.rel = 'stylesheet';
            link.href = 'https://api.mapbox.com/mapbox-gl-js/v2.6.1/mapbox-gl.css';
            document.head.appendChild(link);
        }
        
        // Load Mapbox JS
        if (typeof mapboxgl === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://api.mapbox.com/mapbox-gl-js/v2.6.1/mapbox-gl.js';
            script.onload = function() {
                createMapboxMap();
            };
            document.head.appendChild(script);
        } else {
            createMapboxMap();
        }
    }
    
    /**
     * Create Mapbox Map instance
     */
    function createMapboxMap() {
        // Set access token
        mapboxgl.accessToken = apiKey;
        
        // Create map
        map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [defaultLng, defaultLat],
            zoom: defaultZoom
        });
        
        // Create geocoder
        geocoder = {
            geocode: function(request, callback) {
                const url = `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(request.address)}.json?access_token=${apiKey}`;
                
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.features && data.features.length > 0) {
                            const result = data.features[0];
                            callback([{
                                geometry: {
                                    location: {
                                        lat: result.center[1],
                                        lng: result.center[0]
                                    }
                                }
                            }], 'OK');
                        } else {
                            callback([], 'ZERO_RESULTS');
                        }
                    })
                    .catch(error => {
                        console.error('Geocoding error:', error);
                        callback([], 'ERROR');
                    });
            }
        };
        
        // Load events when map is ready
        map.on('load', function() {
            loadEvents();
        });
    }
    
    /**
     * Initialize OpenStreetMap with custom tile provider
     */
    function initOpenStreetMap() {
        // Load Leaflet CSS
        if (!document.getElementById('leaflet-css')) {
            const link = document.createElement('link');
            link.id = 'leaflet-css';
            link.rel = 'stylesheet';
            link.href = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css';
            document.head.appendChild(link);
        }
        
        // Load Leaflet JS
        if (typeof L === 'undefined') {
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js';
            script.onload = function() {
                createOpenStreetMap();
            };
            document.head.appendChild(script);
        } else {
            createOpenStreetMap();
        }
    }
    
    /**
     * Create OpenStreetMap instance with custom tile provider
     */
    function createOpenStreetMap() {
        // Create map
        map = L.map('map').setView([defaultLat, defaultLng], defaultZoom);
        
        // Add tile layer
        L.tileLayer(tileUrl || 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Create geocoder
        geocoder = {
            geocode: function(request, callback) {
                const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(request.address)}`;
                
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.length > 0) {
                            const result = data[0];
                            callback([{
                                geometry: {
                                    location: {
                                        lat: parseFloat(result.lat),
                                        lng: parseFloat(result.lon)
                                    }
                                }
                            }], 'OK');
                        } else {
                            callback([], 'ZERO_RESULTS');
                        }
                    })
                    .catch(error => {
                        console.error('Geocoding error:', error);
                        callback([], 'ERROR');
                    });
            }
        };
        
        // Load events when map is ready
        setTimeout(function() {
            loadEvents();
        }, 500);
    }
    
    /**
     * Initialize HERE Maps
     */
    function initHereMap() {
        // Load HERE Maps CSS
        if (!document.getElementById('here-css')) {
            const link = document.createElement('link');
            link.id = 'here-css';
            link.rel = 'stylesheet';
            link.href = 'https://js.api.here.com/v3/3.1/mapsjs-ui.css';
            document.head.appendChild(link);
        }
        
        // Load HERE Maps JS
        if (typeof H === 'undefined') {
            const scripts = [
                'https://js.api.here.com/v3/3.1/mapsjs-core.js',
                'https://js.api.here.com/v3/3.1/mapsjs-service.js',
                'https://js.api.here.com/v3/3.1/mapsjs-ui.js',
                'https://js.api.here.com/v3/3.1/mapsjs-mapevents.js'
            ];
            
            let loadedScripts = 0;
            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = function() {
                    loadedScripts++;
                    if (loadedScripts === scripts.length) {
                        createHereMap();
                    }
                };
                document.head.appendChild(script);
            });
        } else {
            createHereMap();
        }
    }
    
    /**
     * Create HERE Map instance
     */
    function createHereMap() {
        // Initialize the platform
        const platform = new H.service.Platform({
            apikey: apiKey
        });
        
        // Get default layers
        const defaultLayers = platform.createDefaultLayers();
        
        // Create map
        map = new H.Map(
            document.getElementById('map'),
            defaultLayers.vector.normal.map,
            {
                center: { lat: defaultLat, lng: defaultLng },
                zoom: defaultZoom,
                pixelRatio: window.devicePixelRatio || 1
            }
        );
        
        // Add event listeners
        window.addEventListener('resize', () => map.getViewPort().resize());
        
        // Add UI and interaction
        const ui = H.ui.UI.createDefault(map, defaultLayers);
        const mapEvents = new H.mapevents.MapEvents(map);
        const behavior = new H.mapevents.Behavior(mapEvents);
        
        // Create geocoder
        const geocodingService = platform.getSearchService();
        geocoder = {
            geocode: function(request, callback) {
                geocodingService.geocode({
                    q: request.address,
                    limit: 1
                }, result => {
                    if (result.items && result.items.length > 0) {
                        const position = result.items[0].position;
                        callback([{
                            geometry: {
                                location: {
                                    lat: position.lat,
                                    lng: position.lng
                                }
                            }
                        }], 'OK');
                    } else {
                        callback([], 'ZERO_RESULTS');
                    }
                }, error => {
                    console.error('Geocoding error:', error);
                    callback([], 'ERROR');
                });
            }
        };
        
        // Load events when map is ready
        setTimeout(function() {
            loadEvents();
        }, 500);
    }
    
    /**
     * Load events from the server and display them on the map
     */
    function loadEvents() {
        // Show loading indicator
        document.getElementById('map-loading').style.display = 'flex';
        
        // Clear existing markers
        clearMarkers();
        
        // Get visible calendars
        const visibleCalendars = [];
        document.querySelectorAll('.calendar-toggle:checked').forEach(toggle => {
            visibleCalendars.push(toggle.value);
        });
        
        // Build URL with parameters
        let url = '<?php echo URLROOT; ?>/calendar/mapEvents';
        
        // Use the filter system if available
        if (window.calendarFilters && typeof window.calendarFilters.getFilterParams === 'function') {
            const filterParams = window.calendarFilters.getFilterParams();
            if (filterParams) {
                url += '?' + filterParams;
            } else {
                // Fallback to just calendar IDs
                url += '?calendar_id=' + visibleCalendars.join(',');
            }
        } else {
            // Fallback to just calendar IDs
            url += '?calendar_id=' + visibleCalendars.join(',');
        }
        
        // Fetch events
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(events => {
                // Add markers for each event
                events.forEach(event => {
                    addMarker(event);
                });
                
                // Hide loading indicator
                document.getElementById('map-loading').style.display = 'none';
                
                // If no events, show message
                if (events.length === 0) {
                    document.getElementById('map').innerHTML = `
                        <div class="alert alert-info m-3">
                            <i class="fas fa-info-circle me-2"></i>
                            No events found with the current filters.
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading events:', error);
                document.getElementById('map-loading').style.display = 'none';
                document.getElementById('map').innerHTML = `
                    <div class="alert alert-danger m-3">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Error loading events: ${error.message}
                    </div>
                `;
            });
    }
    
    /**
     * Add a marker for an event
     * 
     * @param {Object} event Event data
     */
    function addMarker(event) {
        if (!map) return;
        
        const position = {
            lat: parseFloat(event.lat),
            lng: parseFloat(event.lng)
        };
        
        // Format date
        const startDate = new Date(event.start);
        const formattedDate = startDate.toLocaleDateString(undefined, { 
            weekday: 'short', 
            month: 'short', 
            day: 'numeric' 
        });
        
        const formattedTime = event.allDay ? 
            'All day' : 
            startDate.toLocaleTimeString(undefined, { 
                hour: 'numeric', 
                minute: '2-digit' 
            });
        
        // Create popup content
        const popupContent = `
            <div class="event-marker-popup">
                <h5>${event.title}</h5>
                <div class="event-date">
                    <i class="fas fa-calendar-day me-1"></i> ${formattedDate}
                    <i class="fas fa-clock ms-2 me-1"></i> ${formattedTime}
                </div>
                <div class="event-location">
                    <i class="fas fa-map-marker-alt me-1"></i> ${event.location}
                </div>
                <div>
                    <span class="badge bg-secondary">${event.calendar_name}</span>
                    ${event.show_name ? `<span class="badge bg-info">${event.show_name}</span>` : ''}
                </div>
                <div class="mt-2">
                    <a href="<?php echo URLROOT; ?>/calendar/event/${event.id}" class="btn btn-sm btn-primary">View Details</a>
                </div>
            </div>
        `;
        
        // Create marker based on map provider
        let marker;
        
        if (mapProvider === 'google' && typeof google !== 'undefined') {
            // Google Maps marker
            marker = new google.maps.Marker({
                position: position,
                map: map,
                title: event.title,
                icon: markerType === 'custom' && customMarkerUrl ? customMarkerUrl : null
            });
            
            // Add click event
            marker.addListener('click', function() {
                if (infoWindow) {
                    infoWindow.setContent(popupContent);
                    infoWindow.open(map, marker);
                }
            });
        } else if ((mapProvider === 'leaflet' || mapProvider === 'openstreetmap') && typeof L !== 'undefined') {
            // Leaflet marker
            marker = L.marker([position.lat, position.lng], {
                title: event.title,
                icon: markerType === 'custom' && customMarkerUrl ? L.icon({
                    iconUrl: customMarkerUrl,
                    iconSize: [25, 41],
                    iconAnchor: [12, 41],
                    popupAnchor: [1, -34]
                }) : null
            }).addTo(map);
            
            // Add popup
            marker.bindPopup(popupContent);
        } else if (mapProvider === 'mapbox' && typeof mapboxgl !== 'undefined') {
            // Mapbox marker
            const popup = new mapboxgl.Popup({ offset: 25 })
                .setHTML(popupContent);
            
            marker = new mapboxgl.Marker({
                color: markerType === 'custom' && customMarkerUrl ? null : '#FF0000'
            })
                .setLngLat([position.lng, position.lat])
                .setPopup(popup)
                .addTo(map);
        } else if (mapProvider === 'here' && typeof H !== 'undefined') {
            // HERE Maps marker
            marker = new H.map.Marker(position);
            map.addObject(marker);
            
            // Add event listener
            marker.addEventListener('tap', function(evt) {
                const bubble = new H.ui.InfoBubble(position, {
                    content: popupContent
                });
                ui.addBubble(bubble);
            });
        }
        
        // Store marker for later removal
        if (marker) {
            markers.push(marker);
        }
    }
    
    /**
     * Clear all markers from the map
     */
    function clearMarkers() {
        if (!map) return;
        
        if (mapProvider === 'google' && typeof google !== 'undefined') {
            // Google Maps
            markers.forEach(marker => {
                marker.setMap(null);
            });
        } else if ((mapProvider === 'leaflet' || mapProvider === 'openstreetmap') && typeof L !== 'undefined') {
            // Leaflet
            markers.forEach(marker => {
                map.removeLayer(marker);
            });
        } else if (mapProvider === 'mapbox' && typeof mapboxgl !== 'undefined') {
            // Mapbox
            markers.forEach(marker => {
                marker.remove();
            });
        } else if (mapProvider === 'here' && typeof H !== 'undefined') {
            // HERE Maps
            map.removeObjects(markers);
        }
        
        // Clear markers array
        markers = [];
    }
    
    /**
     * Search for a location and update the map
     */
    function searchLocation() {
        const locationInput = document.getElementById('location-search').value;
        
        if (!locationInput) {
            return;
        }
        
        if (geocoder) {
            geocoder.geocode({ address: locationInput }, function(results, status) {
                const okStatus = typeof google !== 'undefined' ? google.maps.GeocoderStatus.OK : 'OK';
                
                if (status === okStatus && results[0]) {
                    const location = results[0].geometry.location;
                    const lat = typeof location.lat === 'function' ? location.lat() : location.lat;
                    const lng = typeof location.lng === 'function' ? location.lng() : location.lng;
                    
                    // Update map with location
                    updateMapWithLocation(lat, lng);
                    
                    // Update filter values
                    if (window.calendarFilters) {
                        window.calendarFilters.activeFilters.lat = lat;
                        window.calendarFilters.activeFilters.lng = lng;
                        window.calendarFilters.activeFilters.radius = parseInt(document.getElementById('radius-filter').value);
                    }
                    
                    // Reload events
                    loadEvents();
                } else {
                    alert('Location not found. Please try a different search term.');
                }
            });
        }
    }
    
    /**
     * Update the map with a new location
     * 
     * @param {number} lat Latitude
     * @param {number} lng Longitude
     */
    function updateMapWithLocation(lat, lng) {
        if (!map) return;
        
        // Center map on location
        if (mapProvider === 'google' && typeof google !== 'undefined') {
            // Google Maps
            map.setCenter({ lat, lng });
            map.setZoom(10);
            
            // Add marker for search location
            if (searchMarker) {
                searchMarker.setMap(null);
            }
            
            searchMarker = new google.maps.Marker({
                position: { lat, lng },
                map: map,
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 8,
                    fillColor: '#4285F4',
                    fillOpacity: 1,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 2
                }
            });
            
            // Add circle for search radius
            if (searchCircle) {
                searchCircle.setMap(null);
            }
            
            const radius = parseInt(document.getElementById('radius-filter').value) * 1609.34; // Convert miles to meters
            
            searchCircle = new google.maps.Circle({
                strokeColor: '#4285F4',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#4285F4',
                fillOpacity: 0.1,
                map: map,
                center: { lat, lng },
                radius: radius
            });
            
            // Fit map to circle
            map.fitBounds(searchCircle.getBounds());
        } else if ((mapProvider === 'leaflet' || mapProvider === 'openstreetmap') && typeof L !== 'undefined') {
            // Leaflet
            map.setView([lat, lng], 10);
            
            // Add marker for search location
            if (searchMarker) {
                map.removeLayer(searchMarker);
            }
            
            searchMarker = L.circleMarker([lat, lng], {
                radius: 8,
                fillColor: '#4285F4',
                color: '#FFFFFF',
                weight: 2,
                opacity: 1,
                fillOpacity: 1
            }).addTo(map);
            
            // Add circle for search radius
            if (searchCircle) {
                map.removeLayer(searchCircle);
            }
            
            const radius = parseInt(document.getElementById('radius-filter').value) * 1609.34; // Convert miles to meters
            
            searchCircle = L.circle([lat, lng], {
                color: '#4285F4',
                fillColor: '#4285F4',
                fillOpacity: 0.1,
                radius: radius
            }).addTo(map);
            
            // Fit map to circle
            map.fitBounds(searchCircle.getBounds());
        } else if (mapProvider === 'mapbox' && typeof mapboxgl !== 'undefined') {
            // Mapbox
            map.flyTo({
                center: [lng, lat],
                zoom: 10
            });
            
            // Add marker for search location
            if (searchMarker) {
                searchMarker.remove();
            }
            
            searchMarker = new mapboxgl.Marker({
                color: '#4285F4'
            })
                .setLngLat([lng, lat])
                .addTo(map);
            
            // Add circle for search radius
            // Note: Mapbox doesn't have built-in circles, would need to use a custom solution
        } else if (mapProvider === 'here' && typeof H !== 'undefined') {
            // HERE Maps
            map.setCenter({ lat, lng });
            map.setZoom(10);
            
            // Add marker for search location
            if (searchMarker) {
                map.removeObject(searchMarker);
            }
            
            searchMarker = new H.map.Marker({ lat, lng });
            map.addObject(searchMarker);
            
            // Add circle for search radius
            if (searchCircle) {
                map.removeObject(searchCircle);
            }
            
            const radius = parseInt(document.getElementById('radius-filter').value) * 1609.34; // Convert miles to meters
            
            searchCircle = new H.map.Circle(
                { lat, lng },
                radius,
                {
                    style: {
                        strokeColor: '#4285F4',
                        lineWidth: 2,
                        fillColor: 'rgba(66, 133, 244, 0.1)'
                    }
                }
            );
            
            map.addObject(searchCircle);
            
            // Fit map to circle
            map.getViewModel().setLookAtData({
                bounds: searchCircle.getBoundingBox()
            });
        }
    }
    
    // Expose functions to global scope for use by other scripts
    window.mapEvents = {
        loadEvents: loadEvents,
        searchLocation: searchLocation,
        updateMapWithLocation: updateMapWithLocation
    };
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
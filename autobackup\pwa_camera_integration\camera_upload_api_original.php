<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Enable CORS for PWA
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if user is logged in
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

try {
    // Validate required fields
    if (!isset($_FILES['image']) || !isset($_POST['entity_type']) || !isset($_POST['entity_id'])) {
        throw new Exception('Missing required fields');
    }

    $entityType = $_POST['entity_type'];
    $entityId = (int)$_POST['entity_id'];
    $uploadedFile = $_FILES['image'];

    // Validate entity type
    $allowedTypes = ['vehicle', 'event', 'show'];
    if (!in_array($entityType, $allowedTypes)) {
        throw new Exception('Invalid entity type');
    }

    // Validate file upload
    if ($uploadedFile['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $uploadedFile['error']);
    }

    // Validate file type
    $allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $uploadedFile['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedMimeTypes)) {
        throw new Exception('Invalid file type. Only images are allowed.');
    }

    // Validate file size (max 10MB)
    $maxSize = 10 * 1024 * 1024; // 10MB
    if ($uploadedFile['size'] > $maxSize) {
        throw new Exception('File too large. Maximum size is 10MB.');
    }

    // Verify entity ownership based on type
    $userId = $_SESSION['user_id'];
    
    if ($entityType === 'vehicle') {
        $stmt = $pdo->prepare("SELECT id FROM vehicles WHERE id = ? AND user_id = ?");
        $stmt->execute([$entityId, $userId]);
        if (!$stmt->fetch()) {
            throw new Exception('Vehicle not found or access denied');
        }
    } elseif ($entityType === 'event') {
        $stmt = $pdo->prepare("SELECT id FROM events WHERE id = ? AND user_id = ?");
        $stmt->execute([$entityId, $userId]);
        if (!$stmt->fetch()) {
            throw new Exception('Event not found or access denied');
        }
    } elseif ($entityType === 'show') {
        $stmt = $pdo->prepare("SELECT id FROM shows WHERE id = ? AND user_id = ?");
        $stmt->execute([$entityId, $userId]);
        if (!$stmt->fetch()) {
            throw new Exception('Show not found or access denied');
        }
    }

    // Create upload directory if it doesn't exist
    $uploadDir = '../uploads/' . $entityType . 's/' . $entityId . '/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $extension = pathinfo($uploadedFile['name'], PATHINFO_EXTENSION);
    if (empty($extension)) {
        // Determine extension from MIME type
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp'
        ];
        $extension = $mimeToExt[$mimeType] ?? 'jpg';
    }

    $filename = 'camera_' . time() . '_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;

    // Move uploaded file
    if (!move_uploaded_file($uploadedFile['tmp_name'], $filepath)) {
        throw new Exception('Failed to save uploaded file');
    }

    // Insert image record into database
    $imageUrl = '/uploads/' . $entityType . 's/' . $entityId . '/' . $filename;
    
    $stmt = $pdo->prepare("
        INSERT INTO images (entity_type, entity_id, filename, file_path, file_size, mime_type, uploaded_by, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        $entityType,
        $entityId,
        $filename,
        $imageUrl,
        $uploadedFile['size'],
        $mimeType,
        $userId
    ]);

    $imageId = $pdo->lastInsertId();

    // Debug logging
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("[PWA Camera Upload] Successfully uploaded image ID: $imageId for $entityType $entityId");
    }

    echo json_encode([
        'success' => true,
        'message' => 'Image uploaded successfully',
        'image_id' => $imageId,
        'image_url' => $imageUrl,
        'filename' => $filename
    ]);

} catch (Exception $e) {
    // Debug logging
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("[PWA Camera Upload] Error: " . $e->getMessage());
    }
    
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
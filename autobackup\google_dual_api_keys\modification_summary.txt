Backup created: 2025-01-25
Modification: Added separate Google API key support for client-side and server-side operations

Files Modified:
1. views/calendar/map_settings.php - Added server-side API key field with Google-specific instructions
2. views/admin/settings_calendar.php - Added server-side API key field to admin settings
3. controllers/CalendarController.php - Updated mapSettings method to handle server_api_key
4. controllers/AdminController.php - Updated admin settings_calendar method with validation
5. models/CalendarModel.php - Added server_api_key to map provider settings
6. helpers/geocoding_helper.php - Updated to use server API key for Google Geocoding

Changes Made:
- Added separate "Server-Side API Key" field for Google Places & Geocoding APIs
- Updated validation to require server API key when Google is selected
- Modified venue search functions to use server API key for Google Places API
- Updated geocoding helper to use server API key for Google Geocoding API
- Added informational alert explaining Google's API key requirements
- Updated JavaScript to show/hide server API key field based on provider selection

Google API Key Requirements:
- Client-Side Key: Maps JavaScript API with HTTP referrer restrictions
- Server-Side Key: Places API + Geocoding API with IP address restrictions
- Both keys must be from the same Google Cloud project

This resolves the "REQUEST_DENIED - API keys with referer restrictions cannot be used with this API" error.
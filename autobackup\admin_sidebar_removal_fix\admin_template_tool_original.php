<?php
/**
 * Admin Template Assignment Tool
 * 
 * This tool allows administrators to assign templates to entities (shows, vehicles, registrations)
 * and view current template assignments.
 */

// Include configuration
require_once '../config/config.php';

// Check if user is logged in and is admin
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ' . BASE_URL . '/auth/login');
    exit();
}

// Database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'assign':
                $entityType = $_POST['entity_type'];
                $entityId = $_POST['entity_id'];
                $templateId = $_POST['template_id'];
                
                try {
                    // Determine the correct table and column names
                    $table = '';
                    switch ($entityType) {
                        case 'show':
                            $table = 'shows';
                            break;
                        case 'vehicle':
                            $table = 'vehicles';
                            break;
                        case 'registration':
                            $table = 'registrations';
                            break;
                        default:
                            throw new Exception('Invalid entity type');
                    }
                    
                    // Update the entity with the template ID
                    $stmt = $pdo->prepare("UPDATE {$table} SET template_id = ? WHERE id = ?");
                    $stmt->execute([$templateId, $entityId]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = "Template assigned successfully to {$entityType} #{$entityId}";
                        $messageType = 'success';
                    } else {
                        $message = "No {$entityType} found with ID #{$entityId}";
                        $messageType = 'warning';
                    }
                } catch (Exception $e) {
                    $message = "Error assigning template: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'remove':
                $entityType = $_POST['entity_type'];
                $entityId = $_POST['entity_id'];
                
                try {
                    // Determine the correct table
                    $table = '';
                    switch ($entityType) {
                        case 'show':
                            $table = 'shows';
                            break;
                        case 'vehicle':
                            $table = 'vehicles';
                            break;
                        case 'registration':
                            $table = 'registrations';
                            break;
                        default:
                            throw new Exception('Invalid entity type');
                    }
                    
                    // Remove the template assignment (set to NULL)
                    $stmt = $pdo->prepare("UPDATE {$table} SET template_id = NULL WHERE id = ?");
                    $stmt->execute([$entityId]);
                    
                    if ($stmt->rowCount() > 0) {
                        $message = "Template removed successfully from {$entityType} #{$entityId}";
                        $messageType = 'success';
                    } else {
                        $message = "No {$entityType} found with ID #{$entityId}";
                        $messageType = 'warning';
                    }
                } catch (Exception $e) {
                    $message = "Error removing template: " . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get all available templates
$templatesStmt = $pdo->query("SELECT * FROM templates ORDER BY type, name");
$templates = $templatesStmt->fetchAll(PDO::FETCH_OBJ);

// Group templates by type
$templatesByType = [];
foreach ($templates as $template) {
    $templatesByType[$template->type][] = $template;
}

// Get entities with assigned templates
$showsStmt = $pdo->query("
    SELECT s.id, s.name, t.name as template_name, t.type as template_type 
    FROM shows s 
    LEFT JOIN templates t ON s.template_id = t.id 
    WHERE s.template_id IS NOT NULL
    ORDER BY s.name
");
$showsWithTemplates = $showsStmt->fetchAll(PDO::FETCH_OBJ);

$vehiclesStmt = $pdo->query("
    SELECT v.id, CONCAT(v.year, ' ', v.make, ' ', v.model) as name, t.name as template_name, t.type as template_type 
    FROM vehicles v 
    LEFT JOIN templates t ON v.template_id = t.id 
    WHERE v.template_id IS NOT NULL
    ORDER BY v.year, v.make, v.model
");
$vehiclesWithTemplates = $vehiclesStmt->fetchAll(PDO::FETCH_OBJ);

$registrationsStmt = $pdo->query("
    SELECT r.id, CONCAT('Registration #', r.id, ' - ', s.name) as name, t.name as template_name, t.type as template_type 
    FROM registrations r 
    LEFT JOIN shows s ON r.show_id = s.id
    LEFT JOIN templates t ON r.template_id = t.id 
    WHERE r.template_id IS NOT NULL
    ORDER BY r.id
");
$registrationsWithTemplates = $registrationsStmt->fetchAll(PDO::FETCH_OBJ);

// Include header
require_once APPROOT . '/views/includes/header.php';
?>
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <?php require APPROOT . '/views/includes/admin_settings_sidebar.php'; ?>
            </div>
            <div class="col-md-9">
        <h1 class="mb-4">Entity Template Assignment Tool</h1>
        
        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Assign Template to Entity</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <input type="hidden" name="action" value="assign">
                            
                            <div class="mb-3">
                                <label for="entity_type" class="form-label">Entity Type</label>
                                <select class="form-select" id="entity_type" name="entity_type" required>
                                    <option value="">Select entity type...</option>
                                    <option value="show">Show</option>
                                    <option value="vehicle">Vehicle</option>
                                    <option value="registration">Registration</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="entity_id" class="form-label">Entity ID</label>
                                <input type="number" class="form-control" id="entity_id" name="entity_id" required>
                                <div class="form-text">Enter the ID of the entity you want to assign a template to.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="template_id" class="form-label">Template</label>
                                <select class="form-select" id="template_id" name="template_id" required>
                                    <option value="">Select template...</option>
                                    <?php foreach ($templatesByType as $type => $typeTemplates): ?>
                                        <optgroup label="<?php echo ucfirst($type); ?> Templates" data-type="<?php echo $type; ?>">
                                            <?php foreach ($typeTemplates as $template): ?>
                                                <option value="<?php echo $template->id; ?>" data-type="<?php echo $template->type; ?>">
                                                    <?php echo htmlspecialchars($template->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </optgroup>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Assign Template</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">Template Type Guidelines</h5>
                    </div>
                    <div class="card-body">
                        <ul class="mb-0">
                            <li><strong>Shows</strong>: Use templates of type "event"</li>
                            <li><strong>Vehicles</strong>: Use templates of type "vehicle"</li>
                            <li><strong>Registrations</strong>: Use templates of type "show"</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">Current Template Assignments</h5>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="shows-tab" data-bs-toggle="tab" data-bs-target="#shows" type="button" role="tab" aria-controls="shows" aria-selected="true">Shows</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="vehicles-tab" data-bs-toggle="tab" data-bs-target="#vehicles" type="button" role="tab" aria-controls="vehicles" aria-selected="false">Vehicles</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="registrations-tab" data-bs-toggle="tab" data-bs-target="#registrations" type="button" role="tab" aria-controls="registrations" aria-selected="false">Registrations</button>
                            </li>
                        </ul>
                        <div class="tab-content pt-3" id="myTabContent">
                            <div class="tab-pane fade show active" id="shows" role="tabpanel" aria-labelledby="shows-tab">
                                <?php if (empty($showsWithTemplates)): ?>
                                    <p class="text-muted">No shows with assigned templates.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Name</th>
                                                    <th>Template</th>
                                                    <th>Type</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($showsWithTemplates as $show): ?>
                                                    <tr>
                                                        <td><?php echo $show->id; ?></td>
                                                        <td><?php echo htmlspecialchars($show->name); ?></td>
                                                        <td><?php echo htmlspecialchars($show->template_name ?? 'Unknown'); ?></td>
                                                        <td><?php echo htmlspecialchars($show->template_type ?? 'Unknown'); ?></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-danger remove-btn" 
                                                                    data-entity-type="show" 
                                                                    data-entity-id="<?php echo $show->id; ?>"
                                                                    data-entity-name="<?php echo htmlspecialchars($show->name); ?>">
                                                                Remove
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="tab-pane fade" id="vehicles" role="tabpanel" aria-labelledby="vehicles-tab">
                                <?php if (empty($vehiclesWithTemplates)): ?>
                                    <p class="text-muted">No vehicles with assigned templates.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Vehicle</th>
                                                    <th>Template</th>
                                                    <th>Type</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($vehiclesWithTemplates as $vehicle): ?>
                                                    <tr>
                                                        <td><?php echo $vehicle->id; ?></td>
                                                        <td><?php echo htmlspecialchars($vehicle->name); ?></td>
                                                        <td><?php echo htmlspecialchars($vehicle->template_name ?? 'Unknown'); ?></td>
                                                        <td><?php echo htmlspecialchars($vehicle->template_type ?? 'Unknown'); ?></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-danger remove-btn" 
                                                                    data-entity-type="vehicle" 
                                                                    data-entity-id="<?php echo $vehicle->id; ?>"
                                                                    data-entity-name="<?php echo htmlspecialchars($vehicle->name); ?>">
                                                                Remove
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="tab-pane fade" id="registrations" role="tabpanel" aria-labelledby="registrations-tab">
                                <?php if (empty($registrationsWithTemplates)): ?>
                                    <p class="text-muted">No registrations with assigned templates.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="table table-striped table-sm">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Registration</th>
                                                    <th>Template</th>
                                                    <th>Type</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($registrationsWithTemplates as $registration): ?>
                                                    <tr>
                                                        <td><?php echo $registration->id; ?></td>
                                                        <td><?php echo htmlspecialchars($registration->name); ?></td>
                                                        <td><?php echo htmlspecialchars($registration->template_name ?? 'Unknown'); ?></td>
                                                        <td><?php echo htmlspecialchars($registration->template_type ?? 'Unknown'); ?></td>
                                                        <td>
                                                            <button class="btn btn-sm btn-danger remove-btn" 
                                                                    data-entity-type="registration" 
                                                                    data-entity-id="<?php echo $registration->id; ?>"
                                                                    data-entity-name="<?php echo htmlspecialchars($registration->name); ?>">
                                                                Remove
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
            </div>
        </div>
    </div>
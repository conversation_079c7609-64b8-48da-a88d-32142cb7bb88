<!DOCTYPE html>
<html>
<head>
    <title>Constructor Debug</title>
</head>
<body>
    <h2>Constructor Debug</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Define BASE_URL first
        window.BASE_URL = window.location.origin;
        output('BASE_URL set to: ' + BASE_URL);

        // Create a simple test class to see if the issue is with the class itself
        class TestClass {
            constructor() {
                output('TestClass constructor called');
                this.version = 'TEST_VERSION';
                output('TestClass version set to: ' + this.version);
            }
        }

        output('Creating TestClass...');
        const testObj = new TestClass();
        output('TestClass created, version: ' + testObj.version);

        // Now let's manually create the CameraBanner class step by step
        output('--- Now testing CameraBanner step by step ---');
        
        // First, let's see if we can access the class
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Script loaded, length: ' + code.length);
                
                // Execute the script
                try {
                    eval(code);
                    output('Script executed successfully');
                    
                    // Check if class exists
                    if (typeof CameraBanner !== 'undefined') {
                        output('CameraBanner class exists');
                        
                        // Try to create instance manually
                        output('Creating CameraBanner manually...');
                        const manualBanner = new CameraBanner();
                        output('Manual creation successful');
                        output('Manual version: ' + manualBanner.version);
                        output('Manual version type: ' + typeof manualBanner.version);
                        
                        // Check window.cameraBanner
                        if (window.cameraBanner) {
                            output('window.cameraBanner exists');
                            output('window.cameraBanner version: ' + window.cameraBanner.version);
                            output('window.cameraBanner === manualBanner: ' + (window.cameraBanner === manualBanner));
                        }
                        
                    } else {
                        output('CameraBanner class NOT found');
                    }
                    
                } catch (error) {
                    output('Script execution error: ' + error.message);
                    output('Error stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
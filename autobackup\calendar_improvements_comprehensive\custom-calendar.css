/**
 * Custom Calendar CSS - BACKUP BEFORE COMPREHENSIVE IMPROVEMENTS
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 1.0.0
 */

/* Global box-sizing for consistent sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Calendar Container */
.custom-calendar {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.calendar-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.calendar-nav-btn {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-nav-btn:hover {
  background-color: #f1f3f5;
}

.calendar-nav-btn.today {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

.calendar-nav-btn.today:hover {
  background-color: #2d6fae;
}

/* Calendar View Selector */
.calendar-view-selector {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.calendar-view-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-view-btn:hover {
  background-color: #e9ecef;
}

.calendar-view-btn.active {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

/* Month View */
.calendar-month {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-top: 1px solid #e9ecef;
  border-left: 1px solid #e9ecef;
  position: relative; /* For multi-day event positioning */
}

/* Multi-day events container */
.calendar-multi-day-events {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-template-rows: auto repeat(6, 1fr); /* Weekday header + up to 6 weeks */
  pointer-events: none; /* Allow clicks to pass through to day cells */
  z-index: 10;
  gap: 0;
}

/* Spanning event elements */
.calendar-spanning-event {
  pointer-events: auto; /* Re-enable clicks on events */
  margin: 2px 1px;
  height: 18px;
  display: flex;
  align-items: center;
  border-radius: 9px; /* Make ends half-circle round (height/2) */
  font-size: 0.8rem;
  color: white;
  padding: 2px 8px; /* Slightly more padding for rounded ends */
  overflow: hidden; /* Hide overflow for scrolling text */
  white-space: nowrap;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  align-self: start; /* Align to top of grid cell */
  /* Subtle text shadow for better readability on light backgrounds */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.7);
  /* Consistent positioning */
  position: relative;
  top: 2px; /* Consistent vertical positioning */
}

/* Scrolling text animation for long event titles - both spanning and single-day */
.calendar-spanning-event .calendar-event-title,
.calendar-event .calendar-event-title {
  display: inline-block;
  animation: scroll-text 8s linear infinite;
  animation-play-state: paused; /* Start paused */
}

.calendar-spanning-event:hover .calendar-event-title,
.calendar-event:hover .calendar-event-title {
  animation-play-state: running; /* Start scrolling on hover */
}

@keyframes scroll-text {
  0% { transform: translateX(0); }
  20% { transform: translateX(0); }
  80% { transform: translateX(calc(-100% + 120px)); }
  100% { transform: translateX(calc(-100% + 120px)); }
}
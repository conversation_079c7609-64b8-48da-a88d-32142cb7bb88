# Map Usability Improvements

## Issues Fixed
1. **No way to reset map view**: Users had no way to return to the filtered view except refreshing the page
2. **Mouse wheel zoom not working**: Scroll wheel zoom was not enabled for Google Maps and other providers

## Changes Made

### 1. Added "Fit to Events" Button
- **Location**: Map card header
- **Functionality**: Resets map view to show all currently filtered events
- **Smart Behavior**: 
  - If events are visible: Fits map to show all current markers
  - If no events: Uses current filtered view settings
  - Removes search markers and closes info windows

### 2. Enhanced Map Zoom Controls
- **Google Maps**: Added `scrollwheel: true` and `gestureHandling: 'auto'`
- **OpenStreetMap**: Explicitly enabled all zoom interactions
  - `scrollWheelZoom: true`
  - `doubleClickZoom: true`
  - `touchZoom: true`
  - `boxZoom: true`
  - `keyboard: true`

### 3. Smart View Tracking
- **Current Filtered View**: Tracks the view established after applying filters
- **Automatic Capture**: Captures view after events are loaded and map bounds are fitted
- **Reset Logic**: "Fit to Events" button uses current filtered view, not system defaults

## Technical Implementation

### View Tracking Variables
```javascript
// Original system defaults (USA-wide view)
const originalMapSettings = {
    lat: [system_default_lat],
    lng: [system_default_lng], 
    zoom: [system_default_zoom]
};

// Current filtered view (updated when events load)
let currentFilteredView = {
    lat: [current_lat],
    lng: [current_lng],
    zoom: [current_zoom],
    hasBounds: [boolean]
};
```

### Functions Added
- `resetMapView()`: Resets to current filtered view or fits to markers
- `captureCurrentFilteredView()`: Captures current map state after events load

### Event Listeners
- Reset button click handler in `initPaginationEventListeners()`

## Files Modified
- `views/calendar/map.php` - All map view improvements

## User Experience Improvements
1. **Intuitive Reset**: Button resets to the view users expect (filtered events)
2. **Better Navigation**: Mouse wheel zoom works consistently across all map providers
3. **Clear Labeling**: Button labeled "Fit to Events" instead of generic "Reset View"
4. **Smart Behavior**: Automatically fits to current markers or uses filtered view
5. **Clean State**: Removes search markers and closes popups when resetting

## Testing
- Test "Fit to Events" button with various filter combinations
- Verify mouse wheel zoom works on all map providers
- Test button behavior with and without events
- Confirm search markers are removed on reset
- Verify info windows close on reset
# Geocoding Fix Solution - Version 3

## Problem Identified
The geocoding functionality in the CalendarController is not working the same way as the admin batch geocoding tool, even though both are using the `geocodeEvent()` function.

## Root Cause
After examining the code, I found that the admin batch geocoding tool and the CalendarController are preparing the data differently before passing it to the `geocodeEvent()` function:

1. In the admin batch tool (AdminController.php), a new array is created with **only the address fields** (no lat/lng)
2. In the CalendarController, the original data array with all fields is passed directly to `geocodeEvent()`

## Solution
The fix requires modifying two sections in the CalendarController.php file to match exactly how the admin batch tool prepares the data:

### 1. In the createEvent method (around line 410-433)

Replace:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'createEvent');
   
    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}
```

With:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - EXACTLY like the admin batch tool
    // Only include address fields, not lat/lng
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? ''
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'createEvent');
    
    // If geocoding was successful, update the original data
    if (isset($eventData['lat']) && $eventData['lat'] && isset($eventData['lng']) && $eventData['lng']) {
        $data['lat'] = $eventData['lat'];
        $data['lng'] = $eventData['lng'];
    }
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
    (isset($data['lat']) && $data['lat'] && isset($data['lng']) && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!isset($data['lat']) || !$data['lat'] || !isset($data['lng']) || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}
```

### 2. In the editEvent method (around line 600-622)

Replace:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
   
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
```

With:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - EXACTLY like the admin batch tool
    // Only include address fields, not lat/lng
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? ''
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'editEvent', $id);
    
    // If geocoding was successful, update the original data
    if (isset($eventData['lat']) && $eventData['lat'] && isset($eventData['lng']) && $eventData['lng']) {
        $data['lat'] = $eventData['lat'];
        $data['lng'] = $eventData['lng'];
    }
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
    (isset($data['lat']) && $data['lat'] && isset($data['lng']) && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!isset($data['lat']) || !$data['lat'] || !isset($data['lng']) || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
```

## Why This Fixes the Issue
This solution ensures that the address data is prepared in exactly the same way in both the CalendarController and the admin batch tool:

1. We create a separate `$eventData` array with **only the address fields** (no lat/lng)
2. We pass this array to `geocodeEvent()` instead of the original data array
3. We update the original data array with the geocoded coordinates from the result **only if** geocoding was successful

The key insight was that the `geocodeEvent()` function already creates its own address array internally, but when we pass a full data array with lat/lng fields, it might be handling those fields differently than when we pass an array with only address fields (as the admin batch tool does).

## Implementation Notes
1. The changes maintain all existing functionality and logging
2. The fix is minimal and focused only on the geocoding process
3. No changes to the geocoding_helper.php file are needed
4. We've added more robust checks for the lat/lng values in the result

After implementing these changes, the geocoding in the CalendarController should work exactly like the admin batch geocoding tool.
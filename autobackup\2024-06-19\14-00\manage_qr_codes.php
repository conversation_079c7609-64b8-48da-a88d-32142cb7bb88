<?php require APPROOT . '/views/includes/header.php'; ?>



<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>QR Code Management</h1>
            <p class="text-muted">Generate and manage QR codes for vehicles</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <?php flash('coordinator_message'); ?>

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-qrcode me-2"></i> Generate QR Codes</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/coordinator/manageQrCodes" method="get" class="mb-4">
                        <div class="row align-items-end">
                            <div class="col-md-6">
                                <label for="show_id" class="form-label">Select Show</label>
                                <select name="show_id" id="show_id" class="form-select" required>
                                    <option value="">-- Select a Show --</option>
                                    <?php foreach($data['shows'] as $show): ?>
                                        <option value="<?php echo $show->id; ?>" <?php echo (isset($_GET['show_id']) && $_GET['show_id'] == $show->id) ? 'selected' : ''; ?>>
                                            <?php echo $show->name; ?> (<?php echo date('M d, Y', strtotime($show->start_date)); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-2"></i> View Registered Vehicles
                                </button>
                            </div>
                        </div>
                    </form>

                    <?php if(isset($data['error']) && $data['error']): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i> <?php echo $data['error']; ?>
                        </div>
                    <?php endif; ?>

                    <?php if($data['selected_show']): ?>
                        <h4 class="mt-4 mb-3">Registered Vehicles for <?php echo $data['selected_show']->name; ?></h4>
                        
                        <?php if(empty($data['vehicles'])): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i> No vehicles with completed payment found for this show.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Reg #</th>
                                            <th>Vehicle</th>
                                            <th>Owner</th>
                                            <th>Category</th>
                                            <th>Payment Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach($data['vehicles'] as $vehicle): ?>
                                            <tr>
                                                <td><?php echo $vehicle->registration_number; ?></td>
                                                <td><?php echo $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model; ?></td>
                                                <td><?php echo $vehicle->owner_name; ?></td>
                                                <td><?php echo $vehicle->category_name; ?></td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check-circle me-1"></i> Paid
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-primary dropdown-toggle" type="button" id="qrActions<?php echo $vehicle->id; ?>" data-bs-toggle="dropdown" data-bs-boundary="viewport" aria-expanded="false">
                                                            <i class="fas fa-qrcode me-1"></i> QR Options
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="qrActions<?php echo $vehicle->id; ?>">
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/printQrCodes/<?php echo $data['selected_show']->id; ?>?vehicle_id=<?php echo $vehicle->id; ?>" target="_blank">
                                                                    <i class="fas fa-print me-2"></i> Print QR Code
                                                                </a>
                                                            </li>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/downloadQrCode/<?php echo $vehicle->id; ?>">
                                                                    <i class="fas fa-download me-2"></i> Download QR Code
                                                                </a>
                                                            </li>
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/printableTemplates?vehicle_id=<?php echo $vehicle->id; ?>&show_id=<?php echo $data['selected_show']->id; ?>">
                                                                    <i class="fas fa-file-alt me-2"></i> Use Custom Template
                                                                </a>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-search me-2"></i> QR Code Lookup</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/coordinator/lookupQrCode" method="post" class="row g-3">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="col-md-6">
                            <label for="registration_number" class="form-label">Registration Number</label>
                            <input type="text" class="form-control" id="registration_number" name="registration_number" placeholder="Enter registration number">
                        </div>
                        
                        <div class="col-md-6">
                            <label for="lookup_show_id" class="form-label">Show (Optional)</label>
                            <select name="lookup_show_id" id="lookup_show_id" class="form-select">
                                <option value="">-- Any Show --</option>
                                <?php foreach($data['shows'] as $show): ?>
                                    <option value="<?php echo $show->id; ?>">
                                        <?php echo $show->name; ?> (<?php echo date('M d, Y', strtotime($show->start_date)); ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-search me-2"></i> Lookup QR Code
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> About QR Code Management</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-0">
                        <h5><i class="fas fa-lightbulb me-2"></i> How to Use QR Codes</h5>
                        <p>QR codes can be generated for vehicles that have completed registration and payment. These QR codes can be used for:</p>
                        <ul>
                            <li><strong>Fan Voting:</strong> Allow show attendees to vote for their favorite vehicles</li>
                            <li><strong>Judging:</strong> Streamline the judging process by scanning vehicle QR codes</li>
                            <li><strong>Vehicle Information:</strong> Provide quick access to vehicle details</li>
                        </ul>
                        <p>You can print QR codes using default templates or use custom templates created by administrators.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
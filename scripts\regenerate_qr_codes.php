<?php
/**
 * QR Code Standardization Script
 * 
 * This script removes existing QR codes and clears the qr_code field in the database
 * so that new QR codes will be generated with the standardized URL pattern:
 * URLROOT . '/show/vote/' . $show_id . '/' . $registration_id
 * 
 * Created: <?php echo date('Y-m-d H:i:s'); ?>
 * Purpose: Standardize all QR codes to use the new voting URL pattern
 */

// Define APPROOT (go up one level from /scripts/ to root)
define('APPROOT', dirname(dirname(__FILE__)));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Start session for web-based execution
    session_start();
    
    // Check if user is logged in and is admin
    $auth = new Auth();
    if (!$auth->isLoggedIn() || !$auth->hasRole('admin')) {
        echo "Access denied. This script can only be run by an administrator.";
        exit;
    }
}

// Create instances
$db = new Database();

// Check if running in web browser or CLI
$isWeb = php_sapi_name() !== 'cli';

if ($isWeb) {
    echo "<h2>QR Code Standardization Script</h2>";
    echo "<hr>";
} else {
    echo "QR Code Standardization Script\n";
    echo "==============================\n\n";
}

try {
    // Get all registrations that have QR codes
    $db->query("SELECT id, show_id, qr_code FROM registrations WHERE qr_code IS NOT NULL AND qr_code != ''");
    $registrations = $db->resultSet();
    
    $totalRegistrations = count($registrations);
    $qrCodesDeleted = 0;
    $registrationsUpdated = 0;
    
    if ($isWeb) {
        echo "<p>Found <strong>{$totalRegistrations}</strong> registrations with QR codes.</p>";
    } else {
        echo "Found {$totalRegistrations} registrations with QR codes.\n\n";
    }
    
    if ($totalRegistrations > 0) {
        if ($isWeb) {
            echo "<p>Processing registrations...</p><ul>";
        } else {
            echo "Processing registrations...\n";
        }
        
        foreach ($registrations as $registration) {
            $message = "Processing registration ID: {$registration->id} (Show ID: {$registration->show_id})... ";
            
            // Delete the physical QR code file if it exists
            $qrCodePath = APPROOT . '/uploads/qrcodes/' . $registration->qr_code;
            if (file_exists($qrCodePath)) {
                if (unlink($qrCodePath)) {
                    $qrCodesDeleted++;
                    $message .= "File deleted. ";
                } else {
                    $message .= "File deletion failed. ";
                }
            } else {
                $message .= "File not found. ";
            }
            
            // Clear the qr_code field in the database
            $db->query("UPDATE registrations SET qr_code = NULL WHERE id = :id");
            $db->bind(':id', $registration->id);
            
            if ($db->execute()) {
                $registrationsUpdated++;
                $message .= "Database updated.";
            } else {
                $message .= "Database update failed.";
            }
            
            if ($isWeb) {
                echo "<li>" . htmlspecialchars($message) . "</li>";
            } else {
                echo $message . "\n";
            }
        }
        
        if ($isWeb) {
            echo "</ul>";
            echo "<h3>Summary:</h3>";
            echo "<ul>";
            echo "<li>Total registrations processed: <strong>{$totalRegistrations}</strong></li>";
            echo "<li>QR code files deleted: <strong>{$qrCodesDeleted}</strong></li>";
            echo "<li>Database records updated: <strong>{$registrationsUpdated}</strong></li>";
            echo "</ul>";
            echo "<p><strong>Note:</strong> QR codes will be regenerated automatically when accessed using the new standardized URL pattern:<br>";
            echo "<code>URLROOT . '/show/vote/' . \$show_id . '/' . \$registration_id</code></p>";
        } else {
            echo "\n";
            echo "Summary:\n";
            echo "--------\n";
            echo "Total registrations processed: {$totalRegistrations}\n";
            echo "QR code files deleted: {$qrCodesDeleted}\n";
            echo "Database records updated: {$registrationsUpdated}\n";
            echo "\n";
            echo "QR codes will be regenerated automatically when accessed using the new standardized URL pattern:\n";
            echo "URLROOT . '/show/vote/' . \$show_id . '/' . \$registration_id\n";
        }
        
    } else {
        if ($isWeb) {
            echo "<p>No QR codes found to process.</p>";
        } else {
            echo "No QR codes found to process.\n";
        }
    }
    
} catch (Exception $e) {
    if ($isWeb) {
        echo "<div style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</div>";
    } else {
        echo "Error: " . $e->getMessage() . "\n";
    }
    exit(1);
}

if ($isWeb) {
    echo "<div style='color: green; font-weight: bold; margin-top: 20px;'>QR Code standardization completed successfully!</div>";
} else {
    echo "\nQR Code standardization completed successfully!\n";
}
?>
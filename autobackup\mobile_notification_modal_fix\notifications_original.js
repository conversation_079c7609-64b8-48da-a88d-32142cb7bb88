/**
 * Notification System JavaScript v3.49.12
 * 
 * This file handles all client-side notification functionality including
 * subscription management, toast notifications, and push notifications.
 * 
 * Location: /public/js/notifications.js
 * Dependencies: Bootstrap 5, Font Awesome
 * 
 * Fixed Issues:
 * - Fixed originalText undefined error in openSubscriptionModal
 * - Added global modal functions (subscribeToEventModal, unsubscribeFromEventModal)
 * - Improved error handling and button state management
 * - Fixed modal backdrop not being removed when modal is cancelled (v3.49.9)
 * - Added proper aria-hidden attribute management to prevent focus conflicts
 * - Added comprehensive modal cleanup functionality
 * - Added periodic cleanup check for orphaned modal backdrops
 * - Fixed DOM element null reference error by adding page reload after subscription changes (v3.49.12)
 */

class NotificationManager {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        this.debugMode = localStorage.getItem('notification_debug') === 'true' || false;
        this.init();
    }
    
    /**
     * Initialize the notification manager
     */
    init() {
        this.dismissedToasts = new Set(); // Track dismissed toasts to prevent re-showing
        this.setupEventListeners();
        this.loadUnreadNotifications();
        this.requestPushPermission();
        
        // Setup global modal functions immediately
        this.setupModalFunctions();
        
        // Check for unread notifications every 30 seconds
        setInterval(() => {
            this.loadUnreadNotifications();
        }, 30000);
        
        // Add a global cleanup check every 5 seconds to catch any lingering modal issues
        setInterval(() => {
            const backdrops = document.querySelectorAll('.modal-backdrop');
            const openModals = document.querySelectorAll('.modal.show');
            
            // If there are backdrops but no open modals, clean up
            if (backdrops.length > 0 && openModals.length === 0) {
                console.log('Cleaning up orphaned modal backdrops');
                this.cleanupModalState();
            }
        }, 5000);
        
        // Mark all visible toasts as read when page is about to unload
        window.addEventListener('beforeunload', () => {
            this.markAllVisibleToastsAsRead();
        });
        
        // Add keyboard shortcut to clear all toasts (Escape key)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const visibleToasts = document.querySelectorAll('.toast-notification[data-notification-id]');
                if (visibleToasts.length > 0) {
                    this.clearAllToasts();
                }
                
                // Also clean up any modal state issues
                setTimeout(() => {
                    this.cleanupModalState();
                }, 100);
            }
        });
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Notification subscription buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-notification-btn]') || e.target.closest('[data-notification-btn]')) {
                e.preventDefault();
                const btn = e.target.matches('[data-notification-btn]') ? e.target : e.target.closest('[data-notification-btn]');
                this.openSubscriptionModal(btn);
            }
        });
        
        // Toast notification close buttons - Use event delegation
        document.addEventListener('click', (e) => {
            // Simple check for close button
            if (e.target.matches('.btn-close') || e.target.matches('.toast-close')) {
                e.preventDefault();
                e.stopPropagation();
                
                const toast = e.target.closest('.toast-notification');
                if (toast) {
                    console.log('🔴 Close button clicked for toast:', toast.getAttribute('data-notification-id'));
                    this.dismissToast(toast);
                }
            }
            
            // Check for modal backdrop clicks
            if (e.target.matches('.modal-backdrop')) {
                // Clean up modal state after backdrop click
                setTimeout(() => {
                    this.cleanupModalState();
                }, 100);
            }
        });
    }
    
    /**
     * Open notification subscription modal
     */
    async openSubscriptionModal(button) {
        const eventId = button.dataset.eventId;
        const eventType = button.dataset.eventType;
        
        if (!eventId || !eventType) {
            this.showAlert('error', 'Invalid event data');
            return;
        }
        
        // Store original button state before try block
        const originalText = button.innerHTML;
        const originalDisabled = button.disabled;
        
        try {
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/notification/subscriptionModal?event_id=${eventId}&event_type=${eventType}`);
            const data = await response.json();
            
            if (data.success) {
                // Create or update modal
                let modal = document.getElementById('notificationModal');
                if (!modal) {
                    modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.id = 'notificationModal';
                    modal.setAttribute('tabindex', '-1');
                    modal.setAttribute('aria-labelledby', 'notificationModalLabel');
                    // Don't set aria-hidden initially to prevent focus conflicts
                    document.body.appendChild(modal);
                }
                
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            ${data.html}
                        </div>
                    </div>
                `;
                
                // Show modal
                const bsModal = new bootstrap.Modal(modal);
                
                // Add event listeners for proper cleanup
                modal.addEventListener('hidden.bs.modal', () => {
                    // Use the centralized cleanup function
                    this.cleanupModalState();
                }, { once: true });
                
                // Handle escape key and backdrop clicks properly
                modal.addEventListener('hide.bs.modal', (e) => {
                    // Ensure proper cleanup even if modal is cancelled
                    console.log('Modal hiding...');
                });
                
                bsModal.show();
                
                // Execute any scripts in the modal content after it's shown
                modal.addEventListener('shown.bs.modal', () => {
                    // Remove aria-hidden when modal is fully shown to prevent focus conflicts
                    modal.removeAttribute('aria-hidden');
                    
                    // Trigger any initialization scripts in the modal
                    const scripts = modal.querySelectorAll('script');
                    scripts.forEach(script => {
                        if (script.innerHTML) {
                            try {
                                eval(script.innerHTML);
                            } catch (e) {
                                console.warn('Error executing modal script:', e);
                            }
                        }
                    });
                }, { once: true });
                
            } else {
                this.showAlert('error', data.message || 'Failed to load subscription modal');
            }
        } catch (error) {
            console.error('Error loading subscription modal:', error);
            this.showAlert('error', 'An error occurred while loading the subscription modal');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = originalDisabled;
        }
    }
    
    /**
     * Clean up any lingering modal backdrops and states
     */
    cleanupModalState() {
        // Remove any lingering backdrops
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => {
            if (backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }
        });
        
        // Remove modal-open class from body
        document.body.classList.remove('modal-open');
        document.body.style.paddingRight = '';
        document.body.style.overflow = '';
        
        // Remove any orphaned modals
        const orphanedModals = document.querySelectorAll('.modal:not(.show)');
        orphanedModals.forEach(modal => {
            if (modal.id === 'notificationModal' && modal.parentNode) {
                modal.parentNode.removeChild(modal);
            }
        });
    }
    
    /**
     * Setup global functions for modal functionality
     */
    setupModalFunctions() {
        // Make subscribeToEventModal globally available
        window.subscribeToEventModal = function() {
            const form = document.getElementById('notificationSubscriptionForm');
            if (!form) {
                console.error('Notification subscription form not found');
                return;
            }
            
            const formData = new FormData(form);
            
            // Validate that at least one notification time is selected
            const notificationTimes = formData.getAll('notification_times[]');
            if (notificationTimes.length === 0) {
                alert('Please select at least one notification time.');
                return;
            }
            
            // If registration notifications are enabled, validate those times too
            const notifyRegistration = document.getElementById('notify_registration_end');
            if (notifyRegistration && notifyRegistration.checked) {
                const registrationTimes = formData.getAll('registration_times[]');
                if (registrationTimes.length === 0) {
                    alert('Please select at least one registration deadline notification time.');
                    return;
                }
            }
            
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
            button.disabled = true;
            
            fetch(`${window.notificationManager.baseUrl}/notification/subscribe`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and show success message
                    const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                    if (modal) {
                        modal.hide();
                        // Ensure cleanup after modal is hidden
                        setTimeout(() => {
                            window.notificationManager.cleanupModalState();
                        }, 300);
                    }
                    
                    // Show success toast or alert
                    if (typeof showToast === 'function') {
                        showToast('success', data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    // Reload page to ensure consistent state and prevent DOM element issues
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500); // Give time for success message to be seen
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while subscribing to notifications.');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        };
        
        // Make unsubscribeFromEventModal globally available
        window.unsubscribeFromEventModal = function() {
            if (!confirm('Are you sure you want to unsubscribe from notifications for this event?')) {
                return;
            }
            
            const form = document.getElementById('notificationSubscriptionForm');
            if (!form) {
                console.error('Notification subscription form not found');
                return;
            }
            
            const formData = new FormData(form);
            
            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Unsubscribing...';
            button.disabled = true;
            
            fetch(`${window.notificationManager.baseUrl}/notification/unsubscribe`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and show success message
                    const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                    if (modal) {
                        modal.hide();
                        // Ensure cleanup after modal is hidden
                        setTimeout(() => {
                            window.notificationManager.cleanupModalState();
                        }, 300);
                    }
                    
                    // Show success toast or alert
                    if (typeof showToast === 'function') {
                        showToast('success', data.message);
                    } else {
                        alert(data.message);
                    }
                    
                    // Reload page to ensure consistent state and prevent DOM element issues
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500); // Give time for success message to be seen
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while unsubscribing.');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        };
        
        // Make updateNotificationButtonState globally available
        window.updateNotificationButtonState = function(isSubscribed) {
            const notificationBtn = document.querySelector('[data-notification-btn]');
            if (notificationBtn) {
                if (isSubscribed) {
                    notificationBtn.classList.remove('btn-outline-primary');
                    notificationBtn.classList.add('btn-primary');
                    notificationBtn.innerHTML = '<i class="fas fa-bell me-2"></i>Subscribed';
                    notificationBtn.title = 'Manage event notifications';
                } else {
                    notificationBtn.classList.remove('btn-primary');
                    notificationBtn.classList.add('btn-outline-primary');
                    notificationBtn.innerHTML = '<i class="fas fa-bell me-2"></i>Notify Me';
                    notificationBtn.title = 'Set up event notifications';
                }
            }
        };
    }
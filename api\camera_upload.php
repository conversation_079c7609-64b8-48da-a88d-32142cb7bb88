<?php
/**
 * DEPRECATED: Camera Upload API Endpoint
 * 
 * This endpoint has been moved to follow the site's controller structure.
 * 
 * NEW LOCATION: PWAController::cameraUpload()
 * NEW ENDPOINT: /pwa/camera-upload
 * 
 * Date Moved: 2025-01-27
 * Version: 3.63.21
 * Reason: Following established site structure using controllers instead of api directory
 */

header('Content-Type: application/json');
http_response_code(410); // Gone

echo json_encode([
    'success' => false,
    'error' => 'Endpoint moved',
    'message' => 'This API endpoint has been moved to /pwa/camera-upload',
    'new_endpoint' => '/pwa/camera-upload',
    'date_moved' => '2025-01-27',
    'version' => '3.63.21'
]);
?>
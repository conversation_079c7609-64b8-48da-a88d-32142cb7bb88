<!DOCTYPE html>
<html>
<head>
    <title>Class Capture Test</title>
</head>
<body>
    <h2>Class Capture Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the script and modify it to capture the class
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Original script loaded');
                
                // Insert a line right after the class definition to capture it
                const modifiedCode = code.replace(
                    'class CameraBanner {',
                    `class CameraBanner {
                    // CAPTURE THE CLASS IMMEDIATELY
                    static captureClass() {
                        window.CameraBannerClassRef = CameraBanner;
                        console.log('Class captured:', typeof CameraBanner);
                    }`
                ).replace(
                    'constructor() {',
                    `constructor() {
                        // Call the capture method
                        CameraBanner.captureClass();`
                );
                
                output('Modified script, executing...');
                
                try {
                    eval(modifiedCode);
                    
                    output('Script executed successfully');
                    output('- CameraBanner class: ' + typeof CameraBanner);
                    output('- window.CameraBannerClassRef: ' + typeof window.CameraBannerClassRef);
                    output('- window.cameraBanner: ' + typeof window.cameraBanner);
                    
                    if (window.cameraBanner) {
                        output('- cameraBanner.version: ' + window.cameraBanner.version);
                        output('- cameraBanner.constructor.name: ' + window.cameraBanner.constructor.name);
                    }
                    
                    if (window.CameraBannerClassRef) {
                        output('✓ Class was captured successfully!');
                        output('- Captured class name: ' + window.CameraBannerClassRef.name);
                        
                        // Try creating a new instance with the captured class
                        const testInstance = new window.CameraBannerClassRef();
                        output('- Test instance version: ' + testInstance.version);
                    }
                    
                } catch (error) {
                    output('Execution error: ' + error.message);
                    output('Stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
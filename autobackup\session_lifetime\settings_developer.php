<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Developer Tools</h1>
            <p class="text-muted">Advanced tools for system administration</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <!-- Development Settings Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary bg-opacity-10 py-3">
                    <h5 class="card-title mb-0 text-primary">
                        <i class="fas fa-cogs me-2"></i>Development Settings
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo BASE_URL; ?>/admin/saveDevSettings" method="POST" class="needs-validation" novalidate>
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="mb-4">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="dev_admin_bypass" name="dev_admin_bypass" value="1" 
                                    <?php echo (isset($data['settings']['dev_admin_bypass']) && $data['settings']['dev_admin_bypass'] == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="dev_admin_bypass">
                                    <strong>Enable Admin Authentication Bypass</strong>
                                </label>
                            </div>
                            <div class="alert alert-danger mt-2">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>SECURITY WARNING:</strong> This setting bypasses all authentication checks and grants admin access to anyone visiting the site. Only enable in secure development environments and disable immediately after testing.
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Developer Tools Dashboard -->
    <div class="row g-4 mb-5">
        <!-- Impersonate User Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-user-secret text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Impersonate User</h4>
                    </div>
                    <p class="card-text text-muted">Log in as another user to troubleshoot issues or provide support.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/impersonateUser" class="stretched-link text-decoration-none">
                        <span class="d-none">View Impersonate User</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Scheduled Tasks Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Scheduled Tasks</h4>
                    </div>
                    <p class="card-text text-muted">Manage automated tasks and event-triggered operations.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/scheduled_tasks" class="stretched-link text-decoration-none">
                        <span class="d-none">View Scheduled Tasks</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
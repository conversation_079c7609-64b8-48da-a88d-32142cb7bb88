<?php
/**
 * BACKUP: views/admin/dashboard.php - Dashboard Reorganization
 * Date: 2025-01-27
 * 
 * Changes made:
 * 1. Removed self-referencing "Dashboard" link from Quick Actions section
 * 2. Moved "System Reports" from Quick Links to Quick Actions section
 * 3. Moved "View Registration page" from Quick Actions to Quick Links section
 * 4. Renamed "View Registration page" to "Registration Dashboard"
 * 
 * This reorganization improves the logical grouping of admin functions:
 * - Quick Links: Direct access to management dashboards
 * - Quick Actions: Functional actions and tools
 */

// This is a backup file created during the admin dashboard reorganization
// The actual implementation is in views/admin/dashboard.php

/**
 * Summary of changes:
 * 
 * Quick Links section now contains:
 * - Payment Dashboard
 * - Default Coordinator Settings  
 * - Manage Coordinators
 * - Registration Dashboard (moved from Quick Actions, renamed)
 * 
 * Quick Actions section now contains:
 * - Manage Users
 * - Manage Roles
 * - Create Show
 * - System Reports (moved from Quick Links)
 * - Form Builder
 * 
 * Removed:
 * - Self-referencing "Dashboard" link from Quick Actions
 */
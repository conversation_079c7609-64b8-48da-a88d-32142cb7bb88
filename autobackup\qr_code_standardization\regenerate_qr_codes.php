<?php
/**
 * QR Code Standardization Script
 * 
 * This script removes existing QR codes and clears the qr_code field in the database
 * so that new QR codes will be generated with the standardized URL pattern:
 * URLROOT . '/show/vote/' . $show_id . '/' . $registration_id
 * 
 * Created: <?php echo date('Y-m-d H:i:s'); ?>
 * Purpose: Standardize all QR codes to use the new voting URL pattern
 */

// Include the configuration
require_once '../../config/config.php';
require_once '../../libraries/Database.php';

// Initialize database connection
$db = new Database();

echo "QR Code Standardization Script\n";
echo "==============================\n\n";

try {
    // Get all registrations that have QR codes
    $db->query("SELECT id, show_id, qr_code FROM registrations WHERE qr_code IS NOT NULL AND qr_code != ''");
    $registrations = $db->resultSet();
    
    $totalRegistrations = count($registrations);
    $qrCodesDeleted = 0;
    $registrationsUpdated = 0;
    
    echo "Found {$totalRegistrations} registrations with QR codes.\n\n";
    
    if ($totalRegistrations > 0) {
        echo "Processing registrations...\n";
        
        foreach ($registrations as $registration) {
            echo "Processing registration ID: {$registration->id} (Show ID: {$registration->show_id})... ";
            
            // Delete the physical QR code file if it exists
            $qrCodePath = '../../uploads/qrcodes/' . $registration->qr_code;
            if (file_exists($qrCodePath)) {
                if (unlink($qrCodePath)) {
                    $qrCodesDeleted++;
                    echo "File deleted. ";
                } else {
                    echo "File deletion failed. ";
                }
            } else {
                echo "File not found. ";
            }
            
            // Clear the qr_code field in the database
            $db->query("UPDATE registrations SET qr_code = NULL WHERE id = :id");
            $db->bind(':id', $registration->id);
            
            if ($db->execute()) {
                $registrationsUpdated++;
                echo "Database updated.\n";
            } else {
                echo "Database update failed.\n";
            }
        }
        
        echo "\n";
        echo "Summary:\n";
        echo "--------\n";
        echo "Total registrations processed: {$totalRegistrations}\n";
        echo "QR code files deleted: {$qrCodesDeleted}\n";
        echo "Database records updated: {$registrationsUpdated}\n";
        echo "\n";
        echo "QR codes will be regenerated automatically when accessed using the new standardized URL pattern:\n";
        echo "URLROOT . '/show/vote/' . \$show_id . '/' . \$registration_id\n";
        
    } else {
        echo "No QR codes found to process.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\nQR Code standardization completed successfully!\n";
?>
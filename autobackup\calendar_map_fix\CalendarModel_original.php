    public function getEventsWithLocation($filters = [])
    {
        try {
            $sql = "SELECT e.*, c.name as calendar_name, s.name as show_name
                    FROM calendar_events e
                    LEFT JOIN calendars c ON e.calendar_id = c.id
                    LEFT JOIN shows s ON e.show_id = s.id
                    WHERE 1=1";
            
            $params = [];
            
            // Filter by start date
            if (isset($filters['start'])) {
                $sql .= " AND e.end_date >= :start";
                $params[':start'] = $filters['start'];
            }
            
            // Filter by end date
            if (isset($filters['end'])) {
                $sql .= " AND e.start_date <= :end";
                $params[':end'] = $filters['end'];
            }
            
            // Filter by calendar ID
            if (isset($filters['calendar_id'])) {
                $sql .= " AND e.calendar_id = :calendar_id";
                $params[':calendar_id'] = $filters['calendar_id'];
            }
            
            // Filter by multiple calendar IDs
            if (isset($filters['calendar_ids']) && is_array($filters['calendar_ids'])) {
                $placeholders = [];
                foreach ($filters['calendar_ids'] as $i => $id) {
                    $param = ":calendar_id_$i";
                    $placeholders[] = $param;
                    $params[$param] = $id;
                }
                $sql .= " AND e.calendar_id IN (" . implode(', ', $placeholders) . ")";
            }
            
            // Filter by state
            if (isset($filters['state'])) {
                $sql .= " AND e.state = :state";
                $params[':state'] = $filters['state'];
            }
            
            // Location-based filtering using Haversine formula
            if (isset($filters['radius']) && isset($filters['lat']) && isset($filters['lng'])) {
                // Earth's radius in miles
                $earthRadius = 3959;
                
                // Haversine formula to calculate distance
                $sql .= " AND (
                    $earthRadius * acos(
                        cos(radians(:lat)) * 
                        cos(radians(e.lat)) * 
                        cos(radians(e.lng) - radians(:lng)) + 
                        sin(radians(:lat)) * 
                        sin(radians(e.lat))
                    ) <= :radius
                )";
                
                $params[':lat'] = $filters['lat'];
                $params[':lng'] = $filters['lng'];
                $params[':radius'] = $filters['radius'];
            }
            
            // Only include events with location data
            $sql .= " AND (e.address1 IS NOT NULL OR e.city IS NOT NULL OR e.state IS NOT NULL)";
            
            // Order by start date
            $sql .= " ORDER BY e.start_date ASC";
            
            $this->db->query($sql, $params);
            return $this->db->resultSet();
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getEventsWithLocation: ' . $e->getMessage());
            throw $e;
        }
    }
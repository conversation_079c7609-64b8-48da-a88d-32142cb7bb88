# Listing Fee Hidden Field Fix

## Summary
Modified the coordinator edit show form to make the listing fee field hidden instead of visible.

## Changes Made
- Changed the listing fee input field from visible to hidden type
- Preserved the field value and name for form submission
- Removed the visible label and help text
- Expanded registration fee field to full width (col-md-12)
- Added support for hidden field type in form template switch statement

## Files Modified
- `views/coordinator/edit_show.php` - Made listing fee field hidden and added hidden field type support

## Backup Files
- `edit_show_original.php` - Original version before changes

## Date
2025-01-27

## Version
Minor update - v3.53.2
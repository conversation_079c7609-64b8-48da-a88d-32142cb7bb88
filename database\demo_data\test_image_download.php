<?php
/**
 * Test Image Download Functionality
 * 
 * This script tests the image downloading functionality to ensure
 * it works correctly before running the full demo data generation.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Image Download Test</h1>";
echo "<p>Testing the image downloading functionality...</p>";

// Test image URL (free from Unsplash)
$testImageUrl = 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=400&h=300&fit=crop';
$testFilename = 'test_image_' . time() . '.jpg';
$testDirectory = '../../uploads/temp';

echo "<h2>Test Configuration</h2>";
echo "<p><strong>Image URL:</strong> {$testImageUrl}</p>";
echo "<p><strong>Filename:</strong> {$testFilename}</p>";
echo "<p><strong>Directory:</strong> {$testDirectory}</p>";

// Create directory if it doesn't exist
if (!file_exists($testDirectory)) {
    mkdir($testDirectory, 0755, true);
    echo "<p>✅ Created test directory</p>";
} else {
    echo "<p>✅ Test directory exists</p>";
}

echo "<h2>Download Test</h2>";

try {
    // Test download
    $context = stream_context_create([
        'http' => [
            'timeout' => 30,
            'user_agent' => 'Mozilla/5.0 (compatible; DemoDataGenerator/1.0)'
        ]
    ]);
    
    echo "<p>📥 Downloading test image...</p>";
    $imageData = file_get_contents($testImageUrl, false, $context);
    
    if ($imageData === false) {
        echo "<p style='color: red;'>❌ Failed to download image</p>";
    } else {
        echo "<p style='color: green;'>✅ Successfully downloaded image (" . strlen($imageData) . " bytes)</p>";
        
        // Save image
        $localPath = $testDirectory . '/' . $testFilename;
        if (file_put_contents($localPath, $imageData) === false) {
            echo "<p style='color: red;'>❌ Failed to save image</p>";
        } else {
            echo "<p style='color: green;'>✅ Successfully saved image to {$localPath}</p>";
            
            // Get image info
            if (file_exists($localPath)) {
                $imageInfo = getimagesize($localPath);
                if ($imageInfo) {
                    echo "<p><strong>Image Info:</strong></p>";
                    echo "<ul>";
                    echo "<li>Width: {$imageInfo[0]}px</li>";
                    echo "<li>Height: {$imageInfo[1]}px</li>";
                    echo "<li>Type: {$imageInfo['mime']}</li>";
                    echo "<li>File Size: " . filesize($localPath) . " bytes</li>";
                    echo "</ul>";
                    
                    // Display image
                    $webPath = str_replace('../../', '', $localPath);
                    echo "<p><strong>Downloaded Image Preview:</strong></p>";
                    echo "<img src='{$webPath}' style='max-width: 400px; border: 1px solid #ccc; border-radius: 5px;' alt='Test Image'>";
                    
                    echo "<p style='color: green;'>🎉 Image download test successful!</p>";
                    
                    // Clean up test file
                    unlink($localPath);
                    echo "<p>🧹 Cleaned up test file</p>";
                    
                } else {
                    echo "<p style='color: red;'>❌ Invalid image file</p>";
                }
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>System Requirements Check</h2>";

// Check if required functions are available
$requirements = [
    'file_get_contents' => function_exists('file_get_contents'),
    'getimagesize' => function_exists('getimagesize'),
    'stream_context_create' => function_exists('stream_context_create'),
    'mkdir' => function_exists('mkdir'),
    'file_put_contents' => function_exists('file_put_contents')
];

foreach ($requirements as $function => $available) {
    $status = $available ? '✅' : '❌';
    $color = $available ? 'green' : 'red';
    echo "<p style='color: {$color};'>{$status} {$function}()</p>";
}

// Check upload directory permissions
$uploadDir = '../../uploads';
if (file_exists($uploadDir)) {
    if (is_writable($uploadDir)) {
        echo "<p style='color: green;'>✅ Upload directory is writable</p>";
    } else {
        echo "<p style='color: red;'>❌ Upload directory is not writable</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠️ Upload directory does not exist (will be created)</p>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If all tests passed, you can proceed with the full demo data generation:</p>";
echo "<p><a href='index.php' class='btn btn-primary'>Go to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin-top: 10px;
}

.btn:hover {
    background-color: #0056b3;
}

ul {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 4px solid #007bff;
}
</style>

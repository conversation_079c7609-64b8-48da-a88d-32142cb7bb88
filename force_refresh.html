<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Cache Refresh</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
            text-align: center;
        }
        .refresh-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 20px auto;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            width: 100%;
            max-width: 300px;
        }
        button:hover {
            background: #0056b3;
        }
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="refresh-container">
        <h1>🔄 Force Cache Refresh</h1>
        <p>This will clear PWA cache and force fresh loading of JavaScript files.</p>
        
        <div id="status" class="status info">
            Ready to clear cache...
        </div>
        
        <button onclick="clearAllCache()">Clear All Cache & Refresh</button>
        
        <div class="step">
            <h3>What this does:</h3>
            <ol>
                <li>Unregisters service worker</li>
                <li>Clears all PWA caches</li>
                <li>Clears browser storage</li>
                <li>Forces page reload with fresh files</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>After refresh:</h3>
            <ol>
                <li>Go back to your main site</li>
                <li>Test camera banner rotation</li>
                <li>You should see new debug messages</li>
            </ol>
        </div>
    </div>

    <script>
        async function clearAllCache() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.className = 'status info';
                statusDiv.textContent = '🔄 Clearing service worker...';
                
                // Unregister service worker
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                }
                
                statusDiv.textContent = '🔄 Clearing caches...';
                
                // Clear all caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }
                
                statusDiv.textContent = '🔄 Clearing storage...';
                
                // Clear local storage
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear IndexedDB if available
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(
                            databases.map(db => {
                                return new Promise((resolve, reject) => {
                                    const deleteReq = indexedDB.deleteDatabase(db.name);
                                    deleteReq.onsuccess = () => resolve();
                                    deleteReq.onerror = () => resolve(); // Don't fail on error
                                });
                            })
                        );
                    } catch (e) {
                        // Ignore errors
                    }
                }
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Cache cleared! Refreshing page...';
                
                // Force reload with cache bypass
                setTimeout(() => {
                    window.location.href = window.location.href + '?nocache=' + Date.now();
                }, 1000);
                
            } catch (error) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '⚠️ Some cache clearing failed, but continuing...';
                
                // Still try to reload
                setTimeout(() => {
                    window.location.reload(true);
                }, 2000);
            }
        }
    </script>
</body>
</html>

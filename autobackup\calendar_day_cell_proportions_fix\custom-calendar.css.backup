/**
 * Custom Calendar CSS - BACKUP BEFORE PROPORTIONS FIX
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 1.0.0
 */

/* Global box-sizing for consistent sizing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Calendar Container */
.custom-calendar {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 100%;
  position: relative;
  box-sizing: border-box;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.calendar-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 10px;
}

.calendar-nav-btn {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-nav-btn:hover {
  background-color: #f1f3f5;
}

.calendar-nav-btn.today {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

.calendar-nav-btn.today:hover {
  background-color: #2d6fae;
}

/* Calendar View Selector */
.calendar-view-selector {
  display: flex;
  gap: 5px;
  margin-bottom: 15px;
}

.calendar-view-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-view-btn:hover {
  background-color: #e9ecef;
}

.calendar-view-btn.active {
  background-color: #3788d8;
  color: white;
  border-color: #3788d8;
}

/* Month View */
.calendar-month {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-top: 1px solid #e9ecef;
  border-left: 1px solid #e9ecef;
}

.calendar-weekday {
  padding: 10px;
  text-align: center;
  font-weight: 600;
  background-color: #f8f9fa;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

.calendar-day {
  min-height: 100px;
  padding: 5px;
  border-right: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
  position: relative;
}

.calendar-day-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.calendar-day-number {
  font-weight: 600;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.calendar-day.today .calendar-day-number {
  background-color: #3788d8;
  color: white;
}

.calendar-day.other-month {
  background-color: #f8f9fa;
  color: #adb5bd;
}

.calendar-day.weekend {
  background-color: #f8f9fa;
}

.calendar-day-events {
  display: flex;
  flex-direction: column;
  gap: 2px;
  overflow: hidden;
}
<?php
/**
 * Geocoding Enhancement Patch
 * 
 * This script updates the CalendarController.php file to use the improved geocoding functionality.
 * It should be run once to apply the changes.
 */

// Initialize the application
require_once dirname(dirname(__FILE__)) . '/core/bootstrap.php';

echo "Starting geocoding enhancement patch...\n";

// Path to the CalendarController.php file
$controllerPath = APPROOT . '/controllers/CalendarController.php';

// Read the current file content
$content = file_get_contents($controllerPath);

if ($content === false) {
    die("Error: Could not read the CalendarController.php file.\n");
}

// Create a backup of the original file
$backupPath = APPROOT . '/autobackup/geocoding_enhancement/CalendarController.php.backup';
if (!file_exists(dirname($backupPath))) {
    mkdir(dirname($backupPath), 0777, true);
}

if (!file_put_contents($backupPath, $content)) {
    die("Error: Could not create backup file.\n");
}

echo "Created backup at: {$backupPath}\n";

// Define the old and new code patterns
$oldCode = <<<'EOD'
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Create address array for geocoding
                $addressData = [
                    'address1' => $data['address1'],
                    'address2' => $data['address2'],
                    'city' => $data['city'],
                    'state' => $data['state'],
                    'zipcode' => $data['zipcode']
                ];
                
                // Attempt to geocode the address
                $coordinates = geocodeAddress($addressData, $mapSettings);
                
                // If geocoding was successful, update coordinates
                if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
                    $data['lat'] = $coordinates['lat'];
                    $data['lng'] = $coordinates['lng'];
                }
            }
EOD;

$newCode = <<<'EOD'
            // Automatically geocode address if needed
            // Load geocoding helper
            require_once APPROOT . '/helpers/geocoding_helper.php';
            
            // Get map provider settings
            $mapSettings = $this->calendarModel->getMapProviderSettings();
            
            // Use the new geocodeEvent function
            $data = geocodeEvent($data, $mapSettings, 'createEvent');
EOD;

// Replace the code in the createEvent method
$createEventPattern = '/public function createEvent\(\) \{.*?\/\/ Automatically geocode address if needed.*?if \(\(\!\\$data\[\'lat\'\] \|\| \!\\$data\[\'lng\'\]\) \&\&.*?\\$data\[\'lng\'\] = \\$coordinates\[\'lng\'\];.*?\}\s+\}/s';
$createEventReplacement = preg_replace($createEventPattern, $newCode, $content, 1, $createEventCount);

if ($createEventCount === 0) {
    echo "Warning: Could not find the geocoding code in the createEvent method.\n";
} else {
    echo "Updated geocoding code in createEvent method.\n";
    $content = $createEventReplacement;
}

// Replace the code in the editEvent method
$editEventPattern = '/public function editEvent\(\\$id\) \{.*?\/\/ Automatically geocode address if needed.*?if \(\(\!\\$data\[\'lat\'\] \|\| \!\\$data\[\'lng\'\]\) \&\&.*?\\$data\[\'lng\'\] = \\$coordinates\[\'lng\'\];.*?\}\s+\}/s';
$editEventReplacement = preg_replace($editEventPattern, $newCode, $content, 1, $editEventCount);

if ($editEventCount === 0) {
    echo "Warning: Could not find the geocoding code in the editEvent method.\n";
} else {
    echo "Updated geocoding code in editEvent method.\n";
    $content = $editEventReplacement;
}

// Write the updated content back to the file
if (file_put_contents($controllerPath, $content)) {
    echo "Successfully updated CalendarController.php with improved geocoding functionality.\n";
} else {
    echo "Error: Could not write to CalendarController.php.\n";
}

echo "Patch completed.\n";
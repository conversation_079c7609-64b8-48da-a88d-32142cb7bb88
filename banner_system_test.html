<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner System Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
        .test-banner {
            width: 100%;
            height: 100px;
            background: black;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            border: 2px solid #ccc;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Banner System Test</h1>
        <p>This will test if the banner system is working at all.</p>
        
        <button onclick="testBannerSystem()">Test Banner System</button>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testRotation()">Test Rotation</button>
        
        <div id="status"></div>
        
        <div class="test-banner" id="test-banner-container">
            Test Banner Container
        </div>
    </div>

    <!-- Load banner system -->
    <script src="public/js/camera-banner.js"></script>

    <script>
        function log(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            statusDiv.innerHTML += `<div class="status ${className}">${message}</div>`;
        }
        
        function testBannerSystem() {
            log('Testing banner system...');
            
            // Check if banner system exists
            if (window.cameraBanner) {
                log('✅ Banner system exists', 'success');
                log(`Version: ${window.cameraBanner.version || 'unknown'}`);
                
                // Check if methods exist
                if (typeof window.cameraBanner.loadBanners === 'function') {
                    log('✅ loadBanners method exists', 'success');
                } else {
                    log('❌ loadBanners method missing', 'error');
                }
                
                if (typeof window.cameraBanner.startRotation === 'function') {
                    log('✅ startRotation method exists', 'success');
                } else {
                    log('❌ startRotation method missing', 'error');
                }
                
                // Check banners
                if (window.cameraBanner.banners) {
                    log(`Banners loaded: ${window.cameraBanner.banners.length}`, 'success');
                    if (window.cameraBanner.banners.length > 0) {
                        log(`First banner: ${JSON.stringify(window.cameraBanner.banners[0])}`);
                    }
                } else {
                    log('❌ No banners loaded', 'error');
                }
                
            } else {
                log('❌ Banner system not found!', 'error');
            }
        }
        
        function testAPI() {
            log('Testing API...');
            
            fetch('/api/cameraBanners')
                .then(response => response.json())
                .then(data => {
                    log('✅ API response received', 'success');
                    log(`API returned: ${JSON.stringify(data)}`);
                    
                    if (data.success && data.banners) {
                        log(`API banners: ${data.banners.length}`, 'success');
                        data.banners.forEach((banner, index) => {
                            log(`Banner ${index + 1}: ${banner.type} - "${banner.text || banner.image_path}" (is_logo: ${banner.is_logo})`);
                        });
                    }
                })
                .catch(error => {
                    log(`❌ API error: ${error.message}`, 'error');
                });
        }
        
        function testRotation() {
            log('Testing rotation...');
            
            if (window.cameraBanner) {
                const container = document.getElementById('test-banner-container');
                if (container) {
                    log('✅ Test container found', 'success');
                    
                    // Try to start rotation
                    try {
                        window.cameraBanner.startRotation('test-banner-container');
                        log('✅ startRotation called', 'success');
                    } catch (error) {
                        log(`❌ startRotation error: ${error.message}`, 'error');
                    }
                } else {
                    log('❌ Test container not found', 'error');
                }
            } else {
                log('❌ Banner system not available', 'error');
            }
        }
        
        // Auto-test on load
        setTimeout(() => {
            log('=== AUTO TEST ===');
            testBannerSystem();
        }, 1000);
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>Script Load Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h2>Script Load Test</h2>
    <div id="logs"></div>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const className = type === 'error' ? 'log error' : type === 'success' ? 'log success' : 'log';
            logs.innerHTML += '<div class="' + className + '">' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        // Test before script loads
        log('Before script load:');
        log('- CameraBanner class: ' + (typeof CameraBanner !== 'undefined' ? 'EXISTS' : 'NOT FOUND'));
        log('- window.cameraBanner: ' + (typeof window.cameraBanner !== 'undefined' ? 'EXISTS' : 'NOT FOUND'));
        log('- BASE_URL: ' + (typeof BASE_URL !== 'undefined' ? BASE_URL : 'NOT DEFINED'));

        // Listen for script load
        const script = document.createElement('script');
        script.src = '/public/js/camera-banner.js';
        script.onload = function() {
            log('Script loaded successfully!', 'success');
            setTimeout(() => {
                log('After script load:');
                log('- CameraBanner class: ' + (typeof CameraBanner !== 'undefined' ? 'EXISTS' : 'NOT FOUND'));
                log('- window.cameraBanner: ' + (typeof window.cameraBanner !== 'undefined' ? 'EXISTS' : 'NOT FOUND'));
                if (window.cameraBanner) {
                    log('- cameraBanner.version: ' + window.cameraBanner.version);
                    log('- cameraBanner type: ' + typeof window.cameraBanner);
                }
                log('- BASE_URL: ' + (typeof BASE_URL !== 'undefined' ? BASE_URL : 'NOT DEFINED'));
            }, 100);
        };
        script.onerror = function() {
            log('Script failed to load!', 'error');
        };
        
        document.head.appendChild(script);

        // Also test after delay
        setTimeout(() => {
            log('After 2 seconds:');
            log('- window.cameraBanner exists: ' + (typeof window.cameraBanner !== 'undefined'));
            if (window.cameraBanner) {
                log('- Version: ' + window.cameraBanner.version);
                log('- Has banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'NO'));
            }
        }, 2000);
    </script>
</body>
</html>
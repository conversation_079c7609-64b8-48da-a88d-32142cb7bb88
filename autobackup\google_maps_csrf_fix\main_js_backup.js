// Backup of main.js changes made to fix Google Maps CSRF token interference
// Date: 2025-01-27
// Issue: CSRF token injection was interfering with Google Maps API requests

// Problem: 
// The global XMLHttpRequest and fetch overrides were adding X-CSRF-TOKEN headers
// to ALL requests, including Google Maps internal API calls to googleapis.com
// This caused Google Maps tile loading to fail with XHR errors

// Solution:
// Added URL filtering to exclude googleapis.com and gstatic.com domains
// from CSRF token injection

// Changes made:
// 1. XMLHttpRequest.prototype.open - added URL parameter and googleapis.com check
// 2. window.fetch override - added early return for Google Maps URLs

console.log("Backup created for Google Maps CSRF fix");
    // Create Google Maps marker (ORIGINAL - DEPRECATED)
    function createGoogleMarker(event, position, eventNumber) {
        // Create a custom marker with number
        const marker = new google.maps.Marker({
            position: position,
            map: map,
            title: event.title,
            label: {
                text: eventNumber.toString(),
                color: 'white',
                fontWeight: 'bold',
                fontSize: '12px'
            },
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                fillColor: event.color || '#3788d8',
                fillOpacity: 0.9,
                strokeWeight: 2,
                strokeColor: '#ffffff',
                scale: 12
            },
            animation: google.maps.Animation.DROP,
            eventId: event.id,
            eventNumber: eventNumber
        });
        
        // Add click listener to marker
        marker.addListener('click', () => {
            // Close any open info window
            if (currentInfoWindow) {
                currentInfoWindow.close();
            }
            
            // Create info window content
            const content = createInfoWindowContent(event);
            
            // Set info window content and open it
            infoWindow.setContent(content);
            infoWindow.open(map, marker);
            currentInfoWindow = infoWindow;
            
            // Jump to the page containing this event and highlight it
            jumpToEventPage(event.id);
        });
        
        return marker;
    }
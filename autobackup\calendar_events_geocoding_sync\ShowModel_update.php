<?php
/**
 * This is a partial file showing the changes needed to fix the geocoding synchronization
 * between shows and calendar events.
 */

// In the ShowModel.php file, we need to modify the updateShow method to include lat and lng
// in the eventData array when updating calendar events.

// The fix should be applied around line 2096 in the updateShow method:

$eventData = [
    'id' => $event->id,
    'calendar_id' => $event->calendar_id,
    'title' => $data['name'],
    'description' => $data['description'] ?? $event->description,
    'start_date' => $data['start_date'],
    'end_date' => $data['end_date'],
    'all_day' => $event->all_day,
    'location' => $data['location'] ?? $event->location,
    'address1' => $address1,
    'address2' => $address2,
    'city' => $city,
    'state' => $state,
    'zipcode' => $zipcode,
    'lat' => $updatedShow->lat ?? null, // Add lat from the updated show
    'lng' => $updatedShow->lng ?? null, // Add lng from the updated show
    'venue_id' => $event->venue_id,
    'url' => $event->url,
    'color' => $event->color,
    'is_recurring' => $event->is_recurring,
    'recurrence_pattern' => $event->recurrence_pattern,
    'recurrence_end_date' => $event->recurrence_end_date,
    'privacy' => 'public', // Always ensure events are public
    'show_id' => $data['id'],
    'created_by' => $data['coordinator_id'] ?? $event->created_by // Use coordinator ID if available
];
<?php
// Admin Header Settings Page
// Data is passed from AdminController
$pageTitle = $title ?? "Racing Header Settings - Admin";
include APPROOT . '/views/includes/header.php';
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-image me-2"></i>Racing Header Settings
                    </h4>
                </div>
                <div class="card-body">
                    <?php if (isset($_SESSION['flash_message'])) : ?>
                        <div class="alert alert-<?php echo $_SESSION['flash_type'] ?? 'success'; ?>">
                            <i class="fas fa-check-circle me-2"></i><?php echo $_SESSION['flash_message']; ?>
                        </div>
                        <?php
                        unset($_SESSION['flash_message']);
                        unset($_SESSION['flash_type']);
                        ?>
                    <?php endif; ?>

                    <form method="POST">
                        <!-- CSRF Token -->
                        <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                        <div class="mb-4">
                            <label for="header_bg_image" class="form-label">
                                <i class="fas fa-image me-2"></i>Header Background Image URL
                            </label>
                            <input type="url"
                                   class="form-control"
                                   id="header_bg_image"
                                   name="header_bg_image"
                                   value="<?php echo htmlspecialchars($current_bg_image ?? ''); ?>"
                                   placeholder="https://example.com/path/to/racing-background.jpg">
                            <div class="form-text">
                                Enter a URL to a racing/automotive background image. The image will show subtly through the carbon fiber pattern.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="header_bg_size" class="form-label">
                                <i class="fas fa-expand-arrows-alt me-2"></i>Background Image Size
                            </label>
                            <select class="form-select" id="header_bg_size" name="header_bg_size">
                                <option value="cover" <?php echo ($header_bg_size ?? 'cover') === 'cover' ? 'selected' : ''; ?>>
                                    Cover (Fill header, may crop image)
                                </option>
                                <option value="contain" <?php echo ($header_bg_size ?? '') === 'contain' ? 'selected' : ''; ?>>
                                    Contain (Show full image, may have empty space)
                                </option>
                                <option value="auto" <?php echo ($header_bg_size ?? '') === 'auto' ? 'selected' : ''; ?>>
                                    Auto (Original size)
                                </option>
                                <option value="100% auto" <?php echo ($header_bg_size ?? '') === '100% auto' ? 'selected' : ''; ?>>
                                    Stretch Width (Full width, maintain aspect ratio)
                                </option>
                            </select>
                            <div class="form-text">
                                Choose how the background image should be sized within the header.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="header_bg_position" class="form-label">
                                <i class="fas fa-crosshairs me-2"></i>Background Image Position
                            </label>
                            <select class="form-select" id="header_bg_position" name="header_bg_position">
                                <option value="center" <?php echo ($header_bg_position ?? 'center') === 'center' ? 'selected' : ''; ?>>
                                    Center
                                </option>
                                <option value="top" <?php echo ($header_bg_position ?? '') === 'top' ? 'selected' : ''; ?>>
                                    Top
                                </option>
                                <option value="bottom" <?php echo ($header_bg_position ?? '') === 'bottom' ? 'selected' : ''; ?>>
                                    Bottom
                                </option>
                                <option value="left" <?php echo ($header_bg_position ?? '') === 'left' ? 'selected' : ''; ?>>
                                    Left
                                </option>
                                <option value="right" <?php echo ($header_bg_position ?? '') === 'right' ? 'selected' : ''; ?>>
                                    Right
                                </option>
                                <option value="top left" <?php echo ($header_bg_position ?? '') === 'top left' ? 'selected' : ''; ?>>
                                    Top Left
                                </option>
                                <option value="top right" <?php echo ($header_bg_position ?? '') === 'top right' ? 'selected' : ''; ?>>
                                    Top Right
                                </option>
                                <option value="bottom left" <?php echo ($header_bg_position ?? '') === 'bottom left' ? 'selected' : ''; ?>>
                                    Bottom Left
                                </option>
                                <option value="bottom right" <?php echo ($header_bg_position ?? '') === 'bottom right' ? 'selected' : ''; ?>>
                                    Bottom Right
                                </option>
                            </select>
                            <div class="form-text">
                                Choose where the background image should be positioned within the header.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="header_carbon_opacity" class="form-label">
                                <i class="fas fa-layer-group me-2"></i>Carbon Fiber Overlay Strength
                            </label>
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <input type="range"
                                           class="form-range"
                                           id="header_carbon_opacity"
                                           name="header_carbon_opacity"
                                           min="0"
                                           max="100"
                                           value="<?php echo htmlspecialchars($header_carbon_opacity ?? '60'); ?>"
                                           oninput="updateCarbonDisplay(this.value)">
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control"
                                               id="carbon_display"
                                               min="0"
                                               max="100"
                                               value="<?php echo htmlspecialchars($header_carbon_opacity ?? '60'); ?>"
                                               onchange="updateCarbonSlider(this.value)">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">
                                Control the strength of the carbon fiber pattern overlay.
                                <strong>0%</strong> = no carbon fiber, <strong>100%</strong> = full carbon fiber effect.
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="header_bg_brightness" class="form-label">
                                <i class="fas fa-sun me-2"></i>Background Image Brightness
                            </label>
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <input type="range"
                                           class="form-range"
                                           id="header_bg_brightness"
                                           name="header_bg_brightness"
                                           min="0"
                                           max="150"
                                           value="<?php echo htmlspecialchars($header_bg_brightness ?? '40'); ?>"
                                           oninput="updateBrightnessDisplay(this.value)">
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="number"
                                               class="form-control"
                                               id="brightness_display"
                                               min="0"
                                               max="150"
                                               value="<?php echo htmlspecialchars($header_bg_brightness ?? '40'); ?>"
                                               onchange="updateBrightnessSlider(this.value)">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-text">
                                Control the brightness of the background image.
                                <strong>0%</strong> = completely black, <strong>100%</strong> = normal, <strong>150%</strong> = bright.
                                <br><small class="text-muted">Recommended: 20-60% for racing images to maintain readability.</small>
                            </div>
                        </div>

                        <?php if (!empty($current_bg_image)) : ?>
                            <div class="mb-4">
                                <label class="form-label">Current Background Preview:</label>
                                <div class="border rounded p-3" style="background: linear-gradient(rgba(20, 20, 20, 0.85), rgba(10, 10, 10, 0.9)), url('<?php echo htmlspecialchars($current_bg_image ?? ''); ?>'); background-size: cover; background-position: center; height: 100px;">
                                    <div class="text-white text-center pt-3">
                                        <strong>Racing Header Preview</strong>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo BASE_URL; ?>/admin/settings_media" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Media Settings
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Header Settings
                            </button>
                        </div>
                    </form>

                    <hr class="my-4">

                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-info-circle me-2"></i>Racing Header Features:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Carbon fiber background pattern</li>
                                <li><i class="fas fa-check text-success me-2"></i>Chrome accents and trim</li>
                                <li><i class="fas fa-check text-success me-2"></i>Racing-style navigation</li>
                                <li><i class="fas fa-check text-success me-2"></i>LED glow effects</li>
                                <li><i class="fas fa-check text-success me-2"></i>Custom background image support</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-palette me-2"></i>Recommended Images:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-car me-2 text-primary"></i>Racing cars or tracks</li>
                                <li><i class="fas fa-road me-2 text-primary"></i>Automotive scenes</li>
                                <li><i class="fas fa-flag-checkered me-2 text-primary"></i>Racing flags or patterns</li>
                                <li><i class="fas fa-cog me-2 text-primary"></i>Engine or mechanical details</li>
                                <li><i class="fas fa-image me-2 text-primary"></i>High contrast, dark images work best</li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-ruler-combined me-2"></i>Image Size Guide:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-desktop me-2 text-warning"></i><strong>Optimal:</strong> 1920x200px</li>
                                <li><i class="fas fa-expand me-2 text-info"></i><strong>Wide:</strong> 2560x300px</li>
                                <li><i class="fas fa-compress me-2 text-secondary"></i><strong>Minimum:</strong> 1200x150px</li>
                                <li><i class="fas fa-weight me-2 text-danger"></i><strong>File size:</strong> Under 500KB</li>
                                <li><i class="fas fa-file-image me-2 text-success"></i><strong>Format:</strong> JPG, PNG, WebP</li>
                            </ul>
                            <div class="alert alert-info mt-2 p-2">
                                <small><strong>Size Tip:</strong> If your image looks zoomed in, try "Contain" or "Auto" sizing, or use a wider image (2560px+).</small>
                            </div>
                            <div class="alert alert-warning mt-2 p-2">
                                <small><strong>Control Guide:</strong><br>
                                • <strong>Carbon Fiber:</strong> 40-80% for authentic look<br>
                                • <strong>Brightness:</strong> 30-60% for racing images<br>
                                • <strong>Tip:</strong> Lower brightness prevents image bleeding through carbon fiber gaps</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Carbon fiber opacity slider interaction
function updateCarbonDisplay(value) {
    document.getElementById('carbon_display').value = value;
    document.getElementById('header_carbon_opacity').value = value;
    updatePreview();
}

function updateCarbonSlider(value) {
    // Ensure value is within range
    if (value < 0) value = 0;
    if (value > 100) value = 100;

    document.getElementById('header_carbon_opacity').value = value;
    document.getElementById('carbon_display').value = value;
    updatePreview();
}

// Brightness slider interaction
function updateBrightnessDisplay(value) {
    document.getElementById('brightness_display').value = value;
    document.getElementById('header_bg_brightness').value = value;
    updatePreview();
}

function updateBrightnessSlider(value) {
    // Ensure value is within range
    if (value < 0) value = 0;
    if (value > 150) value = 150;

    document.getElementById('header_bg_brightness').value = value;
    document.getElementById('brightness_display').value = value;
    updatePreview();
}

// Update preview with both carbon fiber and brightness
function updatePreview() {
    const previewElement = document.querySelector('.border.rounded.p-3');
    if (previewElement) {
        const carbonOpacity = document.getElementById('header_carbon_opacity').value / 100;
        const brightness = document.getElementById('header_bg_brightness').value;
        const currentBgImage = '<?php echo htmlspecialchars($current_bg_image ?? ''); ?>';

        if (currentBgImage) {
            // Calculate brightness overlay opacity
            const brightnessOverlay = (100 - brightness) / 100;

            // Apply layered background with carbon fiber and brightness control
            previewElement.style.background = `
                linear-gradient(45deg, rgba(42, 42, 42, ${carbonOpacity * 0.8}) 25%, transparent 25%),
                linear-gradient(-45deg, rgba(42, 42, 42, ${carbonOpacity * 0.8}) 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, rgba(42, 42, 42, ${carbonOpacity * 0.8}) 75%),
                linear-gradient(-45deg, transparent 75%, rgba(42, 42, 42, ${carbonOpacity * 0.8}) 75%),
                radial-gradient(circle at 25% 25%, rgba(68, 68, 68, ${carbonOpacity * 0.6}) 1px, transparent 1px),
                radial-gradient(circle at 75% 75%, rgba(68, 68, 68, ${carbonOpacity * 0.6}) 1px, transparent 1px),
                linear-gradient(rgba(0, 0, 0, ${brightnessOverlay}), rgba(0, 0, 0, ${brightnessOverlay})),
                url('${currentBgImage}')
            `;
            previewElement.style.backgroundSize = '12px 12px, 12px 12px, 12px 12px, 12px 12px, 6px 6px, 6px 6px, cover, cover';
            previewElement.style.backgroundPosition = '0 0, 0 0, 0 0, 0 0, 0 0, 3px 3px, center, center';
            previewElement.style.backgroundRepeat = 'repeat, repeat, repeat, repeat, repeat, repeat, no-repeat, no-repeat';
        }
    }
}

// Add event listeners when page loads
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('header_carbon_opacity').addEventListener('input', function() {
        updateCarbonDisplay(this.value);
    });

    document.getElementById('header_bg_brightness').addEventListener('input', function() {
        updateBrightnessDisplay(this.value);
    });

    // Initial preview update
    updatePreview();
});
</script>

<?php include APPROOT . '/views/includes/footer.php'; ?>

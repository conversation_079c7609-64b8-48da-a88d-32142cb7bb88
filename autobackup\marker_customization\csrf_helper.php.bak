<?php
/**
 * CSRF Helper
 * 
 * Functions for CSRF protection.
 */

// Define CSRF token name if not already defined
if (!defined('CSRF_TOKEN_NAME')) {
    define('CSRF_TOKEN_NAME', 'csrf_token');
}

/**
 * Generate CSRF token
 * 
 * @return string CSRF token
 */
function generateCsrfToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Generate CSRF input field
 * 
 * @return string HTML input field with CSRF token
 */
function generateCsrfInput() {
    $token = generateCsrfToken();
    return '<input type="hidden" name="' . htmlspecialchars(CSRF_TOKEN_NAME) . '" value="' . htmlspecialchars($token) . '">';
}

/**
 * Verify CSRF token
 * 
 * @param string $token Token to verify
 * @param string $source Source of the token (post, get, header)
 * @return bool True if token is valid, false otherwise
 */
function verifyCsrfToken($token = null, $source = 'post') {
    // If no token provided, try to get it from the specified source
    if ($token === null) {
        if ($source === 'post') {
            $token = $_POST[CSRF_TOKEN_NAME] ?? '';
        } elseif ($source === 'get') {
            $token = $_GET[CSRF_TOKEN_NAME] ?? '';
        } elseif ($source === 'header') {
            // For AJAX requests, check the X-CSRF-TOKEN header
            $headers = getallheaders();
            $token = $headers['X-CSRF-TOKEN'] ?? '';
        }
    }
    
    // Debug logging
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log('CSRF Verification - Token from ' . $source . ': ' . $token);
        error_log('CSRF Verification - Token in session: ' . ($_SESSION[CSRF_TOKEN_NAME] ?? 'not set'));
    }
    
    if (!isset($_SESSION[CSRF_TOKEN_NAME]) || empty($token)) {
        error_log('CSRF token validation failed: Token missing');
        return false;
    }
    
    $result = hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
    
    if (!$result) {
        error_log('CSRF token validation failed: Token mismatch');
        // Log the tokens for debugging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log('Expected: ' . $_SESSION[CSRF_TOKEN_NAME] . ', Received: ' . $token);
        }
    }
    
    return $result;
}

/**
 * Get CSRF token input field
 * 
 * @return string HTML input field with CSRF token
 */
function csrfTokenField() {
    $token = generateCsrfToken();
    return '<input type="hidden" name="' . htmlspecialchars(CSRF_TOKEN_NAME) . '" value="' . htmlspecialchars($token) . '">';
}

/**
 * Get CSRF token for JavaScript
 * 
 * @return string JavaScript code to set CSRF token in meta tag and AJAX headers
 */
function csrfTokenJavaScript() {
    $token = generateCsrfToken();
    return "
    <meta name=\"csrf-token\" content=\"" . htmlspecialchars($token) . "\">
    <script>
        // Set CSRF token for AJAX requests
        document.addEventListener('DOMContentLoaded', function() {
            // Function to add CSRF token to AJAX requests
            const addCsrfTokenToAjax = () => {
                const token = document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content');
                
                // Add token to XMLHttpRequest
                const originalXhrOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function() {
                    const result = originalXhrOpen.apply(this, arguments);
                    this.setRequestHeader('X-CSRF-TOKEN', token);
                    return result;
                };
                
                // Add token to fetch requests
                const originalFetch = window.fetch;
                window.fetch = function(url, options = {}) {
                    if (!options.headers) {
                        options.headers = {};
                    }
                    
                    if (typeof options.headers.append === 'function') {
                        options.headers.append('X-CSRF-TOKEN', token);
                    } else {
                        options.headers['X-CSRF-TOKEN'] = token;
                    }
                    
                    return originalFetch.call(this, url, options);
                };
            };
            
            // Add CSRF token to AJAX requests
            addCsrfTokenToAjax();
        });
    </script>";
}
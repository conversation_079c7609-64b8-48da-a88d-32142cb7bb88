<?php require APPROOT . '/views/includes/header.php'; ?>

<style>
/* Force white backgrounds for notification preferences - highest priority */
.card,
.card-header,
.card-body {
    background-color: #ffffff !important;
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

.table,
.table th,
.table td {
    background-color: #ffffff !important;
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

/* Override dark mode completely for this page */
@media (prefers-color-scheme: dark) {
    .card,
    .card-header,
    .card-body,
    .table,
    .table th,
    .table td {
        background-color: #ffffff !important;
        color: #212529 !important;
        border-color: #dee2e6 !important;
    }
}
</style>

<div class="container event-subscriptions-page">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-calendar-check me-2"></i>Event Subscriptions</h1>
            <p class="text-muted">Manage your individual event notification subscriptions.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/user/notifications" class="btn btn-primary me-2">
                <i class="fas fa-cog me-2"></i>Notification Settings
            </a>
            <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Profile
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Active Subscriptions Card -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-calendar-check me-2"></i>Your Event Subscriptions
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($subscriptions)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Active Subscriptions</h5>
                            <p class="text-muted">You haven't subscribed to any event notifications yet.</p>
                            <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-primary">
                                <i class="fas fa-calendar me-2"></i>Browse Events
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Event</th>
                                        <th>Type</th>
                                        <th>Date</th>
                                        <th>Notifications</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($subscriptions as $subscription): ?>
                                        <tr>
                                            <td>
                                                <?php 
                                                // Determine the correct URL based on event type
                                                if ($subscription->event_type === 'calendar_event') {
                                                    $eventUrl = BASE_URL . '/calendar/event/' . $subscription->event_id;
                                                } else {
                                                    $eventUrl = BASE_URL . '/show/view/' . $subscription->event_id;
                                                }
                                                ?>
                                                <strong>
                                                    <a href="<?php echo $eventUrl; ?>" class="text-decoration-none event-title-link">
                                                        <?php echo htmlspecialchars($subscription->event_title); ?>
                                                    </a>
                                                </strong>
                                            </td>
                                            <td>
                                                <?php if ($subscription->event_type === 'calendar_event'): ?>
                                                    <span class="badge bg-primary">
                                                        <i class="fas fa-calendar me-1"></i>Event
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">
                                                        <i class="fas fa-car me-1"></i>Car Show
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $eventDate = new DateTime($subscription->event_date);
                                                echo $eventDate->format('M j, Y g:i A');
                                                ?>
                                            </td>
                                            <td>
                                                <?php 
                                                $notificationTimes = json_decode($subscription->notification_times, true);
                                                if ($notificationTimes && is_array($notificationTimes)) {
                                                    foreach ($notificationTimes as $minutes) {
                                                        if ($minutes >= 1440) {
                                                            $days = floor($minutes / 1440);
                                                            echo '<span class="badge bg-info me-1">' . $days . ' day' . ($days > 1 ? 's' : '') . '</span>';
                                                        } elseif ($minutes >= 60) {
                                                            $hours = floor($minutes / 60);
                                                            echo '<span class="badge bg-info me-1">' . $hours . ' hour' . ($hours > 1 ? 's' : '') . '</span>';
                                                        } else {
                                                            echo '<span class="badge bg-info me-1">' . $minutes . ' min</span>';
                                                        }
                                                    }
                                                } else {
                                                    echo '<span class="badge bg-secondary">Default</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                        onclick="unsubscribeFromEvent(<?php echo $subscription->event_id; ?>, '<?php echo $subscription->event_type; ?>')">
                                                    <i class="fas fa-times me-1"></i>Unsubscribe
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Help Card -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>About Subscriptions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="fas fa-calendar-check text-success me-2"></i>Event Subscriptions</h6>
                        <p class="small text-muted">When you subscribe to an event, you'll receive notifications based on your <a href="<?php echo BASE_URL; ?>/user/notifications">notification settings</a>.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-bell text-primary me-2"></i>Notification Types</h6>
                        <p class="small text-muted">Notifications can be sent via email, SMS, push notifications, or in-app alerts depending on your preferences.</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6><i class="fas fa-clock text-warning me-2"></i>Timing</h6>
                        <p class="small text-muted">Each subscription shows when you'll receive reminders before the event starts.</p>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <a href="<?php echo BASE_URL; ?>/user/notifications" class="btn btn-primary btn-sm">
                            <i class="fas fa-cog me-2"></i>Configure Settings
                        </a>
                        <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-calendar me-2"></i>Browse Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Card styling consistent with /user/notifications */
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-label {
    cursor: pointer;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    background-color: #ffffff !important;
    color: #212529 !important;
    border-color: #dee2e6 !important;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    background-color: inherit !important;
    color: inherit !important;
    border-bottom-color: #dee2e6 !important;
}

.card-body {
    background-color: #ffffff !important;
    color: #212529 !important;
}

/* Event title link styling */
.event-title-link {
    color: inherit !important;
    transition: color 0.2s ease;
}

.event-title-link:hover {
    color: #0d6efd !important;
    text-decoration: underline !important;
}

.event-title-link:focus {
    color: #0d6efd !important;
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}
</style>

<script>
function unsubscribeFromEvent(eventId, eventType) {
    if (!confirm('Are you sure you want to unsubscribe from notifications for this event?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('event_id', eventId);
    formData.append('event_type', eventType);
    formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $csrf_token; ?>');
    
    fetch('<?php echo BASE_URL; ?>/notification/unsubscribe', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the row from the table
            const row = event.target.closest('tr');
            row.remove();
            
            // Show success message
            showToast('Successfully unsubscribed from event notifications', 'success');
            
            // If no more rows, show the "no subscriptions" message
            const tbody = document.querySelector('tbody');
            if (tbody && tbody.children.length === 0) {
                location.reload(); // Reload to show the "no subscriptions" message
            }
        } else {
            showToast(data.message || 'Failed to unsubscribe from event', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while unsubscribing', 'error');
    });
}

function showToast(message, type = 'info') {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
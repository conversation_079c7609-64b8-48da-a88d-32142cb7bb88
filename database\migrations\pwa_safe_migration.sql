-- PWA Safe Migration - Handles existing columns gracefully
-- Run this if you're getting duplicate column errors

-- First, let's check what columns already exist
SELECT 'Checking existing PWA columns in users table...' as status;
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME IN ('pwa_installed', 'pwa_standalone', 'push_supported', 'last_seen');

-- Create push subscriptions table (safe)
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_active (active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notification preferences table (safe)
CREATE TABLE IF NOT EXISTS notification_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_reminders TINYINT(1) DEFAULT 1,
    registration_updates TINYINT(1) DEFAULT 1,
    judging_reminders TINYINT(1) DEFAULT 1,
    payment_notifications TINYINT(1) DEFAULT 1,
    new_events TINYINT(1) DEFAULT 0,
    marketing TINYINT(1) DEFAULT 0,
    push_enabled TINYINT(1) DEFAULT 1,
    email_enabled TINYINT(1) DEFAULT 1,
    sms_enabled TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create offline sync queue table (safe)
CREATE TABLE IF NOT EXISTS offline_sync_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    sync_type ENUM('registration', 'payment', 'scoring', 'image_upload', 'form_data') NOT NULL,
    sync_data JSON NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_sync_type (sync_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create PWA metrics table (safe)
CREATE TABLE IF NOT EXISTS pwa_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    installed_users INT DEFAULT 0,
    push_subscribers INT DEFAULT 0,
    offline_usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_date (metric_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create notification log table (safe)
CREATE TABLE IF NOT EXISTS notification_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    notification_type ENUM('push', 'email', 'sms', 'in_app') NOT NULL,
    title VARCHAR(255),
    message TEXT,
    status ENUM('sent', 'delivered', 'failed', 'clicked') DEFAULT 'sent',
    metadata JSON,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_type (user_id, notification_type),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add PWA settings (safe with IGNORE)
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_enabled', '1', 'Enable Progressive Web App features', 'boolean', NOW()),
('push_notifications_enabled', '1', 'Enable push notifications', 'boolean', NOW()),
('offline_mode_enabled', '1', 'Enable offline functionality', 'boolean', NOW()),
('pwa_app_name', 'RER Events', 'PWA application name', 'text', NOW()),
('pwa_short_name', 'RER Events', 'PWA short name', 'text', NOW()),
('pwa_description', 'Rowan Elite Rides Events & Shows Management', 'PWA description', 'text', NOW()),
('pwa_theme_color', '#dc3545', 'PWA theme color', 'color', NOW()),
('pwa_background_color', '#ffffff', 'PWA background color', 'color', NOW()),
('vapid_public_key', '', 'VAPID public key for push notifications', 'text', NOW()),
('vapid_private_key', '', 'VAPID private key for push notifications (encrypted)', 'password', NOW());

-- Insert default notification preferences for existing users (safe)
INSERT IGNORE INTO notification_preferences (user_id, event_reminders, registration_updates, judging_reminders, payment_notifications)
SELECT id, 1, 1, 1, 1 
FROM users;

-- Record migration
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_migration_version', '1.0.0', 'PWA features migration version', 'text', NOW());

SELECT 'Safe PWA migration completed! Tables and settings created.' as status;
SELECT 'Note: User table columns may need to be added manually if they do not exist.' as note;
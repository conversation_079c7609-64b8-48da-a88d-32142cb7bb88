-- Migration: Add missing notification preference columns
-- Date: 2024-12-21
-- Purpose: Add columns for event_reminders, registration_updates, judging_updates, award_notifications, system_announcements, and reminder_times

-- Add missing columns to user_notification_preferences table
ALTER TABLE `user_notification_preferences` 
ADD COLUMN `event_reminders` TINYINT(1) DEFAULT 1 AFTER `toast_notifications`,
ADD COLUMN `registration_updates` TINYINT(1) DEFAULT 1 AFTER `event_reminders`,
ADD COLUMN `judging_updates` TINYINT(1) DEFAULT 1 AFTER `registration_updates`,
ADD COLUMN `award_notifications` TINYINT(1) DEFAULT 1 AFTER `judging_updates`,
ADD COLUMN `system_announcements` TINYINT(1) DEFAULT 1 AFTER `award_notifications`,
ADD COLUMN `reminder_times` VARCHAR(255) DEFAULT '[1440, 60]' AFTER `system_announcements`;

-- Update existing records to have default values for new columns
UPDATE `user_notification_preferences` SET 
    `event_reminders` = 1,
    `registration_updates` = 1,
    `judging_updates` = 1,
    `award_notifications` = 1,
    `system_announcements` = 1,
    `reminder_times` = '[1440, 60]'
WHERE `event_reminders` IS NULL;

-- Verify the changes
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
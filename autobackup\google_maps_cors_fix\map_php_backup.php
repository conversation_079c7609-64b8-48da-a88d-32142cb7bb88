<?php
// Backup of map.php changes made to fix Google Maps tile loading issues
// Date: <?php echo date('Y-m-d H:i:s'); ?>

// Changes made:
// 1. Removed &loading=async parameter from Google Maps API URL
// 2. Added renderingType: RASTER to map options
// 3. Added tile loading event listeners for debugging
// 4. Added timeout fallback to OpenStreetMap if Google Maps fails
// 5. Added tile detection after 3 seconds for debugging

// Key changes in loadGoogleMapsAPI():
// - script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap&v=3.60`;
// - Removed: &loading=async

// Key changes in initGoogleMap():
// - Added: renderingType: google.maps.RenderingType.RASTER
// - Added: gestureHandling: 'greedy'
// - Added: restriction: null
// - Added: tilesloaded and idle event listeners
// - Added: setTimeout tile detection

// Key changes in loadGoogleMapsAPI():
// - Added: 15 second timeout fallback to OpenStreetMap

echo "Backup created successfully";
?>
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Edit Show</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/coordinator/show/<?php echo $data['id']; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Show
                </a>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Show Details</h5>
            <?php if (isset($data['formTemplate'])): ?>
            <small class="text-muted">Using template: <?php echo isset($data['formTemplate']) ? $data['formTemplate']->name : 'Default Show Form'; ?></small>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <form action="<?php echo URLROOT; ?>/coordinator/editShow/<?php echo $data['id']; ?>" method="post">
                <?php echo csrfTokenField(); ?>
                <input type="hidden" name="id" value="<?php echo $data['id']; ?>">
                
                <!-- Basic Show Information -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo (!empty($data['name_err'])) ? 'is-invalid' : ''; ?>" 
                               id="name" name="name" value="<?php echo $data['name']; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['name_err']; ?></div>
                    </div>
                    <div class="col-md-6">
                        <label for="location" class="form-label">Venue <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo (!empty($data['location_err'])) ? 'is-invalid' : ''; ?>" 
                               id="location" name="location" value="<?php echo $data['location']; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['location_err']; ?></div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="4"><?php echo $data['description']; ?></textarea>
                        <div class="form-text">Provide a detailed description of the show.</div>
                    </div>
                </div>
                
                <hr>
                <h5 class="mb-3">Dates and Registration</h5>
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                        <?php 
                        // Format the datetime value for the datetime-local input
                        $start_date_formatted = '';
                        if (!empty($data['start_date'])) {
                            $dateTime = new DateTime($data['start_date']);
                            $start_date_formatted = $dateTime->format('Y-m-d\TH:i');
                        }
                        ?>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['start_date_err'])) ? 'is-invalid' : ''; ?>" 
                               id="start_date" name="start_date" value="<?php echo $start_date_formatted; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['start_date_err']; ?></div>
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                        <?php 
                        // Format the datetime value for the datetime-local input
                        $end_date_formatted = '';
                        if (!empty($data['end_date'])) {
                            $dateTime = new DateTime($data['end_date']);
                            $end_date_formatted = $dateTime->format('Y-m-d\TH:i');
                        }
                        ?>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['end_date_err'])) ? 'is-invalid' : ''; ?>" 
                               id="end_date" name="end_date" value="<?php echo $end_date_formatted; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['end_date_err']; ?></div>
                    </div>
                    <div class="col-md-3">
                        <label for="registration_start" class="form-label">Registration Start Date & Time <span class="text-danger">*</span></label>
                        <?php 
                        // Format the datetime value for the datetime-local input
                        $registration_start_formatted = '';
                        if (!empty($data['registration_start'])) {
                            $dateTime = new DateTime($data['registration_start']);
                            $registration_start_formatted = $dateTime->format('Y-m-d\TH:i');
                        }
                        ?>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['registration_start_err'])) ? 'is-invalid' : ''; ?>" 
                               id="registration_start" name="registration_start" value="<?php echo $registration_start_formatted; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['registration_start_err']; ?></div>
                    </div>
                    <div class="col-md-3">
                        <label for="registration_end" class="form-label">Registration End Date & Time <span class="text-danger">*</span></label>
                        <?php 
                        // Format the datetime value for the datetime-local input
                        $registration_end_formatted = '';
                        if (!empty($data['registration_end'])) {
                            $dateTime = new DateTime($data['registration_end']);
                            $registration_end_formatted = $dateTime->format('Y-m-d\TH:i');
                        }
                        ?>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['registration_end_err'])) ? 'is-invalid' : ''; ?>" 
                               id="registration_end" name="registration_end" value="<?php echo $registration_end_formatted; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['registration_end_err']; ?></div>
                    </div>
                </div>
                
                <hr>
                <h5 class="mb-3">Show Settings</h5>
                
                <div class="row mb-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo (!empty($data['status_err'])) ? 'is-invalid' : ''; ?>" 
                                id="status" name="status" required>
                            <option value="draft" <?php echo ($data['status'] == 'draft') ? 'selected' : ''; ?>>Draft</option>
                            <option value="published" <?php echo ($data['status'] == 'published') ? 'selected' : ''; ?>>Published</option>
                            <option value="cancelled" <?php echo ($data['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                            <option value="completed" <?php echo ($data['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                        </select>
                        <div class="invalid-feedback"><?php echo $data['status_err']; ?></div>
                        <div class="form-text">Set the current status of the show.</div>
                    </div>
                    <div class="col-md-4">
                        <label for="fan_voting_enabled" class="form-label">Fan Favorite Voting</label>
                        <select class="form-select" id="fan_voting_enabled" name="fan_voting_enabled">
                            <option value="1" <?php echo (isset($data['fan_voting_enabled']) && $data['fan_voting_enabled']) ? 'selected' : ''; ?>>Enabled</option>
                            <option value="0" <?php echo (isset($data['fan_voting_enabled']) && !$data['fan_voting_enabled']) ? 'selected' : ''; ?>>Disabled</option>
                        </select>
                        <div class="form-text">Enable or disable fan favorite voting for this show.</div>
                    </div>
                    <div class="col-md-4">
                        <label for="is_free" class="form-label">Free Registration</label>
                        <select class="form-select" id="is_free" name="is_free">
                            <option value="1" <?php echo (isset($data['is_free']) && $data['is_free']) ? 'selected' : ''; ?>>Yes (Free)</option>
                            <option value="0" <?php echo (isset($data['is_free']) && !$data['is_free']) ? 'selected' : ''; ?>>No (Paid)</option>
                        </select>
                        <div class="form-text">Is registration free for this show?</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="registration_fee" class="form-label">Registration Fee ($)</label>
                        <input type="number" step="0.01" min="0" class="form-control" 
                               id="registration_fee" name="registration_fee" 
                               value="<?php echo isset($data['registration_fee']) ? $data['registration_fee'] : '0.00'; ?>"
                               <?php echo (isset($data['is_free']) && $data['is_free']) ? 'disabled' : ''; ?>>
                        <div class="form-text">Base registration fee for the show (if not free).</div>
                    </div>
                    <div class="col-md-6">
                        <label for="listing_fee" class="form-label">Listing Fee ($)</label>
                        <input type="number" step="0.01" min="0" class="form-control" 
                               id="listing_fee" name="listing_fee" 
                               value="<?php echo isset($data['listing_fee']) ? $data['listing_fee'] : '0.00'; ?>">
                        <div class="form-text">Optional fee for listing in the show program.</div>
                    </div>
                </div>
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Role Management</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <?php if (hasFlashMessage('user_success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_success')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (hasFlashMessage('user_error')) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_error')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Role Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($roles as $role => $displayName) : ?>
                            <div class="col-md-3 col-sm-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <h5 class="card-title">
                                            <?php echo $displayName; ?>
                                        </h5>
                                        <div class="display-4 my-3">
                                            <?php echo $roleCounts[$role] ?? 0; ?>
                                        </div>
                                        <p class="card-text text-muted">Users</p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php foreach ($roles as $role => $displayName) : ?>
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <?php echo $displayName; ?>
                            <span class="badge bg-secondary ms-2"><?php echo $roleCounts[$role] ?? 0; ?></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($usersByRole[$role])) : ?>
                            <p class="text-muted">No users with this role.</p>
                        <?php else : ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Created</th>
                                            <th>Last Login</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($usersByRole[$role] as $user) : ?>
                                            <tr>
                                                <td>
                                                    <?php if ($user->profile_image) : ?>
                                                        <img src="<?php echo BASE_URL; ?>/<?php echo $user->profile_image; ?>" alt="Profile" class="rounded-circle me-2" width="30" height="30">
                                                    <?php else : ?>
                                                        <i class="fas fa-user-circle me-2 text-secondary" style="font-size: 1.5rem;"></i>
                                                    <?php endif; ?>
                                                    <?php echo $user->name; ?>
                                                </td>
                                                <td><?php echo $user->email; ?></td>
                                                <td>
                                                    <?php if ($user->status == 'active') : ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($user->status == 'inactive') : ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php else : ?>
                                                        <span class="badge bg-warning text-dark">Pending</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo date('M j, Y', strtotime($user->created_at)); ?></td>
                                                <td>
                                                    <?php echo $user->last_login ? date('M j, Y', strtotime($user->last_login)) : 'Never'; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group">
                                                        <a href="<?php echo BASE_URL; ?>/admin/assignRole/<?php echo $user->id; ?>" class="btn btn-sm btn-primary">
                                                            <i class="fas fa-user-tag"></i> Change Role
                                                        </a>
                                                        <a href="<?php echo BASE_URL; ?>/admin/editUser/<?php echo $user->id; ?>" class="btn btn-sm btn-secondary">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Role Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Role</th>
                                    <th>Description</th>
                                    <th>Capabilities</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-danger">Administrator</span></td>
                                    <td>Full system access and configuration</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Manage all users and roles</li>
                                            <li>Configure system settings</li>
                                            <li>Manage all shows and events</li>
                                            <li>Access all system features</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">Show Coordinator</span></td>
                                    <td>Manages specific shows, judges, and registrations</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Create and manage assigned shows</li>
                                            <li>Assign judges to categories</li>
                                            <li>Manage registrations and participants</li>
                                            <li>View and publish results</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">Judge</span></td>
                                    <td>Evaluates vehicles using the judging interface</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Score vehicles in assigned categories</li>
                                            <li>Submit judging results</li>
                                            <li>View assigned show details</li>
                                            <li>Register own vehicles for shows</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">Registered User</span></td>
                                    <td>Can register vehicles for shows and view their registrations</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Register vehicles for shows</li>
                                            <li>View own registrations and results</li>
                                            <li>Manage own vehicle information</li>
                                            <li>Vote in fan choice competitions</li>
                                        </ul>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
# Notification System Consolidation - Implementation Plan

## Objective
Fix and consolidate the notification system by:
1. Making `/user/notifications` fully functional with all preference fields
2. Converting `/user/notification_preferences` to subscription management only
3. Ensuring PWA and notification system compatibility

## Current Issues
- `/user/notifications` shows fields that don't save to database
- `/user/notification_preferences` duplicates basic functionality
- Database table missing required columns

## Implementation Steps

### Phase 1: Database Schema Updates
- Add missing columns to `user_notification_preferences` table:
  - `event_reminders` TINYINT(1) DEFAULT 1
  - `registration_updates` TINYINT(1) DEFAULT 1  
  - `judging_updates` TINYINT(1) DEFAULT 1
  - `award_notifications` TINYINT(1) DEFAULT 1
  - `system_announcements` TINYINT(1) DEFAULT 1
  - `reminder_times` VARCHAR(255) DEFAULT '[1440, 60]'

### Phase 2: Model Updates
- Update `NotificationModel::updateUserPreferences()` to handle all fields
- Update `NotificationModel::getUserPreferences()` to return all fields
- Ensure backward compatibility

### Phase 3: Controller Fixes
- Fix `UserController::notifications()` to properly save all fields
- Ensure proper validation and sanitization

### Phase 4: View Updates
- Convert `/user/notification_preferences` to subscription-only page
- Ensure `/user/notifications` displays all preferences correctly

### Phase 5: Testing
- Test all notification types work
- Test PWA notifications still function
- Test subscription management works
- Test preference persistence

## Files to Modify
- `database/schema.sql` - Add missing columns
- `models/NotificationModel.php` - Update methods
- `controllers/UserController.php` - Fix notifications method
- `views/user/notification_preferences.php` - Convert to subscriptions only
- `views/user/notifications.php` - Verify functionality

## Backup Location
- `/autobackup/notification_system_consolidation/`
# Geocoding Fix for Calendar Events (Version 2)

## Issue Description
The geocoding functionality in the CalendarController was not working the same way as the admin batch geocoding tool, even though both were using the `geocodeEvent()` function. This caused addresses to be geocoded differently depending on which method was used.

## Root Cause Analysis
After examining the code, we found that while both the CalendarController and the admin batch tool were using the `geocodeEvent()` function, there were differences in how the address data was being prepared:

1. In the admin batch tool, a new array was created with all address fields explicitly set with defaults
2. In the CalendarController, the original data array was passed directly to `geocodeEvent()`
3. The `geocodeEvent()` function expects certain fields to be set with defaults

## Solution
The fix ensures that the address data is prepared in exactly the same way in both the CalendarController and the admin batch tool:

1. Create a separate `$eventData` array with all address fields explicitly set with defaults
2. Pass this array to `geocodeEvent()` instead of the original data array
3. Update the original data array with the geocoded coordinates from the result

## Changes Made
1. Updated the geocoding code in the `createEvent` method to prepare address data the same way as the admin batch tool
2. Updated the geocoding code in the `editEvent` method to prepare address data the same way as the admin batch tool
3. Ensured all address fields have default values to prevent undefined index errors
4. Maintained the existing error logging and user feedback

## Implementation
Replace the geocoding sections in the CalendarController with the code provided in `CalendarController_geocoding_fix.php`.

## Testing
After applying the fix, test the following scenarios:
1. Create a new event with a complete address
2. Edit an existing event and modify its address
3. Use the admin batch geocoding tool on events with addresses

All three methods should now produce consistent geocoding results.

## Version
This fix was implemented in version 1.0.4 of the Calendar Controller.
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 400px; width: 100%; border: 1px solid #ccc; margin: 20px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Google Maps API Diagnostic Test</h1>
    
    <div id="status-container">
        <div class="status info">
            <strong>Testing Google Maps API...</strong>
            <p>This page will test if Google Maps API loads correctly and identify any issues.</p>
        </div>
    </div>
    
    <h2>Test Results</h2>
    <div id="test-results"></div>
    
    <h2>Map Display Test</h2>
    <div id="map"></div>
    
    <h2>Console Output</h2>
    <pre id="console-output"></pre>
    
    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        let consoleOutput = '';
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toISOString();
            consoleOutput += `[${timestamp}] ${type.toUpperCase()}: ${args.join(' ')}\n`;
            document.getElementById('console-output').textContent = consoleOutput;
        }
        
        console.log = function(...args) {
            addToConsole('log', ...args);
            originalLog.apply(console, args);
        };
        
        console.error = function(...args) {
            addToConsole('error', ...args);
            originalError.apply(console, args);
        };
        
        console.warn = function(...args) {
            addToConsole('warn', ...args);
            originalWarn.apply(console, args);
        };
        
        // Test results container
        const testResults = document.getElementById('test-results');
        
        function addTestResult(status, title, message) {
            const div = document.createElement('div');
            div.className = `status ${status}`;
            div.innerHTML = `<strong>${title}</strong><p>${message}</p>`;
            testResults.appendChild(div);
        }
        
        // Test 1: Check if running on HTTPS
        if (location.protocol === 'https:') {
            addTestResult('success', 'HTTPS Check', 'Page is served over HTTPS - Good for Google Maps API');
        } else {
            addTestResult('warning', 'HTTPS Check', 'Page is not served over HTTPS - Some Google Maps features may not work');
        }
        
        // Test 2: Check domain
        addTestResult('info', 'Domain Check', `Current domain: ${location.hostname}`);
        
        // Test 3: CSP Headers Check
        fetch(location.href, { method: 'HEAD' })
            .then(response => {
                const csp = response.headers.get('Content-Security-Policy');
                if (csp) {
                    if (csp.includes('maps.googleapis.com')) {
                        addTestResult('success', 'CSP Headers', 'Content Security Policy allows Google Maps domains');
                    } else {
                        addTestResult('error', 'CSP Headers', 'Content Security Policy may block Google Maps. Check CSP configuration.');
                    }
                } else {
                    addTestResult('warning', 'CSP Headers', 'No Content Security Policy detected');
                }
            })
            .catch(error => {
                addTestResult('warning', 'CSP Headers', 'Could not check CSP headers: ' + error.message);
            });
        
        // Google Maps initialization
        let map;
        let initAttempted = false;
        
        function initMap() {
            initAttempted = true;
            console.log('Google Maps API loaded successfully');
            addTestResult('success', 'Google Maps API', 'API loaded and callback executed');
            
            try {
                // Test map creation
                map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 39.8283, lng: -98.5795 }, // Center of US
                    zoom: 4,
                    mapTypeControl: true,
                    fullscreenControl: true,
                    streetViewControl: false
                });
                
                console.log('Google Maps initialized successfully');
                addTestResult('success', 'Map Initialization', 'Map created and displayed successfully');
                
                // Test marker creation
                const marker = new google.maps.Marker({
                    position: { lat: 39.8283, lng: -98.5795 },
                    map: map,
                    title: 'Test Marker'
                });
                
                addTestResult('success', 'Marker Test', 'Marker created successfully');
                
                // Test InfoWindow
                const infoWindow = new google.maps.InfoWindow({
                    content: '<div><h3>Test InfoWindow</h3><p>Google Maps is working correctly!</p></div>'
                });
                
                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });
                
                addTestResult('success', 'InfoWindow Test', 'InfoWindow functionality working');
                
            } catch (error) {
                console.error('Error initializing map:', error);
                addTestResult('error', 'Map Initialization', 'Failed to create map: ' + error.message);
            }
        }
        
        // Handle authentication failures
        window.gm_authFailure = function() {
            console.error('Google Maps authentication failed');
            addTestResult('error', 'Authentication', 'Google Maps API key authentication failed. Check your API key, billing, and domain restrictions.');
        };
        
        // Load Google Maps API
        // Using the API key from your system
        const apiKey = 'AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM';
        
        if (apiKey && apiKey.length > 10) {
            addTestResult('success', 'API Key', 'API key is present and appears valid');
        } else {
            addTestResult('error', 'API Key', 'API key is missing or invalid');
        }
        
        if (apiKey) {
            console.log('Loading Google Maps API...');
            
            const script = document.createElement('script');
            script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initMap&v=3.60&loading=async`;
            script.async = true;
            script.defer = true;
            
            script.onerror = function() {
                console.error('Failed to load Google Maps API script');
                addTestResult('error', 'Script Loading', 'Failed to load Google Maps API script. Check network connectivity and API key.');
            };
            
            script.onload = function() {
                console.log('Google Maps API script loaded');
                addTestResult('success', 'Script Loading', 'Google Maps API script loaded successfully');
            };
            
            document.head.appendChild(script);
            
            // Timeout check
            setTimeout(() => {
                if (!initAttempted) {
                    console.error('Google Maps API failed to initialize within 10 seconds');
                    addTestResult('error', 'Initialization Timeout', 'Google Maps API did not initialize within 10 seconds. Check for console errors.');
                }
            }, 10000);
        }
        
        // Additional diagnostic information
        setTimeout(() => {
            addTestResult('info', 'User Agent', navigator.userAgent);
            addTestResult('info', 'Screen Resolution', `${screen.width}x${screen.height}`);
            addTestResult('info', 'Viewport Size', `${window.innerWidth}x${window.innerHeight}`);
        }, 1000);
    </script>
</body>
</html>
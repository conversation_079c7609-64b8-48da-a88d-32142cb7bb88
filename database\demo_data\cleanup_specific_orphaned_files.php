<?php
/**
 * Cleanup Specific Orphaned Files
 * 
 * This script only deletes the specific orphaned files identified in the debug output.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Cleanup Specific Orphaned Files</h1>";
echo "<p>Deleting only the specific orphaned files (and their thumbnails) identified in the debug output...</p>";

// Exact list of orphaned filenames from your debug output
$orphanedFiles = [
    // Show files (43 files)
    'show_6_flyer1.jpg',
    'show_6_flyer2.jpg',
    'show_7_flyer1.jpg',
    'show_7_flyer2.jpg',
    'show_8_flyer1.jpg',
    'show_8_flyer2.jpg',
    'show_flyer1.jpg',
    'show_11_flyer1.jpg',
    'show_11_flyer2.jpg',
    'show_12_flyer1.jpg',
    'show_12_flyer2.jpg',
    'show_13_flyer1.jpg',
    'show_13_flyer2.jpg',
    'show_75_686ecd795c78a.jpg',
    'show_76_686ecd79ea32b.jpg',
    'show_77_686ecd7a8005a.jpg',
    'show_78_686ecd7b1a27b.jpg',
    'show_79_686ecd7ba52b9.jpg',
    'show_80_686ecd7c3ff52.jpg',
    'show_81_686ecd7ccc3bf.jpg',
    'show_82_686ecd7d64a82.jpg',
    'show_83_686ecd7df38c7.jpg',
    'show_84_686ecd7ea8df2.jpg',
    'show_85_686ed587e8490.jpg',
    'show_86_686ed588db315.jpg',
    'show_87_686ed58970df6.jpg',
    'show_88_686ed58a0c986.jpg',
    'show_89_686ed58a9ac81.jpg',
    'show_90_686ed58b30de5.jpg',
    'show_91_686ed58bb81e5.jpg',
    'show_92_686ed58c5304a.jpg',
    'show_93_686ed58cdda31.jpg',
    'show_94_686ed58d91de4.jpg',
    'show_95_686ed8a02f5d3.jpg',
    'show_96_686ed8a0c19e5.jpg',
    'show_97_686ed8a15d62c.jpg',
    'show_98_686ed8a1e9ba0.jpg',
    'show_99_686ed8a2845c7.jpg',
    'show_100_686ed8a31e55b.jpg',
    'show_101_686ed8a3aa3cc.jpg',
    'show_102_686ed8a442311.jpg',
    'show_103_686ed8a4d82ef.jpg',
    'show_104_686ed8a59e508.jpg',
    
    // Vehicle files (72 files)
    'vehicle_3_6839cc5613a6f.png',
    'vehicle_4_1.jpg',
    'vehicle_4_2.jpg',
    'vehicle_6_2.jpg',
    'vehicle_6_3.jpg',
    'vehicle_7_1.jpg',
    'vehicle_8_1.jpg',
    'vehicle_9_1.jpg',
    'vehicle_10_1.jpg',
    'vehicle_11_1.jpg',
    'vehicle_12_1.jpg',
    'vehicle_13_1.jpg',
    'vehicle_145_686ecd6ddd233.jpg',
    'vehicle_146_686ecd6e72ee5.jpg',
    'vehicle_147_686ecd6f08cb2.jpg',
    'vehicle_148_686ecd6f9310c.jpg',
    'vehicle_149_686ecd70296b8.jpg',
    'vehicle_150_686ecd70c4aaf.jpg',
    'vehicle_151_686ecd715b31d.jpg',
    'vehicle_152_686ecd71e4c7b.jpg',
    'vehicle_153_686ecd72780b5.jpg',
    'vehicle_154_686ecd730d50c.jpg',
    'vehicle_155_686ecd739730b.jpg',
    'vehicle_156_686ecd742df88.jpg',
    'vehicle_157_686ecd74b8358.jpg',
    'vehicle_158_686ecd75488e8.jpg',
    'vehicle_159_686ecd75cef10.jpg',
    'vehicle_160_686ecd76909a5.jpg',
    'vehicle_161_686ecd771fdbd.jpg',
    'vehicle_162_686ecd77abeb4.jpg',
    'vehicle_163_686ecd783f18e.jpg',
    'vehicle_164_686ecd78c7097.jpg',
    'vehicle_165_686ed57c3a7bf.jpg',
    'vehicle_166_686ed57cc4604.jpg',
    'vehicle_167_686ed57d52940.jpg',
    'vehicle_168_686ed57ddc16d.jpg',
    'vehicle_169_686ed57e7aa23.jpg',
    'vehicle_170_686ed57f204ba.jpg',
    'vehicle_171_686ed57fa9d19.jpg',
    'vehicle_172_686ed5803d832.jpg',
    'vehicle_173_686ed580c7254.jpg',
    'vehicle_174_686ed5815d89b.jpg',
    'vehicle_175_686ed581e9463.jpg',
    'vehicle_176_686ed58282b86.jpg',
    'vehicle_177_686ed58318d24.jpg',
    'vehicle_178_686ed583a8a30.jpg',
    'vehicle_179_686ed58445a9a.jpg',
    'vehicle_180_686ed584f04a3.jpg',
    'vehicle_181_686ed58587f8e.jpg',
    'vehicle_182_686ed58634e2e.jpg',
    'vehicle_183_686ed586c2a93.jpg',
    'vehicle_184_686ed58757ef6.jpg',
    'vehicle_185_686ed8943c4c3.jpg',
    'vehicle_186_686ed894c90b0.jpg',
    'vehicle_187_686ed8955ebec.jpg',
    'vehicle_188_686ed895e7c86.jpg',
    'vehicle_189_686ed8968f97f.jpg',
    'vehicle_190_686ed89747087.jpg',
    'vehicle_191_686ed897d2e7e.jpg',
    'vehicle_192_686ed89869750.jpg',
    'vehicle_193_686ed89900266.jpg',
    'vehicle_194_686ed89984cdf.jpg',
    'vehicle_195_686ed89a1a287.jpg',
    'vehicle_196_686ed89aa4050.jpg',
    'vehicle_197_686ed89b3b684.jpg',
    'vehicle_198_686ed89bc423f.jpg',
    'vehicle_199_686ed89c54409.jpg',
    'vehicle_200_686ed89d5294d.jpg',
    'vehicle_201_686ed89ddbc32.jpg',
    'vehicle_202_686ed89e7035f.jpg',
    'vehicle_203_686ed89f0399a.jpg',
    'vehicle_204_686ed89f8773c.jpg'
];

// Directories to search for these files (including thumbnails)
$searchDirs = [
    '../../uploads/vehicles/',
    '../../uploads/vehicles/thumbnails/',
    '../../uploads/shows/',
    '../../uploads/shows/thumbnails/',
    '../../uploads/',
    '../../uploads/thumbnails/',
    '../../public/uploads/vehicles/',
    '../../public/uploads/vehicles/thumbnails/',
    '../../public/uploads/shows/',
    '../../public/uploads/shows/thumbnails/',
    '../../public/uploads/',
    '../../public/uploads/thumbnails/'
];

echo "<p><strong>Orphaned files to find and delete:</strong> " . count($orphanedFiles) . "</p>";

$foundFiles = [];
$notFoundFiles = [];

echo "<h2>Searching for Orphaned Files</h2>";

foreach ($orphanedFiles as $filename) {
    $found = false;
    
    foreach ($searchDirs as $dir) {
        if (!is_dir($dir)) continue;
        
        $fullPath = $dir . $filename;
        if (file_exists($fullPath)) {
            $foundFiles[] = $fullPath;
            echo "<div style='color: red;'>❌ FOUND: {$fullPath}</div>";
            $found = true;
            break; // Stop searching once found
        }
    }
    
    if (!$found) {
        $notFoundFiles[] = $filename;
        echo "<div style='color: gray;'>⚪ NOT FOUND: {$filename}</div>";
    }
}

echo "<h2>Summary</h2>";
echo "<p><strong>Files found on disk:</strong> " . count($foundFiles) . "</p>";
echo "<p><strong>Files not found:</strong> " . count($notFoundFiles) . "</p>";

if (!empty($foundFiles)) {
    echo "<h2>Delete Orphaned Files</h2>";
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>⚠️ WARNING:</strong> This will permanently delete " . count($foundFiles) . " orphaned files. Make sure you have backups!";
    echo "</div>";
    
    if (isset($_POST['action']) && $_POST['action'] == 'delete_confirmed') {
        echo "<h3>Deleting Files...</h3>";
        
        $deletedCount = 0;
        $failedCount = 0;
        
        foreach ($foundFiles as $file) {
            if (file_exists($file) && is_file($file)) {
                if (unlink($file)) {
                    echo "<div style='color: green;'>✅ DELETED: " . htmlspecialchars($file) . "</div>";
                    $deletedCount++;
                } else {
                    echo "<div style='color: red;'>❌ FAILED: " . htmlspecialchars($file) . "</div>";
                    $failedCount++;
                }
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>Deletion Complete!</strong><br>";
        echo "✅ Deleted: {$deletedCount} files<br>";
        if ($failedCount > 0) {
            echo "❌ Failed: {$failedCount} files<br>";
        }
        echo "</div>";
        
    } else {
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='delete_confirmed'>";
        echo "<button type='submit' style='background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;' onclick='return confirm(\"Are you absolutely sure you want to delete " . count($foundFiles) . " orphaned files? This cannot be undone!\")'>DELETE " . count($foundFiles) . " ORPHANED FILES</button>";
        echo "</form>";
        
        echo "<h3>Files that will be deleted:</h3>";
        echo "<div style='max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;'>";
        foreach ($foundFiles as $file) {
            echo "<div>" . htmlspecialchars($file) . "</div>";
        }
        echo "</div>";
    }
} else {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>✅ No orphaned files found on disk!</strong> All the orphaned files may have already been cleaned up.";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
</style>

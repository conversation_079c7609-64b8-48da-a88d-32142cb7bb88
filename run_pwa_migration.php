<?php
/**
 * PWA Migration Runner
 * Ensures PWA database tables exist for push notifications
 */

require_once 'config/config.php';
require_once 'core/Database.php';

try {
    $db = new Database();
    
    echo "Starting PWA migration...\n";
    
    // Read the migration file
    $migrationFile = __DIR__ . '/database/migrations/pwa_minimal_migration.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception('Migration file not found: ' . $migrationFile);
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $db->query($statement);
            $result = $db->execute();
            
            if ($result) {
                $successCount++;
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
                }
            }
        } catch (Exception $e) {
            $errorCount++;
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "  Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\nPWA Migration Results:\n";
    echo "- Successful statements: $successCount\n";
    echo "- Failed statements: $errorCount\n";
    
    // Verify critical tables exist
    $criticalTables = ['push_subscriptions', 'notification_preferences'];
    
    echo "\nVerifying critical tables:\n";
    foreach ($criticalTables as $table) {
        $db->query("SHOW TABLES LIKE :table");
        $db->bind(':table', $table);
        $result = $db->single();
        
        if ($result) {
            echo "✓ Table '$table' exists\n";
        } else {
            echo "✗ Table '$table' missing\n";
        }
    }
    
    echo "\nPWA migration completed!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
}
?>
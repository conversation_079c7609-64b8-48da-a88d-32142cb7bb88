<?php
/**
 * Test User Data Generator - ORIGINAL VERSION WITH BUGS
 * 
 * This script generates a large number of test users to test the performance
 * of the optimized admin users page with pagination and search functionality.
 * 
 * Usage: Run this script from the command line or browser to generate test data.
 * WARNING: This will add many users to your database. Use only for testing!
 * 
 * BUGS FIXED:
 * 1. Invalid parameter number error - mixed Database class and PDO methods
 * 2. Division by zero error when no users generated
 * 3. Invalid role 'staff' not in ENUM
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class TestUserGenerator {
    private $db;
    private $batchSize = 100; // Insert users in batches for better performance
    
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Generate test users
     * 
     * @param int $count Number of users to generate
     * @param bool $verbose Whether to show progress output
     */
    public function generateUsers($count = 1000, $verbose = true) {
        if ($verbose) {
            echo "Generating {$count} test users...\n";
        }
        
        $startTime = microtime(true);
        $roles = ['user', 'coordinator', 'judge', 'staff', 'admin']; // BUG: 'staff' not in ENUM
        $statuses = ['active', 'inactive', 'pending'];
        $domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        
        // First names and last names for realistic data
        $firstNames = [
            'John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 
            'James', 'Jessica', 'William', 'Ashley', 'Richard', 'Amanda', 'Joseph', 
            'Stephanie', 'Thomas', 'Melissa', 'Christopher', 'Nicole', 'Daniel', 
            'Elizabeth', 'Matthew', 'Helen', 'Anthony', 'Deborah', 'Mark', 'Rachel',
            'Donald', 'Carolyn', 'Steven', 'Janet', 'Paul', 'Catherine', 'Andrew',
            'Maria', 'Joshua', 'Heather', 'Kenneth', 'Diane', 'Kevin', 'Ruth',
            'Brian', 'Julie', 'George', 'Joyce', 'Timothy', 'Virginia', 'Ronald',
            'Victoria', 'Jason', 'Kelly', 'Edward', 'Christina', 'Jeffrey', 'Joan'
        ];
        
        $lastNames = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
            'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
            'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
            'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark',
            'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King',
            'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green',
            'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
            'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz'
        ];
        
        $cities = [
            'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
            'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
            'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis',
            'Seattle', 'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville',
            'Detroit', 'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville',
            'Baltimore', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento'
        ];
        
        $states = [
            'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID',
            'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS',
            'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK',
            'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
        ];
        
        $generated = 0;
        $batches = ceil($count / $this->batchSize);
        
        for ($batch = 0; $batch < $batches; $batch++) {
            $batchStart = microtime(true);
            $usersInBatch = min($this->batchSize, $count - $generated);
            
            // Prepare batch insert
            $sql = "INSERT INTO users (name, email, password, role, status, created_at, last_login, phone, address, city, state, zip) VALUES ";
            $values = [];
            $params = [];
            
            for ($i = 0; $i < $usersInBatch; $i++) {
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $name = $firstName . ' ' . $lastName;
                
                // Create unique email
                $emailBase = strtolower($firstName . '.' . $lastName . ($generated + $i + 1));
                $domain = $domains[array_rand($domains)];
                $email = $emailBase . '@' . $domain;
                
                $role = $roles[array_rand($roles)];
                $status = $statuses[array_rand($statuses)];
                
                // Random dates for created_at and last_login
                $createdDays = rand(1, 365);
                $createdAt = date('Y-m-d H:i:s', strtotime("-{$createdDays} days"));
                
                $lastLoginDays = rand(0, $createdDays);
                $lastLogin = $lastLoginDays > 0 ? date('Y-m-d H:i:s', strtotime("-{$lastLoginDays} days")) : null;
                
                // Random address data
                $phone = sprintf('(%03d) %03d-%04d', rand(200, 999), rand(200, 999), rand(1000, 9999));
                $address = rand(100, 9999) . ' ' . $lastNames[array_rand($lastNames)] . ' St';
                $city = $cities[array_rand($cities)];
                $state = $states[array_rand($states)];
                $zip = sprintf('%05d', rand(10000, 99999));
                
                $values[] = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $params = array_merge($params, [
                    $name, $email, password_hash('password123', PASSWORD_DEFAULT),
                    $role, $status, $createdAt, $lastLogin, $phone, $address, $city, $state, $zip
                ]);
            }
            
            $sql .= implode(', ', $values);
            
            try {
                // BUG: Mixed Database class and PDO methods causing parameter conflicts
                $this->db->query($sql);
                for ($p = 0; $p < count($params); $p++) {
                    $this->db->bind(':param' . ($p + 1), $params[$p]);
                }
                
                // Use manual parameter binding for batch insert
                $stmt = $this->db->dbh->prepare($sql); // BUG: dbh is private
                $stmt->execute($params);
                
                $generated += $usersInBatch;
                $batchTime = round((microtime(true) - $batchStart) * 1000, 2);
                
                if ($verbose) {
                    echo "Batch " . ($batch + 1) . "/{$batches}: Generated {$usersInBatch} users in {$batchTime}ms (Total: {$generated}/{$count})\n";
                }
                
            } catch (Exception $e) {
                echo "Error in batch " . ($batch + 1) . ": " . $e->getMessage() . "\n";
                break;
            }
        }
        
        $totalTime = round((microtime(true) - $startTime) * 1000, 2);
        
        if ($verbose) {
            echo "\nTest data generation completed!\n";
            echo "Generated: {$generated} users\n";
            echo "Total time: {$totalTime}ms\n";
            echo "Average per user: " . round($totalTime / $generated, 2) . "ms\n"; // BUG: Division by zero if $generated = 0
        }
        
        return $generated;
    }
    
    /**
     * Clean up test users (removes users with test email domains)
     */
    public function cleanupTestUsers($verbose = true) {
        if ($verbose) {
            echo "Cleaning up test users...\n";
        }
        
        $testDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        $domainConditions = array_map(function($domain) {
            return "email LIKE '%@{$domain}'";
        }, $testDomains);
        
        $sql = "DELETE FROM users WHERE " . implode(' OR ', $domainConditions) . " AND name REGEXP '^[A-Za-z]+ [A-Za-z]+[0-9]*$'";
        
        $this->db->query($sql);
        $this->db->execute();
        
        if ($verbose) {
            echo "Test users cleaned up.\n";
        }
    }
}

// Command line usage
if (php_sapi_name() === 'cli') {
    $generator = new TestUserGenerator();
    
    $count = isset($argv[1]) ? (int)$argv[1] : 1000;
    $action = isset($argv[2]) ? $argv[2] : 'generate';
    
    if ($action === 'cleanup') {
        $generator->cleanupTestUsers();
    } else {
        echo "Generating {$count} test users for performance testing...\n";
        echo "WARNING: This will add test data to your database!\n";
        echo "Press Enter to continue or Ctrl+C to cancel...\n";
        fgets(STDIN);
        
        $generator->generateUsers($count);
    }
} else {
    // Web usage
    echo "<h1>Test User Generator</h1>";
    echo "<p><strong>WARNING:</strong> This will add test data to your database!</p>";
    
    if (isset($_GET['action'])) {
        $generator = new TestUserGenerator();
        
        if ($_GET['action'] === 'generate') {
            $count = isset($_GET['count']) ? (int)$_GET['count'] : 1000;
            echo "<pre>";
            $generator->generateUsers($count);
            echo "</pre>";
        } elseif ($_GET['action'] === 'cleanup') {
            echo "<pre>";
            $generator->cleanupTestUsers();
            echo "</pre>";
        }
    } else {
        echo '<p><a href="?action=generate&count=1000">Generate 1000 Test Users</a></p>';
        echo '<p><a href="?action=generate&count=5000">Generate 5000 Test Users</a></p>';
        echo '<p><a href="?action=cleanup">Cleanup Test Users</a></p>';
    }
}
?>
<?php
/**
 * BACKUP: PaymentModel.php - Admin Payment Dashboard Enhancement
 * Date: <?php echo date('Y-m-d H:i:s'); ?>
 * 
 * Added methods to support admin payment dashboard:
 * - getAllPaymentsWithUserInfo() - Enhanced payment data with user details
 * - getPaymentStatistics() - Payment statistics for dashboard
 * - getRecentPayments() - Recent payment activity
 * - getPendingPaymentsCount() - Count of pending payments
 * - getPaymentDetailsById() - Detailed payment information
 */

// This is a backup file created during the admin payment dashboard enhancement
// The actual implementation is in models/PaymentModel.php

/**
 * Key additions made:
 * 
 * 1. getAllPaymentsWithUserInfo() - Enhanced payment listing
 *    - Joins with users table for user information
 *    - Joins with payment_methods for method names
 *    - Includes related registration/show information
 *    - Provides description field for easy identification
 * 
 * 2. getPaymentStatistics() - Dashboard statistics
 *    - Total payments and amounts
 *    - Completed payments and amounts
 *    - Pending payments and amounts
 *    - Rejected payment counts
 *    - Payment type breakdown (registration vs listing)
 * 
 * 3. getRecentPayments() - Recent activity tracking
 *    - Configurable time period (default 30 days)
 *    - Limited to 50 most recent payments
 *    - Includes user and payment method information
 * 
 * 4. getPendingPaymentsCount() - Quick pending count
 *    - Used for dashboard badges and notifications
 * 
 * 5. getPaymentDetailsById() - Comprehensive payment details
 *    - Complete payment information
 *    - User details including contact information
 *    - Payment method information
 *    - Related registration/show details
 *    - Processing history (who processed the payment)
 */
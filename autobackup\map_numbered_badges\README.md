# Map View Numbered Badges Implementation

## Summary
Added numbered badges to both the event list and corresponding map markers in the map view. Events are sorted by start date (soonest first) and numbered sequentially starting from 1.

## Features Added
- **Event Sorting**: Events are now sorted by start date with the soonest events first
- **Numbered Badges**: Each event in the list displays a numbered badge (1, 2, 3, etc.)
- **Numbered Map Markers**: Corresponding map markers display the same numbers
- **Cross-Reference**: Users can easily match events in the list with markers on the map

## Technical Implementation
- Events are sorted using JavaScript's sort() method on the start date
- Only events with valid location data (lat/lng) are numbered and displayed
- Event numbering starts at 1 and increments for each event with location data
- All map providers (Google Maps, OpenStreetMap/Leaflet, Mapbox, HERE Maps) support numbered markers

## Map Provider Support
- **Google Maps**: Uses label property with custom circle markers
- **OpenStreetMap/Leaflet**: Uses custom HTML div icons with numbers
- **Mapbox**: Uses custom HTML elements with flexbox centering
- **HERE Maps**: Uses custom SVG icons with embedded text

## CSS Enhancements
- Added `.event-number-badge` class for consistent badge styling
- Updated `.event-item` to use flexbox layout
- Added `.event-item-content` for proper content alignment

## Files Modified
- `views/calendar/map.php` - Main map view with numbered badges implementation

## Version
- Updated: 2024-12-20
- Change: Added numbered badges to map view events and markers
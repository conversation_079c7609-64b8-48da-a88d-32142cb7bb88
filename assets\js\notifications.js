/**
 * DEPRECATED FILE - DO NOT USE
 * 
 * This file has been moved to the correct location: /public/js/notifications.js
 * 
 * JavaScript and CSS files MUST be placed in /public/ directory:
 * - JavaScript: /public/js/
 * - CSS: /public/css/
 * - Images: /public/images/
 * 
 * This file should be deleted.
 */

console.error('DEPRECATED: This JavaScript file has been moved to /public/js/notifications.js');
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
        this.init();
    }
    
    /**
     * Initialize the notification manager
     */
    init() {
        this.setupEventListeners();
        this.loadUnreadNotifications();
        this.requestPushPermission();
        
        // Check for unread notifications every 30 seconds
        setInterval(() => {
            this.loadUnreadNotifications();
        }, 30000);
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Notification subscription buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-notification-btn]') || e.target.closest('[data-notification-btn]')) {
                e.preventDefault();
                const btn = e.target.matches('[data-notification-btn]') ? e.target : e.target.closest('[data-notification-btn]');
                this.openSubscriptionModal(btn);
            }
        });
        
        // Toast notification close buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.toast-close') || e.target.closest('.toast-close')) {
                e.preventDefault();
                const toast = e.target.closest('.toast-notification');
                if (toast) {
                    this.dismissToast(toast);
                }
            }
        });
    }
    
    /**
     * Open notification subscription modal
     */
    async openSubscriptionModal(button) {
        const eventId = button.dataset.eventId;
        const eventType = button.dataset.eventType;
        
        if (!eventId || !eventType) {
            this.showAlert('error', 'Invalid event data');
            return;
        }
        
        try {
            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
            button.disabled = true;
            
            const response = await fetch(`${this.baseUrl}/notification/subscriptionModal?event_id=${eventId}&event_type=${eventType}`);
            const data = await response.json();
            
            if (data.success) {
                // Create or update modal
                let modal = document.getElementById('notificationModal');
                if (!modal) {
                    modal = document.createElement('div');
                    modal.className = 'modal fade';
                    modal.id = 'notificationModal';
                    modal.setAttribute('tabindex', '-1');
                    modal.setAttribute('aria-labelledby', 'notificationModalLabel');
                    modal.setAttribute('aria-hidden', 'true');
                    document.body.appendChild(modal);
                }
                
                modal.innerHTML = `
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            ${data.html}
                        </div>
                    </div>
                `;
                
                // Show modal
                const bsModal = new bootstrap.Modal(modal);
                bsModal.show();
                
            } else {
                this.showAlert('error', data.message || 'Failed to load subscription modal');
            }
        } catch (error) {
            console.error('Error loading subscription modal:', error);
            this.showAlert('error', 'An error occurred while loading the subscription modal');
        } finally {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }
    
    /**
     * Load unread notifications
     */
    async loadUnreadNotifications() {
        try {
            const response = await fetch(`${this.baseUrl}/notification/getUnread`);
            const data = await response.json();
            
            // Update notification badge
            this.updateNotificationBadge(data.push.length + data.toast.length);
            
            // Show toast notifications
            this.showToastNotifications(data.toast);
            
        } catch (error) {
            console.error('Error loading unread notifications:', error);
        }
    }
    
    /**
     * Update notification badge in navigation
     */
    updateNotificationBadge(count) {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (count > 0) {
                badge.textContent = count > 99 ? '99+' : count;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    }
    
    /**
     * Show toast notifications
     */
    showToastNotifications(notifications) {
        const container = this.getToastContainer();
        
        notifications.forEach(notification => {
            // Check if toast already exists
            if (document.querySelector(`[data-notification-id="${notification.id}"]`)) {
                return;
            }
            
            const toast = this.createToastElement(notification);
            container.appendChild(toast);
            
            // Auto-dismiss after 10 seconds
            setTimeout(() => {
                this.dismissToast(toast);
            }, 10000);
        });
    }
    
    /**
     * Get or create toast container
     */
    getToastContainer() {
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        return container;
    }
    
    /**
     * Create toast element
     */
    createToastElement(notification) {
        const toast = document.createElement('div');
        toast.className = 'toast-notification alert alert-info alert-dismissible fade show';
        toast.setAttribute('data-notification-id', notification.id);
        toast.style.minWidth = '300px';
        toast.style.marginBottom = '10px';
        
        const eventIcon = notification.event_type === 'car_show' ? 'fa-car' : 'fa-calendar';
        
        toast.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="fas ${eventIcon} me-3 mt-1 text-primary"></i>
                <div class="flex-grow-1">
                    <h6 class="alert-heading mb-1">${this.escapeHtml(notification.title)}</h6>
                    <p class="mb-0 small">${this.escapeHtml(notification.message).substring(0, 100)}${notification.message.length > 100 ? '...' : ''}</p>
                    <small class="text-muted">${this.formatTimeAgo(notification.created_at)}</small>
                </div>
                <button type="button" class="btn-close toast-close" aria-label="Close"></button>
            </div>
        `;
        
        return toast;
    }
    
    /**
     * Dismiss toast notification
     */
    async dismissToast(toast) {
        const notificationId = toast.getAttribute('data-notification-id');
        
        // Animate out
        toast.classList.remove('show');
        
        setTimeout(() => {
            toast.remove();
        }, 150);
        
        // Mark as read on server
        if (notificationId) {
            try {
                await this.markNotificationsRead('toast', [notificationId]);
            } catch (error) {
                console.error('Error marking toast as read:', error);
            }
        }
    }
    
    /**
     * Mark notifications as read
     */
    async markNotificationsRead(type, notificationIds) {
        const formData = new FormData();
        formData.append('type', type);
        formData.append('notification_ids', JSON.stringify(notificationIds));
        formData.append(this.getCsrfTokenName(), this.csrfToken);
        
        const response = await fetch(`${this.baseUrl}/notification/markRead`, {
            method: 'POST',
            body: formData
        });
        
        return response.json();
    }
    
    /**
     * Request push notification permission
     */
    async requestPushPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            try {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    console.log('Push notifications enabled');
                }
            } catch (error) {
                console.error('Error requesting push permission:', error);
            }
        }
    }
    
    /**
     * Show browser push notification
     */
    showPushNotification(title, message, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                body: message,
                icon: `${this.baseUrl}/assets/images/notification-icon.png`,
                badge: `${this.baseUrl}/assets/images/notification-badge.png`,
                ...options
            });
            
            // Auto-close after 10 seconds
            setTimeout(() => {
                notification.close();
            }, 10000);
            
            return notification;
        }
    }
    
    /**
     * Show alert message
     */
    showAlert(type, message) {
        // Try to use existing toast system if available
        if (typeof showToast === 'function') {
            showToast(type, message);
            return;
        }
        
        // Fallback to browser alert
        alert(message);
    }
    
    /**
     * Get CSRF token name
     */
    getCsrfTokenName() {
        return document.querySelector('meta[name="csrf-token-name"]')?.getAttribute('content') || 'csrf_token';
    }
    
    /**
     * Escape HTML
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    /**
     * Format time ago
     */
    formatTimeAgo(dateString) {
        // Convert UTC database timestamp to local timezone
        let date;
        if (window.TimezoneHelper && window.TimezoneHelper.parseMySQLDateTime) {
            date = window.TimezoneHelper.parseMySQLDateTime(dateString);
        } else {
            // Fallback: assume UTC and convert to local
            date = new Date(dateString + (dateString.includes('T') ? '' : 'T00:00:00Z'));
        }
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days !== 1 ? 's' : ''} ago`;
        }
    }
}

// Initialize notification manager when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.notificationManager = new NotificationManager();
});

// Global functions for backward compatibility
function openNotificationModal(eventId, eventType) {
    if (window.notificationManager) {
        const button = document.createElement('button');
        button.dataset.eventId = eventId;
        button.dataset.eventType = eventType;
        window.notificationManager.openSubscriptionModal(button);
    }
}

function showToast(type, message) {
    if (window.notificationManager) {
        const notification = {
            id: Date.now(),
            title: type.charAt(0).toUpperCase() + type.slice(1),
            message: message,
            created_at: new Date().toISOString(),
            event_type: 'system'
        };
        window.notificationManager.showToastNotifications([notification]);
    }
}
# Calendar Events Geocoding Synchronization Fix

This fix addresses an issue where the lat and lng coordinates from shows were not being properly synchronized with their corresponding calendar events. When a show was geocoded, the coordinates were saved to the shows table but not propagated to the calendar_events table.

## Issue Details

1. The shows table correctly stores lat and lng coordinates after geocoding
2. The calendar_events table already has lat and lng columns
3. When updating a show, the system creates or updates a corresponding calendar event
4. However, the lat and lng values from the show were not being properly copied to the calendar event

## Solution

The solution modifies the ShowModel's updateShow method to ensure that when a show is geocoded, the coordinates are also updated in any associated calendar events.

## Files Modified

- `models/ShowModel.php` - Updated to propagate geocoded coordinates to calendar events

## Implementation Details

1. After geocoding a show and updating its lat/lng values in the database
2. The code now explicitly updates any associated calendar events with these coordinates
3. This ensures that both the shows table and calendar_events table have consistent geocoding data

## Testing

To verify the fix:
1. Edit a show with a complete address
2. Check that the show is geocoded (lat/lng values are populated in the shows table)
3. Check that the corresponding calendar event also has the same lat/lng values
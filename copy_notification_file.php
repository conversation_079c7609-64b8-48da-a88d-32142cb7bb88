<?php
// Simple script to copy the new notification preferences file
$source = 'd:/Downloads/events and shows/notification_preferences_temp.php';
$destination = 'd:/Downloads/events and shows/views/user/notification_preferences.php';

if (file_exists($source)) {
    $content = file_get_contents($source);
    file_put_contents($destination, $content);
    echo "✅ File copied successfully!\n";
    
    // Clean up temp file
    unlink($source);
    echo "✅ Temp file cleaned up\n";
} else {
    echo "❌ Source file not found\n";
}
?>
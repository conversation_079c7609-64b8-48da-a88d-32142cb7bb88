# Facebook Profile Image Storage Fix - August 19, 2024

## Issue Description
The Facebook profile image sync feature was not working correctly due to inconsistent storage approaches. The system was using two different storage methods for profile images:

1. Some profile images were stored as URLs in the `profile_image` column of the `users` table, particularly Facebook profile images.
2. Other profile images were stored as files in the `/uploads/users/` directory with references in the `images` table.

This inconsistency caused problems with image display and management, as the system had to check multiple locations to find a user's profile image.

## Storage Solution
The solution standardizes the storage approach for all profile images:

1. **All profile images will be stored as files in the `/uploads/users/` directory.**
2. **All profile image references will be stored in the `images` table with entity_type='user'.**
3. **The `profile_image` column in the `users` table will be cleared (set to NULL) after migrating images.**

This approach ensures:
- Consistent image handling across the application
- Better performance by avoiding external URL dependencies
- Improved security by hosting all images locally
- Simplified image management with a single source of truth

## Implementation Details

### 1. Modified Auth.php
- Updated `downloadFacebookProfileImage` method to clear the `profile_image` field in the `users` table after successfully downloading and storing the image in the `images` table.
- Ensured all Facebook profile images are stored locally in the `/uploads/users/` directory.

### 2. Modified UserController.php
- Updated `syncFacebookImage` method to clear the `profile_image` field in the `users` table before attempting to download and store the Facebook profile image.
- Ensured consistent storage approach when users manually sync their Facebook profile image.

### 3. Created Migration Script
- Created `test/migrate_facebook_profile_images.php` to migrate existing Facebook profile images from the `users` table to the `images` table.
- The script downloads images from Facebook URLs, stores them locally, and updates database references.

## Files Modified
- `core/Auth.php`
- `controllers/UserController.php`

## Files Created
- `test/migrate_facebook_profile_images.php`

## How to Test
1. Log in with a user account that has a Facebook connection.
2. Go to the user profile page.
3. Click the "Sync with Facebook" button.
4. Verify that the Facebook profile image is displayed correctly.
5. Check the database to ensure that:
   - The image file exists in the `/uploads/users/` directory
   - The image reference exists in the `images` table with entity_type='user'
   - The `profile_image` field in the `users` table is NULL

## Migration Script Usage
A migration script has been created to migrate existing Facebook profile images from the `users` table to the `images` table. This script should be run once to ensure all profile images are stored consistently.

To run the migration script:
1. From the command line: `php test/migrate_facebook_profile_images.php`
2. Or, as an administrator, navigate to `/test/migrate_facebook_profile_images.php` in your browser.

## Storage Benefits
- **Portability**: All images are stored locally, making the application more portable
- **Performance**: No dependency on external URLs that might be slow or unavailable
- **Consistency**: Single approach to image storage simplifies code and reduces bugs
- **Security**: Better control over image content and reduced risk from external sources
- **Backup**: Local images can be included in regular backup procedures
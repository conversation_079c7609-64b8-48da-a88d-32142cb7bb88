<?php
/**
 * Geocoding Helper
 * 
 * This helper provides functions for geocoding addresses to get latitude and longitude coordinates.
 * It supports multiple geocoding providers and automatically selects the best available one.
 * 
 * Version 1.2 - Enhanced with better error handling, address simplification, and direct event geocoding
 */

/**
 * Geocode an address to get latitude and longitude coordinates
 * 
 * @param string|array $address Address to geocode (string or array of address components)
 * @param array $mapSettings Optional map provider settings
 * @return array|false Array with lat and lng keys or false if geocoding failed
 */
function geocodeAddress($address, $mapSettings = null) {
    // If address is an array, build address string
    if (is_array($address)) {
        $addressParts = [];
        if (!empty($address['address1'])) $addressParts[] = $address['address1'];
        if (!empty($address['address2'])) $addressParts[] = $address['address2'];
        if (!empty($address['city'])) $addressParts[] = $address['city'];
        if (!empty($address['state'])) $addressParts[] = $address['state'];
        if (!empty($address['zipcode'])) $addressParts[] = $address['zipcode'];
        
        // If we don't have enough address information, return false
        if (empty($addressParts)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Geocoding failed: Not enough address information provided");
            }
            return false;
        }
        
        $addressString = implode(', ', $addressParts);
    } else {
        $addressString = trim($address);
        
        // If address is empty, return false
        if (empty($addressString)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Geocoding failed: Empty address provided");
            }
            return false;
        }
    }
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Attempting to geocode address: {$addressString}");
    }
    
    // Get map provider settings if not provided
    if ($mapSettings === null) {
        $calendarModel = new CalendarModel();
        $mapSettings = $calendarModel->getMapProviderSettings();
    }
    
    // Try to geocode with the configured provider
    $provider = $mapSettings['provider'] ?? 'openstreetmap';
    $apiKey = $mapSettings['api_key'] ?? '';
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log("Using geocoding provider: {$provider}");
    }
    
    // Try the configured provider first
    $coordinates = false;
    
    switch ($provider) {
        case 'google':
            $coordinates = geocodeWithGoogle($addressString, $apiKey);
            break;
        case 'mapbox':
            $coordinates = geocodeWithMapbox($addressString, $apiKey);
            break;
        case 'here':
            $coordinates = geocodeWithHere($addressString, $apiKey);
            break;
        case 'openstreetmap':
        default:
            $coordinates = geocodeWithOpenStreetMap($addressString);
            break;
    }
    
    // If the primary provider failed, try OpenStreetMap as a fallback
    if (!$coordinates && $provider !== 'openstreetmap') {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Primary provider failed, trying OpenStreetMap as fallback");
        }
        $coordinates = geocodeWithOpenStreetMap($addressString);
    }
    
    // If still no coordinates, try with a simplified address
    if (!$coordinates) {
        $simplifiedAddress = simplifyAddress($addressString);
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("Trying with simplified address: {$simplifiedAddress}");
        }
        $coordinates = geocodeWithOpenStreetMap($simplifiedAddress);
    }
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        if ($coordinates) {
            error_log("Geocoding successful: " . json_encode($coordinates));
        } else {
            error_log("All geocoding attempts failed for address: {$addressString}");
        }
    }
    
    return $coordinates;
}

/**
 * Geocode an event directly
 * 
 * This function takes an event data array and attempts to geocode the address.
 * It updates the lat and lng fields in the data array if successful.
 * 
 * @param array $data Event data array
 * @param array|null $mapSettings Optional map provider settings
 * @param string $context Context for logging (e.g., 'createEvent', 'editEvent')
 * @param int|null $id Optional ID for logging
 * @return array Updated data array with geocoded coordinates
 */
function geocodeEvent($data, $mapSettings = null, $context = 'unknown', $id = null) {
    if ((!isset($data['lat']) || !$data['lat'] || !isset($data['lng']) || !$data['lng']) && 
        (isset($data['address1']) && $data['address1'] || 
         isset($data['city']) && $data['city'] || 
         isset($data['state']) && $data['state'] || 
         isset($data['zipcode']) && $data['zipcode'])) {
        
        $idInfo = $id ? " ID: {$id}" : "";
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("{$context} - Attempting to geocode address for event{$idInfo}");
        }
        
        // Create address array for geocoding
        $addressData = [
            'address1' => $data['address1'] ?? '',
            'address2' => $data['address2'] ?? '',
            'city' => $data['city'] ?? '',
            'state' => $data['state'] ?? '',
            'zipcode' => $data['zipcode'] ?? ''
        ];
        
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("{$context} - Address data for geocoding: " . json_encode($addressData));
        }
        
        // Get map provider settings if not provided
        if ($mapSettings === null) {
            if (class_exists('CalendarModel')) {
                $calendarModel = new CalendarModel();
                $mapSettings = $calendarModel->getMapProviderSettings();
                
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("{$context} - Map provider settings: " . json_encode($mapSettings));
                }
            } else {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("{$context} - CalendarModel class not found, using default settings");
                }
                $mapSettings = ['provider' => 'openstreetmap'];
            }
        }
        
        // Attempt to geocode the address
        $coordinates = geocodeAddress($addressData, $mapSettings);
        
        // If geocoding was successful, update coordinates
        if ($coordinates && isset($coordinates['lat']) && isset($coordinates['lng'])) {
            $data['lat'] = $coordinates['lat'];
            $data['lng'] = $coordinates['lng'];
            
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("{$context} - Geocoding successful: lat={$data['lat']}, lng={$data['lng']}");
            }
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("{$context} - Geocoding failed for event{$idInfo}");
            }
        }
    }
    
    return $data;
}
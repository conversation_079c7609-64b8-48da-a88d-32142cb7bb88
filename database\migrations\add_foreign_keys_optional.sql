-- Optional: Add foreign key constraints after tables are created
-- Only run this if you want explicit foreign key constraints
-- The PWA features will work without these constraints

-- Check if we can add foreign keys by verifying users table structure
SELECT 'Checking if foreign keys can be added...' as status;

-- Check users table
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_KEY, EXTRA
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME = 'id';

-- Check if push_subscriptions table exists and has the right structure
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'push_subscriptions' 
AND COLUMN_NAME = 'user_id';

-- Try to add foreign key for push_subscriptions (may fail if data types don't match)
-- Uncomment the following lines if you want to try adding foreign keys:

/*
ALTER TABLE push_subscriptions 
ADD CONSTRAINT fk_push_subscriptions_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
*/

/*
ALTER TABLE notification_preferences 
ADD CONSTRAINT fk_notification_preferences_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
*/

/*
ALTER TABLE offline_sync_queue 
ADD CONSTRAINT fk_offline_sync_queue_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
*/

/*
ALTER TABLE notification_log 
ADD CONSTRAINT fk_notification_log_user_id 
FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;
*/

SELECT 'Foreign key constraints are commented out.' as status;
SELECT 'Uncomment the ALTER TABLE statements above if you want to add them.' as instruction;
SELECT 'The PWA system will work correctly without explicit foreign key constraints.' as note;
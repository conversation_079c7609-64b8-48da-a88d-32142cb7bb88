<?php 
// Ensure APPROOT is defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(dirname(dirname(__FILE__))));
}

// Make APPROOT available globally
if (!isset($GLOBALS['APPROOT'])) {
    $GLOBALS['APPROOT'] = APPROOT;
}

require APPROOT . '/views/includes/header.php'; 
?>

<style>
    /* Styles for clickable cards with button exceptions */
    .stretched-link-wrapper {
        position: static;
    }
    .stretched-link-wrapper::after {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 0;
        content: "";
    }
    .z-index-1 {
        z-index: 1;
    }
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <?php if (!empty($data['show']->banner_image)): ?>
            <div class="card mb-4">
                <?php 
                // Check if thumbnail exists
                $imagePath = 'uploads/shows/' . $data['show']->banner_image;
                $thumbnailPath = 'uploads/shows/thumbnails/' . $data['show']->banner_image;
                $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                $useThumbnail = file_exists($fullThumbnailPath);
                ?>
                <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                     class="card-img-top" alt="<?php echo $data['show']->name; ?>">
            </div>
            <?php endif; ?>
            
            <div class="card mb-4">
                <div class="card-body">
                    <h5 class="card-title"><?php echo $data['show']->name; ?></h5>
                    <?php if ($data['show']->fan_voting_enabled): ?>
                        <div class="badge badge-success mb-3">Fan Favorite Voting Enabled</div>
                    <?php endif; ?>
                    <p class="card-text"><?php echo nl2br($data['show']->description); ?></p>
                    <!-- Key Show Information -->
                    <div class="card mb-3 border-0 bg-light">
                        <div class="card-body">
                            <div class="row">
                                <!-- Show Dates -->
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Show Dates & Times</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="text-primary me-3">
                                                    <i class="fas fa-calendar-day fa-2x"></i>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">Start Date & Time</div>
                                                    <div class="fw-bold">
                                                        <?php 
                                                        // Format date and time on separate lines
                                                        echo date('l, F j, Y', strtotime($data['show']->start_date));
                                                        ?>
                                                    </div>
                                                    <div class="text-muted small">
                                                        <i class="far fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($data['show']->start_date)); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <div class="text-primary me-3">
                                                    <i class="fas fa-calendar-check fa-2x"></i>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">End Date & Time</div>
                                                    <div class="fw-bold">
                                                        <?php 
                                                        // Format date and time on separate lines
                                                        echo date('l, F j, Y', strtotime($data['show']->end_date));
                                                        ?>
                                                    </div>
                                                    <div class="text-muted small">
                                                        <i class="far fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($data['show']->end_date)); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Registration Dates -->
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Registration Period</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex align-items-center mb-2">
                                                <div class="text-success me-3">
                                                    <i class="fas fa-hourglass-start fa-2x"></i>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">Registration Opens</div>
                                                    <div class="fw-bold">
                                                        <?php 
                                                        // Format date and time on separate lines
                                                        echo date('l, F j, Y', strtotime($data['show']->registration_start));
                                                        ?>
                                                    </div>
                                                    <div class="text-muted small">
                                                        <i class="far fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($data['show']->registration_start)); ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <div class="text-success me-3">
                                                    <i class="fas fa-hourglass-end fa-2x"></i>
                                                </div>
                                                <div>
                                                    <div class="small text-muted">Registration Closes</div>
                                                    <div class="fw-bold">
                                                        <?php 
                                                        // Format date and time on separate lines
                                                        echo date('l, F j, Y', strtotime($data['show']->registration_end));
                                                        ?>
                                                    </div>
                                                    <div class="text-muted small">
                                                        <i class="far fa-clock me-1"></i>
                                                        <?php echo date('g:i A', strtotime($data['show']->registration_end)); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Venue and Coordinator Information -->
                            <div class="row">
                                <!-- Venue Information -->
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 border-info">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Venue</h6>
                                        </div>
                                        <div class="card-body">
                                            <h6 class="card-title"><?php echo $data['show']->location; ?></h6>
                                            <?php if (isset($data['show']->address1) && !empty($data['show']->address1)): ?>
                                                <p class="card-text mb-1"><?php echo $data['show']->address1; ?></p>
                                            <?php endif; ?>
                                            <?php if (isset($data['show']->address2) && !empty($data['show']->address2)): ?>
                                                <p class="card-text mb-1"><?php echo $data['show']->address2; ?></p>
                                            <?php endif; ?>
                                            <?php 
                                            $locationParts = [];
                                            if (isset($data['show']->city) && !empty($data['show']->city)) $locationParts[] = $data['show']->city;
                                            if (isset($data['show']->state) && !empty($data['show']->state)) $locationParts[] = $data['show']->state;
                                            if (isset($data['show']->zipcode) && !empty($data['show']->zipcode)) $locationParts[] = $data['show']->zipcode;
                                            if (!empty($locationParts)): 
                                            ?>
                                                <p class="card-text"><?php echo implode(', ', $locationParts); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Coordinator Information -->
                                <div class="col-md-6 mb-3">
                                    <div class="card h-100 border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>Coordinator Contact</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php 
                                            // Get coordinator information from users table if coordinator_id is set
                                            $coordinatorInfo = null;
                                            if (isset($data['show']->coordinator_id) && !empty($data['show']->coordinator_id)) {
                                                require_once APPROOT . '/models/UserModel.php';
                                                $userModel = new UserModel();
                                                $coordinatorInfo = $userModel->getUserById($data['show']->coordinator_id);
                                            }
                                            
                                            if ($coordinatorInfo): 
                                            ?>
                                                <h6 class="card-title"><?php echo $coordinatorInfo->name; ?></h6>
                                                
                                                <?php if (!empty($coordinatorInfo->email)): ?>
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-envelope me-2 text-muted"></i>
                                                        <a href="mailto:<?php echo $coordinatorInfo->email; ?>"><?php echo $coordinatorInfo->email; ?></a>
                                                    </p>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($coordinatorInfo->phone)): ?>
                                                    <p class="card-text">
                                                        <i class="fas fa-phone me-2 text-muted"></i>
                                                        <a href="tel:<?php echo preg_replace('/[^0-9]/', '', $coordinatorInfo->phone); ?>"><?php echo $coordinatorInfo->phone; ?></a>
                                                    </p>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <p class="card-text text-muted">No coordinator assigned</p>
                                            <?php endif; ?>
                                            
                                            <?php if ($data['show']->status == 'completed'): ?>
                                                <div class="mt-2">
                                                    <a href="<?php echo BASE_URL; ?>/show/results/<?php echo $data['show']->id; ?>" class="btn btn-success">
                                                        <i class="fas fa-trophy me-2"></i>View Show Results
                                                    </a>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Social Media Sharing -->
                    <?php if (isset($data['social_settings']) && 
                             ((isset($data['social_settings']->enable_facebook_sharing) && $data['social_settings']->enable_facebook_sharing && 
                               isset($data['social_settings']->show_facebook_share_button) && $data['social_settings']->show_facebook_share_button) || 
                              (isset($data['social_settings']->enable_facebook_events) && $data['social_settings']->enable_facebook_events && 
                               isset($data['social_settings']->show_facebook_event_button) && $data['social_settings']->show_facebook_event_button))): ?>
                    <div class="row mb-3">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title"><i class="fas fa-share-alt me-2"></i>Share This Show</h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        <?php if (isset($data['social_settings']->enable_facebook_sharing) && $data['social_settings']->enable_facebook_sharing && 
                                               isset($data['social_settings']->show_facebook_share_button) && $data['social_settings']->show_facebook_share_button): ?>
                                            <a href="<?php echo BASE_URL; ?>/social/shareToFacebook/<?php echo $data['show']->id; ?>" class="btn btn-primary btn-sm">
                                                <i class="fab fa-facebook me-1"></i> Share on Facebook
                                            </a>
                                            <a href="<?php echo BASE_URL; ?>/social/shareToGroup/<?php echo $data['show']->id; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="fas fa-users me-1"></i> Share to Facebook Group
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if (isset($data['social_settings']->enable_facebook_events) && $data['social_settings']->enable_facebook_events && 
                                               isset($data['social_settings']->show_facebook_event_button) && $data['social_settings']->show_facebook_event_button && 
                                               isset($_SESSION['user_id']) && 
                                               (isset($data['is_admin']) && $data['is_admin'] || 
                                                (isset($data['is_coordinator']) && $data['is_coordinator'] && $data['show']->coordinator_id == $_SESSION['user_id']))): ?>
                                            <a href="<?php echo BASE_URL; ?>/social/createFacebookEvent/<?php echo $data['show']->id; ?>" class="btn btn-info btn-sm">
                                                <i class="far fa-calendar-alt me-1"></i> Create Facebook Event
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
<?php
/**
 * Check Camera Banners Database Status
 */

require_once 'config/config.php';
require_once 'config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "<h2>Camera Banners Database Status</h2>";
    
    // Check if table exists
    $table_check = "SHOW TABLES LIKE 'camera_banners'";
    $stmt = $db->prepare($table_check);
    $stmt->execute();
    $table_exists = $stmt->rowCount() > 0;
    
    if (!$table_exists) {
        echo "<p style='color: red;'>❌ camera_banners table does NOT exist!</p>";
        echo "<p>Run install_camera_banners.php to create it.</p>";
    } else {
        echo "<p style='color: green;'>✅ camera_banners table exists</p>";
        
        // Check banner count
        $count_query = "SELECT COUNT(*) as count FROM camera_banners";
        $count_stmt = $db->prepare($count_query);
        $count_stmt->execute();
        $count = $count_stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<p><strong>Total banners:</strong> $count</p>";
        
        if ($count > 0) {
            // Show all banners
            $banners_query = "SELECT * FROM camera_banners ORDER BY sort_order ASC";
            $banners_stmt = $db->prepare($banners_query);
            $banners_stmt->execute();
            $banners = $banners_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Current Banners:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Type</th><th>Text</th><th>Image Path</th><th>Active</th><th>Sort Order</th></tr>";
            
            foreach ($banners as $banner) {
                $active_status = $banner['active'] ? 'Yes' : 'No';
                echo "<tr>";
                echo "<td>{$banner['id']}</td>";
                echo "<td>{$banner['type']}</td>";
                echo "<td>" . htmlspecialchars($banner['text']) . "</td>";
                echo "<td>" . htmlspecialchars($banner['image_path']) . "</td>";
                echo "<td>$active_status</td>";
                echo "<td>{$banner['sort_order']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Check active banners
            $active_query = "SELECT COUNT(*) as count FROM camera_banners WHERE active = 1";
            $active_stmt = $db->prepare($active_query);
            $active_stmt->execute();
            $active_count = $active_stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            echo "<p><strong>Active banners:</strong> $active_count</p>";
            
            if ($active_count == 0) {
                echo "<p style='color: red;'>❌ No active banners! This is why rotation is not working.</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ No banners in database!</p>";
            echo "<p>Would you like to add default banners?</p>";
            echo "<form method='post'>";
            echo "<button type='submit' name='add_defaults'>Add Default Banners</button>";
            echo "</form>";
        }
    }
    
    // Check delay setting
    echo "<h3>Banner Delay Setting:</h3>";
    $delay_query = "SELECT setting_value FROM system_settings WHERE setting_key = 'camera_banner_delay'";
    $delay_stmt = $db->prepare($delay_query);
    $delay_stmt->execute();
    $delay_result = $delay_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($delay_result) {
        echo "<p><strong>Current delay:</strong> {$delay_result['setting_value']}ms</p>";
    } else {
        echo "<p style='color: red;'>❌ No delay setting found!</p>";
    }
    
    // Handle adding default banners
    if (isset($_POST['add_defaults'])) {
        if ($table_exists) {
            $insert_banners = "
            INSERT INTO camera_banners (type, text, active, sort_order) VALUES 
            ('text', 'Welcome to our Event Platform!', TRUE, 1),
            ('text', 'Check out our upcoming events!', TRUE, 2),
            ('text', 'Register your vehicle today!', TRUE, 3)";
            
            $db->exec($insert_banners);
            
            // Add delay setting
            $insert_delay = "
            INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, description) VALUES 
            ('camera_banner_delay', '5000', 'Banner rotation delay in milliseconds for camera modals', 'media', 'Camera banner delay')
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            
            $db->exec($insert_delay);
            
            echo "<p style='color: green;'>✅ Default banners and settings added! Refresh page to see them.</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>

<?php
// DEBUG FILE - REMOVE AFTER TESTING
// Simple debug page to test camera upload endpoint

session_start();
require_once 'config/config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Not logged in - redirecting to login";
    header('Location: /login');
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Session Data:</h2>";
    echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    
    echo "<h2>POST Data Received:</h2>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";
    
    echo "<h2>FILES Data Received:</h2>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";
    
    echo "<h2>Testing PWA Camera Upload Endpoint:</h2>";
    
    // Test the actual endpoint with cURL
    $ch = curl_init();
    $url = 'http://' . $_SERVER['HTTP_HOST'] . '/pwa/cameraUpload';
    echo "<p><strong>Testing URL:</strong> $url</p>";
    echo "<p><strong>Note:</strong> Testing PWA camera upload endpoint</p>";
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    
    // Prepare POST data as array (cURL will handle multipart/form-data automatically)
    $postData = array();
    
    // Add form fields
    foreach ($_POST as $key => $value) {
        $postData[$key] = $value;
    }
    
    // Add CSRF token
    require_once 'helpers/csrf_helper.php';
    $postData['csrf_token'] = generateCsrfToken();
    
    // Add file if present
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        $postData['image'] = new CURLFile($_FILES['image']['tmp_name'], $_FILES['image']['type'], $_FILES['image']['name']);
    }
    
    echo "<p><strong>POST Data being sent:</strong></p>";
    echo "<pre>" . print_r($postData, true) . "</pre>";
    
    // Set POST fields (cURL will automatically set Content-Type to multipart/form-data)
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    
    // Pass session cookie for authentication
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<h4>HTTP Response Code: $httpCode</h4>";
    echo "<h4>Response Content:</h4>";
    echo "<textarea style='width:100%; height:300px;'>" . htmlspecialchars($response) . "</textarea>";
    
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Camera Upload Debug</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <h1>Camera Upload Debug</h1>
    <p>User ID: <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></p>
    
    <form method="POST" enctype="multipart/form-data">
        <input type="hidden" name="entity_type" value="show">
        <input type="hidden" name="entity_id" value="14">
        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
        <p>Upload a test image:</p>
        <input type="file" name="image" accept="image/*">
        <br><br>
        <button type="submit">Test PWA Camera Upload Endpoint</button>
    </form>
    
    <hr>
    
    <h2>Direct URL Test:</h2>
    <p><a href="/pwa/cameraUpload" target="_blank">Click to test /pwa/cameraUpload directly</a></p>
</body>
</html>
<?php
/**
 * Update Facebook Profile Sync
 * 
 * This script updates the database to support Facebook profile image synchronization.
 */

// Define the application root directory
define('APPROOT', __DIR__);

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';
require_once APPROOT . '/models/ImageEditorModel.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create database instance
$db = new Database();

// Display header
echo "===========================================\n";
echo "Facebook Profile Image Sync Update\n";
echo "===========================================\n\n";

// Check if DEBUG_MODE is defined
if (defined('DEBUG_MODE') && DEBUG_MODE) {
    echo "Running in DEBUG mode\n\n";
}

// Check if facebook_id column exists
echo "Checking if facebook_id column exists...\n";
$db->query("SHOW COLUMNS FROM users LIKE 'facebook_id'");
$facebookIdExists = $db->rowCount() > 0;

if ($facebookIdExists) {
    echo "facebook_id column already exists.\n";
} else {
    echo "facebook_id column does not exist. It will be created.\n";
}

// Check if facebook_token column exists
echo "Checking if facebook_token column exists...\n";
$db->query("SHOW COLUMNS FROM users LIKE 'facebook_token'");
$facebookTokenExists = $db->rowCount() > 0;

if ($facebookTokenExists) {
    echo "facebook_token column already exists.\n";
} else {
    echo "facebook_token column does not exist. It will be created.\n";
}

// Run the database updates directly
echo "Running database updates...\n";

try {
    // Add facebook_token column if it doesn't exist
    if (!$facebookTokenExists) {
        echo "Adding facebook_token column to users table...\n";
        $db->query("ALTER TABLE users ADD COLUMN facebook_token VARCHAR(1000) NULL COMMENT 'Facebook access token for API calls'");
        $result = $db->execute();
        echo "Result: " . ($result ? "Success" : "Failed") . "\n";
    }
    
    // Add facebook_id column if it doesn't exist
    if (!$facebookIdExists) {
        echo "Adding facebook_id column to users table...\n";
        $db->query("ALTER TABLE users ADD COLUMN facebook_id VARCHAR(255) NULL COMMENT 'Facebook user ID'");
        $result = $db->execute();
        echo "Result: " . ($result ? "Success" : "Failed") . "\n";
    }
    
    // Update system version
    echo "Updating system version...\n";
    $db->query("UPDATE system_settings SET setting_value = '3.35.17' WHERE setting_key = 'version'");
    $result = $db->execute();
    echo "Result: " . ($result ? "Success" : "Failed") . "\n";
    
    echo "\nDatabase updates completed successfully.\n\n";
    
    // Verify columns were created
    $db->query("SHOW COLUMNS FROM users LIKE 'facebook_id'");
    $facebookIdExists = $db->rowCount() > 0;
    
    $db->query("SHOW COLUMNS FROM users LIKE 'facebook_token'");
    $facebookTokenExists = $db->rowCount() > 0;
    
    echo "Verification:\n";
    echo "facebook_id column exists: " . ($facebookIdExists ? "Yes" : "No") . "\n";
    echo "facebook_token column exists: " . ($facebookTokenExists ? "Yes" : "No") . "\n\n";
    
    // Check if profile_image column exists
    echo "Checking for profile_image column...\n";
    $db->query("SHOW COLUMNS FROM users LIKE 'profile_image'");
    $profileImageExists = $db->rowCount() > 0;
    
    if ($profileImageExists) {
        echo "profile_image column exists. Checking for Facebook profile images to migrate...\n";
        
        // Get users with profile_image URLs
        $db->query("SELECT id, profile_image FROM users WHERE profile_image IS NOT NULL AND profile_image != ''");
        $users = $db->resultSet();
        
        $migratedCount = 0;
        
        foreach ($users as $user) {
            // Check if the profile_image is a Facebook URL
            if (strpos($user->profile_image, 'facebook') !== false || 
                strpos($user->profile_image, 'fbcdn') !== false || 
                strpos($user->profile_image, 'fbsbx') !== false) {
                
                echo "Found Facebook profile image for user {$user->id}. Migrating...\n";
                
                try {
                    // Download the image directly
                    echo "Downloading image from URL: " . $user->profile_image . "\n";
                    
                    // Set a user agent to avoid potential blocking
                    $context = stream_context_create([
                        'http' => [
                            'method' => 'GET',
                            'header' => [
                                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                                'Accept: image/jpeg,image/png,image/*,*/*'
                            ]
                        ]
                    ]);
                    
                    $imageContent = @file_get_contents($user->profile_image, false, $context);
                    
                    if (!$imageContent) {
                        echo "Failed to download image with file_get_contents, trying cURL...\n";
                        
                        // Try with cURL as a fallback
                        $ch = curl_init($user->profile_image);
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
                        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
                        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: image/jpeg,image/png,image/*,*/*']);
                        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
                        $imageContent = curl_exec($ch);
                        
                        if (curl_errno($ch)) {
                            echo "cURL error: " . curl_error($ch) . "\n";
                            curl_close($ch);
                            continue;
                        }
                        
                        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                        curl_close($ch);
                        
                        if ($httpCode != 200 || empty($imageContent)) {
                            echo "HTTP error: " . $httpCode . "\n";
                            continue;
                        }
                        
                        echo "Successfully downloaded image with cURL\n";
                    }
                    
                    // Verify the image content is valid
                    if (strlen($imageContent) < 100) {
                        echo "Downloaded image is too small: " . strlen($imageContent) . " bytes\n";
                        continue;
                    }
                    
                    // Check if the content is a valid image
                    $tempImageCheck = @imagecreatefromstring($imageContent);
                    if ($tempImageCheck === false) {
                        echo "Downloaded content is not a valid image\n";
                        continue;
                    }
                    imagedestroy($tempImageCheck);
                    
                    // Create a temporary file
                    $tempFile = tempnam(sys_get_temp_dir(), 'fb_img_');
                    $bytesWritten = file_put_contents($tempFile, $imageContent);
                    
                    if ($bytesWritten === false) {
                        echo "Failed to write to temporary file: " . $tempFile . "\n";
                        continue;
                    }
                    
                    echo "Created temporary file: " . $tempFile . " (size: " . filesize($tempFile) . " bytes)\n";
                    
                    // Create a file array similar to $_FILES
                    $fileArray = [
                        'name' => 'facebook_profile.jpg',
                        'type' => 'image/jpeg',
                        'tmp_name' => $tempFile,
                        'error' => 0,
                        'size' => filesize($tempFile)
                    ];
                    
                    // Load image editor model
                    $imageEditorModel = new ImageEditorModel();
                    
                    // First, delete any existing profile images for this user
                    $db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
                    $db->bind(':entity_type', 'user');
                    $db->bind(':entity_id', $user->id);
                    $existingImages = $db->resultSet();
                    
                    echo "Found " . count($existingImages) . " existing profile images for user " . $user->id . "\n";
                    
                    // Delete each existing image
                    foreach ($existingImages as $image) {
                        echo "Deleting existing image: " . $image->id . "\n";
                        $imageEditorModel->deleteImage($image->id);
                    }
                    
                    // Process the image upload
                    $uploadDir = 'uploads/users/';
                    
                    // Create directory if it doesn't exist
                    if (!file_exists($uploadDir)) {
                        echo "Creating upload directory: " . $uploadDir . "\n";
                        if (!mkdir($uploadDir, 0755, true)) {
                            echo "Failed to create upload directory: " . $uploadDir . "\n";
                            @unlink($tempFile);
                            continue;
                        }
                    }
                    
                    echo "Processing image upload\n";
                    $imageData = $imageEditorModel->processImageUpload(
                        $fileArray, 
                        'user', 
                        $user->id, 
                        $uploadDir,
                        $user->id,
                        true // Set as primary image
                    );
                    
                    // Clean up the temporary file
                    @unlink($tempFile);
                    
                    if ($imageData) {
                        $migratedCount++;
                        echo "Successfully migrated profile image for user {$user->id}\n";
                        
                        // Clear the profile_image field since we've migrated it
                        $db->query("UPDATE users SET profile_image = NULL WHERE id = :id");
                        $db->bind(':id', $user->id);
                        $db->execute();
                    } else {
                        echo "Failed to process image upload for user {$user->id}\n";
                    }
                } catch (Exception $e) {
                    echo "Error migrating profile image for user {$user->id}: " . $e->getMessage() . "\n";
                }
            }
        }
        
        echo "Migrated {$migratedCount} profile images\n\n";
    } else {
        echo "No profile_image column found in users table, skipping migration\n\n";
    }
    
} catch (Exception $e) {
    echo "\nError updating database: " . $e->getMessage() . "\n";
    exit(1);
}

echo "Update completed successfully!\n";
echo "You can now synchronize user profile images with Facebook.\n";
echo "===========================================\n";
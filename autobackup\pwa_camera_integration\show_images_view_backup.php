<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <?php if (isset($_SESSION['flash_messages']['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $_SESSION['flash_messages']['success']['message']; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php unset($_SESSION['flash_messages']['success']); ?>
    <?php endif; ?>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Show Images</h1>
            <p class="text-muted">
                <?php echo $show->name; ?> - <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
            </p>
        </div>
        <div class="col-md-6 text-end">
            <a href="javascript:history.back();" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <?php
                        // Find the primary image from the images array
                        $primaryImage = null;
                        if (!empty($images)) {
                            foreach ($images as $image) {
                                if (isset($image->is_primary) && $image->is_primary) {
                                    $primaryImage = $image;
                                    break;
                                }
                            }
                            
                            // If no primary image is set but we have images, use the first one
                            if (!$primaryImage && count($images) > 0) {
                                $primaryImage = $images[0];
                            }
                        }
                        
                        if ($primaryImage): 
                            // Use the file_path from the primary image
                            $primaryImagePath = BASE_URL . '/' . $primaryImage->file_path;
                        ?>
                            <img src="<?php echo $primaryImagePath; ?>" class="img-fluid rounded" alt="<?php echo $show->name; ?>">
                        <?php elseif (!empty($show->banner_image)): ?>
                        <?php 
                        // Fallback to banner_image if no primary image found in images array
                        $imagePath = 'uploads/shows/' . $show->banner_image;
                        $thumbnailPath = 'uploads/shows/thumbnails/' . $show->banner_image;
                        $fullThumbnailPath = APPROOT . '/' . $thumbnailPath;
                        $useThumbnail = file_exists($fullThumbnailPath);
                        ?>
                        <img src="<?php echo BASE_URL; ?>/<?php echo $useThumbnail ? $thumbnailPath : $imagePath; ?>" 
                             class="img-fluid rounded" alt="<?php echo $show->name; ?>">
                        <?php else: ?>
                        <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 150px;">
                            <i class="fas fa-calendar-alt fa-4x text-secondary"></i>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <h5><?php echo $show->name; ?></h5>
                    <p class="text-muted"><?php echo $show->location; ?></p>
                    
                    <div class="mb-2">
                        <strong>Dates:</strong> <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?> - <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                    </div>
                    
                    <div class="mb-2">
                        <strong>Status:</strong> 
                        <?php if (isset($show->status) && $show->status == 'published'): ?>
                            <span class="badge bg-success">Published</span>
                        <?php elseif (isset($show->status) && $show->status == 'completed'): ?>
                            <span class="badge bg-info">Completed</span>
                        <?php elseif (isset($show->status) && $show->status == 'cancelled'): ?>
                            <span class="badge bg-danger">Cancelled</span>
                        <?php else: ?>
                            <span class="badge bg-warning">Draft</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Image Management</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <a href="<?php echo BASE_URL; ?>/image_editor/upload/show/<?php echo $show->id; ?>" class="btn btn-success w-100 mb-2">
                                <i class="fas fa-upload me-2"></i> Upload Images
                            </a>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-info w-100 mb-2" 
                                    data-camera-capture="true" 
                                    data-entity-type="show" 
                                    data-entity-id="<?php echo $show->id; ?>">
                                <i class="fas fa-camera me-2"></i> Take Photo
                            </button>
                        </div>
                        <div class="col-md-4">
                            <a href="<?php echo BASE_URL; ?>/image_editor/show/<?php echo $show->id; ?>" class="btn btn-warning w-100 mb-2">
                                <i class="fas fa-edit me-2"></i> Image Editor
                            </a>
                        </div>
                    </div>
                    
                    <?php if (empty($images)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No images have been uploaded for this show yet. 
                        Use the buttons above to upload images or take photos.
                    </div>
                    <?php else: ?>
                    <div class="row">
                        <?php foreach ($images as $image): ?>
                        <div class="col-md-4 col-sm-6 mb-4">
                            <div class="card h-100">
                                <?php 
                                // Check if thumbnail exists
                                $thumbnailPath = !empty($image->thumbnail_path) ? $image->thumbnail_path : null;
                                
                                if (empty($thumbnailPath)) {
                                    // Try to determine thumbnail path based on original image path
                                    $pathInfo = pathinfo($image->file_path);
                                    $possibleThumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['basename'];
                                    $fullThumbnailPath = APPROOT . '/' . $possibleThumbnailPath;
                                    
                                    if (file_exists($fullThumbnailPath)) {
                                        $thumbnailPath = $possibleThumbnailPath;
                                    }
                                }
                                ?>
                                <div class="position-relative">
                                    <img src="<?php echo BASE_URL; ?>/<?php echo $thumbnailPath ? $thumbnailPath : $image->file_path; ?>" 
                                         class="card-img-top" alt="Show Image" style="height: 180px; object-fit: cover;">
                                    <div class="position-absolute top-0 start-0 p-2">
                                        <?php if (isset($image->is_primary) && $image->is_primary): ?>
                                        <span class="badge bg-success"><i class="fas fa-star"></i> Primary</span>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($show->banner_image) && $show->banner_image == $image->file_name): ?>
                                        <span class="badge bg-info ms-1"><i class="fas fa-image"></i> Banner</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title text-truncate"><?php echo $image->file_name; ?></h6>
                                    <p class="card-text small">
                                        <span class="badge bg-info"><?php echo $image->width; ?> x <?php echo $image->height; ?></span>
                                        <span class="badge bg-secondary"><?php echo formatFileSize($image->file_size); ?></span>
                                    </p>
                                    <div class="btn-group w-100">
                                        <a href="<?php echo BASE_URL; ?>/image_editor/edit/<?php echo $image->id; ?>" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit me-1"></i> Edit
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" data-bs-boundary="viewport" data-bs-display="static" aria-expanded="false">
                                            <span class="visually-hidden">Toggle Dropdown</span>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/crop/<?php echo $image->id; ?>">Crop</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/resize/<?php echo $image->id; ?>">Resize</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/rotate/<?php echo $image->id; ?>">Rotate</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/filter/<?php echo $image->id; ?>">Apply Filter</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/text/<?php echo $image->id; ?>">Add Text</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/image_editor/draw/<?php echo $image->id; ?>">Draw on Image</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <a class="dropdown-item <?php echo (isset($image->is_primary) && $image->is_primary) ? 'active' : ''; ?>" 
                                                   href="<?php echo BASE_URL; ?>/image_editor/setPrimary/<?php echo $image->id; ?>/show/<?php echo $show->id; ?>">
                                                    <?php if (isset($image->is_primary) && $image->is_primary): ?>
                                                    <i class="fas fa-check me-1"></i> Primary Image
                                                    <?php else: ?>
                                                    <i class="fas fa-star me-1"></i> Set as Primary
                                                    <?php endif; ?>
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item <?php echo (!empty($show->banner_image) && $show->banner_image == $image->file_name) ? 'active' : ''; ?>" 
                                                   href="<?php echo BASE_URL; ?>/image_editor/setBanner/<?php echo $image->id; ?>/show/<?php echo $show->id; ?>">
                                                    <?php if (!empty($show->banner_image) && $show->banner_image == $image->file_name): ?>
                                                    <i class="fas fa-check me-1"></i> Banner Image
                                                    <?php else: ?>
                                                    <i class="fas fa-image me-1"></i> Set as Banner
                                                    <?php endif; ?>
                                                </a>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="<?php echo BASE_URL; ?>/image_editor/delete/<?php echo $image->id; ?>">Delete Image</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include PWA Features JavaScript -->
<script src="<?php echo BASE_URL; ?>/public/js/pwa-features.js"></script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
/**
 * Custom Calendar JavaScript - <PERSON><PERSON>K<PERSON> BEFORE COMPREHENSIVE IMPROVEMENTS
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 3.35.69 - UNIVERSAL SCROLLING TEXT
 */

// Version check - this will show in console to confirm new version is loaded
console.log('=== CUSTOM-CALENDAR.JS LOADED - VERSION 3.35.69 - UNIVERSAL SCROLLING TEXT ===');

class CustomCalendar {
    /**
     * Initialize the calendar
     * 
     * @param {string} containerId - The ID of the container element
     * @param {Object} options - Calendar options
     */
    constructor(containerId, options = {}) {
        // Store container element
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Container element with ID "${containerId}" not found.`);
            return;
        }

        // Default options
        this.options = {
            defaultView: 'month',
            firstDayOfWeek: 0, // 0 = Sunday, 1 = Monday, etc.
            timeFormat: '12', // '12' or '24'
            showWeekends: true,
            businessHoursStart: '09:00',
            businessHoursEnd: '17:00',
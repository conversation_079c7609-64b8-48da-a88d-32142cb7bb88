<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Venue Management</h1>
            <p class="text-muted mb-0">Optimized for managing thousands of venues</p>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-info me-2 d-none d-sm-inline">
                <i class="fas fa-calendar me-2"></i> Calendar
            </a>
            <a href="<?php echo URLROOT; ?>/calendar/createVenue" class="btn btn-primary me-2">
                <i class="fas fa-plus me-2 d-none d-sm-inline"></i> Add Venue
            </a>
            <?php if (isAdmin()): ?>
            <a href="<?php echo URLROOT; ?>/calendar/batchGeocode" class="btn btn-warning me-2 d-none d-sm-inline">
                <i class="fas fa-map-marker-alt me-2"></i> Batch Geocode
            </a>
            <?php endif; ?>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php flash('calendar_message'); ?>

    <!-- Venue Overview Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Venue Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 col-md-4 col-lg-3">
                            <div class="card h-100 border-success shadow-sm venue-overview-card" 
                                 data-filter="all" 
                                 style="cursor: pointer;">
                                <div class="card-body text-center p-3">
                                    <h6 class="card-title">
                                        <span class="badge bg-success mb-2">All Venues</span>
                                    </h6>
                                    <div class="display-5 fw-bold my-2">
                                        <?php echo number_format($data['venue_counts']['total'] ?? 0); ?>
                                    </div>
                                    <p class="card-text text-muted small mb-0">Total Venues</p>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>Click to view
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!empty($data['venue_counts']['by_state'])): ?>
                            <?php $stateCount = 0; ?>
                            <?php foreach ($data['venue_counts']['by_state'] as $state => $count): ?>
                                <?php if ($stateCount >= 3) break; ?>
                                <div class="col-6 col-md-4 col-lg-3">
                                    <div class="card h-100 border-info shadow-sm venue-overview-card" 
                                         data-filter="state" 
                                         data-state="<?php echo $state; ?>"
                                         style="cursor: pointer;">
                                        <div class="card-body text-center p-3">
                                            <h6 class="card-title">
                                                <span class="badge bg-info mb-2"><?php echo $state; ?></span>
                                            </h6>
                                            <div class="display-5 fw-bold my-2">
                                                <?php echo number_format($count); ?>
                                            </div>
                                            <p class="card-text text-muted small mb-0">Venues</p>
                                            <small class="text-muted">
                                                <i class="fas fa-eye me-1"></i>Click to view
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <?php $stateCount++; ?>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Venue Details Section (Lazy Loaded) -->
    <div class="row mb-4 venue-section" id="venue-section" style="display: none;">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-success bg-opacity-25">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <span class="badge bg-success me-2">Venue Details</span>
                            <span class="badge bg-secondary" id="venue-count-display">0</span>
                        </h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="closeVenueSection()">
                                <i class="fas fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Search and Filter Controls -->
                <div class="card-body border-bottom">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="search-venues" class="form-label">Search Venues</label>
                            <input type="text" class="form-control" id="search-venues" 
                                   placeholder="Search by name, address, or city...">
                        </div>
                        <div class="col-md-2">
                            <label for="state-filter" class="form-label">State</label>
                            <select class="form-select" id="state-filter">
                                <option value="">All States</option>
                                <?php if (!empty($data['venue_counts']['by_state'])): ?>
                                    <?php foreach ($data['venue_counts']['by_state'] as $state => $count): ?>
                                        <option value="<?php echo $state; ?>"><?php echo $state; ?> (<?php echo $count; ?>)</option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="city-filter" class="form-label">City</label>
                            <input type="text" class="form-control" id="city-filter" placeholder="Filter by city...">
                        </div>
                        <div class="col-md-2">
                            <label for="per-page-venues" class="form-label">Per Page</label>
                            <select class="form-select" id="per-page-venues">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-success" onclick="searchVenues()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearVenueSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Loading Indicator -->
                <div class="card-body text-center" id="loading-venues">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading venues...</p>
                </div>
                
                <!-- Venues Content (Will be populated via AJAX) -->
                <div id="venues-content" style="display: none;">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Lazy Loading -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Venue overview card click handlers
    document.querySelectorAll('.venue-overview-card').forEach(card => {
        card.addEventListener('click', function() {
            const filter = this.dataset.filter;
            const state = this.dataset.state;
            const count = parseInt(this.querySelector('.display-5').textContent.replace(/,/g, ''));

            if (count > 0) {
                loadVenueSection(filter, state);
            }
        });
    });

    // Search input handlers
    document.getElementById('search-venues').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchVenues();
        }
    });

    // Filter change handlers
    document.getElementById('state-filter').addEventListener('change', searchVenues);
    document.getElementById('city-filter').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchVenues();
        }
    });
    document.getElementById('per-page-venues').addEventListener('change', searchVenues);
});

function loadVenueSection(filter = 'all', state = '') {
    // Show the venue section
    const section = document.getElementById('venue-section');
    section.style.display = 'block';

    // Scroll to the section
    section.scrollIntoView({ behavior: 'smooth', block: 'start' });

    // Set filter if specified
    if (filter === 'state' && state) {
        document.getElementById('state-filter').value = state;
    }

    // Load venues
    loadVenues(1);
}

function closeVenueSection() {
    const section = document.getElementById('venue-section');
    section.style.display = 'none';
}

function loadVenues(page = 1) {
    const loadingDiv = document.getElementById('loading-venues');
    const contentDiv = document.getElementById('venues-content');

    // Show loading, hide content
    loadingDiv.style.display = 'block';
    contentDiv.style.display = 'none';

    // Get filter values
    const search = document.getElementById('search-venues').value;
    const state = document.getElementById('state-filter').value;
    const city = document.getElementById('city-filter').value;
    const perPage = document.getElementById('per-page-venues').value;

    // Build URL parameters
    const params = new URLSearchParams({
        page: page,
        per_page: perPage,
        search: search,
        state: state,
        city: city
    });

    // Make AJAX request
    fetch('<?php echo BASE_URL; ?>/calendar/loadVenues?' + params.toString(), {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            renderVenues(data);
        } else {
            showVenueError(data.error || 'Failed to load venues');
        }
    })
    .catch(error => {
        console.error('Error loading venues:', error);
        showVenueError('Network error occurred');
    });
}

function searchVenues() {
    loadVenues(1);
}

function clearVenueSearch() {
    document.getElementById('search-venues').value = '';
    document.getElementById('state-filter').value = '';
    document.getElementById('city-filter').value = '';
    loadVenues(1);
}

function renderVenues(data) {
    const loadingDiv = document.getElementById('loading-venues');
    const contentDiv = document.getElementById('venues-content');

    // Hide loading
    loadingDiv.style.display = 'none';

    // Render venues table and pagination
    let html = '';

    if (data.venues.length === 0) {
        html = '<div class="card-body text-center"><p class="text-muted">No venues found.</p></div>';
    } else {
        html = renderVenuesTable(data.venues, data.pagination);
    }

    contentDiv.innerHTML = html;
    contentDiv.style.display = 'block';

    // Update venue count display
    document.getElementById('venue-count-display').textContent = data.pagination.total_venues.toLocaleString();
}

function renderVenuesTable(venues, pagination) {
    let html = '<div class="table-responsive"><table class="table table-striped table-hover mb-0">';

    // Table header
    html += '<thead class="table-light"><tr>';
    html += '<th>Name</th><th>Location</th><th>Contact</th><th>Events</th><th>Actions</th>';
    html += '</tr></thead><tbody>';

    // Table rows
    venues.forEach(venue => {
        html += '<tr>';
        html += '<td><strong>' + venue.name + '</strong>';
        if (venue.description) {
            html += '<br><small class="text-muted">' + (venue.description.length > 50 ? venue.description.substring(0, 50) + '...' : venue.description) + '</small>';
        }
        html += '</td>';
        html += '<td>';
        if (venue.address1) html += venue.address1 + '<br>';
        if (venue.city && venue.state) html += venue.city + ', ' + venue.state;
        if (venue.zipcode) html += ' ' + venue.zipcode;
        html += '</td>';
        html += '<td>';
        if (venue.phone) html += `<div><i class="fas fa-phone me-1"></i> ${venue.phone}</div>`;
        if (venue.email) html += `<div><i class="fas fa-envelope me-1"></i> ${venue.email}</div>`;
        if (venue.website) html += `<div><i class="fas fa-globe me-1"></i> <a href="${venue.website}" target="_blank">Website</a></div>`;
        if (!venue.phone && !venue.email && !venue.website) html += '<span class="text-muted">No contact info</span>';
        html += '</td>';
        html += '<td><span class="badge bg-info">' + (venue.event_count || 0) + '</span></td>';
        html += '<td>' + getVenueActions(venue.id) + '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';

    // Pagination
    if (pagination.total_pages > 1) {
        html += renderVenuePagination(pagination);
    }

    // Results info
    html += '<div class="card-footer text-muted small">';
    html += `Showing ${pagination.start_record}-${pagination.end_record} of ${pagination.total_venues.toLocaleString()} venues`;
    html += '</div>';

    return html;
}

function getVenueActions(venueId) {
    return `
        <div class="btn-group btn-group-sm">
            <a href="<?php echo BASE_URL; ?>/calendar/editVenue/${venueId}" class="btn btn-primary">
                <i class="fas fa-edit"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>/calendar/deleteVenue/${venueId}" class="btn btn-danger" onclick="return confirm('Are you sure?')">
                <i class="fas fa-trash"></i>
            </a>
        </div>
    `;
}

function renderVenuePagination(pagination) {
    let html = '<nav class="mt-3"><ul class="pagination pagination-sm justify-content-center">';

    // Previous button
    if (pagination.has_prev) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadVenues(${pagination.current_page - 1})">Previous</a></li>`;
    }

    // Page numbers (simplified)
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const active = i === pagination.current_page ? 'active' : '';
        html += `<li class="page-item ${active}"><a class="page-link" href="#" onclick="loadVenues(${i})">${i}</a></li>`;
    }

    // Next button
    if (pagination.has_next) {
        html += `<li class="page-item"><a class="page-link" href="#" onclick="loadVenues(${pagination.current_page + 1})">Next</a></li>`;
    }

    html += '</ul></nav>';
    return html;
}

function showVenueError(message) {
    const loadingDiv = document.getElementById('loading-venues');
    const contentDiv = document.getElementById('venues-content');

    loadingDiv.style.display = 'none';
    contentDiv.innerHTML = `<div class="card-body text-center"><div class="alert alert-danger">${message}</div></div>`;
    contentDiv.style.display = 'block';
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>

# Calendar Filter Fix

This fix addresses the issue where filtering on the events calendar was not working properly. The main problem was that the calendar was always displaying all events regardless of the filter settings.

## Issues Fixed

1. Added proper support for category and tag filtering in the CalendarModel
2. Improved handling of non-existent category and tag tables in the database
3. Disabled category and tag filters in the UI when no categories or tags are available
4. Enhanced error handling for category and tag filtering
5. Added debug logging for SQL queries when DEBUG_MODE is enabled

## Files Modified

1. `models/CalendarModel.php` - Updated the getEvents method to properly handle category and tag filtering
2. `public/js/calendar-filters.js` - Improved handling of category and tag filters when they don't exist

## Date

<?php echo date('Y-m-d'); ?>
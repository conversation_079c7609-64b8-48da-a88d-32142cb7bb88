<?php
/**
 * Notification Controller
 * 
 * This controller handles all notification-related user interactions,
 * including preferences, subscriptions, and notification management.
 */
class NotificationController extends Controller {
    private $notificationModel;
    private $notificationService;
    protected $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        parent::__construct();
        
        $this->db = new Database();
        $this->notificationModel = $this->model('NotificationModel');
        $this->notificationService = $this->model('NotificationService');
        
        // Note: Authentication is handled per method as needed
    }
    
    /**
     * Display user notification preferences
     */
    public function preferences() {
        if (!$this->isLoggedIn()) {
            $this->redirect('/auth/login');
        }
        $userId = $_SESSION['user_id'];
        $preferences = $this->notificationModel->getUserPreferences($userId);
        $subscriptions = $this->notificationModel->getUserSubscriptions($userId);
        
        $data = [
            'title' => 'Notification Preferences',
            'preferences' => $preferences,
            'subscriptions' => $subscriptions
        ];
        
        $this->view('user/notification_preferences', $data);
    }
    
    /**
     * Update user notification preferences
     */
    public function updatePreferences() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->redirect('/notification/preferences');
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            $this->setFlashMessage('error', 'Invalid request. Please try again.');
            $this->redirect('/notification/preferences');
        }
        
        $userId = $_SESSION['user_id'];
        
        $preferences = [
            'email_notifications' => isset($_POST['email_notifications']),
            'sms_notifications' => isset($_POST['sms_notifications']),
            'push_notifications' => isset($_POST['push_notifications']),
            'toast_notifications' => isset($_POST['toast_notifications'])
        ];
        
        if ($this->notificationModel->updateUserPreferences($userId, $preferences)) {
            $this->setFlashMessage('success', 'Notification preferences updated successfully.');
        } else {
            $this->setFlashMessage('error', 'Failed to update notification preferences.');
        }
        
        $this->redirect('/notification/preferences');
    }
    
    /**
     * Subscribe to event notifications (AJAX)
     */
    public function subscribe() {
        if (!$this->isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Please log in to subscribe to notifications']);
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid CSRF token']);
        }
        
        $userId = $_SESSION['user_id'];
        $eventId = (int) ($_POST['event_id'] ?? 0);
        $eventType = $_POST['event_type'] ?? '';
        $notificationTimes = $_POST['notification_times'] ?? [];
        $notifyRegistrationEnd = isset($_POST['notify_registration_end']);
        $registrationTimes = $_POST['registration_times'] ?? [];
        
        // Validate input
        if (!$eventId || !in_array($eventType, ['calendar_event', 'car_show'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid event data']);
        }
        
        if (!is_array($notificationTimes) || empty($notificationTimes)) {
            $this->jsonResponse(['success' => false, 'message' => 'Please select at least one notification time']);
        }
        
        // Convert notification times to integers
        $notificationTimes = array_map('intval', $notificationTimes);
        $registrationTimes = array_map('intval', $registrationTimes);
        
        // Validate notification times
        $validTimes = [15, 30, 60, 120, 360, 720, 1440, 2880, 4320, 10080]; // 15min to 1 week
        foreach ($notificationTimes as $time) {
            if (!in_array($time, $validTimes)) {
                $this->jsonResponse(['success' => false, 'message' => 'Invalid notification time selected']);
            }
        }
        
        if ($notifyRegistrationEnd && !empty($registrationTimes)) {
            foreach ($registrationTimes as $time) {
                if (!in_array($time, $validTimes)) {
                    $this->jsonResponse(['success' => false, 'message' => 'Invalid registration notification time selected']);
                }
            }
        }
        
        // Subscribe to event
        $success = $this->notificationModel->subscribeToEvent(
            $userId, 
            $eventId, 
            $eventType, 
            $notificationTimes, 
            $notifyRegistrationEnd, 
            $registrationTimes
        );
        
        if ($success) {
            $this->jsonResponse(['success' => true, 'message' => 'Successfully subscribed to event notifications']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to subscribe to event notifications']);
        }
    }
    
    /**
     * Unsubscribe from event notifications (AJAX)
     */
    public function unsubscribe() {
        if (!$this->isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Please log in to manage notifications']);
        }
        
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
        }
        
        // Validate CSRF token
        if (!$this->validateCSRF()) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid CSRF token']);
        }
        
        $userId = $_SESSION['user_id'];
        $eventId = (int) ($_POST['event_id'] ?? 0);
        $eventType = $_POST['event_type'] ?? '';
        
        // Validate input
        if (!$eventId || !in_array($eventType, ['calendar_event', 'car_show'])) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid event data']);
        }
        
        // Unsubscribe from event
        $success = $this->notificationModel->unsubscribeFromEvent($userId, $eventId, $eventType);
        
        if ($success) {
            $this->jsonResponse(['success' => true, 'message' => 'Successfully unsubscribed from event notifications']);
        } else {
            $this->jsonResponse(['success' => false, 'message' => 'Failed to unsubscribe from event notifications']);
        }
    }
    
    /**
     * Check subscription status (AJAX)
     */
    public function checkSubscription() {
        $userId = $_SESSION['user_id'];
        $eventId = (int) ($_GET['event_id'] ?? 0);
        $eventType = $_GET['event_type'] ?? '';
        
        if (!$eventId || !in_array($eventType, ['calendar_event', 'car_show'])) {
            $this->jsonResponse(['subscribed' => false]);
        }
        
        $isSubscribed = $this->notificationModel->isSubscribed($userId, $eventId, $eventType);
        
        $this->jsonResponse(['subscribed' => $isSubscribed]);
    }
    
    /**
     * Get unread notifications for user (AJAX)
     */
    public function getUnread() {
        $userId = $_SESSION['user_id'];
        
        // Get unread push notifications
        $this->db->query('SELECT * FROM user_push_notifications 
                          WHERE user_id = :user_id AND is_read = 0 
                          ORDER BY created_at DESC LIMIT 10');
        $this->db->bind(':user_id', $userId);
        $pushNotifications = $this->db->resultSet();
        
        // Get unread toast notifications
        $this->db->query('SELECT * FROM user_toast_notifications 
                          WHERE user_id = :user_id AND is_read = 0 
                          ORDER BY created_at DESC LIMIT 10');
        $this->db->bind(':user_id', $userId);
        $toastNotifications = $this->db->resultSet();
        
        $this->jsonResponse([
            'push' => $pushNotifications,
            'toast' => $toastNotifications
        ]);
    }
    
    /**
     * Mark notifications as read (AJAX)
     */
    public function markRead() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
        }
        
        $userId = $_SESSION['user_id'];
        $type = $_POST['type'] ?? '';
        $notificationIdsJson = $_POST['notification_ids'] ?? '[]';
        
        // Decode JSON string to array
        $notificationIds = json_decode($notificationIdsJson, true);
        
        if (!in_array($type, ['push', 'toast']) || !is_array($notificationIds)) {
            $this->jsonResponse(['success' => false, 'message' => 'Invalid parameters']);
        }
        
        $table = $type === 'push' ? 'user_push_notifications' : 'user_toast_notifications';
        
        if (!empty($notificationIds)) {
            $placeholders = str_repeat('?,', count($notificationIds) - 1) . '?';
            $this->db->query("UPDATE $table SET is_read = 1 
                              WHERE user_id = ? AND id IN ($placeholders)");
            $this->db->bind(1, $userId);
            foreach ($notificationIds as $index => $id) {
                $this->db->bind($index + 2, (int) $id);
            }
            $this->db->execute();
        }
        
        $this->jsonResponse(['success' => true]);
    }
    
    /**
     * Check if user is subscribed to an event (AJAX)
     */
    public function checkSubscription() {
        try {
            if (!$this->isLoggedIn()) {
                $this->jsonResponse(['success' => false, 'subscribed' => false, 'message' => 'Not logged in']);
                return;
            }
            
            $eventId = (int) ($_GET['event_id'] ?? 0);
            $eventType = $_GET['event_type'] ?? '';
            
            if (!$eventId || !in_array($eventType, ['calendar_event', 'car_show'])) {
                $this->jsonResponse(['success' => false, 'subscribed' => false, 'message' => 'Invalid event data']);
                return;
            }
            
            $userId = $_SESSION['user_id'];
            $isSubscribed = $this->notificationModel->isSubscribed($userId, $eventId, $eventType);
            
            $this->jsonResponse(['success' => true, 'subscribed' => $isSubscribed]);
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationController::checkSubscription - Error: " . $e->getMessage());
            }
            $this->jsonResponse(['success' => false, 'subscribed' => false, 'message' => 'Error checking subscription']);
        }
    }
    
    /**
     * Get unread notifications for current user (AJAX)
     */
    public function getUnread() {
        try {
            if (!$this->isLoggedIn()) {
                $this->jsonResponse(['push' => [], 'toast' => []]);
                return;
            }
            
            $userId = $_SESSION['user_id'];
            
            $unreadNotifications = $this->notificationModel->getUnreadNotifications($userId);
            
            // Separate push and toast notifications
            $pushNotifications = [];
            $toastNotifications = [];
            
            foreach ($unreadNotifications as $notification) {
                if (isset($notification->notification_type) && $notification->notification_type === 'push') {
                    $pushNotifications[] = $notification;
                } else {
                    $toastNotifications[] = $notification;
                }
            }
            
            $this->jsonResponse([
                'push' => $pushNotifications,
                'toast' => $toastNotifications
            ]);
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationController::getUnread - Error: " . $e->getMessage());
            }
            $this->jsonResponse(['push' => [], 'toast' => []]);
        }
    }
    
    /**
     * Mark notifications as read (AJAX)
     */
    public function markRead() {
        try {
            if (!$this->isLoggedIn()) {
                $this->jsonResponse(['success' => false, 'message' => 'Not logged in']);
                return;
            }
            
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                $this->jsonResponse(['success' => false, 'message' => 'Invalid request method']);
                return;
            }
            
            $type = $_POST['type'] ?? '';
            $notificationIds = json_decode($_POST['notification_ids'] ?? '[]', true);
            
            if (!in_array($type, ['toast', 'push']) || !is_array($notificationIds) || empty($notificationIds)) {
                $this->jsonResponse(['success' => false, 'message' => 'Invalid data']);
                return;
            }
            
            $userId = $_SESSION['user_id'];
            
            $success = $this->notificationModel->markNotificationsAsRead($userId, $notificationIds, $type);
            
            if ($success) {
                $this->jsonResponse(['success' => true, 'message' => 'Notifications marked as read']);
            } else {
                $this->jsonResponse(['success' => false, 'message' => 'Failed to mark notifications as read']);
            }
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationController::markRead - Error: " . $e->getMessage());
            }
            $this->jsonResponse(['success' => false, 'message' => 'Error marking notifications as read']);
        }
    }
    
    /**
     * Display notification subscription modal content (AJAX)
     */
    public function subscriptionModal() {
        if (!$this->isLoggedIn()) {
            $this->jsonResponse(['success' => false, 'message' => 'Please log in to set up notifications']);
        }
        $eventId = (int) ($_GET['event_id'] ?? 0);
        $eventType = $_GET['event_type'] ?? '';
        
        // Debug logging
        if (DEBUG_MODE) {
            error_log("NotificationController::subscriptionModal - Event ID: $eventId, Event Type: $eventType");
        }
        
        if (!$eventId || !in_array($eventType, ['calendar_event', 'car_show'])) {
            if (DEBUG_MODE) {
                error_log("NotificationController::subscriptionModal - Invalid event data");
            }
            $this->jsonResponse(['success' => false, 'message' => 'Invalid event data']);
        }
        
        // Get event details
        if ($eventType === 'calendar_event') {
            $this->db->query('SELECT id, title, start_date, end_date FROM calendar_events WHERE id = :id');
        } else {
            $this->db->query('SELECT id, name as title, start_date, end_date, registration_end_date FROM shows WHERE id = :id');
        }
        $this->db->bind(':id', $eventId);
        $event = $this->db->single();
        
        // Debug logging
        if (DEBUG_MODE) {
            error_log("NotificationController::subscriptionModal - Event query result: " . print_r($event, true));
        }
        
        if (!$event) {
            if (DEBUG_MODE) {
                error_log("NotificationController::subscriptionModal - Event not found");
            }
            $this->jsonResponse(['success' => false, 'message' => 'Event not found']);
        }
        
        // Check if user is already subscribed
        $userId = $_SESSION['user_id'];
        $isSubscribed = $this->notificationModel->isSubscribed($userId, $eventId, $eventType);
        
        // Get default notification times
        $defaultTimes = $this->notificationModel->getNotificationSettings('default_notification_times') ?? [1440, 60, 15];
        
        $data = [
            'event' => $event,
            'event_type' => $eventType,
            'is_subscribed' => $isSubscribed,
            'default_times' => $defaultTimes,
            'csrf_token' => $this->generateCSRF()
        ];
        
        try {
            ob_start();
            include APPROOT . '/views/shared/notification_subscription_modal.php';
            $html = ob_get_clean();
            
            if (DEBUG_MODE) {
                error_log("NotificationController::subscriptionModal - Modal HTML generated successfully");
            }
            
            $this->jsonResponse(['success' => true, 'html' => $html]);
        } catch (Exception $e) {
            if (DEBUG_MODE) {
                error_log("NotificationController::subscriptionModal - Error generating modal: " . $e->getMessage());
            }
            $this->jsonResponse(['success' => false, 'message' => 'Error loading notification modal: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Test notification sending (Admin only)
     */
    public function test() {
        // Check if user is admin
        if (!$this->isAdmin()) {
            $this->redirect('/');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!$this->validateCSRF()) {
                $this->setFlashMessage('error', 'Invalid request. Please try again.');
                $this->redirect('/notification/test');
            }
            
            $userId = (int) ($_POST['user_id'] ?? 0);
            $type = $_POST['type'] ?? '';
            $subject = $_POST['subject'] ?? '';
            $message = $_POST['message'] ?? '';
            $deliveryMethod = $_POST['delivery_method'] ?? 'immediate';
            $scheduledTime = $_POST['scheduled_time'] ?? null;
            
            if (!$userId || !in_array($type, ['email', 'sms', 'push', 'toast']) || !$subject || !$message) {
                $this->setFlashMessage('error', 'Please fill in all required fields.');
                $this->redirect('/notification/test');
            }
            
            // Validate scheduled time if provided
            if ($deliveryMethod === 'queued' && $scheduledTime) {
                $scheduledDateTime = DateTime::createFromFormat('Y-m-d\TH:i', $scheduledTime);
                if (!$scheduledDateTime) {
                    $this->setFlashMessage('error', 'Invalid scheduled time format.');
                    $this->redirect('/notification/test');
                }
                $scheduledTime = $scheduledDateTime->format('Y-m-d H:i:s');
            }
            
            try {
                if ($deliveryMethod === 'immediate') {
                    // Send immediately
                    $success = $this->notificationService->sendTestNotification($userId, $type, $subject, $message);
                    
                    if ($success) {
                        $this->setFlashMessage('success', 'Test notification sent immediately.');
                    } else {
                        $this->setFlashMessage('error', 'Failed to send test notification.');
                    }
                } else {
                    // Queue for processing
                    $success = $this->notificationModel->queueTestNotification($userId, $type, $subject, $message, $scheduledTime);
                    
                    if ($success) {
                        if ($scheduledTime) {
                            $this->setFlashMessage('success', 'Test notification scheduled and added to queue.');
                        } else {
                            $this->setFlashMessage('success', 'Test notification added to queue for immediate processing.');
                        }
                    } else {
                        $this->setFlashMessage('error', 'Failed to queue test notification.');
                    }
                }
            } catch (Exception $e) {
                $this->setFlashMessage('error', 'Error: ' . $e->getMessage());
            }
            
            $this->redirect('/notification/test');
        }
        
        // Get all users for testing
        $userModel = $this->model('UserModel');
        $users = $userModel->getUsers();
        
        $data = [
            'title' => 'Test Notifications',
            'users' => $users,
            'csrf_token' => $this->generateCSRF()
        ];
        
        $this->view('admin/test_notifications', $data);
    }
    
    /**
     * Process notification queue (Cron job endpoint)
     */
    public function process() {
        // This endpoint should be called by cron jobs
        // Add basic security to prevent unauthorized access
        $cronKey = $_GET['key'] ?? '';
        $expectedKey = hash('sha256', 'notification_cron_' . date('Y-m-d'));
        
        if ($cronKey !== $expectedKey) {
            http_response_code(403);
            echo "Unauthorized";
            exit;
        }
        
        try {
            $results = $this->notificationService->processPendingNotifications();
            
            echo json_encode([
                'success' => true,
                'processed' => $results['processed'],
                'sent' => $results['sent'],
                'failed' => $results['failed'],
                'errors' => $results['errors']
            ]);
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Webhook endpoint for SMS delivery status
     */
    public function webhook() {
        $provider = $_GET['provider'] ?? '';
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Log webhook data for debugging
        if (DEBUG_MODE) {
            error_log("NotificationController: Webhook received from $provider: " . json_encode($data));
        }
        
        // Process webhook based on provider
        switch ($provider) {
            case 'twilio':
                $this->processTwilioWebhook($data);
                break;
            case 'textmagic':
                $this->processTextMagicWebhook($data);
                break;
            // Add other providers as needed
        }
        
        http_response_code(200);
        echo "OK";
    }
    
    /**
     * Process Twilio webhook
     * 
     * @param array $data Webhook data
     */
    private function processTwilioWebhook($data) {
        // Process Twilio delivery status updates
        // This can be used to update notification status in the database
        if (isset($data['MessageStatus'])) {
            $status = $data['MessageStatus'];
            $messageSid = $data['MessageSid'] ?? '';
            
            if (DEBUG_MODE) {
                error_log("NotificationController: Twilio message $messageSid status: $status");
            }
            
            // Update notification status based on Twilio status
            // Implementation depends on how you want to track message IDs
        }
    }
    
    /**
     * Process TextMagic webhook
     * 
     * @param array $data Webhook data
     */
    private function processTextMagicWebhook($data) {
        // Process TextMagic delivery status updates
        if (isset($data['status'])) {
            $status = $data['status'];
            $messageId = $data['id'] ?? '';
            
            if (DEBUG_MODE) {
                error_log("NotificationController: TextMagic message $messageId status: $status");
            }
        }
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool
     */
    private function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    /**
     * Check if user is admin
     * 
     * @return bool
     */
    private function isAdmin() {
        return isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
    }
    
    /**
     * Validate CSRF token
     * 
     * @return bool
     */
    private function validateCSRF() {
        return verifyCsrfToken();
    }
    
    /**
     * Generate CSRF token
     * 
     * @return string
     */
    private function generateCSRF() {
        return generateCsrfToken();
    }
}
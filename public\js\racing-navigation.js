/**
 * Racing Dashboard Navigation JavaScript
 * Handles the badass mobile drawer functionality
 */

// Toggle Racing Drawer
function toggleRacingDrawer() {
    console.log('toggleRacingDrawer called');
    const drawer = document.getElementById('racingDrawer');
    const overlay = document.getElementById('racingOverlay');

    console.log('Drawer element:', drawer);
    console.log('Overlay element:', overlay);

    if (!drawer || !overlay) {
        console.error('Drawer or overlay not found!');
        return;
    }

    if (drawer.classList.contains('open')) {
        console.log('Closing drawer');
        closeRacingDrawer();
    } else {
        console.log('Opening drawer');
        openRacingDrawer();
    }
}

// Open Racing Drawer
function openRacingDrawer() {
    console.log('openRacingDrawer called');
    const drawer = document.getElementById('racingDrawer');
    const overlay = document.getElementById('racingOverlay');

    if (!drawer || !overlay) {
        console.error('Cannot open - drawer or overlay missing');
        return;
    }

    console.log('Adding open class to drawer');
    drawer.classList.add('open');
    overlay.classList.add('active');

    // Prevent body scroll when drawer is open
    document.body.style.overflow = 'hidden';

    console.log('Drawer should now be visible');

    // Add engine sound effect (optional)
    playEngineSound();
}

// Close Racing Drawer
function closeRacingDrawer() {
    const drawer = document.getElementById('racingDrawer');
    const overlay = document.getElementById('racingOverlay');
    
    drawer.classList.remove('open');
    overlay.classList.remove('active');
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Close all submenus
    closeAllSubmenus();
}

// Toggle Submenu
function toggleSubmenu(menuId) {
    const submenu = document.getElementById('submenu-' + menuId);
    const allSubmenus = document.querySelectorAll('.racing-submenu');
    
    // Close all other submenus
    allSubmenus.forEach(menu => {
        if (menu.id !== 'submenu-' + menuId) {
            menu.classList.remove('active');
        }
    });
    
    // Toggle current submenu
    if (submenu) {
        submenu.classList.toggle('active');
        
        // Add button press effect
        addButtonPressEffect(menuId);
    }
}

// Close All Submenus
function closeAllSubmenus() {
    const allSubmenus = document.querySelectorAll('.racing-submenu');
    allSubmenus.forEach(menu => {
        menu.classList.remove('active');
    });
}

// Add Button Press Effect
function addButtonPressEffect(buttonId) {
    const buttons = document.querySelectorAll('.racing-button');
    buttons.forEach(button => {
        const content = button.querySelector('.button-content');
        if (content && button.onclick.toString().includes(buttonId)) {
            // Add press effect
            content.style.transform = 'scale(0.95)';
            content.style.boxShadow = 'inset 0 2px 5px rgba(0, 0, 0, 0.5)';
            
            // Remove effect after animation
            setTimeout(() => {
                content.style.transform = '';
                content.style.boxShadow = '';
            }, 150);
        }
    });
}

// Play Engine Sound Effect (Optional)
function playEngineSound() {
    // You can add a subtle engine sound here if desired
    // For now, we'll use a simple beep or leave it silent
    try {
        // Create a subtle electronic beep
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
        
        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
        // Silently fail if audio context not supported
    }
}

// Handle Escape Key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeRacingDrawer();
    }
});

// Handle Window Resize
window.addEventListener('resize', function() {
    // Close drawer on desktop resize
    if (window.innerWidth >= 992) {
        closeRacingDrawer();
    }
});

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    console.log('Racing navigation loaded');

    const menuButton = document.querySelector('.racing-menu-btn');
    const drawer = document.getElementById('racingDrawer');
    const overlay = document.getElementById('racingOverlay');

    console.log('Menu button found:', menuButton);
    console.log('Drawer found:', drawer);
    console.log('Overlay found:', overlay);

    if (menuButton) {
        // Add click handler
        menuButton.addEventListener('click', function(e) {
            console.log('Menu button clicked');
            e.preventDefault();
            e.stopPropagation();

            // Work on all screen sizes for testing
            toggleRacingDrawer();
        });

        // Add visual feedback
        menuButton.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.95)';
        });

        menuButton.addEventListener('touchend', function() {
            this.style.transform = 'scale(1)';
        });
    } else {
        console.error('Racing menu button not found!');
    }
});

// Touch gesture support for mobile
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', function(e) {
    touchStartY = e.changedTouches[0].screenY;
});

document.addEventListener('touchend', function(e) {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipeGesture();
});

function handleSwipeGesture() {
    const drawer = document.getElementById('racingDrawer');
    const swipeThreshold = 50;
    
    // Swipe down to close drawer
    if (drawer.classList.contains('open')) {
        if (touchStartY < touchEndY - swipeThreshold) {
            closeRacingDrawer();
        }
    }
}

// Add racing button hover effects
document.addEventListener('DOMContentLoaded', function() {
    const racingButtons = document.querySelectorAll('.racing-button');
    
    racingButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            // Add subtle glow effect
            const content = this.querySelector('.button-content');
            if (content) {
                content.style.boxShadow = '0 0 20px rgba(0, 255, 255, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)';
            }
        });
        
        button.addEventListener('mouseleave', function() {
            // Remove glow effect
            const content = this.querySelector('.button-content');
            if (content) {
                content.style.boxShadow = '';
            }
        });
    });
});

<?php
/**
 * PWA Fix Verification Script
 * Tests that the PWA notification fixes are working correctly
 */

// Set up basic environment
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/test';

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__FILE__));
}

// Load configuration
require_once APPROOT . '/config/config.php';

echo "PWA Fix Verification Test\n";
echo "========================\n\n";

// Test 1: Check if PWAController can be loaded without errors
echo "1. Testing PWAController instantiation:\n";
try {
    require_once APPROOT . '/controllers/PWAController.php';
    $pwaController = new PWAController();
    echo "   ✓ PWAController loaded successfully\n";
    echo "   ✓ No validation_helper.php dependency error\n";
} catch (Exception $e) {
    echo "   ✗ PWAController failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check VAPID key configuration
echo "2. Testing VAPID key configuration:\n";
echo "   VAPID_PUBLIC_KEY defined: " . (defined('VAPID_PUBLIC_KEY') ? 'Yes' : 'No') . "\n";
echo "   VAPID_PRIVATE_KEY defined: " . (defined('VAPID_PRIVATE_KEY') ? 'Yes' : 'No') . "\n";

if (defined('VAPID_PUBLIC_KEY') && !empty(VAPID_PUBLIC_KEY)) {
    echo "   ✓ VAPID public key is configured\n";
    echo "   Key length: " . strlen(VAPID_PUBLIC_KEY) . " characters\n";
} else {
    echo "   ✗ VAPID public key is not configured\n";
}

echo "\n";

// Test 3: Test VAPID key endpoint simulation
echo "3. Testing VAPID key endpoint:\n";
try {
    // Capture output to test the endpoint
    ob_start();
    $pwaController->getVapidKey();
    $output = ob_get_clean();
    
    $response = json_decode($output, true);
    
    if ($response && isset($response['success'])) {
        if ($response['success']) {
            echo "   ✓ VAPID key endpoint returns success\n";
            echo "   ✓ Response format is valid JSON\n";
            if (isset($response['publicKey']) && !empty($response['publicKey'])) {
                echo "   ✓ Public key is present in response\n";
            } else {
                echo "   ✗ Public key is missing from response\n";
            }
        } else {
            echo "   ✗ VAPID key endpoint returned error: " . ($response['error'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "   ✗ Invalid response format\n";
        echo "   Raw output: " . $output . "\n";
    }
} catch (Exception $e) {
    echo "   ✗ VAPID key endpoint test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check required models
echo "4. Testing required model dependencies:\n";
$requiredModels = [
    'NotificationModel.php',
    'UserModel.php', 
    'ShowModel.php',
    'RegistrationModel.php',
    'CalendarModel.php',
    'VehicleModel.php'
];

foreach ($requiredModels as $model) {
    $modelPath = APPROOT . '/models/' . $model;
    if (file_exists($modelPath)) {
        echo "   ✓ $model exists\n";
    } else {
        echo "   ✗ $model missing\n";
    }
}

echo "\n";

// Test 5: Check required controllers
echo "5. Testing required controller dependencies:\n";
$requiredControllers = [
    'RegistrationController.php',
    'PaymentController.php',
    'JudgeController.php'
];

foreach ($requiredControllers as $controller) {
    $controllerPath = APPROOT . '/controllers/' . $controller;
    if (file_exists($controllerPath)) {
        echo "   ✓ $controller exists\n";
    } else {
        echo "   ✗ $controller missing\n";
    }
}

echo "\n";

echo "Test Summary:\n";
echo "=============\n";
echo "The PWA notification system should now work without the validation_helper.php error.\n";
echo "Key fixes applied:\n";
echo "- Removed non-existent validation_helper.php requirement\n";
echo "- Enhanced API routing for notification endpoints\n";
echo "- Improved VAPID key configuration and error handling\n";
echo "- Fixed JavaScript error handling for undefined values\n";

echo "\nTo test in browser:\n";
echo "1. Visit: " . (defined('BASE_URL') ? BASE_URL : 'https://your-domain.com') . "/api/notifications/vapid-key\n";
echo "2. Should return JSON with success:true and publicKey\n";
echo "3. Check browser console for PWA initialization messages\n";
?>
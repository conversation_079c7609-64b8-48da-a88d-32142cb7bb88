<?php
/**
 * Show Model
 * 
 * This model handles all database operations related to car shows.
 * 
 * Version 3.34.43 - Fixed property redeclaration error
 * - Fixed fatal error: "Cannot redeclare ShowModel::$systemFields" in ShowModel.php
 * - Removed duplicate declarations of $systemFields and $standardFields properties
 * - Merged system fields from both declarations to maintain functionality
 * 
 * Version 3.34.10 - Fixed PHP 8.4 deprecation warning
 * - Added proper declaration for $eventTrigger property to fix PHP 8.4 deprecation warning
 * 
 * Version 3.34.9 - Added event trigger support
 * - Added integration with EventTrigger for scheduled tasks
 * - Modified updateStatus method to trigger events on status changes
 * 
 * Version 2.19.95 - Fixed duplicate method declaration
 * - Fixed fatal error: "Cannot redeclare ShowModel::tableExists()" in ShowModel.php
 * - Removed duplicate tableExists() method declaration at the end of ShowModel class
 * - Maintained consistent database table existence checking functionality
 * 
 * Version 2.19.90 - Added age weights management functionality
 * - Added getAgeWeights method to retrieve age weights for a show
 * - Added createAgeWeight method to create new age weights
 * - Added updateAgeWeight method to update existing age weights
 * - Added deleteAgeWeight method to delete age weights
 * - Added createAgeWeightsTable method to create the age_weights table if it doesn't exist
 * - Added tableExists method to check if a table exists in the database
 * 
 * Version 2.19.94 - Fixed issues with missing methods and deprecated properties
 * - Added assignJudge method to fix fatal error in CoordinatorController
 * - Added removeJudgeAssignment method to fix fatal error in CoordinatorController
 * - Added category support to judging metrics
 * - Enhanced createJudgingMetricsTable to add category_id column
 * - Updated createMetric and updateMetric to handle category_id
 * - Added getMetricById method to fix fatal error in CoordinatorController
 * - Added updateMetric method to support editing metrics
 * - Added createMetric method to support adding new metrics
 * - Added getJudgeAssignments method to fix fatal error in CoordinatorController
 * - Added getShowCategories and getJudgingMetrics methods
 * - Replaced calls to EmergencyFormFieldManager::detectFieldType with our own implementation
 * - Fixed all occurrences of $standardFields and $systemFields
 * - Added proper variable initialization
 * - Fixed duplicate method declaration for detectFieldType
 * - Added missing property declaration for $formFieldManager
 * - Added missing getUserCompletedShows method with proper error handling
 */
class ShowModel {
    private $db;
    private $customFieldValuesModel;
    private $emergencyFormFieldManager;
    private $formFieldManager; // Added to fix deprecation warning
    private $eventTrigger; // Added to fix PHP 8.4 deprecation of dynamic properties
    
    // Standard fields that are part of the shows table
    private $standardFields = [
        'id', 'name', 'description', 'location', 'start_date', 'end_date', 
        'registration_start', 'registration_end', 'coordinator_id', 'status', 
        'fan_voting_enabled', 'registration_fee', 'is_free', 'listing_fee', 
        'listing_paid', 'featured_image_id', 'created_at', 'updated_at'
    ];
    
    // System fields that should not be saved to the database
    private $systemFields = [
        'name_err', 'location_err', 'start_date_err', 'end_date_err', 
        'registration_start_err', 'registration_end_err', 'coordinator_id_err', 
        'status_err', 'title', 'coordinators', 'template', 'data',
        'id', 'created_at', 'updated_at'
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize CustomFieldValuesModel if it exists
        if (file_exists(APPROOT . '/models/CustomFieldValuesModel.php')) {
            require_once APPROOT . '/models/CustomFieldValuesModel.php';
            $this->customFieldValuesModel = new CustomFieldValuesModel();
        }
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
        
        // Initialize DynamicFormFieldManager
        if (file_exists(APPROOT . '/models/DynamicFormFieldManager.php')) {
            require_once APPROOT . '/models/DynamicFormFieldManager.php';
            $this->formFieldManager = new DynamicFormFieldManager();
        } else {
            // Fallback to FormFieldManager if needed
            require_once APPROOT . '/models/FormFieldManager.php';
            $this->formFieldManager = new FormFieldManager();
        }
    }
    
    private function parseLocation($location) {
        $result = [
            'city' => '',
            'state' => ''
        ];
        
        if (empty($location)) {
            return $result;
        }
        
        // Common state abbreviations and full names mapping
        $stateMap = [
            'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
            'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
            'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
            'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
            'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
            'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
            'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
            'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
            'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
            'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
            'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
            'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
            'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
        ];
        
        // Flip the map to also search by full state name
        $stateNameToAbbr = array_flip($stateMap);
        
        // Try to match common patterns
        
        // Pattern 1: "City, State" or "City, ST"
        if (preg_match('/^(.+?),\s*([A-Za-z]{2,})$/i', trim($location), $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
            } else {
                $result['state'] = $stateCandidate;
            }
        }
        // Pattern 2: "City ST" or "City State"
        else if (preg_match('/^(.+?)\s+([A-Za-z]{2,})$/i', trim($location), $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
            } else {
                $result['state'] = $stateCandidate;
            }
        }
        // If no pattern matches, just use the whole string as the city
        else {
            $result['city'] = trim($location);
        }
        
        return $result;
    }
    
    /**
     * Get filtered shows with pagination
     * 
     * @param string $status Status filter (e.g., 'published')
     * @param string $search Search term for show name or description
     * @param string $state State filter
     * @param string $city City filter
     * @param string $showDate Show date filter (YYYY-MM-DD)
     * @param int $fanVoting Fan voting filter (-1 = all, 0 = disabled, 1 = enabled)
     * @param int $limit Number of records per page
     * @param int $offset Offset for pagination
     * @return array Array of show objects
     */
    public function getFilteredShows($status, $search = '', $state = '', $city = '', $showDate = '', $fanVoting = -1, $limit = 20, $offset = 0) {
        try {
            // Check if tables exist
            $usersTableExists = $this->tableExists('users');
            $registrationsTableExists = $this->tableExists('registrations');
            
            // Build SQL based on available tables
            if ($usersTableExists && $registrationsTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($usersTableExists) {
                $sql = 'SELECT s.*, u.name as coordinator_name, 0 as registration_count 
                        FROM shows s 
                        LEFT JOIN users u ON s.coordinator_id = u.id';
            } else if ($registrationsTableExists) {
                $sql = 'SELECT s.*, NULL as coordinator_name, 
                        (SELECT COUNT(*) FROM registrations WHERE show_id = s.id) as registration_count 
                        FROM shows s';
            } else {
                $sql = 'SELECT s.*, NULL as coordinator_name, 0 as registration_count 
                        FROM shows s';
            }
            
            // Start building WHERE clause
            $whereConditions = [];
            $params = [];
            
            // Status filter (required)
            $whereConditions[] = 's.status = :status';
            $params[':status'] = $status;
            
            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search OR s.description LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            // State filter
            if (!empty($state)) {
                $whereConditions[] = 's.location LIKE :state';
                $params[':state'] = '%' . $state . '%';
            }
            
            // City filter
            if (!empty($city)) {
                $whereConditions[] = 's.location LIKE :city';
                $params[':city'] = '%' . $city . '%';
            }
            
            // Show date filter
            if (!empty($showDate)) {
                $whereConditions[] = '(:show_date BETWEEN s.start_date AND s.end_date)';
                $params[':show_date'] = $showDate;
            }
            
            // Fan voting filter
            if ($fanVoting !== -1) {
                $whereConditions[] = 's.fan_voting_enabled = :fan_voting';
                $params[':fan_voting'] = $fanVoting;
            }
            
            // Combine WHERE conditions
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            // Add ORDER BY
            $sql .= ' ORDER BY s.start_date DESC';
            
            // Add LIMIT and OFFSET for pagination
            $sql .= ' LIMIT :limit OFFSET :offset';
            $params[':limit'] = $limit;
            $params[':offset'] = $offset;
            
            // Execute query
            $this->db->query($sql);
            
            // Bind parameters
            foreach ($params as $param => $value) {
                if ($param === ':limit' || $param === ':offset') {
                    $this->db->bind($param, $value, PDO::PARAM_INT);
                } else {
                    $this->db->bind($param, $value);
                }
            }
            
            $shows = $this->db->resultSet();
            
            // Parse location for each show
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                $show->city = $locationData['city'];
                $show->state = $locationData['state'];
            }
            
            return $shows;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getFilteredShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Count filtered shows (for pagination)
     * 
     * @param string $status Status filter (e.g., 'published')
     * @param string $search Search term for show name or description
     * @param string $state State filter
     * @param string $city City filter
     * @param string $showDate Show date filter (YYYY-MM-DD)
     * @param int $fanVoting Fan voting filter (-1 = all, 0 = disabled, 1 = enabled)
     * @return int Total count of filtered shows
     */
    public function countFilteredShows($status, $search = '', $state = '', $city = '', $showDate = '', $fanVoting = -1) {
        try {
            // Build SQL
            $sql = 'SELECT COUNT(*) as total FROM shows s';
            
            // Start building WHERE clause
            $whereConditions = [];
            $params = [];
            
            // Status filter (required)
            $whereConditions[] = 's.status = :status';
            $params[':status'] = $status;
            
            // Search filter
            if (!empty($search)) {
                $whereConditions[] = '(s.name LIKE :search OR s.description LIKE :search)';
                $params[':search'] = '%' . $search . '%';
            }
            
            // State filter
            if (!empty($state)) {
                $whereConditions[] = 's.location LIKE :state';
                $params[':state'] = '%' . $state . '%';
            }
            
            // City filter
            if (!empty($city)) {
                $whereConditions[] = 's.location LIKE :city';
                $params[':city'] = '%' . $city . '%';
            }
            
            // Show date filter
            if (!empty($showDate)) {
                $whereConditions[] = '(:show_date BETWEEN s.start_date AND s.end_date)';
                $params[':show_date'] = $showDate;
            }
            
            // Fan voting filter
            if ($fanVoting !== -1) {
                $whereConditions[] = 's.fan_voting_enabled = :fan_voting';
                $params[':fan_voting'] = $fanVoting;
            }
            
            // Combine WHERE conditions
            if (!empty($whereConditions)) {
                $sql .= ' WHERE ' . implode(' AND ', $whereConditions);
            }
            
            // Execute query
            $this->db->query($sql);
            
            // Bind parameters
            foreach ($params as $param => $value) {
                $this->db->bind($param, $value);
            }
            
            $result = $this->db->single();
            return $result->total;
        } catch (Exception $e) {
            error_log("Error in ShowModel::countFilteredShows: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get unique states from shows
     * 
     * @return array Array of unique states
     */
    public function getUniqueStatesFromShows() {
        try {
            // Get all published shows
            $shows = $this->getShows('published');
            $states = [];
            
            // Extract states using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                if (!empty($locationData['state']) && !in_array($locationData['state'], $states)) {
                    $states[] = $locationData['state'];
                }
            }
            
            // Sort states alphabetically
            sort($states);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($states as $state) {
                $stateObj = new stdClass();
                $stateObj->state = $state;
                $result[] = $stateObj;
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueStatesFromShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get unique cities from shows
     * 
     * @return array Array of unique cities
     */
    public function getUniqueCitiesFromShows() {
        try {
            // Get all published shows
            $shows = $this->getShows('published');
            $cities = [];
            
            // Extract cities using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                if (!empty($locationData['city']) && !in_array($locationData['city'], $cities)) {
                    $cities[] = $locationData['city'];
                }
            }
            
            // Sort cities alphabetically
            sort($cities);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($cities as $city) {
                $cityObj = new stdClass();
                $cityObj->city = $city;
                $result[] = $cityObj;
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueCitiesFromShows: " . $e->getMessage());
            return [];
        }
    }
}
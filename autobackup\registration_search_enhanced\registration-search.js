/**
 * Events and Shows Management System
 * Registration Live Search Functionality - Enhanced Version
 * 
 * This script provides live search functionality for the registrations table.
 * It uses DataTables' built-in search functionality but enhances it with:
 * 1. A hidden column containing searchable data (email, phone, license plate, etc.)
 * 2. Real-time search as you type
 * 3. Clear button functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Registration search script loaded');
    // Initialize the live search functionality if the elements exist
    setTimeout(initRegistrationSearch, 500); // Delay to ensure DataTables is loaded
});

/**
 * Initialize the registration search functionality
 */
function initRegistrationSearch() {
    const searchInput = document.getElementById('registrationSearch');
    const registrationsTable = document.getElementById('registrationsTable');
    
    if (!searchInput || !registrationsTable) {
        console.log('Search input or table not found');
        return; // Exit if elements don't exist
    }
    
    // Check if DataTable is available
    if (typeof $.fn.DataTable === 'undefined') {
        console.error('DataTables library not loaded. Please include DataTables library.');
        return;
    }
    
    // Wait for DataTable to be initialized
    let attempts = 0;
    const maxAttempts = 10;
    
    const checkDataTable = setInterval(function() {
        if ($.fn.dataTable.isDataTable('#registrationsTable')) {
            console.log('DataTable found, setting up live search');
            clearInterval(checkDataTable);
            setupLiveSearch();
        } else {
            console.log('Waiting for DataTable to initialize... Attempt ' + (attempts + 1));
            attempts++;
            
            // If we've waited too long, initialize DataTable ourselves
            if (attempts >= maxAttempts) {
                console.log('DataTable not initialized after ' + maxAttempts + ' attempts. Initializing now.');
                clearInterval(checkDataTable);
                
                try {
                    // Initialize DataTable
                    $('#registrationsTable').DataTable({
                        "order": [[ 1, "asc" ]],
                        "pageLength": 25,
                        "columnDefs": [
                            { "visible": false, "targets": 0 }, // Hide the search data column
                            { "type": "html", "targets": 1 } // Properly sort the display number column that contains HTML
                        ]
                    });
                    
                    // Now set up the live search
                    setupLiveSearch();
                } catch (error) {
                    console.error('Error initializing DataTable:', error);
                }
            }
        }
    }, 300);
    
    function setupLiveSearch() {
        const table = $('#registrationsTable').DataTable();
        
        // Custom live search functionality - SIMPLIFIED VERSION
        searchInput.addEventListener('keyup', function() {
            console.log('Search input keyup event triggered');
            const searchText = this.value.toLowerCase();
            console.log('Searching for:', searchText);
            
            // Use the built-in DataTables search functionality
            // This is much simpler and more reliable
            table.search(searchText).draw();
        });
        
        // Clear search when the X is clicked
        searchInput.addEventListener('search', function() {
            if (this.value === '') {
                table.search('').draw();
            }
        });
        
        // Add clear button functionality
        const clearButton = document.getElementById('clearSearch');
        if (clearButton) {
            clearButton.addEventListener('click', function() {
                searchInput.value = '';
                table.search('').draw();
                searchInput.focus();
            });
        }
    }
}
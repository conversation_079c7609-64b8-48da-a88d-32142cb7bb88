<?php
/**
 * Cleanup Orphaned Files
 * 
 * This script finds and removes image files that are no longer referenced in the database.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

echo "<h1>Cleanup Orphaned Files</h1>";
echo "<p>Finding and removing image files that are no longer referenced in the database...</p>";

try {
    $db = new Database();
    
    // Get all image filenames currently in the database
    $db->query("SELECT file_name, file_path FROM images");
    $dbImages = $db->resultSet();
    
    $dbFilenames = [];
    $dbFilePaths = [];
    
    foreach ($dbImages as $image) {
        $dbFilenames[] = $image->file_name;
        if (!empty($image->file_path)) {
            $dbFilePaths[] = $image->file_path;
        }
    }
    
    echo "<p><strong>Images in database:</strong> " . count($dbImages) . "</p>";
    
    // Define upload directories to check
    $uploadDirs = [
        '../../uploads/vehicles/',
        '../../uploads/shows/',
        '../../uploads/events/',
        '../../uploads/users/',
        '../../uploads/other/',
        '../../uploads/',
        '../../public/uploads/vehicles/',
        '../../public/uploads/shows/',
        '../../public/uploads/events/',
        '../../public/uploads/users/',
        '../../public/uploads/other/',
        '../../public/uploads/'
    ];
    
    $orphanedFiles = [];
    $totalFiles = 0;
    
    echo "<h2>Scanning Upload Directories</h2>";
    
    foreach ($uploadDirs as $dir) {
        if (!is_dir($dir)) {
            continue;
        }
        
        echo "<h3>Scanning: {$dir}</h3>";
        
        // Get all image files in directory
        $files = glob($dir . '*.{jpg,jpeg,png,gif,webp,JPG,JPEG,PNG,GIF,WEBP}', GLOB_BRACE);
        
        echo "<p>Files found: " . count($files) . "</p>";
        $totalFiles += count($files);
        
        foreach ($files as $filePath) {
            $filename = basename($filePath);
            
            // Check if this file is referenced in the database
            $isReferenced = false;
            
            // Check by filename
            if (in_array($filename, $dbFilenames)) {
                $isReferenced = true;
            }
            
            // Check by full path
            $relativePath = str_replace('../../', '', $filePath);
            if (in_array($relativePath, $dbFilePaths)) {
                $isReferenced = true;
            }
            
            // Also check without leading path
            $shortPath = str_replace('../../public/', '', $filePath);
            if (in_array($shortPath, $dbFilePaths)) {
                $isReferenced = true;
            }
            
            if (!$isReferenced) {
                $orphanedFiles[] = $filePath;
                echo "<div style='color: red;'>❌ ORPHANED: {$filename}</div>";
            } else {
                echo "<div style='color: green;'>✅ REFERENCED: {$filename}</div>";
            }
        }
    }
    
    echo "<h2>Summary</h2>";
    echo "<p><strong>Total files scanned:</strong> {$totalFiles}</p>";
    echo "<p><strong>Orphaned files found:</strong> " . count($orphanedFiles) . "</p>";
    echo "<p><strong>Referenced files:</strong> " . ($totalFiles - count($orphanedFiles)) . "</p>";
    
    if (!empty($orphanedFiles)) {
        echo "<h2>Orphaned Files to Delete</h2>";
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>⚠️ WARNING:</strong> The following files will be permanently deleted. Make sure you have backups!";
        echo "</div>";
        
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<input type='hidden' name='action' value='delete_orphaned'>";
        
        foreach ($orphanedFiles as $file) {
            echo "<div style='margin: 5px 0;'>";
            echo "<label><input type='checkbox' name='files[]' value='" . htmlspecialchars($file) . "' checked> ";
            echo htmlspecialchars(basename($file)) . " <small>(" . htmlspecialchars($file) . ")</small></label>";
            echo "</div>";
        }
        
        echo "<br><button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Are you sure you want to delete these files? This cannot be undone!\")'>Delete Selected Files</button>";
        echo "</form>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>✅ No orphaned files found!</strong> All files are properly referenced in the database.";
        echo "</div>";
    }
    
    // Handle deletion if form was submitted
    if ($_POST['action'] == 'delete_orphaned' && !empty($_POST['files'])) {
        echo "<h2>Deleting Files</h2>";
        
        $deletedCount = 0;
        $failedCount = 0;
        
        foreach ($_POST['files'] as $file) {
            // Security check - make sure file is in allowed directories
            $realPath = realpath($file);
            $allowedPaths = [
                realpath('../../uploads/'),
                realpath('../../public/uploads/')
            ];
            
            $isAllowed = false;
            foreach ($allowedPaths as $allowedPath) {
                if ($allowedPath && strpos($realPath, $allowedPath) === 0) {
                    $isAllowed = true;
                    break;
                }
            }
            
            if (!$isAllowed) {
                echo "<div style='color: red;'>❌ SECURITY: Skipped {$file} (not in allowed directory)</div>";
                $failedCount++;
                continue;
            }
            
            if (file_exists($file) && is_file($file)) {
                if (unlink($file)) {
                    echo "<div style='color: green;'>✅ DELETED: " . htmlspecialchars(basename($file)) . "</div>";
                    $deletedCount++;
                } else {
                    echo "<div style='color: red;'>❌ FAILED: " . htmlspecialchars(basename($file)) . "</div>";
                    $failedCount++;
                }
            } else {
                echo "<div style='color: orange;'>⚠️ NOT FOUND: " . htmlspecialchars(basename($file)) . "</div>";
                $failedCount++;
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>Deletion Complete!</strong><br>";
        echo "✅ Deleted: {$deletedCount} files<br>";
        if ($failedCount > 0) {
            echo "❌ Failed: {$failedCount} files<br>";
        }
        echo "</div>";
        
        echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>Refresh to scan again</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

form {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 15px;
    background: #f8f9fa;
}

label {
    display: block;
    margin: 5px 0;
    font-size: 14px;
}

small {
    color: #666;
}
</style>

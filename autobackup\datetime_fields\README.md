# DateTime Fields Update

This update changes the show date fields from DATE to DATETIME type, allowing for more precise scheduling of shows and registration periods.

## Changes Made

1. **Database Schema**
   - Modified `start_date`, `end_date`, `registration_start`, and `registration_end` fields in the `shows` table from DATE to DATETIME type
   - Added default times to existing records (12:00:00 for start_date, 23:59:59 for end_date, etc.)

2. **Form Templates**
   - Updated the default form templates to use datetime fields instead of date fields
   - Changed field labels to indicate time component (e.g., "Start Date & Time" instead of "Start Date")
   - Updated existing form templates in the database

3. **Form Handling**
   - Updated `add_with_template.php` and `edit_with_template.php` to properly handle datetime fields
   - Added proper formatting for datetime values in HTML datetime-local inputs
   - Ensured backward compatibility with existing date values

4. **Date Validation**
   - Updated `isRegistrationOpen()` method in ShowModel.php to properly handle datetime values
   - Removed unnecessary time adjustment (setting end date to 23:59:59) since the time is now explicitly stored

## Files Modified

- `models/FormDesignerModel.php` - Updated default field types from 'date' to 'datetime'
- `models/ShowModel.php` - Updated date handling in isRegistrationOpen method
- `views/admin/shows/add_with_template.php` - Added support for datetime fields
- `views/admin/shows/edit_with_template.php` - Added support for datetime fields
- `views/coordinator/edit_show.php` - Updated to use datetime-local inputs
- `views/coordinator/shows/create_with_template.php` - Updated to use datetime-local inputs
- `sql/update_v3.35.0_datetime_fields.sql` - SQL script to update database schema
- `CHANGELOG.md` - Added entry for version 3.35.0

## How to Apply the Update

1. Run the `update_datetime_fields.php` script as an administrator
2. The script will:
   - Update the database schema
   - Update existing form templates
   - Update the system version number

## Benefits

- More precise scheduling of shows and registration periods
- Ability to set specific times for registration cutoffs
- Better user experience with clear indication of time components
- Improved validation of registration periods

## Version

This update is part of version 3.35.0 of the Events and Shows Management System.
<!DOCTYPE html>
<html>
<head>
    <title>Test Fixed Banner Transitions</title>
    <style>
        .camera-banner {
            width: 400px;
            height: 150px;
            border: 2px solid #ccc;
            margin: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
            position: relative;
        }
        
        .test-container {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
        }
        
        .controls {
            margin: 10px 0;
        }
        
        .controls button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h2>Test Fixed Banner Transitions</h2>
    <div id="output"></div>

    <div class="test-container">
        <h3>Banner Container (Should NOT flash black anymore)</h3>
        <div class="camera-banner" id="camera-banner-content">
            <img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Static Logo" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">
        </div>
        
        <div class="controls">
            <button onclick="testBannerRotation()">Start Smooth Banner Rotation</button>
            <button onclick="stopBannerRotation()">Stop Banner Rotation</button>
            <button onclick="resetToLogo()">Reset to Logo</button>
        </div>
        
        <div id="banner-info">
            <strong>Expected behavior:</strong>
            <ul>
                <li>Logo shows for 5 seconds first</li>
                <li>Then rotates through 3 text banners every 3 seconds</li>
                <li>Smooth fade transitions (no black flashes)</li>
                <li>Text banners have semi-transparent dark background</li>
            </ul>
        </div>
    </div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        function testBannerRotation() {
            output('=== Testing Fixed Banner Rotation ===');
            
            if (window.cameraBanner) {
                output('✓ CameraBanner instance found');
                output('- Version: ' + window.cameraBanner.version);
                output('- Banners loaded: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'none'));
                
                if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                    output('- Starting smooth rotation...');
                    window.cameraBanner.startRotation('camera-banner-content');
                    output('✓ Rotation started - watch for smooth transitions!');
                } else {
                    output('✗ No banners loaded, trying to load them first...');
                    window.cameraBanner.loadBanners().then(() => {
                        output('✓ Banners loaded: ' + window.cameraBanner.banners.length);
                        output('- Starting smooth rotation...');
                        window.cameraBanner.startRotation('camera-banner-content');
                        output('✓ Rotation started - watch for smooth transitions!');
                    }).catch(error => {
                        output('✗ Failed to load banners: ' + error.message);
                    });
                }
            } else {
                output('✗ CameraBanner instance not found, loading script...');
                
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now();
                script.onload = function() {
                    output('✓ Script loaded, waiting for initialization...');
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            output('✓ CameraBanner now available');
                            testBannerRotation(); // Retry
                        } else {
                            output('✗ CameraBanner still not available');
                        }
                    }, 2000);
                };
                script.onerror = function() {
                    output('✗ Failed to load camera-banner.js');
                };
                document.head.appendChild(script);
            }
        }
        
        function stopBannerRotation() {
            if (window.cameraBanner && typeof window.cameraBanner.stopRotation === 'function') {
                window.cameraBanner.stopRotation();
                output('✓ Banner rotation stopped');
            } else {
                output('✗ Cannot stop rotation - cameraBanner not available');
            }
        }
        
        function resetToLogo() {
            const container = document.getElementById('camera-banner-content');
            if (container) {
                container.innerHTML = '<img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Static Logo" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">';
                output('✓ Reset to static logo');
            }
        }

        // Auto-test when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                output('Page loaded, checking banner system...');
                if (window.cameraBanner) {
                    output('✓ CameraBanner already available');
                    output('- Version: ' + window.cameraBanner.version);
                    output('- Banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'loading...'));
                } else {
                    output('⚠ CameraBanner not yet available, will be loaded on demand');
                }
            }, 1000);
        });
    </script>
</body>
</html>
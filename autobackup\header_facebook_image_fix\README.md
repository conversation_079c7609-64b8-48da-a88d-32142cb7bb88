# Header Facebook Image Fix

## Issue
The header.php file was trying to use the `getUserProfileImageUrl()` function without including the helper file that defines it.

## Error Message
```
Fatal error: Uncaught Error: Call to undefined function getUserProfileImageUrl() in /home/<USER>/events.rowaneliterides.com/views/includes/header.php:211
```

## Fix
Added the following code at the beginning of the header.php file:

```php
<?php
// Include the facebook image helper
require_once APPROOT . '/helpers/facebook_image_helper.php';
?>
```

## Date
Fixed on: <?php echo date('Y-m-d'); ?>
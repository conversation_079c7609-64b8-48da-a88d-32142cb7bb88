# Image Viewer Passive Events Fix

## Issue
Browser console warnings about non-passive event listeners:
- `[Violation] Added non-passive event listener to a scroll-blocking 'wheel' event`
- `[Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event`

## Root Cause
The image viewer was adding event listeners for 'wheel' and 'touchstart' events without specifying the passive option. Modern browsers recommend marking event handlers as passive when they don't need to prevent default behavior, or explicitly marking them as non-passive when they do.

## Solution
Added `{ passive: false }` option to event listeners that call `preventDefault()`:

### Files Modified
- `public/js/image-viewer.js`

### Changes Made
1. **Wheel Event (Line 238)**: Added `{ passive: false }` to wheel event listener because it calls `e.preventDefault()` to prevent page scrolling during zoom
2. **Touch Events (Lines 259-260)**: Added `{ passive: false }` to touchstart and touchmove event listeners because the drag method calls `e.preventDefault()`

### Code Changes
```javascript
// Before
this.imageWrapper.addEventListener('wheel', (e) => { ... });
this.imageWrapper.addEventListener('touchstart', (e) => this.startDrag(e));
document.addEventListener('touchmove', (e) => this.drag(e));

// After  
this.imageWrapper.addEventListener('wheel', (e) => { ... }, { passive: false });
this.imageWrapper.addEventListener('touchstart', (e) => this.startDrag(e), { passive: false });
document.addEventListener('touchmove', (e) => this.drag(e), { passive: false });
```

## Result
- Eliminates browser console warnings about passive event listeners
- Maintains proper functionality for zoom and drag operations
- Improves page responsiveness by explicitly declaring event handler behavior

## Testing
- Test image viewer zoom with mouse wheel
- Test image viewer drag functionality on touch devices
- Verify no console warnings appear
- Confirm all image viewer functionality works as expected

## Version
Applied to image-viewer.js v3.53.1
Date: 2025-01-27
<?php
/**
 * Coordinator Controller
 * 
 * This controller handles all coordinator-related functionality.
 */
class CoordinatorController extends Controller {
    private $showModel;
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $judgingModel;
    private $defaultCategoryModel;
    private $defaultMetricModel;
    private $defaultAgeWeightModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a coordinator
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->judgingModel = $this->model('JudgingModel');
        $this->defaultCategoryModel = $this->model('DefaultCategoryModel');
        $this->defaultMetricModel = $this->model('DefaultMetricModel');
        $this->defaultAgeWeightModel = $this->model('DefaultAgeWeightModel');
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('coordinator/dashboard');
    }
    
    /**
     * Create a new show
     */
    public function createShow() {
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'name' => trim($_POST['name']),
                'description' => trim($_POST['description']),
                'location' => trim($_POST['location']),
                'start_date' => trim($_POST['start_date']),
                'end_date' => trim($_POST['end_date']),
                'registration_open_date' => trim($_POST['registration_open_date']),
                'registration_close_date' => trim($_POST['registration_close_date']),
                'coordinator_id' => $this->auth->getCurrentUserId(),
                'status' => isset($_POST['status']) ? 'published' : 'draft',
                'fan_voting_enabled' => isset($_POST['fan_voting_enabled']) ? 1 : 0,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_open_date_err' => '',
                'registration_close_date_err' => ''
            ];
            
            // Validate name
            if (empty($data['name'])) {
                $data['name_err'] = 'Please enter a name';
            }
            
            // Validate location
            if (empty($data['location'])) {
                $data['location_err'] = 'Please enter a location';
            }
            
            // Validate dates
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif (!empty($data['start_date']) && strtotime($data['end_date']) < strtotime($data['start_date'])) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            if (empty($data['registration_open_date'])) {
                $data['registration_open_date_err'] = 'Please enter a registration open date';
            }
            
            if (empty($data['registration_close_date'])) {
                $data['registration_close_date_err'] = 'Please enter a registration close date';
            } elseif (!empty($data['registration_open_date']) && strtotime($data['registration_close_date']) < strtotime($data['registration_open_date'])) {
                $data['registration_close_date_err'] = 'Registration close date must be after registration open date';
            }
            
            // Check for errors
            if (empty($data['name_err']) && empty($data['location_err']) && 
                empty($data['start_date_err']) && empty($data['end_date_err']) && 
                empty($data['registration_open_date_err']) && empty($data['registration_close_date_err'])) {
                
                // Create show
                $showId = $this->showModel->createShow($data);
                
                if ($showId) {
                    // Set success message
                    $this->setFlashMessage('coordinator_message', 'Show created successfully');
                    
                    // Redirect to show page
                    $this->redirect('coordinator/show/' . $showId);
                } else {
                    $this->redirect('home/error/Something%20went%20wrong');
                }
            } else {
                // Load view with errors
                $data['title'] = 'Create Show';
                $this->view('coordinator/shows/create', $data);
            }
        } else {
            // Init data
            $data = [
                'name' => '',
                'description' => '',
                'location' => '',
                'start_date' => '',
                'end_date' => '',
                'registration_open_date' => '',
                'registration_close_date' => '',
                'status' => 'draft',
                'fan_voting_enabled' => 0,
                'name_err' => '',
                'location_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'registration_open_date_err' => '',
                'registration_close_date_err' => '',
                'title' => 'Create Show'
            ];
            
            // Load view
            $this->view('coordinator/shows/create', $data);
        }
    }
    
    /**
     * Reports dashboard
     */
    public function reports() {
        // Get shows for this coordinator
        $userId = $this->auth->getCurrentUserId();
        
        // If admin, get all shows, otherwise get only shows assigned to this coordinator
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db = new Database();
            $this->db->query('SELECT s.*, 
                             (SELECT COUNT(*) FROM registrations r WHERE r.show_id = s.id) as registration_count 
                             FROM shows s 
                             WHERE s.coordinator_id = :coordinator_id 
                             ORDER BY s.start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Reports Dashboard',
            'shows' => $shows
        ];
        
        $this->view('coordinator/reports/index', $data);
    }
    
    /**
     * Registration Report
     * 
     * @return void
     */
    public function registrationReport() {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show ID from query string if provided
        $showId = isset($_GET['show_id']) ? filter_var($_GET['show_id'], FILTER_VALIDATE_INT) : null;
        
        // If no show ID provided, get the most recent show for this coordinator
        if (!$showId) {
            $this->db = new Database();
            $this->db->query('SELECT id FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC LIMIT 1');
            $this->db->bind(':coordinator_id', $userId);
            $show = $this->db->single();
            
            if ($show) {
                $showId = $show->id;
            } else {
                $this->setFlashMessage('report_message', 'No shows found', 'alert alert-warning');
                $this->redirect('coordinator/reports');
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registration statistics
        $this->db = new Database();
        
        // Total registrations
        $this->db->query('SELECT COUNT(*) as total FROM registrations WHERE show_id = :show_id');
        $this->db->bind(':show_id', $showId);
        $totalRegistrations = $this->db->single()->total;
        
        // Registrations by category
        $this->db->query('SELECT sc.name, COUNT(r.id) as count 
                         FROM show_categories sc 
                         LEFT JOIN registrations r ON r.category_id = sc.id 
                         WHERE sc.show_id = :show_id 
                         GROUP BY sc.id 
                         ORDER BY count DESC');
        $this->db->bind(':show_id', $showId);
        $registrationsByCategory = $this->db->resultSet();
        
        // Registrations by day
        $this->db->query('SELECT DATE(created_at) as date, COUNT(*) as count 
                         FROM registrations 
                         WHERE show_id = :show_id 
                         GROUP BY DATE(created_at) 
                         ORDER BY date');
        $this->db->bind(':show_id', $showId);
        $registrationsByDay = $this->db->resultSet();
        
        // Registrations by status
        $this->db->query('SELECT status, COUNT(*) as count 
                         FROM registrations 
                         WHERE show_id = :show_id 
                         GROUP BY status');
        $this->db->bind(':show_id', $showId);
        $registrationsByStatus = $this->db->resultSet();
        
        // Get all shows for the dropdown
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Registration Report - ' . $show->name,
            'show' => $show,
            'shows' => $shows,
            'total_registrations' => $totalRegistrations,
            'registrations_by_category' => $registrationsByCategory,
            'registrations_by_day' => $registrationsByDay,
            'registrations_by_status' => $registrationsByStatus
        ];
        
        $this->view('coordinator/reports/registration_report', $data);
    }
    
    /**
     * Financial Report
     * 
     * @return void
     */
    public function financialReport() {
        // Get user ID
        $userId = $this->auth->getCurrentUserId();
        
        // Get show ID from query string if provided
        $showId = isset($_GET['show_id']) ? filter_var($_GET['show_id'], FILTER_VALIDATE_INT) : null;
        
        // If no show ID provided, get the most recent show for this coordinator
        if (!$showId) {
            $this->db = new Database();
            $this->db->query('SELECT id FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC LIMIT 1');
            $this->db->bind(':coordinator_id', $userId);
            $show = $this->db->single();
            
            if ($show) {
                $showId = $show->id;
            } else {
                $this->setFlashMessage('report_message', 'No shows found', 'alert alert-warning');
                $this->redirect('coordinator/reports');
                return;
            }
        }
        
        // Get show details
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->setFlashMessage('report_message', 'Show not found', 'alert alert-danger');
            $this->redirect('coordinator/reports');
            return;
        }
        
        // Check if user is authorized to view this show
        if (!$this->auth->hasRole('admin') && $show->coordinator_id != $userId) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get financial statistics
        $this->db = new Database();
        
        // Total revenue
        $this->db->query('SELECT SUM(fee) as total_revenue FROM registrations WHERE show_id = :show_id AND status = "approved"');
        $this->db->bind(':show_id', $showId);
        $totalRevenue = $this->db->single()->total_revenue ?? 0;
        
        // Revenue by category
        $this->db->query('SELECT sc.name, SUM(r.fee) as revenue, COUNT(r.id) as count 
                         FROM show_categories sc 
                         LEFT JOIN registrations r ON r.category_id = sc.id AND r.status = "approved"
                         WHERE sc.show_id = :show_id 
                         GROUP BY sc.id 
                         ORDER BY revenue DESC');
        $this->db->bind(':show_id', $showId);
        $revenueByCategory = $this->db->resultSet();
        
        // Revenue by day
        $this->db->query('SELECT DATE(created_at) as date, SUM(fee) as revenue, COUNT(*) as count 
                         FROM registrations 
                         WHERE show_id = :show_id AND status = "approved"
                         GROUP BY DATE(created_at) 
                         ORDER BY date');
        $this->db->bind(':show_id', $showId);
        $revenueByDay = $this->db->resultSet();
        
        // Get all shows for the dropdown
        if ($this->auth->hasRole('admin')) {
            $shows = $this->showModel->getShows();
        } else {
            $this->db->query('SELECT * FROM shows WHERE coordinator_id = :coordinator_id ORDER BY start_date DESC');
            $this->db->bind(':coordinator_id', $userId);
            $shows = $this->db->resultSet();
        }
        
        $data = [
            'title' => 'Financial Report - ' . $show->name,
            'show' => $show,
            'shows' => $shows,
            'total_revenue' => $totalRevenue,
            'revenue_by_category' => $revenueByCategory,
            'revenue_by_day' => $revenueByDay
        ];
        
        $this->view('coordinator/reports/financial_report', $data);
    }
}
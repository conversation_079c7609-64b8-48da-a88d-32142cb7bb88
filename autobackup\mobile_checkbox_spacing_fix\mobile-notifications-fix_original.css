/**
 * Mobile Notification Fix CSS v1.0.0
 * 
 * Additional mobile-specific styles for the notification system
 * to ensure proper functionality and layout on mobile devices.
 * 
 * Location: /public/css/mobile-notifications-fix.css
 * Dependencies: notifications.css, Bootstrap 5
 */

/* Mobile-specific enhancements */
@media (max-width: 768px) {
    /* Ensure modal is properly sized on mobile */
    .mobile-enhanced.modal .modal-dialog {
        margin: 0.25rem;
        max-width: calc(100vw - 0.5rem);
        width: calc(100vw - 0.5rem);
        height: calc(100vh - 0.5rem);
        max-height: calc(100vh - 0.5rem);
    }
    
    .mobile-enhanced.modal .modal-content {
        height: 100%;
        max-height: 100%;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
    }
    
    .mobile-enhanced.modal .modal-body {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        padding: 1rem 0.75rem;
    }
    
    .mobile-enhanced.modal .modal-header,
    .mobile-enhanced.modal .modal-footer {
        flex-shrink: 0;
        padding: 0.75rem;
    }
    
    /* Better touch targets for mobile */
    .mobile-enhanced .form-check {
        min-height: 48px;
        padding: 0.75rem 0.5rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        border-radius: 8px;
        transition: background-color 0.2s ease;
    }
    
    .mobile-enhanced .form-check:active {
        background-color: rgba(13, 110, 253, 0.1);
    }
    
    .mobile-enhanced .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.75rem;
        margin-left: 0;
        flex-shrink: 0;
    }
    
    .mobile-enhanced .form-check-label {
        flex: 1;
        font-size: 0.95rem;
        line-height: 1.4;
        cursor: pointer;
        -webkit-tap-highlight-color: transparent;
    }
    
    .mobile-enhanced .form-switch .form-check-input {
        width: 3rem;
        height: 1.5rem;
    }
    
    /* Modal footer buttons */
    .mobile-enhanced .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
        border-top: 1px solid #dee2e6;
    }
    
    .mobile-enhanced .modal-footer .btn {
        width: 100%;
        min-height: 48px;
        font-size: 1rem;
        font-weight: 500;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin-bottom: 0;
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
        position: relative;
        overflow: hidden;
    }
    
    .mobile-enhanced .modal-footer .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
    
    /* Ripple effect for buttons */
    .mobile-enhanced .modal-footer .btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.3s, height 0.3s;
        pointer-events: none;
    }
    
    .mobile-enhanced .modal-footer .btn:active::after {
        width: 200px;
        height: 200px;
    }
    
    /* Notification times columns */
    .mobile-enhanced .notification-times-column {
        padding: 0.75rem 0.5rem;
        margin-bottom: 1rem;
        background-color: rgba(248, 249, 250, 0.5);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        width: 100%;
        box-sizing: border-box;
    }
    
    /* Registration deadline container */
    .mobile-enhanced #notify_registration_end_container {
        flex-direction: row;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 1rem;
        background-color: rgba(13, 202, 240, 0.1);
        border: 1px solid rgba(13, 202, 240, 0.2);
        border-radius: 8px;
        min-height: 48px;
    }
    
    .mobile-enhanced #notify_registration_end_container .form-check-input {
        margin-left: auto;
        margin-right: 0;
    }
    
    .mobile-enhanced #notify_registration_end_container .form-check-label {
        flex: 1;
        margin-right: 1rem;
        font-size: 0.9rem;
    }
    
    /* Alert styling */
    .mobile-enhanced .alert {
        font-size: 0.9rem;
        padding: 0.75rem;
        line-height: 1.4;
        border-radius: 8px;
    }
    
    /* Modal title */
    .mobile-enhanced .modal-title {
        font-size: 1.1rem;
        line-height: 1.3;
    }
    
    /* Grid improvements */
    .mobile-enhanced .row.g-3 {
        --bs-gutter-x: 0.5rem;
        --bs-gutter-y: 0.5rem;
    }
    
    .mobile-enhanced .col-12.col-md-6 {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
}

/* Extra small screens */
@media (max-width: 576px) {
    .mobile-enhanced.modal .modal-dialog {
        margin: 0;
        max-width: 100vw;
        width: 100vw;
        height: 100vh;
        max-height: 100vh;
    }
    
    .mobile-enhanced.modal .modal-content {
        border-radius: 0;
    }
    
    .mobile-enhanced .modal-header,
    .mobile-enhanced .modal-footer {
        padding: 0.75rem 1rem;
    }
    
    .mobile-enhanced .modal-body {
        padding: 1rem;
    }
    
    .mobile-enhanced .notification-times-column {
        padding: 0.5rem;
        background-color: rgba(248, 249, 250, 0.3);
    }
    
    .mobile-enhanced .form-check-label {
        font-size: 0.9rem;
    }
    
    .mobile-enhanced #notify_registration_end_container .form-check-label {
        font-size: 0.85rem;
    }
}

/* Landscape orientation adjustments */
@media (max-width: 768px) and (orientation: landscape) {
    .mobile-enhanced.modal .modal-dialog {
        height: calc(100vh - 1rem);
        max-height: calc(100vh - 1rem);
    }
    
    .mobile-enhanced .modal-body {
        max-height: calc(100vh - 200px);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-enhanced .form-check-input {
        border-width: 0.5px;
    }
    
    .mobile-enhanced .modal-content {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
    .mobile-enhanced .modal-content {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .mobile-enhanced .modal-header,
    .mobile-enhanced .modal-footer {
        border-color: #4a5568;
    }
    
    .mobile-enhanced .notification-times-column {
        background-color: rgba(74, 85, 104, 0.3);
        border-color: rgba(74, 85, 104, 0.5);
    }
    
    .mobile-enhanced .form-check:active {
        background-color: rgba(66, 153, 225, 0.2);
    }
    
    .mobile-enhanced #notify_registration_end_container {
        background-color: rgba(66, 153, 225, 0.2);
        border-color: rgba(66, 153, 225, 0.3);
    }
}

/* Accessibility improvements for mobile */
@media (max-width: 768px) {
    .mobile-enhanced .form-check-input:focus {
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        outline: none;
    }
    
    .mobile-enhanced .btn:focus {
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        outline: none;
    }
    
    .mobile-enhanced .modal-header .btn-close:focus {
        box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
        outline: none;
    }
}

/* Animation improvements for mobile */
@media (max-width: 768px) {
    .mobile-enhanced.modal.fade .modal-dialog {
        transition: transform 0.2s ease-out;
    }
    
    .mobile-enhanced .form-check {
        transition: background-color 0.15s ease;
    }
    
    .mobile-enhanced .btn {
        transition: all 0.15s ease;
    }
}

/* Prevent zoom on input focus (iOS Safari) */
@media (max-width: 768px) {
    .mobile-enhanced input[type="checkbox"] {
        font-size: 16px;
    }
}

/* Body class for mobile notifications */
.mobile-notifications-enhanced {
    /* Prevent body scroll when modal is open on mobile */
}

.mobile-notifications-enhanced.modal-open {
    overflow: hidden;
    position: fixed;
    width: 100%;
}
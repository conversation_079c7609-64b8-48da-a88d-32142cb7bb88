<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid container-lg">
    <div class="row mb-4 align-items-center">
        <div class="col-8 col-md-6">
            <h1 class="h2 mb-0">Role Management</h1>
        </div>
        <div class="col-4 col-md-6 text-end">
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2 d-none d-sm-inline"></i> Back
            </a>
        </div>
    </div>

    <?php if (hasFlashMessage('user_success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_success')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (hasFlashMessage('user_error')) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('user_error')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Role Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <?php 
                        // Define badge colors for each role
                        $roleBadges = [
                            'admin' => 'danger',
                            'coordinator' => 'primary',
                            'judge' => 'success',
                            'staff' => 'warning',
                            'user' => 'info'
                        ];
                        
                        foreach ($roles as $role => $displayName) : 
                            $badgeColor = $roleBadges[$role] ?? 'secondary';
                        ?>
                            <div class="col-6 col-md-4 col-lg">
                                <div class="card h-100 border-<?php echo $badgeColor; ?> shadow-sm">
                                    <div class="card-body text-center p-3">
                                        <h6 class="card-title">
                                            <span class="badge bg-<?php echo $badgeColor; ?> mb-2">
                                                <?php echo $displayName; ?>
                                            </span>
                                        </h6>
                                        <div class="display-5 fw-bold my-2">
                                            <?php echo $roleCounts[$role] ?? 0; ?>
                                        </div>
                                        <p class="card-text text-muted small mb-0">Users</p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php 
    // Define badge colors for each role
    $roleBadges = [
        'admin' => 'danger',
        'coordinator' => 'primary',
        'judge' => 'success',
        'staff' => 'warning',
        'user' => 'info'
    ];
    
    foreach ($roles as $role => $displayName) : 
        $badgeColor = $roleBadges[$role] ?? 'secondary';
    ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-<?php echo $badgeColor; ?> bg-opacity-25">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <span class="badge bg-<?php echo $badgeColor; ?> me-2"><?php echo $displayName; ?></span>
                                <span class="badge bg-secondary"><?php echo $roleCounts[$role] ?? 0; ?></span>
                            </h5>
                            <?php if (!empty($usersByRole[$role])) : ?>
                                <button class="btn btn-sm btn-outline-secondary d-md-none" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo ucfirst($role); ?>" aria-expanded="true">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="collapse show" id="collapse<?php echo ucfirst($role); ?>">
                        <div class="card-body p-0">
                            <?php if (empty($usersByRole[$role])) : ?>
                                <div class="p-3">
                                    <p class="text-muted mb-0">No users with this role.</p>
                                </div>
                            <?php else : ?>
                                <!-- Mobile view: Cards -->
                                <div class="d-md-none">
                                    <?php foreach ($usersByRole[$role] as $user) : ?>
                                        <div class="border-bottom p-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <?php if ($user->profile_image) : ?>
                                                    <img src="<?php echo BASE_URL; ?>/<?php echo $user->profile_image; ?>" alt="Profile" class="rounded-circle me-2" width="40" height="40">
                                                <?php else : ?>
                                                    <i class="fas fa-user-circle me-2 text-secondary" style="font-size: 2rem;"></i>
                                                <?php endif; ?>
                                                <div>
                                                    <h6 class="mb-0"><?php echo $user->name; ?></h6>
                                                    <small class="text-muted"><?php echo $user->email; ?></small>
                                                </div>
                                            </div>
                                            
                                            <div class="row g-2 mb-2">
                                                <div class="col-6">
                                                    <small class="d-block text-muted">Status:</small>
                                                    <?php if ($user->status == 'active') : ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php elseif ($user->status == 'inactive') : ?>
                                                        <span class="badge bg-danger">Inactive</span>
                                                    <?php else : ?>
                                                        <span class="badge bg-warning text-dark">Pending</span>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="col-6">
                                                    <small class="d-block text-muted">Created:</small>
                                                    <?php echo date('M j, Y', strtotime($user->created_at)); ?>
                                                </div>
                                            </div>
                                            
                                            <div class="d-flex gap-2 mt-2">
                                                <a href="<?php echo BASE_URL; ?>/admin/assignRole/<?php echo $user->id; ?>" class="btn btn-sm btn-primary flex-grow-1">
                                                    <i class="fas fa-user-tag"></i> Change Role
                                                </a>
                                                <a href="<?php echo BASE_URL; ?>/admin/editUser/<?php echo $user->id; ?>" class="btn btn-sm btn-secondary flex-grow-1">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <!-- Desktop view: Table -->
                                <div class="table-responsive d-none d-md-block">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Name</th>
                                                <th>Email</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Last Login</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($usersByRole[$role] as $user) : ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if ($user->profile_image) : ?>
                                                                <img src="<?php echo BASE_URL; ?>/<?php echo $user->profile_image; ?>" alt="Profile" class="rounded-circle me-2" width="30" height="30">
                                                            <?php else : ?>
                                                                <i class="fas fa-user-circle me-2 text-secondary" style="font-size: 1.5rem;"></i>
                                                            <?php endif; ?>
                                                            <?php echo $user->name; ?>
                                                        </div>
                                                    </td>
                                                    <td><?php echo $user->email; ?></td>
                                                    <td>
                                                        <?php if ($user->status == 'active') : ?>
                                                            <span class="badge bg-success">Active</span>
                                                        <?php elseif ($user->status == 'inactive') : ?>
                                                            <span class="badge bg-danger">Inactive</span>
                                                        <?php else : ?>
                                                            <span class="badge bg-warning text-dark">Pending</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo date('M j, Y', strtotime($user->created_at)); ?></td>
                                                    <td>
                                                        <?php echo $user->last_login ? date('M j, Y', strtotime($user->last_login)) : 'Never'; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group">
                                                            <a href="<?php echo BASE_URL; ?>/admin/assignRole/<?php echo $user->id; ?>" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-user-tag"></i> Change Role
                                                            </a>
                                                            <a href="<?php echo BASE_URL; ?>/admin/editUser/<?php echo $user->id; ?>" class="btn btn-sm btn-secondary">
                                                                <i class="fas fa-edit"></i> Edit
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">Role Descriptions</h5>
                </div>
                <div class="card-body p-0">
                    <!-- Mobile-friendly accordion for small screens -->
                    <div class="d-md-none">
                        <div class="accordion" id="roleAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="adminHeading">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#adminCollapse" aria-expanded="true" aria-controls="adminCollapse">
                                        <span class="badge bg-danger me-2">Administrator</span>
                                    </button>
                                </h2>
                                <div id="adminCollapse" class="accordion-collapse collapse show" aria-labelledby="adminHeading" data-bs-parent="#roleAccordion">
                                    <div class="accordion-body">
                                        <p><strong>Description:</strong> Full system access and configuration</p>
                                        <p><strong>Capabilities:</strong></p>
                                        <ul class="mb-0">
                                            <li>Manage all users and roles</li>
                                            <li>Configure system settings</li>
                                            <li>Manage all shows and events</li>
                                            <li>Access all system features</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="coordinatorHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#coordinatorCollapse" aria-expanded="false" aria-controls="coordinatorCollapse">
                                        <span class="badge bg-primary me-2">Show Coordinator</span>
                                    </button>
                                </h2>
                                <div id="coordinatorCollapse" class="accordion-collapse collapse" aria-labelledby="coordinatorHeading" data-bs-parent="#roleAccordion">
                                    <div class="accordion-body">
                                        <p><strong>Description:</strong> Manages specific shows, judges, and registrations</p>
                                        <p><strong>Capabilities:</strong></p>
                                        <ul class="mb-0">
                                            <li>Create and manage assigned shows</li>
                                            <li>Assign judges to categories</li>
                                            <li>Manage registrations and participants</li>
                                            <li>View and publish results</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="judgeHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#judgeCollapse" aria-expanded="false" aria-controls="judgeCollapse">
                                        <span class="badge bg-success me-2">Judge</span>
                                    </button>
                                </h2>
                                <div id="judgeCollapse" class="accordion-collapse collapse" aria-labelledby="judgeHeading" data-bs-parent="#roleAccordion">
                                    <div class="accordion-body">
                                        <p><strong>Description:</strong> Evaluates vehicles using the judging interface</p>
                                        <p><strong>Capabilities:</strong></p>
                                        <ul class="mb-0">
                                            <li>Score vehicles in assigned categories</li>
                                            <li>Submit judging results</li>
                                            <li>View assigned show details</li>
                                            <li>Register own vehicles for shows</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="staffHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#staffCollapse" aria-expanded="false" aria-controls="staffCollapse">
                                        <span class="badge bg-warning text-dark me-2">Show Staff</span>
                                    </button>
                                </h2>
                                <div id="staffCollapse" class="accordion-collapse collapse" aria-labelledby="staffHeading" data-bs-parent="#roleAccordion">
                                    <div class="accordion-body">
                                        <p><strong>Description:</strong> Assists with show operations for assigned shows</p>
                                        <p><strong>Capabilities:</strong></p>
                                        <ul class="mb-0">
                                            <li>Create and edit registrations</li>
                                            <li>Process payments for registrations</li>
                                            <li>Check in vehicles on show day</li>
                                            <li>Print registration cards</li>
                                            <li>Access only shows they are assigned to</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="userHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#userCollapse" aria-expanded="false" aria-controls="userCollapse">
                                        <span class="badge bg-info me-2">Registered User</span>
                                    </button>
                                </h2>
                                <div id="userCollapse" class="accordion-collapse collapse" aria-labelledby="userHeading" data-bs-parent="#roleAccordion">
                                    <div class="accordion-body">
                                        <p><strong>Description:</strong> Can register vehicles for shows and view their registrations</p>
                                        <p><strong>Capabilities:</strong></p>
                                        <ul class="mb-0">
                                            <li>Register vehicles for shows</li>
                                            <li>View own registrations and results</li>
                                            <li>Manage own vehicle information</li>
                                            <li>Vote in fan choice competitions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Table for medium and larger screens -->
                    <div class="d-none d-md-block table-responsive">
                        <table class="table table-striped mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 15%">Role</th>
                                    <th style="width: 25%">Description</th>
                                    <th style="width: 60%">Capabilities</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><span class="badge bg-danger">Administrator</span></td>
                                    <td>Full system access and configuration</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Manage all users and roles</li>
                                            <li>Configure system settings</li>
                                            <li>Manage all shows and events</li>
                                            <li>Access all system features</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-primary">Show Coordinator</span></td>
                                    <td>Manages specific shows, judges, and registrations</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Create and manage assigned shows</li>
                                            <li>Assign judges to categories</li>
                                            <li>Manage registrations and participants</li>
                                            <li>View and publish results</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-success">Judge</span></td>
                                    <td>Evaluates vehicles using the judging interface</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Score vehicles in assigned categories</li>
                                            <li>Submit judging results</li>
                                            <li>View assigned show details</li>
                                            <li>Register own vehicles for shows</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-warning text-dark">Show Staff</span></td>
                                    <td>Assists with show operations for assigned shows</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Create and edit registrations</li>
                                            <li>Process payments for registrations</li>
                                            <li>Check in vehicles on show day</li>
                                            <li>Print registration cards</li>
                                            <li>Access only shows they are assigned to</li>
                                        </ul>
                                    </td>
                                </tr>
                                <tr>
                                    <td><span class="badge bg-info">Registered User</span></td>
                                    <td>Can register vehicles for shows and view their registrations</td>
                                    <td>
                                        <ul class="mb-0">
                                            <li>Register vehicles for shows</li>
                                            <li>View own registrations and results</li>
                                            <li>Manage own vehicle information</li>
                                            <li>Vote in fan choice competitions</li>
                                        </ul>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
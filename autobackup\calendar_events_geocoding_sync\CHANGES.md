# Calendar Events Geocoding Synchronization - Changes

## Files Modified

1. `models/ShowModel.php`
   - Updated the code that updates calendar events to include lat and lng values
   - Added code to fetch the latest show data to ensure we have the most up-to-date coordinates
   - Added detailed logging to help diagnose geocoding synchronization issues

2. `models/CalendarModel.php`
   - Updated the createEventFromShow method to include lat and lng values when creating new calendar events
   - Added detailed logging to help diagnose geocoding synchronization issues
   - The syncEventsWithShows method already included lat and lng values, but we added more logging

3. Created `test/test_geocoding_sync.php`
   - A test script to verify that geocoding data is properly synchronized between shows and calendar events
   - The script checks for mismatches and can fix them if needed

## Changes in Detail

### ShowModel.php

When updating calendar events associated with a show, we now:
1. Fetch the latest show data to ensure we have the most up-to-date lat/lng values
2. Include these values in the eventData array sent to the updateEvent method
3. Add detailed logging to track the coordinates being sent

### CalendarModel.php

When creating a new calendar event from a show, we now:
1. Include the show's lat and lng values in the eventData array
2. Add detailed logging to track the coordinates being used

When syncing events with shows, we added:
1. Detailed logging to track the coordinates being used for each event

### Test Script

The test_geocoding_sync.php script:
1. Finds shows with geocoding data (lat/lng values)
2. Checks their associated calendar events
3. Verifies that the coordinates match
4. Updates any mismatched coordinates
5. Provides detailed output for debugging

## How to Verify the Fix

1. Run the test script at `/test/test_geocoding_sync.php`
2. Edit a show with a complete address to trigger geocoding
3. Check that both the show and its associated calendar events have the same coordinates
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-cog me-2"></i>Notification Settings</h1>
            <p class="text-muted">Configure global notification settings and SMS providers.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Global Settings Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe me-2"></i>Global Notification Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/admin/updateNotificationSettings" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_enabled" 
                                           name="email_enabled" <?php echo $settings['email_enabled'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="email_enabled">
                                        <i class="fas fa-envelope me-2 text-primary"></i>
                                        <strong>Enable Email Notifications</strong>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="sms_enabled" 
                                           name="sms_enabled" <?php echo $settings['sms_enabled'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="sms_enabled">
                                        <i class="fas fa-sms me-2 text-success"></i>
                                        <strong>Enable SMS Notifications</strong>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="push_enabled" 
                                           name="push_enabled" <?php echo $settings['push_enabled'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="push_enabled">
                                        <i class="fas fa-bell me-2 text-warning"></i>
                                        <strong>Enable Push Notifications</strong>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="toast_enabled" 
                                           name="toast_enabled" <?php echo $settings['toast_enabled'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="toast_enabled">
                                        <i class="fas fa-comment me-2 text-info"></i>
                                        <strong>Enable Site Notifications</strong>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_from_address" class="form-label">From Email Address</label>
                                    <input type="email" class="form-control" id="email_from_address" 
                                           name="email_from_address" value="<?php echo htmlspecialchars($settings['email_from_address']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email_from_name" class="form-label">From Name</label>
                                    <input type="text" class="form-control" id="email_from_name" 
                                           name="email_from_name" value="<?php echo htmlspecialchars($settings['email_from_name']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_notification_attempts" class="form-label">Max Retry Attempts</label>
                                    <input type="number" class="form-control" id="max_notification_attempts" 
                                           name="max_notification_attempts" value="<?php echo $settings['max_notification_attempts']; ?>" 
                                           min="1" max="10" required>
                                    <div class="form-text">Maximum number of attempts to send a failed notification</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notification_retry_interval" class="form-label">Retry Interval (minutes)</label>
                                    <input type="number" class="form-control" id="notification_retry_interval" 
                                           name="notification_retry_interval" value="<?php echo $settings['notification_retry_interval']; ?>" 
                                           min="5" max="1440" required>
                                    <div class="form-text">Minutes to wait before retrying failed notifications</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Save Global Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- SMS Providers Card -->
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sms me-2"></i>SMS Providers
                    </h5>
                    <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#testSmsModal">
                        <i class="fas fa-paper-plane me-2"></i>Test SMS
                    </button>
                </div>
                <div class="card-body">
                    <?php if (DEBUG_MODE): ?>
                        <div class="alert alert-info">
                            <strong>Debug Info:</strong> 
                            SMS Providers count: <?php echo count($smsProviders ?? []); ?>
                            <?php if (!empty($smsProviders)): ?>
                                <br>Providers: <?php echo implode(', ', array_map(function($p) { return $p->name; }, $smsProviders)); ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (empty($smsProviders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <h5 class="text-muted">No SMS Providers Configured</h5>
                            <p class="text-muted">Configure at least one SMS provider to enable SMS notifications.</p>
                            <div class="mt-3">
                                <a href="<?php echo BASE_URL; ?>/admin/installNotifications" class="btn btn-primary me-2">
                                    <i class="fas fa-download me-2"></i>Install Notification System
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/checkSmsProviders" class="btn btn-outline-info" target="_blank">
                                    <i class="fas fa-search me-2"></i>Check SMS Providers
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="accordion" id="smsProvidersAccordion">
                            <?php foreach ($smsProviders as $index => $provider): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading<?php echo $provider->id; ?>">
                                        <button class="accordion-button <?php echo $index === 0 ? '' : 'collapsed'; ?>" 
                                                type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#collapse<?php echo $provider->id; ?>" 
                                                aria-expanded="<?php echo $index === 0 ? 'true' : 'false'; ?>" 
                                                aria-controls="collapse<?php echo $provider->id; ?>">
                                            <div class="d-flex align-items-center w-100">
                                                <i class="fas fa-sms me-3"></i>
                                                <div class="flex-grow-1">
                                                    <strong><?php echo htmlspecialchars($provider->name); ?></strong>
                                                    <?php if ($provider->is_default): ?>
                                                        <span class="badge bg-primary ms-2">Default</span>
                                                    <?php endif; ?>
                                                    <?php if ($provider->is_active): ?>
                                                        <span class="badge bg-success ms-2">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary ms-2">Inactive</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo $provider->id; ?>" 
                                         class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" 
                                         aria-labelledby="heading<?php echo $provider->id; ?>" 
                                         data-bs-parent="#smsProvidersAccordion">
                                        <div class="accordion-body">
                                            <form action="<?php echo BASE_URL; ?>/admin/updateSmsProvider" method="post">
                                                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                                                <input type="hidden" name="provider_id" value="<?php echo $provider->id; ?>">
                                                
                                                <?php 
                                                $config = json_decode($provider->configuration, true);
                                                ?>
                                                
                                                <?php if ($provider->provider_key === 'twilio'): ?>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Account SID</label>
                                                                <input type="text" class="form-control" name="config[account_sid]" 
                                                                       value="<?php echo htmlspecialchars($config['account_sid'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Auth Token</label>
                                                                <input type="password" class="form-control" name="config[auth_token]" 
                                                                       value="<?php echo htmlspecialchars($config['auth_token'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">From Number</label>
                                                                <input type="text" class="form-control" name="config[from_number]" 
                                                                       value="<?php echo htmlspecialchars($config['from_number'] ?? ''); ?>" 
                                                                       placeholder="+**********">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php elseif ($provider->provider_key === 'textmagic'): ?>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Username</label>
                                                                <input type="text" class="form-control" name="config[username]" 
                                                                       value="<?php echo htmlspecialchars($config['username'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">API Key</label>
                                                                <input type="password" class="form-control" name="config[api_key]" 
                                                                       value="<?php echo htmlspecialchars($config['api_key'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php elseif ($provider->provider_key === 'nexmo'): ?>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">API Key</label>
                                                                <input type="text" class="form-control" name="config[api_key]" 
                                                                       value="<?php echo htmlspecialchars($config['api_key'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">API Secret</label>
                                                                <input type="password" class="form-control" name="config[api_secret]" 
                                                                       value="<?php echo htmlspecialchars($config['api_secret'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">From</label>
                                                                <input type="text" class="form-control" name="config[from]" 
                                                                       value="<?php echo htmlspecialchars($config['from'] ?? ''); ?>" 
                                                                       placeholder="Events">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php elseif ($provider->provider_key === 'clicksend'): ?>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Username</label>
                                                                <input type="text" class="form-control" name="config[username]" 
                                                                       value="<?php echo htmlspecialchars($config['username'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">API Key</label>
                                                                <input type="password" class="form-control" name="config[api_key]" 
                                                                       value="<?php echo htmlspecialchars($config['api_key'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php elseif ($provider->provider_key === 'plivo'): ?>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Auth ID</label>
                                                                <input type="text" class="form-control" name="config[auth_id]" 
                                                                       value="<?php echo htmlspecialchars($config['auth_id'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Auth Token</label>
                                                                <input type="password" class="form-control" name="config[auth_token]" 
                                                                       value="<?php echo htmlspecialchars($config['auth_token'] ?? ''); ?>">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-3">
                                                                <label class="form-label">Source Number</label>
                                                                <input type="text" class="form-control" name="config[src]" 
                                                                       value="<?php echo htmlspecialchars($config['src'] ?? ''); ?>" 
                                                                       placeholder="+**********">
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-3">
                                                            <input class="form-check-input" type="checkbox" name="is_active" 
                                                                   id="active_<?php echo $provider->id; ?>" 
                                                                   <?php echo $provider->is_active ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="active_<?php echo $provider->id; ?>">
                                                                Active
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch mb-3">
                                                            <input class="form-check-input" type="checkbox" name="is_default" 
                                                                   id="default_<?php echo $provider->id; ?>" 
                                                                   <?php echo $provider->is_default ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="default_<?php echo $provider->id; ?>">
                                                                Default Provider
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <div class="d-grid">
                                                    <button type="submit" class="btn btn-success">
                                                        <i class="fas fa-save me-2"></i>Update <?php echo htmlspecialchars($provider->name); ?>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Cron Job Instructions -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>Cron Job Setup
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-3">Add this cron job to process notifications every 5 minutes:</p>
                    <div class="bg-dark text-light p-3 rounded mb-3">
                        <code>*/5 * * * * curl -s "<?php echo BASE_URL; ?>/notification/process?key=<?php echo hash('sha256', 'notification_cron_' . date('Y-m-d')); ?>"</code>
                    </div>
                    <p class="small text-muted">
                        <strong>Note:</strong> The security key changes daily for security. 
                        You can also create a PHP script to call this endpoint.
                    </p>
                    
                    <h6 class="mt-4">Alternative PHP Script:</h6>
                    <div class="bg-dark text-light p-3 rounded">
                        <code>
                            &lt;?php<br>
                            $key = hash('sha256', 'notification_cron_' . date('Y-m-d'));<br>
                            $url = "<?php echo BASE_URL; ?>/notification/process?key=" . $key;<br>
                            file_get_contents($url);<br>
                            ?&gt;
                        </code>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Card -->
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Notification Statistics
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary"><?php echo $stats['pending'] ?? 0; ?></h4>
                                <small class="text-muted">Pending</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success"><?php echo $stats['sent_today'] ?? 0; ?></h4>
                            <small class="text-muted">Sent Today</small>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-warning"><?php echo $stats['failed'] ?? 0; ?></h4>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info"><?php echo $stats['subscriptions'] ?? 0; ?></h4>
                            <small class="text-muted">Active Subscriptions</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test SMS Modal -->
<div class="modal fade" id="testSmsModal" tabindex="-1" aria-labelledby="testSmsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testSmsModalLabel">
                    <i class="fas fa-paper-plane me-2"></i>Test SMS Notification
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?php echo BASE_URL; ?>/notification/test" method="post">
                <div class="modal-body">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <input type="hidden" name="type" value="sms">
                    
                    <div class="mb-3">
                        <label for="test_user_id" class="form-label">Select User</label>
                        <select class="form-select" id="test_user_id" name="user_id" required>
                            <option value="">Choose a user...</option>
                            <?php foreach ($users as $user): ?>
                                <?php if (!empty($user->phone)): ?>
                                    <option value="<?php echo $user->id; ?>">
                                        <?php echo htmlspecialchars($user->name); ?> (<?php echo htmlspecialchars($user->phone); ?>)
                                    </option>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Only users with phone numbers are shown</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="test_subject" class="form-label">Subject</label>
                        <input type="text" class="form-control" id="test_subject" name="subject" 
                               value="Test SMS Notification" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="test_message" class="form-label">Message</label>
                        <textarea class="form-control" id="test_message" name="message" rows="3" required>This is a test SMS notification from your events system.</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>Send Test SMS
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
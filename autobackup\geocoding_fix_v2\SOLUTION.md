# Geocoding Fix Solution

## Problem Identified
The geocoding functionality in the CalendarController is not working the same way as the admin batch geocoding tool, even though both are using the `geocodeEvent()` function.

## Root Cause
After examining the code, I found that while both the CalendarController and the admin batch tool use the `geocodeEvent()` function, there are differences in how the address data is being prepared:

1. In the admin batch tool (AdminController.php), a new array is created with all address fields explicitly set with defaults
2. In the CalendarController, the original data array is passed directly to `geocodeEvent()`

## Solution
The fix requires modifying two sections in the CalendarController.php file:

### 1. In the createEvent method (around line 410-433)

Replace:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'createEvent');
   
    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}
```

With:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - ensure all fields are properly set with defaults
    // This matches exactly how the admin batch tool prepares the data
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? '',
        'lat' => $data['lat'] ?? null,
        'lng' => $data['lng'] ?? null
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'createEvent');
    
    // Update the original data with the geocoded coordinates
    $data['lat'] = $eventData['lat'];
    $data['lng'] = $eventData['lng'];
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}
```

### 2. In the editEvent method (around line 584-607)

Replace:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Use the enhanced geocodeEvent function instead of geocodeAddress
    // This provides better fallback mechanisms and error handling
    $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
   
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
```

With:
```php
// Automatically geocode address if needed
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - ensure all fields are properly set with defaults
    // This matches exactly how the admin batch tool prepares the data
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? '',
        'lat' => $data['lat'] ?? null,
        'lng' => $data['lng'] ?? null
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'editEvent', $id);
    
    // Update the original data with the geocoded coordinates
    $data['lat'] = $eventData['lat'];
    $data['lng'] = $eventData['lng'];
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
```

## Why This Fixes the Issue
This solution ensures that the address data is prepared in exactly the same way in both the CalendarController and the admin batch tool:

1. We create a separate `$eventData` array with all address fields explicitly set with defaults
2. We pass this array to `geocodeEvent()` instead of the original data array
3. We update the original data array with the geocoded coordinates from the result

This matches exactly how the admin batch tool prepares the data before geocoding, which should make the geocoding work consistently across all parts of the application.

## Implementation Notes
1. The changes maintain all existing functionality and logging
2. The fix is minimal and focused only on the geocoding process
3. No changes to the geocoding_helper.php file are needed

After implementing these changes, the geocoding in the CalendarController should work exactly like the admin batch geocoding tool.
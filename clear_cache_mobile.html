<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear Cache - Mobile</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f0f0f0;
            text-align: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin: 20px auto;
            max-width: 500px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
            font-size: 16px;
        }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 20px 30px;
            border-radius: 5px;
            font-size: 20px;
            cursor: pointer;
            margin: 10px;
            width: 100%;
            max-width: 300px;
        }
        button:hover {
            background: #c82333;
        }
        .step {
            background: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗑️ Clear All Cache</h1>
        <p>This will completely clear all cached files and force fresh loading.</p>
        
        <div id="status" class="status info">
            Ready to clear all cache...
        </div>
        
        <button onclick="clearEverything()">CLEAR ALL CACHE NOW</button>
        
        <div class="step">
            <h3>This will:</h3>
            <ol>
                <li>Clear all PWA caches</li>
                <li>Clear browser storage</li>
                <li>Unregister service worker</li>
                <li>Force reload with fresh files</li>
                <li>Load updated banner rotation code</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>After clearing:</h3>
            <p>You'll be redirected to the main site. Test the camera banner rotation - it should work without debug messages.</p>
        </div>
    </div>

    <script>
        async function clearEverything() {
            const statusDiv = document.getElementById('status');
            
            try {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '🗑️ Clearing all caches...';
                
                // Clear all caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    await Promise.all(
                        cacheNames.map(cacheName => caches.delete(cacheName))
                    );
                }
                
                statusDiv.textContent = '🗑️ Clearing storage...';
                
                // Clear all storage
                localStorage.clear();
                sessionStorage.clear();
                
                // Clear IndexedDB
                if ('indexedDB' in window) {
                    try {
                        const databases = await indexedDB.databases();
                        await Promise.all(
                            databases.map(db => {
                                return new Promise((resolve) => {
                                    const deleteReq = indexedDB.deleteDatabase(db.name);
                                    deleteReq.onsuccess = () => resolve();
                                    deleteReq.onerror = () => resolve();
                                });
                            })
                        );
                    } catch (e) {
                        // Ignore errors
                    }
                }
                
                statusDiv.textContent = '🗑️ Unregistering service worker...';
                
                // Unregister service worker
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    for (let registration of registrations) {
                        await registration.unregister();
                    }
                }
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ All cache cleared! Redirecting...';
                
                // Force reload with cache bypass
                setTimeout(() => {
                    // Use multiple cache-busting parameters
                    const timestamp = Date.now();
                    const random = Math.random().toString(36).substring(7);
                    window.location.href = `/?nocache=${timestamp}&refresh=${random}&v=1.0.30`;
                }, 2000);
                
            } catch (error) {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '⚠️ Cache clearing completed with warnings. Redirecting...';
                
                setTimeout(() => {
                    window.location.href = '/?force=' + Date.now();
                }, 3000);
            }
        }
    </script>
</body>
</html>

# Shows Geocoding Fix

This fix addresses an issue where show addresses were not being geocoded properly. The main problem was that the shows table was missing the `lat` and `lng` columns needed to store the geocoded coordinates.

## Changes Made

1. Added detailed logging to the ShowModel's updateShow method to better diagnose geocoding issues
2. Created a SQL script to add the missing lat and lng columns to the shows table
3. Created a test script to verify that geocoding works for a specific show
4. Created a fix script that can be run from the Admin Script Runner to add the missing columns

## Files Modified

- `models/ShowModel.php` - Added detailed logging to the geocoding process
- `sql/update_shows_geocoding.sql` - SQL script to add lat and lng columns
- `test/fix_shows_geocoding.php` - <PERSON><PERSON><PERSON> to add lat and lng columns via web interface
- `scripts/test_show_geocoding.php` - Script to test geocoding for a specific show

## How to Apply the Fix

1. Log in as an administrator
2. Go to Admin > Script Runner
3. Find and run the "Fix Shows Geocoding" script
4. The script will add the lat and lng columns to the shows table if they don't exist
5. After running the script, edit a show to trigger the geocoding process

## Verification

After applying the fix, you can verify that it worked by:

1. Editing a show with a valid address
2. Checking the database to see if the lat and lng columns have values
3. If needed, you can run the test_show_geocoding.php script to test geocoding for a specific show

## Troubleshooting

If geocoding still doesn't work after applying the fix, check the following:

1. Make sure the geocoding_helper.php file exists in the helpers directory
2. Check that the shows table has lat and lng columns
3. Verify that the address information is complete enough for geocoding
4. Check the error logs for any geocoding-related errors
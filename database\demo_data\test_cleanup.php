<?php
/**
 * Test Comprehensive Cleanup Functionality
 * 
 * This script tests the enhanced cleanup functionality to ensure
 * it properly removes all demo data including failed generation attempts.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once 'generate_demo_data.php';

echo "<h1>Comprehensive Cleanup Test</h1>";
echo "<p>Testing the enhanced cleanup functionality...</p>";

try {
    $db = new Database();
    $generator = new DemoDataGenerator();
    
    echo "<h2>Before Cleanup - Current Database State</h2>";
    
    // Check current data counts
    $tables = ['users', 'shows', 'vehicles', 'registrations', 'payments', 'judging_scores', 'fan_votes', 'images'];
    $beforeCounts = [];
    
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM {$table}");
            $result = $db->single();
            $beforeCounts[$table] = $result->count ?? 0;
        } catch (Exception $e) {
            $beforeCounts[$table] = 'N/A';
        }
    }
    
    // Check for demo users specifically
    $db->query("SELECT COUNT(*) as count FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
    $demoResult = $db->single();
    $demoUsers = $demoResult->count ?? 0;
    
    // Check for demo patterns
    $db->query("SELECT COUNT(*) as count FROM registrations WHERE registration_number LIKE 'RER-%'");
    $demoRegResult = $db->single();
    $demoRegistrations = $demoRegResult->count ?? 0;
    
    $db->query("SELECT COUNT(*) as count FROM payments WHERE payment_reference LIKE 'PAY-%'");
    $demoPayResult = $db->single();
    $demoPayments = $demoPayResult->count ?? 0;
    
    $db->query("SELECT COUNT(*) as count FROM images WHERE file_name LIKE 'demo_%'");
    $demoImgResult = $db->single();
    $demoImages = $demoImgResult->count ?? 0;
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table</th><th>Total Records</th></tr>";
    foreach ($beforeCounts as $table => $count) {
        echo "<tr><td>{$table}</td><td>{$count}</td></tr>";
    }
    echo "</table>";
    
    echo "<h3>Demo Data Detected</h3>";
    echo "<ul>";
    echo "<li><strong>Demo Users:</strong> {$demoUsers}</li>";
    echo "<li><strong>Demo Registrations:</strong> {$demoRegistrations}</li>";
    echo "<li><strong>Demo Payments:</strong> {$demoPayments}</li>";
    echo "<li><strong>Demo Images:</strong> {$demoImages}</li>";
    echo "</ul>";
    
    if ($demoUsers > 0 || $demoRegistrations > 0 || $demoPayments > 0 || $demoImages > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>🧹 Demo Data Found!</strong> The comprehensive cleanup will remove all detected demo data.";
        echo "</div>";
        
        echo "<h2>Test Actions</h2>";
        echo "<p><a href='?action=test_cleanup' class='btn btn-danger'>🧹 Run Comprehensive Cleanup Test</a></p>";
        echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Status</a></p>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
        echo "<strong>✅ Clean Database!</strong> No demo data detected in the database.";
        echo "</div>";
        
        echo "<h2>Test Options</h2>";
        echo "<p><a href='index.php?action=generate' class='btn btn-primary'>🚀 Generate Demo Data First</a></p>";
        echo "<p><a href='?' class='btn btn-secondary'>🔄 Refresh Status</a></p>";
    }
    
    // Handle cleanup test
    if (isset($_GET['action']) && $_GET['action'] === 'test_cleanup') {
        echo "<hr>";
        echo "<h2>Running Comprehensive Cleanup Test</h2>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
        echo "<pre>";
        
        $deletedCount = $generator->cleanupDemoData(true);
        
        echo "</pre>";
        echo "</div>";
        
        echo "<h2>After Cleanup - Database State</h2>";
        
        // Check data counts after cleanup
        $afterCounts = [];
        foreach ($tables as $table) {
            try {
                $db->query("SELECT COUNT(*) as count FROM {$table}");
                $result = $db->single();
                $afterCounts[$table] = $result->count ?? 0;
            } catch (Exception $e) {
                $afterCounts[$table] = 'N/A';
            }
        }
        
        // Check for remaining demo data
        $db->query("SELECT COUNT(*) as count FROM users WHERE email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com'");
        $remainingDemoUsers = $db->single()->count ?? 0;
        
        $db->query("SELECT COUNT(*) as count FROM registrations WHERE registration_number LIKE 'RER-%'");
        $remainingDemoRegs = $db->single()->count ?? 0;
        
        $db->query("SELECT COUNT(*) as count FROM payments WHERE payment_reference LIKE 'PAY-%'");
        $remainingDemoPayments = $db->single()->count ?? 0;
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Table</th><th>Before</th><th>After</th><th>Difference</th></tr>";
        foreach ($beforeCounts as $table => $beforeCount) {
            $afterCount = $afterCounts[$table];
            $difference = is_numeric($beforeCount) && is_numeric($afterCount) ? ($beforeCount - $afterCount) : 'N/A';
            
            $diffColor = $difference > 0 ? 'color: green;' : ($difference < 0 ? 'color: red;' : '');
            
            echo "<tr>";
            echo "<td>{$table}</td>";
            echo "<td>{$beforeCount}</td>";
            echo "<td>{$afterCount}</td>";
            echo "<td style='{$diffColor}'>{$difference}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Cleanup Results</h3>";
        echo "<ul>";
        echo "<li><strong>Total Records Deleted:</strong> {$deletedCount}</li>";
        echo "<li><strong>Remaining Demo Users:</strong> {$remainingDemoUsers}</li>";
        echo "<li><strong>Remaining Demo Registrations:</strong> {$remainingDemoRegs}</li>";
        echo "<li><strong>Remaining Demo Payments:</strong> {$remainingDemoPayments}</li>";
        echo "</ul>";
        
        if ($remainingDemoUsers == 0 && $remainingDemoRegs == 0 && $remainingDemoPayments == 0) {
            echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>🎉 Cleanup Successful!</strong> All demo data has been completely removed.";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
            echo "<strong>⚠️ Cleanup Incomplete!</strong> Some demo data may still remain.";
            echo "</div>";
        }
        
        echo "<p><a href='?' class='btn btn-primary'>🔄 Refresh Status</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
}

th, td {
    padding: 8px 12px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
}

.btn-primary { background-color: #007bff; color: white; }
.btn-secondary { background-color: #6c757d; color: white; }
.btn-danger { background-color: #dc3545; color: white; }

.btn:hover { opacity: 0.8; }
</style>

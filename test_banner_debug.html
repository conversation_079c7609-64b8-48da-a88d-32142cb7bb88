<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Banner Debug Test - API Fixed</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-banner-container {
            height: 80px;
            border: 2px solid #007bff;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            margin: 10px 0;
            position: relative;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .api-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .btn-test {
            margin: 5px;
            padding: 10px 20px;
        }
    </style>
</head>
<body data-debug-mode="true">
    <div class="debug-container">
        <h1><i class="fas fa-bug"></i> Camera Banner Debug Test</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>API Test</h3>
                <div class="api-test">
                    <button onclick="testAPI()" class="btn btn-primary btn-test">Test Banner API</button>
                    <button onclick="testInstallation()" class="btn btn-warning btn-test">Test Installation</button>
                    <div id="apiStatus"></div>
                </div>
                
                <h3>Banner System Test</h3>
                <div class="test-banner-container">
                    <div id="test-banner-content">
                        <div class="banner-text">Test Container Ready</div>
                    </div>
                </div>
                
                <button onclick="startBannerTest()" class="btn btn-success btn-test">Start Banner Rotation</button>
                <button onclick="stopBannerTest()" class="btn btn-danger btn-test">Stop Banner Rotation</button>
                <button onclick="enableDebug()" class="btn btn-info btn-test">Enable Debug Mode</button>
            </div>
            
            <div class="col-md-6">
                <h3>Console Output</h3>
                <div class="console-output" id="consoleOutput">
                    Console output will appear here...
                </div>
                <button onclick="clearConsole()" class="btn btn-secondary btn-test">Clear Console</button>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h3>Camera Modal Test</h3>
                <button onclick="testCameraModal()" class="btn btn-primary btn-test">
                    <i class="fas fa-camera"></i> Test Camera Modal
                </button>
                <button onclick="testQRModal()" class="btn btn-success btn-test">
                    <i class="fas fa-qrcode"></i> Test QR Scanner Modal
                </button>
            </div>
        </div>
    </div>

    <!-- Hidden file input for camera test -->
    <input type="file" name="test_camera_input" style="display: none;" accept="image/*">

    <script src="public/js/camera-banner.js"></script>
    <script src="public/js/pwa-features.js"></script>
    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        
        function logToConsole(message, type = 'log') {
            const consoleDiv = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : '#00ff00';
            
            consoleDiv.innerHTML += `<div style="color: ${color};">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
            
            // Also log to real console
            if (type === 'error') {
                originalError(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = function(...args) {
            logToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            logToConsole(args.join(' '), 'error');
        };
        
        // Test functions
        async function testAPI() {
            const statusDiv = document.getElementById('apiStatus');
            statusDiv.innerHTML = '<div class="alert alert-info">Testing API...</div>';
            
            try {
                const response = await fetch('/api/camera-banners.php');
                const data = await response.json();
                
                if (data.success) {
                    statusDiv.innerHTML = `
                        <div class="alert alert-success">✅ API Working</div>
                        <small>Found ${data.banners.length} banners, delay: ${data.delay}ms</small>
                    `;
                    console.log('API Response:', data);
                } else {
                    statusDiv.innerHTML = '<div class="alert alert-danger">❌ API Error</div>';
                    console.error('API Error:', data);
                }
            } catch (error) {
                statusDiv.innerHTML = '<div class="alert alert-danger">❌ API Failed</div>';
                console.error('API Request Failed:', error);
            }
        }
        
        async function testInstallation() {
            console.log('Testing camera banner installation...');
            
            // Check if table exists
            try {
                const response = await fetch('/api/camera-banners.php');
                const data = await response.json();
                
                if (data.success) {
                    console.log('✅ Camera banners table exists and working');
                    console.log('Banner count:', data.banners.length);
                    console.log('Delay setting:', data.delay);
                } else {
                    console.error('❌ Camera banners not properly installed');
                }
            } catch (error) {
                console.error('❌ Installation test failed:', error);
            }
        }
        
        function startBannerTest() {
            console.log('Starting banner rotation test...');
            
            if (window.cameraBanner) {
                console.log('✅ Camera banner system found');
                window.cameraBanner.startRotation('test-banner-content');
            } else {
                console.error('❌ Camera banner system not loaded');
            }
        }
        
        function stopBannerTest() {
            console.log('Stopping banner rotation test...');
            
            if (window.cameraBanner) {
                window.cameraBanner.stopRotation();
            }
        }
        
        function enableDebug() {
            localStorage.setItem('camera_banner_debug', 'true');
            console.log('Debug mode enabled - reload page to take effect');
        }
        
        function clearConsole() {
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        function testCameraModal() {
            console.log('Testing camera modal...');
            if (window.pwaFeatures) {
                window.pwaFeatures.openCamera('test_camera_input');
            } else {
                console.error('PWA Features not loaded');
            }
        }
        
        function testQRModal() {
            console.log('Testing QR scanner modal...');
            if (window.pwaFeatures) {
                window.pwaFeatures.openQRScanner();
            } else {
                console.error('PWA Features not loaded');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== Camera Banner Debug Test Started ===');
            
            setTimeout(() => {
                if (window.cameraBanner) {
                    console.log('✅ Camera banner system loaded');
                    console.log('Banner count:', window.cameraBanner.banners.length);
                } else {
                    console.error('❌ Camera banner system not loaded');
                }
                
                if (window.pwaFeatures) {
                    console.log('✅ PWA Features loaded');
                } else {
                    console.error('❌ PWA Features not loaded');
                }
            }, 2000);
        });
    </script>
</body>
</html>
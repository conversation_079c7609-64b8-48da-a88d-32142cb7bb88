<?php
/**
 * Registration Model
 * 
 * This model handles all database operations related to vehicle registrations.
 */
class RegistrationModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Ensure all required fields are present in the registration object
     * 
     * @param object $registration Registration object to check
     * @return void
     */
    private function ensureRequiredFields(&$registration) {
        // Basic registration fields
        if (!isset($registration->id)) $registration->id = 0;
        if (!isset($registration->status)) $registration->status = 'unknown';
        
        // Vehicle fields
        if (!isset($registration->make)) $registration->make = 'Unknown Make';
        if (!isset($registration->model)) $registration->model = 'Unknown Model';
        if (!isset($registration->year)) $registration->year = '';
        if (!isset($registration->color)) $registration->color = '';
        if (!isset($registration->owner_id)) $registration->owner_id = 0;
        
        // User fields
        if (!isset($registration->owner_name)) $registration->owner_name = 'Unknown Owner';
        
        // Show fields
        if (!isset($registration->show_name)) $registration->show_name = 'Unknown Show';
        if (!isset($registration->start_date)) $registration->start_date = '';
        if (!isset($registration->end_date)) $registration->end_date = '';
        if (!isset($registration->show_location)) $registration->show_location = '';
        
        // Category fields
        if (!isset($registration->category_name)) $registration->category_name = 'Unknown Category';
        
        // Date fields
        if (!isset($registration->created_at)) {
            if (isset($registration->registration_date)) {
                $registration->created_at = $registration->registration_date;
            } else {
                $registration->created_at = date('Y-m-d H:i:s');
            }
        }
    }
    
    /**
     * Get all registrations for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function getShowRegistrations($showId) {
        try {
            // First try with registration_number column
            $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id,
                            u.name as owner_name, sc.name as category_name,
                            s.name as show_name, s.start_date, s.end_date, s.location as show_location
                            FROM registrations r 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN users u ON v.owner_id = u.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            JOIN shows s ON r.show_id = s.id
                            WHERE r.show_id = :show_id 
                            ORDER BY r.registration_number');
            $this->db->bind(':show_id', $showId);
            
            $result = $this->db->resultSet();
            
            // Ensure all required fields are present
            if (is_array($result)) {
                foreach ($result as &$registration) {
                    $this->ensureRequiredFields($registration);
                }
            }
            
            return $result;
        } catch (Exception $e) {
            // If registration_number column doesn't exist, use id for ordering
            error_log('Error in getShowRegistrations: ' . $e->getMessage() . '. Trying alternative query.');
            
            try {
                $this->db->query('SELECT r.*, v.make, v.model, v.year, v.color, v.owner_id,
                                u.name as owner_name, sc.name as category_name,
                                s.name as show_name, s.start_date, s.end_date, s.location as show_location
                                FROM registrations r 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN users u ON v.owner_id = u.id 
                                JOIN show_categories sc ON r.category_id = sc.id 
                                JOIN shows s ON r.show_id = s.id
                                WHERE r.show_id = :show_id 
                                ORDER BY r.id');
                $this->db->bind(':show_id', $showId);
                
                $result = $this->db->resultSet();
                
                // Ensure all required fields are present
                if (is_array($result)) {
                    foreach ($result as &$registration) {
                        $this->ensureRequiredFields($registration);
                    }
                }
                
                return $result;
            } catch (Exception $e2) {
                error_log('Error in getShowRegistrations fallback: ' . $e2->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Get registrations for a user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getUserRegistrations($userId) {
        try {
            // Try to select with checked_in column
            $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                            s.location as show_location, r.checked_in,
                            v.make, v.model, v.year, sc.name as category_name 
                            FROM registrations r 
                            JOIN shows s ON r.show_id = s.id 
                            JOIN vehicles v ON r.vehicle_id = v.id 
                            JOIN show_categories sc ON r.category_id = sc.id 
                            WHERE v.owner_id = :user_id 
                            ORDER BY s.start_date DESC');
            $this->db->bind(':user_id', $userId);
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            try {
                // If there's an error (likely because checked_in doesn't exist), try without it
                $this->db->query('SELECT r.*, s.name as show_name, s.start_date, s.end_date, 
                                s.location as show_location, 0 as checked_in,
                                v.make, v.model, v.year, sc.name as category_name 
                                FROM registrations r 
                                JOIN shows s ON r.show_id = s.id 
                                JOIN vehicles v ON r.vehicle_id = v.id 
                                JOIN show_categories sc ON r.category_id = sc.id 
                                WHERE v.owner_id = :user_id 
                                ORDER BY s.start_date DESC');
                $this->db->bind(':user_id', $userId);
                
                $result = $this->db->resultSet();
                return is_array($result) ? $result : [];
            } catch (Exception $e) {
                // Log error if needed
                error_log('Error in getUserRegistrations: ' . $e->getMessage());
                return [];
            }
        }
    }
    
    /**
     * Public method to get category prefix for display number
     * 
     * @param int $categoryId Category ID
     * @return string Category prefix (e.g., "CL" for Classics)
     */
    public function getCategoryPrefixPublic($categoryId) {
        return $this->getCategoryPrefix($categoryId);
    }
    
    /**
     * Get category prefix for display number
     * 
     * @param int $categoryId Category ID
     * @return string Category prefix (e.g., "CL" for Classics)
     */
    private function getCategoryPrefix($categoryId) {
        try {
            // Get category name
            $this->db->query('SELECT name FROM show_categories WHERE id = :id');
            $this->db->bind(':id', $categoryId);
            $category = $this->db->single();
            
            if (!$category) {
                return 'X-'; // Default prefix if category not found
            }
            
            // Clean the category name - remove parentheses and special characters
            $cleanName = preg_replace('/\([^)]*\)/', '', $category->name); // Remove content in parentheses
            $cleanName = trim(preg_replace('/[^a-zA-Z0-9\s]/', '', $cleanName)); // Remove special characters
            
            // Get words from the cleaned name
            $words = explode(' ', $cleanName);
            $words = array_filter($words); // Remove empty elements
            $prefix = '';
            
            // Handle different category naming patterns
            if (count($words) == 0) {
                // If no valid words after cleaning, use first two letters of original name
                $prefix = strtoupper(substr(preg_replace('/[^a-zA-Z0-9]/', '', $category->name), 0, 2));
            }
            else if (count($words) == 1) {
                // For single words, use first two letters
                $prefix = strtoupper(substr($words[array_key_first($words)], 0, 2));
            }
            else if (count($words) == 2) {
                // For two words, use first letter of each
                $prefix = strtoupper(
                    substr($words[array_key_first($words)], 0, 1) . 
                    substr($words[array_key_first($words) + 1], 0, 1)
                );
            }
            else {
                // For multiple words, use a more distinctive approach
                // If first word is short (like "The", "A", etc.), use first letter of first word and first letter of second word
                if (strlen($words[array_key_first($words)]) <= 3) {
                    $prefix = strtoupper(
                        substr($words[array_key_first($words)], 0, 1) . 
                        substr($words[array_key_first($words) + 1], 0, 1)
                    );
                } 
                // Otherwise use first letter of first word and first letter of last significant word
                else {
                    $prefix = strtoupper(
                        substr($words[array_key_first($words)], 0, 1) . 
                        substr($words[array_key_last($words)], 0, 1)
                    );
                }
            }
            
            // Special case handling for common categories
            $lowerName = strtolower($category->name);
            if (strpos($lowerName, 'classic') !== false) {
                $prefix = 'CL';
            } else if (strpos($lowerName, 'muscle') !== false) {
                $prefix = 'MU';
            } else if (strpos($lowerName, 'truck') !== false || strpos($lowerName, 'suv') !== false) {
                $prefix = 'TR';
            } else if (strpos($lowerName, 'motorcycle') !== false || strpos($lowerName, 'bike') !== false) {
                $prefix = 'MC';
            } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'asian') !== false) {
                $prefix = 'AI';
            } else if (strpos($lowerName, 'import') !== false && strpos($lowerName, 'european') !== false) {
                $prefix = 'EI';
            } else if (strpos($lowerName, 'modern') !== false) {
                $prefix = 'MO';
            } else if (strpos($lowerName, 'custom') !== false || strpos($lowerName, 'modified') !== false) {
                $prefix = 'CM';
            } else if (strpos($lowerName, 'contemporary') !== false) {
                $prefix = 'CT';
            } else if (strpos($lowerName, 'antique') !== false) {
                $prefix = 'AN';
            } else if (strpos($lowerName, 'vintage') !== false) {
                $prefix = 'VN';
            } else if (strpos($lowerName, 'sport') !== false) {
                $prefix = 'SP';
            } else if (strpos($lowerName, 'luxury') !== false) {
                $prefix = 'LX';
            } else if (strpos($lowerName, 'electric') !== false || strpos($lowerName, 'ev') !== false) {
                $prefix = 'EV';
            } else if (strpos($lowerName, 'hybrid') !== false) {
                $prefix = 'HY';
            } else if (strpos($lowerName, 'convertible') !== false) {
                $prefix = 'CV';
            } else if (strpos($lowerName, 'coupe') !== false) {
                $prefix = 'CP';
            } else if (strpos($lowerName, 'sedan') !== false) {
                $prefix = 'SD';
            } else if (strpos($lowerName, 'race') !== false || strpos($lowerName, 'racing') !== false) {
                $prefix = 'RC';
            } else if (strpos($lowerName, 'off') !== false && strpos($lowerName, 'road') !== false) {
                $prefix = 'OR';
            }
            
            // For custom categories, ensure we have a meaningful 2-letter prefix
            if (strlen($prefix) != 2) {
                // If we have a single letter, add the next most significant letter
                if (strlen($prefix) == 1) {
                    // Try to find a second significant letter
                    if (count($words) > 1) {
                        // Use the first letter of the second word
                        $prefix .= strtoupper(substr($words[array_key_first($words) + 1], 0, 1));
                    } else if (strlen($words[array_key_first($words)]) > 1) {
                        // Use the second letter of the first word
                        $prefix .= strtoupper(substr($words[array_key_first($words)], 1, 1));
                    } else {
                        // Fallback: add X
                        $prefix .= 'X';
                    }
                } else if (strlen($prefix) > 2) {
                    // If we have more than 2 letters, truncate to 2
                    $prefix = substr($prefix, 0, 2);
                } else {
                    // If we have no letters, use default
                    $prefix = 'XX';
                }
            }
            
            return $prefix;
        } catch (Exception $e) {
            error_log('Error in getCategoryPrefix: ' . $e->getMessage());
            return 'XX'; // Default prefix if there's an error
        }
    }
    
    /**
     * Count registrations by category for a show
     * 
     * @param int $showId Show ID
     * @return array
     */
    public function countRegistrationsByCategory($showId) {
        try {
            // Try with max_entries column
            $this->db->query('SELECT sc.id, sc.name as category_name, COUNT(r.id) as count, sc.max_entries 
                            FROM show_categories sc 
                            LEFT JOIN registrations r ON sc.id = r.category_id AND r.status = :status 
                            WHERE sc.show_id = :show_id 
                            GROUP BY sc.id, sc.name, sc.max_entries 
                            ORDER BY sc.name');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            // If max_entries column doesn't exist, use a default value of 0
            error_log('Error in countRegistrationsByCategory: ' . $e->getMessage() . '. Using fallback query.');
            
            $this->db->query('SELECT sc.id, sc.name as category_name, COUNT(r.id) as count, 0 as max_entries 
                            FROM show_categories sc 
                            LEFT JOIN registrations r ON sc.id = r.category_id AND r.status = :status 
                            WHERE sc.show_id = :show_id 
                            GROUP BY sc.id, sc.name 
                            ORDER BY sc.name');
            $this->db->bind(':show_id', $showId);
            $this->db->bind(':status', 'approved');
            
            return $this->db->resultSet();
        }
    }
}
<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.1 - Fixed transaction method names
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $userModel;
    private $showModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in for most calendar actions
        // Individual methods will check permissions as needed
        
        // Load models
        $this->calendarModel = $this->model('CalendarModel');
        $this->userModel = $this->model('UserModel');
        $this->showModel = $this->model('ShowModel');
    }
    
    /**
     * Calendar index - displays the main calendar view
     * 
     * @return void
     */
    public function index() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }
        
        // Get all calendars
        $calendars = $this->calendarModel->getCalendars();
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars
        ];
        
        $this->view('calendar/index', $data);
    }
    
    /**
     * Display the map view of events
     *
     * @return void
     */
    public function map()
    {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('users/login');
        }
        
        // Get all calendars
        $calendars = $this->calendarModel->getCalendars();
        
        // Get map provider settings
        $mapSettings = $this->calendarModel->getMapProviderSettings();
        
        $data = [
            'title' => 'Calendar Map View',
            'calendars' => $calendars,
            'mapSettings' => $mapSettings
        ];
        
        $this->view('calendar/map', $data);
    }
    
    /**
     * Manage map settings
     *
     * @return void
     */
    public function mapSettings()
    {
        // Check if user is logged in and is admin
        if (!isLoggedIn() || !isAdmin()) {
            redirect('users/login');
        }
        
        // Get current map settings
        $mapSettings = $this->calendarModel->getMapProviderSettings();
        
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $data = [
                'provider' => trim($_POST['provider']),
                'api_key' => trim($_POST['api_key']),
                'default_zoom' => (int)$_POST['default_zoom'],
                'default_lat' => (float)$_POST['default_lat'],
                'default_lng' => (float)$_POST['default_lng'],
                'filter_radius' => (int)$_POST['filter_radius'],
                'tile_url' => trim($_POST['tile_url']),
                'attribution' => trim($_POST['attribution']),
                'errors' => []
            ];
            
            // Validate provider
            if (empty($data['provider'])) {
                $data['errors']['provider'] = 'Please select a map provider';
            }
            
            // Validate API key for providers that require it
            if (in_array($data['provider'], ['google', 'mapbox', 'here']) && empty($data['api_key'])) {
                $data['errors']['api_key'] = 'API key is required for this provider';
            }
            
            // Validate tile URL for OpenStreetMap
            if ($data['provider'] == 'openstreetmap' && empty($data['tile_url'])) {
                $data['errors']['tile_url'] = 'Tile URL is required for OpenStreetMap';
            }
            
            // If no errors, update settings
            if (empty($data['errors'])) {
                if ($this->calendarModel->updateMapProviderSettings($data)) {
                    flash('calendar_message', 'Map settings updated successfully', 'alert alert-success');
                    redirect('calendar/mapSettings');
                } else {
                    flash('calendar_message', 'Failed to update map settings', 'alert alert-danger');
                }
            }
        } else {
            $data = $mapSettings;
            $data['errors'] = [];
        }
        
        $data['title'] = 'Map Settings';
        
        $this->view('calendar/map_settings', $data);
    }
}
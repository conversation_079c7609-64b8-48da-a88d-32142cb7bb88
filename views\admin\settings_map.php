<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Map Settings</h1>
            <p class="text-muted">Configure map provider settings and location services</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_calendar" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Calendar Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Map settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form action="<?php echo BASE_URL; ?>/admin/settings_map" method="POST">
        <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
        
        <div class="row">
            <!-- Map Provider Settings -->
            <div class="col-md-8 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-primary text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-map-marked-alt me-2"></i> Map Provider Settings</h3>
                    </div>
                    <div class="card-body p-4">
                        <div class="mb-3">
                            <label for="map_provider" class="form-label">Map Provider</label>
                            <select class="form-select" id="map_provider" name="map_provider">
                                <option value="openstreetmap" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'openstreetmap' ? 'selected' : ''; ?>>OpenStreetMap (Free)</option>
                                <option value="google" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'google' ? 'selected' : ''; ?>>Google Maps (Paid)</option>
                                <option value="mapbox" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'mapbox' ? 'selected' : ''; ?>>Mapbox (Paid)</option>
                                <option value="here" <?php echo isset($mapSettings['provider']) && $mapSettings['provider'] == 'here' ? 'selected' : ''; ?>>HERE Maps (Paid)</option>
                            </select>
                            <div class="form-text">The map provider to use for displaying event locations</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_api_key" class="form-label">API Key (Client-Side)</label>
                            <input type="text" class="form-control" id="map_api_key" name="map_api_key" value="<?php echo isset($mapSettings['api_key']) ? $mapSettings['api_key'] : ''; ?>">
                            <div class="form-text">
                                <strong>For Google:</strong> Maps JavaScript API key with HTTP referrer restrictions.<br>
                                <strong>For others:</strong> Main API key for the selected provider.
                            </div>
                        </div>
                        
                        <div class="mb-3 google-server-key" style="display: <?php echo (isset($mapSettings['provider']) && $mapSettings['provider'] == 'google') ? 'block' : 'none'; ?>;">
                            <label for="map_server_api_key" class="form-label">Server-Side API Key <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="map_server_api_key" name="map_server_api_key" value="<?php echo isset($mapSettings['server_api_key']) ? $mapSettings['server_api_key'] : ''; ?>">
                            <div class="form-text">
                                <strong>Google Places & Geocoding API key</strong> with IP address restrictions (no referrer restrictions).
                            </div>
                        </div>
                        
                        <div class="mb-3 google-api-info" style="display: <?php echo (isset($mapSettings['provider']) && $mapSettings['provider'] == 'google') ? 'block' : 'none'; ?>;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Google API Key Requirements:</h6>
                                <ul class="mb-0 small">
                                    <li><strong>Client-Side Key:</strong> Maps JavaScript API with HTTP referrer restrictions</li>
                                    <li><strong>Server-Side Key:</strong> Places API + Geocoding API with IP address restrictions</li>
                                    <li>Both keys must be enabled for the same Google Cloud project</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_lat" class="form-label">Default Latitude</label>
                            <input type="text" class="form-control" id="map_default_lat" name="map_default_lat" value="<?php echo isset($mapSettings['default_lat']) ? $mapSettings['default_lat'] : '39.8283'; ?>">
                            <div class="form-text">Default center latitude for the map (e.g., 39.8283 for center of US)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_lng" class="form-label">Default Longitude</label>
                            <input type="text" class="form-control" id="map_default_lng" name="map_default_lng" value="<?php echo isset($mapSettings['default_lng']) ? $mapSettings['default_lng'] : '-98.5795'; ?>">
                            <div class="form-text">Default center longitude for the map (e.g., -98.5795 for center of US)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_default_zoom" class="form-label">Default Zoom Level</label>
                            <input type="number" class="form-control" id="map_default_zoom" name="map_default_zoom" value="<?php echo isset($mapSettings['default_zoom']) ? $mapSettings['default_zoom'] : '4'; ?>" min="1" max="20">
                            <div class="form-text">Default zoom level for the map (1-20, where 1 is zoomed out)</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="map_filter_radius" class="form-label">Default Filter Radius (miles)</label>
                            <input type="number" class="form-control" id="map_filter_radius" name="map_filter_radius" value="<?php echo isset($mapSettings['filter_radius']) ? $mapSettings['filter_radius'] : '100'; ?>" min="1" max="1000">
                            <div class="form-text">Default radius for location-based filtering in miles</div>
                        </div>
                        
                        <div id="openstreetmap_settings" class="<?php echo (!isset($mapSettings['provider']) || $mapSettings['provider'] == 'openstreetmap') ? '' : 'd-none'; ?>">
                            <div class="mb-3">
                                <label for="map_tile_url" class="form-label">Tile URL (OpenStreetMap)</label>
                                <input type="text" class="form-control" id="map_tile_url" name="map_tile_url" value="<?php echo isset($mapSettings['tile_url']) ? $mapSettings['tile_url'] : 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'; ?>">
                                <div class="form-text">URL template for OpenStreetMap tiles</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="map_attribution" class="form-label">Attribution (OpenStreetMap)</label>
                                <input type="text" class="form-control" id="map_attribution" name="map_attribution" value="<?php echo isset($mapSettings['attribution']) ? $mapSettings['attribution'] : '&copy; <a href=&quot;https://www.openstreetmap.org/copyright&quot;>OpenStreetMap</a> contributors'; ?>">
                                <div class="form-text">Attribution text required by OpenStreetMap</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Information Panel -->
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-info text-white py-3">
                        <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Map Information</h3>
                    </div>
                    <div class="card-body p-4">
                        <h5>Map Providers</h5>
                        <ul>
                            <li><strong>OpenStreetMap:</strong> Free, open-source mapping</li>
                            <li><strong>Google Maps:</strong> High-quality, requires API key</li>
                            <li><strong>Mapbox:</strong> Customizable, requires API key</li>
                            <li><strong>HERE Maps:</strong> Enterprise-grade, requires API key</li>
                        </ul>
                        
                        <h5>Map Tools</h5>
                        <div class="d-grid gap-2">
                            <a href="<?php echo BASE_URL; ?>/admin/settings_map_tools" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-tools me-1"></i> Map Tools
                            </a>
                            <a href="<?php echo BASE_URL; ?>/calendar/map" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-map me-1"></i> View Map
                            </a>
                        </div>
                        
                        <div class="alert alert-warning mt-3">
                            <small><i class="fas fa-exclamation-triangle me-1"></i> Google Maps requires both client-side and server-side API keys for full functionality.</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end mb-5">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i> Save Settings
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide provider-specific settings based on provider selection
    const mapProviderSelect = document.getElementById('map_provider');
    const osmSettings = document.getElementById('openstreetmap_settings');
    const googleServerKeyField = document.querySelector('.google-server-key');
    const googleApiInfo = document.querySelector('.google-api-info');
    
    function toggleProviderSettings() {
        const provider = mapProviderSelect.value;
        
        // Hide all provider-specific settings
        osmSettings.classList.add('d-none');
        googleServerKeyField.style.display = 'none';
        googleApiInfo.style.display = 'none';
        
        // Show relevant settings based on provider
        if (provider === 'openstreetmap') {
            osmSettings.classList.remove('d-none');
        } else if (provider === 'google') {
            googleServerKeyField.style.display = 'block';
            googleApiInfo.style.display = 'block';
        }
    }
    
    // Initial setup
    toggleProviderSettings();
    
    // Listen for changes
    mapProviderSelect.addEventListener('change', toggleProviderSettings);
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
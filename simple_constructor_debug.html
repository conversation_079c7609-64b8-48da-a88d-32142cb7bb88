<!DOCTYPE html>
<html>
<head>
    <title>Simple Constructor Debug</title>
</head>
<body>
    <h2>Simple Constructor Debug</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the script and add simple debug lines
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Original script loaded');
                
                // Add debug logging right after the version line
                const modifiedCode = code.replace(
                    "this.version = '3.63.11-baseurl-fix'; // Set version first",
                    `this.version = '3.63.11-baseurl-fix'; // Set version first
                    window.versionDebug = 'Version set to: ' + this.version;
                    console.log('DEBUG: Version set to:', this.version);`
                ).replace(
                    "this.banners = [];",
                    `this.banners = [];
                    window.bannersDebug = 'Banners array created, length: ' + this.banners.length;
                    console.log('DEBUG: Banners array created');`
                ).replace(
                    "} catch (error) {",
                    `window.constructorSuccess = 'Constructor completed successfully';
                    console.log('DEBUG: Constructor completed successfully');
                } catch (error) {
                    window.constructorError = 'Constructor error: ' + error.message;`
                );
                
                output('Added debug logging, executing...');
                
                try {
                    eval(modifiedCode);
                    
                    output('Script executed successfully');
                    
                    // Show debug info
                    output('=== Debug Variables ===');
                    output('- versionDebug: ' + (window.versionDebug || 'NOT SET'));
                    output('- bannersDebug: ' + (window.bannersDebug || 'NOT SET'));
                    output('- constructorSuccess: ' + (window.constructorSuccess || 'NOT SET'));
                    output('- constructorError: ' + (window.constructorError || 'NOT SET'));
                    
                    if (window.cameraBanner) {
                        output('=== Instance Properties ===');
                        output('- version: ' + window.cameraBanner.version);
                        output('- banners length: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined'));
                        
                        // Check if properties exist at all
                        output('=== Property Check ===');
                        for (let prop in window.cameraBanner) {
                            if (window.cameraBanner.hasOwnProperty(prop)) {
                                output('- ' + prop + ': ' + typeof window.cameraBanner[prop] + ' = ' + window.cameraBanner[prop]);
                            }
                        }
                    }
                    
                } catch (error) {
                    output('Execution error: ' + error.message);
                    output('Stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
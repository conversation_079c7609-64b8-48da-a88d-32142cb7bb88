<?php
/**
 * Small Test for User Generator Fix
 * Tests the fixed user generator with just 5 users
 */

// Load configuration
require_once 'config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once 'core/Database.php';
require_once 'core/Auth.php';

// Include the fixed generator
require_once 'database/test_data/generate_test_users.php';

echo "<h1>Test User Generator - Small Test</h1>";
echo "<p>Testing the fixed generator with 5 users...</p>";

try {
    $generator = new TestUserGenerator();
    echo "<pre>";
    $result = $generator->generateUsers(5, true);
    echo "</pre>";
    
    echo "<p><strong>Test completed successfully!</strong></p>";
    echo "<p>Generated: {$result} users</p>";
    
} catch (Exception $e) {
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
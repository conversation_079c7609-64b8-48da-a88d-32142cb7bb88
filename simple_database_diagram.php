<?php
/**
 * Simple Database Diagram Generator
 * 
 * A simplified version that works with all MySQL versions
 * Focuses on table structure without complex foreign key detection
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

class SimpleDatabaseDiagram {
    private $db;
    private $tables = [];
    
    public function __construct() {
        try {
            $this->db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
            
            if ($this->db->connect_error) {
                throw new Exception("Connection failed: " . $this->db->connect_error);
            }
            
            $this->analyzeTables();
        } catch (Exception $e) {
            die("Error: " . $e->getMessage());
        }
    }
    
    private function analyzeTables() {
        $query = "SHOW TABLES";
        $result = $this->db->query($query);
        
        if (!$result) {
            throw new Exception("Failed to get table list: " . $this->db->error);
        }
        
        while ($row = $result->fetch_array()) {
            $tableName = $row[0];
            $this->tables[$tableName] = $this->getTableInfo($tableName);
        }
    }
    
    private function getTableInfo($tableName) {
        $info = [
            'columns' => [],
            'row_count' => 0,
            'primary_keys' => [],
            'indexes' => []
        ];
        
        // Get column information
        $query = "DESCRIBE `$tableName`";
        $result = $this->db->query($query);
        
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                $info['columns'][] = [
                    'name' => $row['Field'],
                    'type' => $row['Type'],
                    'null' => $row['Null'],
                    'key' => $row['Key'],
                    'default' => $row['Default']
                ];
                
                if ($row['Key'] === 'PRI') {
                    $info['primary_keys'][] = $row['Field'];
                }
            }
        }
        
        // Get row count
        try {
            $countQuery = "SELECT COUNT(*) as count FROM `$tableName`";
            $countResult = $this->db->query($countQuery);
            if ($countResult) {
                $countRow = $countResult->fetch_assoc();
                $info['row_count'] = $countRow['count'];
            }
        } catch (Exception $e) {
            $info['row_count'] = 'N/A';
        }
        
        return $info;
    }
    
    public function generateHTML() {
        $html = "<!DOCTYPE html>
<html>
<head>
    <title>Database Structure - " . DB_NAME . "</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .table-container { border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }
        .table-name { background: #007cba; color: white; padding: 10px; margin: -15px -15px 15px -15px; border-radius: 5px 5px 0 0; }
        .column { padding: 5px 0; border-bottom: 1px solid #eee; }
        .primary-key { background: #fff3cd; font-weight: bold; }
        .stats { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .summary { background: #d4edda; padding: 15px; margin: 20px 0; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Database Structure: " . DB_NAME . "</h1>
    <div class='summary'>
        <h2>📊 Database Summary</h2>
        <p><strong>Total Tables:</strong> " . count($this->tables) . "</p>
        <p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>
    </div>";
        
        // Table overview
        $html .= "<h2>📋 Table Overview</h2>";
        $html .= "<table>";
        $html .= "<tr><th>Table Name</th><th>Columns</th><th>Primary Keys</th><th>Row Count</th></tr>";
        
        foreach ($this->tables as $tableName => $info) {
            $columnCount = count($info['columns']);
            $primaryKeys = implode(', ', $info['primary_keys']);
            $rowCount = $info['row_count'];
            
            $html .= "<tr>";
            $html .= "<td><strong>$tableName</strong></td>";
            $html .= "<td>$columnCount</td>";
            $html .= "<td>$primaryKeys</td>";
            $html .= "<td>$rowCount</td>";
            $html .= "</tr>";
        }
        
        $html .= "</table>";
        
        // Detailed table structures
        $html .= "<h2>🗂️ Detailed Table Structures</h2>";
        
        foreach ($this->tables as $tableName => $info) {
            $html .= "<div class='table-container'>";
            $html .= "<div class='table-name'>$tableName</div>";
            
            $html .= "<div class='stats'>";
            $html .= "<strong>Columns:</strong> " . count($info['columns']) . " | ";
            $html .= "<strong>Primary Keys:</strong> " . (empty($info['primary_keys']) ? 'None' : implode(', ', $info['primary_keys'])) . " | ";
            $html .= "<strong>Rows:</strong> " . $info['row_count'];
            $html .= "</div>";
            
            foreach ($info['columns'] as $column) {
                $isPrimary = in_array($column['name'], $info['primary_keys']);
                $class = $isPrimary ? 'column primary-key' : 'column';
                
                $html .= "<div class='$class'>";
                $html .= "<strong>" . $column['name'] . "</strong> ";
                $html .= "<em>" . $column['type'] . "</em> ";
                $html .= ($column['null'] === 'NO' ? 'NOT NULL' : 'NULL') . " ";
                if ($column['key']) {
                    $html .= "<span style='color: #007cba;'>[" . $column['key'] . "]</span> ";
                }
                if ($column['default'] !== null) {
                    $html .= "DEFAULT: " . $column['default'];
                }
                $html .= "</div>";
            }
            
            $html .= "</div>";
        }
        
        $html .= "</body></html>";
        
        return $html;
    }
    
    public function generateSimpleList() {
        $output = "Database: " . DB_NAME . "\n";
        $output .= "Generated: " . date('Y-m-d H:i:s') . "\n";
        $output .= "Total Tables: " . count($this->tables) . "\n\n";
        
        foreach ($this->tables as $tableName => $info) {
            $output .= "Table: $tableName\n";
            $output .= "  Columns: " . count($info['columns']) . "\n";
            $output .= "  Rows: " . $info['row_count'] . "\n";
            $output .= "  Primary Keys: " . (empty($info['primary_keys']) ? 'None' : implode(', ', $info['primary_keys'])) . "\n";
            
            foreach ($info['columns'] as $column) {
                $output .= "    - " . $column['name'] . " (" . $column['type'] . ")";
                if (in_array($column['name'], $info['primary_keys'])) {
                    $output .= " [PRIMARY KEY]";
                }
                $output .= "\n";
            }
            $output .= "\n";
        }
        
        return $output;
    }
    
    public function saveToFile($content, $filename) {
        file_put_contents($filename, $content);
        echo "Saved to: $filename\n";
    }
}

// Main execution
try {
    $diagram = new SimpleDatabaseDiagram();
    
    // Get format from URL parameter or command line argument
    $format = 'html'; // default
    
    if (php_sapi_name() === 'cli') {
        // Command line execution
        $format = isset($argv[1]) ? $argv[1] : 'html';
    } else {
        // Web execution
        $format = isset($_GET['format']) ? $_GET['format'] : 'html';
    }
    
    switch ($format) {
        case 'html':
            $content = $diagram->generateHTML();
            if (php_sapi_name() === 'cli') {
                $diagram->saveToFile($content, 'simple_database_diagram.html');
            } else {
                header('Content-Type: text/html');
                echo $content;
            }
            break;
            
        case 'text':
            $content = $diagram->generateSimpleList();
            if (php_sapi_name() === 'cli') {
                $diagram->saveToFile($content, 'simple_database_diagram.txt');
            } else {
                header('Content-Type: text/plain');
                echo $content;
            }
            break;
            
        default:
            echo "Usage: Access with ?format=html or ?format=text\n";
            break;
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
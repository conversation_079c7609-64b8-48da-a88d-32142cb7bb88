<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Payment Settings</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/payment/methods" class="btn btn-info me-2">
                <i class="fas fa-credit-card me-2"></i> Payment Methods
            </a>
            <a href="<?php echo BASE_URL; ?>/payment/pending" class="btn btn-warning me-2">
                <i class="fas fa-clock me-2"></i> Pending Payments
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
        </div>
    </div>

    <?php if (hasFlashMessage('settings_success')) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo getFlashMessage('settings_success')['message']; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Payment Configuration</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/payment/settings" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">General Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="currency" class="form-label">Currency</label>
                                    <select class="form-select" id="currency" name="currency">
                                        <option value="USD" <?php echo (isset($data['payment_settings']['currency']) && $data['payment_settings']['currency'] == 'USD') ? 'selected' : ''; ?>>USD - US Dollar</option>
                                        <option value="EUR" <?php echo (isset($data['payment_settings']['currency']) && $data['payment_settings']['currency'] == 'EUR') ? 'selected' : ''; ?>>EUR - Euro</option>
                                        <option value="GBP" <?php echo (isset($data['payment_settings']['currency']) && $data['payment_settings']['currency'] == 'GBP') ? 'selected' : ''; ?>>GBP - British Pound</option>
                                        <option value="CAD" <?php echo (isset($data['payment_settings']['currency']) && $data['payment_settings']['currency'] == 'CAD') ? 'selected' : ''; ?>>CAD - Canadian Dollar</option>
                                        <option value="AUD" <?php echo (isset($data['payment_settings']['currency']) && $data['payment_settings']['currency'] == 'AUD') ? 'selected' : ''; ?>>AUD - Australian Dollar</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="default_registration_fee" class="form-label">Default Registration Fee</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="default_registration_fee" name="default_registration_fee" 
                                               value="<?php echo isset($data['payment_settings']['default_registration_fee']) ? $data['payment_settings']['default_registration_fee'] : '0.00'; ?>" step="0.01" min="0">
                                    </div>
                                    <div class="form-text">Default fee for vehicle registrations if not specified for a show.</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="default_show_listing_fee" class="form-label">Default Show Listing Fee</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="default_show_listing_fee" name="default_show_listing_fee" 
                                               value="<?php echo isset($data['payment_settings']['default_show_listing_fee']) ? $data['payment_settings']['default_show_listing_fee'] : '0.00'; ?>" step="0.01" min="0">
                                    </div>
                                    <div class="form-text">Fee charged to list a show on the platform.</div>
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="show_listing_fee_enabled" name="show_listing_fee_enabled" 
                                           value="true" <?php echo (isset($data['payment_settings']['show_listing_fee_enabled']) && $data['payment_settings']['show_listing_fee_enabled'] == 'true') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="show_listing_fee_enabled">Enable Show Listing Fees</label>
                                    <div class="form-text">If disabled, all shows can be listed for free.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Payment Provider Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                    <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" 
                                           value="<?php echo isset($data['payment_settings']['paypal_client_id']) ? $data['payment_settings']['paypal_client_id'] : ''; ?>">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="paypal_secret" class="form-label">PayPal Secret</label>
                                    <input type="password" class="form-control" id="paypal_secret" name="paypal_secret" 
                                           value="<?php echo isset($data['payment_settings']['paypal_secret']) ? $data['payment_settings']['paypal_secret'] : ''; ?>">
                                </div>
                                
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="paypal_sandbox" name="paypal_sandbox" 
                                           value="true" <?php echo (isset($data['payment_settings']['paypal_sandbox']) && $data['payment_settings']['paypal_sandbox'] == 'true') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="paypal_sandbox">PayPal Sandbox Mode</label>
                                    <div class="form-text">Enable for testing. Disable for live payments.</div>
                                </div>
                                
                                <hr class="my-4">
                                
                                <h5>Cash App Settings</h5>
                                <div class="mb-3">
                                    <label for="cashapp_id" class="form-label">Cash App ID</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="text" class="form-control" id="cashapp_id" name="cashapp_id" 
                                               value="<?php echo isset($data['payment_settings']['cashapp_id']) ? $data['payment_settings']['cashapp_id'] : (isset($settings['cashapp_id']) ? $settings['cashapp_id'] : ''); ?>">
                                    </div>
                                    <div class="form-text">Your Cash App $Cashtag for receiving payments.</div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Cash App by sending money to this $Cashtag.
                                </div>
                                
                                <h5>Venmo Settings</h5>
                                <div class="mb-3">
                                    <label for="venmo_id" class="form-label">Venmo ID</label>
                                    <div class="input-group">
                                        <span class="input-group-text">@</span>
                                        <input type="text" class="form-control" id="venmo_id" name="venmo_id" 
                                               value="<?php echo isset($data['payment_settings']['venmo_id']) ? $data['payment_settings']['venmo_id'] : (isset($settings['venmo_id']) ? $settings['venmo_id'] : ''); ?>">
                                    </div>
                                    <div class="form-text">Your Venmo username for receiving payments.</div>
                                </div>
                                
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Venmo by sending money to this username.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Admin Controller - Calendar Settings
 * 
 * This is a partial backup of the AdminController.php file,
 * containing only the settings_calendar method.
 */

public function settings_calendar() {
    // Check if user is logged in and is an admin
    if (!isLoggedIn() || !isAdmin()) {
        redirect('users/login');
    }
    
    // Load calendar model
    $calendarModel = $this->model('CalendarModel');
    
    // Check if form was submitted
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Sanitize POST data
        $_POST = $this->sanitizeInput($_POST);
        
        // Get map settings
        $mapSettings = [
            'provider' => trim($_POST['map_provider']),
            'api_key' => trim($_POST['map_api_key']),
            'default_lat' => trim($_POST['map_default_lat']),
            'default_lng' => trim($_POST['map_default_lng']),
            'default_zoom' => trim($_POST['map_default_zoom']),
            'filter_radius' => trim($_POST['map_filter_radius']),
            'tile_url' => trim($_POST['map_tile_url']),
            'attribution' => trim($_POST['map_attribution'])
        ];
        
        // Get calendar settings
        $calendarSettings = [
            'default_view' => trim($_POST['calendar_default_view']),
            'start_day' => trim($_POST['calendar_start_day']),
            'time_format' => trim($_POST['calendar_time_format']),
            'date_format' => trim($_POST['calendar_date_format']),
            'events_per_page' => trim($_POST['calendar_events_per_page'])
        ];
        
        // Update settings
        $mapUpdated = $calendarModel->updateMapProviderSettings($mapSettings);
        $calendarUpdated = $calendarModel->updateCalendarSettings($calendarSettings);
        
        // Prepare data for view
        $data = [
            'title' => 'Calendar & Map Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css',
            'mapSettings' => $mapSettings,
            'calendarSettings' => $calendarSettings,
            'success' => $mapUpdated && $calendarUpdated,
            'error' => !($mapUpdated && $calendarUpdated) ? 'Failed to update some settings' : ''
        ];
        
        $this->view('admin/settings_calendar', $data);
    } else {
        // Get current settings
        $mapSettings = $calendarModel->getMapProviderSettings();
        $calendarSettings = $calendarModel->getCalendarDisplaySettings();
        
        // Prepare data for view
        $data = [
            'title' => 'Calendar & Map Settings',
            'custom_css' => BASE_URL . '/css/admin-settings.css',
            'mapSettings' => $mapSettings,
            'calendarSettings' => $calendarSettings
        ];
        
        $this->view('admin/settings_calendar', $data);
    }
}
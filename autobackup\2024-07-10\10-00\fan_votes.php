<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Fan Favorite Votes - <?php echo $data['show']->name; ?></h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/coordinator/exportFanVotes/<?php echo $data['show']->id; ?>" class="btn btn-secondary">
                    <i class="fas fa-download me-2"></i> Export CSV
                </a>
                <a href="<?php echo URLROOT; ?>/coordinator/qrCodes/<?php echo $data['show']->id; ?>" class="btn btn-primary">
                    <i class="fas fa-qrcode me-2"></i> QR Codes
                </a>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </button>
            </div>
        </div>
    </div>
    
    <?php flash('coordinator_message'); ?>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <!-- Vote Statistics -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">Total Votes</h5>
                            <p class="card-text display-4"><?php echo count($data['votes']); ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">Unique IPs</h5>
                            <p class="card-text display-4"><?php echo $data['unique_ips']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">Facebook Votes</h5>
                            <p class="card-text display-4"><?php echo $data['facebook_votes']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title">Vehicles Voted</h5>
                            <p class="card-text display-4"><?php echo $data['vehicles_with_votes']; ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Vote Rankings -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Vote Rankings</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>Rank</th>
                                    <th>Registration #</th>
                                    <th>Vehicle</th>
                                    <th>Owner</th>
                                    <th>Category</th>
                                    <th>Votes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $rank = 1; ?>
                                <?php foreach($data['rankings'] as $vehicle): ?>
                                    <tr>
                                        <td><?php echo $rank++; ?></td>
                                        <td><?php echo $vehicle->registration_number; ?></td>
                                        <td><?php echo $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model; ?></td>
                                        <td><?php echo $vehicle->owner_name; ?></td>
                                        <td><?php echo $vehicle->category_name; ?></td>
                                        <td><span class="badge bg-primary"><?php echo $vehicle->vote_count; ?></span></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- All Votes -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">All Votes</h5>
                    <div>
                        <input type="text" id="voteSearch" class="form-control form-control-sm" placeholder="Search...">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="votesTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>IP Address</th>
                                    <th>Facebook User</th>
                                    <th>Vehicle</th>
                                    <th>Category</th>
                                    <th>Date/Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach($data['votes'] as $vote): ?>
                                    <tr>
                                        <td><?php echo $vote->id; ?></td>
                                        <td><?php echo $vote->voter_ip; ?></td>
                                        <td>
                                            <?php if(!empty($vote->fb_user_id)): ?>
                                                <?php echo $vote->fb_user_name; ?>
                                                <?php if(!empty($vote->fb_user_email)): ?>
                                                    <br><small class="text-muted"><?php echo $vote->fb_user_email; ?></small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">IP-based vote</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $vote->year . ' ' . $vote->make . ' ' . $vote->model; ?></td>
                                        <td><?php echo $vote->category_name; ?></td>
                                        <td><?php echo date('M j, Y g:i A', strtotime($vote->created_at)); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    document.getElementById('voteSearch').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const table = document.getElementById('votesTable');
        const rows = table.getElementsByTagName('tr');
        
        for (let i = 1; i < rows.length; i++) {
            let found = false;
            const cells = rows[i].getElementsByTagName('td');
            
            for (let j = 0; j < cells.length; j++) {
                const cellText = cells[j].textContent.toLowerCase();
                
                if (cellText.indexOf(searchValue) > -1) {
                    found = true;
                    break;
                }
            }
            
            rows[i].style.display = found ? '' : 'none';
        }
    });
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
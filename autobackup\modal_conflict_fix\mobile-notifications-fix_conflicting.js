/**
 * Mobile Notification Fix v1.0.1
 * 
 * This file provides additional mobile-specific enhancements for the notification system
 * to ensure proper functionality on mobile devices.
 * 
 * Location: /public/js/mobile-notifications-fix.js
 * Dependencies: notifications.js, Bootstrap 5
 */

(function() {
    'use strict';
    
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                     window.innerWidth <= 768 ||
                     ('ontouchstart' in window);
    
    if (!isMobile) {
        console.log('🔧 Mobile notification fix: Not a mobile device, skipping mobile enhancements');
        return;
    }
    
    console.log('🔧 Mobile notification fix: Initializing mobile enhancements');
    
    // Wait for DOM and notification manager to be ready
    function initMobileFix() {
        // Add mobile-specific CSS class to body
        document.body.classList.add('mobile-notifications-enhanced');
        
        // Override modal button handlers with mobile-friendly versions
        setupMobileModalHandlers();
        
        // Add touch event enhancements
        setupTouchEnhancements();
        
        // Add viewport meta tag if missing (for proper mobile scaling)
        ensureViewportMeta();
        
        // Monitor for modal creation and enhance them
        observeModalCreation();
    }
    
    function setupMobileModalHandlers() {
        // Override global functions with mobile-enhanced versions
        const originalSubscribe = window.subscribeToEventModal;
        const originalUnsubscribe = window.unsubscribeFromEventModal;
        
        window.subscribeToEventModal = function(e) {
            console.log('🔧 Mobile subscribe function called');
            
            // Prevent default behavior
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // Add visual feedback
            const button = e ? e.target : event.target;
            if (button) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
            
            // Call original function
            if (originalSubscribe) {
                return originalSubscribe.call(this, e);
            } else {
                // Fallback implementation
                return handleSubscribeAction(e);
            }
        };
        
        window.unsubscribeFromEventModal = function(e) {
            console.log('🔧 Mobile unsubscribe function called');
            
            // Prevent default behavior
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            // Add visual feedback
            const button = e ? e.target : event.target;
            if (button) {
                button.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);
            }
            
            // Call original function
            if (originalUnsubscribe) {
                return originalUnsubscribe.call(this, e);
            } else {
                // Fallback implementation
                return handleUnsubscribeAction(e);
            }
        };
    }
    
    function setupTouchEnhancements() {
        // Add touch event listeners for better mobile interaction - ONLY for notification elements
        document.addEventListener('touchstart', function(e) {
            // Handle notification buttons only
            if (e.target.matches('[data-notification-btn]') || e.target.closest('[data-notification-btn]')) {
                const btn = e.target.matches('[data-notification-btn]') ? e.target : e.target.closest('[data-notification-btn]');
                btn.style.opacity = '0.7';
            }
            
            // Handle modal buttons ONLY in notification modal
            const notificationModal = document.getElementById('notificationModal');
            if (notificationModal && notificationModal.contains(e.target)) {
                if (e.target.matches('.modal-footer .btn') || e.target.closest('.modal-footer .btn')) {
                    const btn = e.target.matches('.modal-footer .btn') ? e.target : e.target.closest('.modal-footer .btn');
                    btn.style.opacity = '0.7';
                }
            }
        }, { passive: true });
        
        document.addEventListener('touchend', function(e) {
            // Reset opacity for notification buttons
            if (e.target.matches('[data-notification-btn]') || e.target.closest('[data-notification-btn]')) {
                const btn = e.target.matches('[data-notification-btn]') ? e.target : e.target.closest('[data-notification-btn]');
                setTimeout(() => {
                    btn.style.opacity = '';
                }, 150);
            }
            
            // Handle modal button touches ONLY in notification modal
            const notificationModal = document.getElementById('notificationModal');
            if (notificationModal && notificationModal.contains(e.target)) {
                // Reset opacity for modal buttons
                if (e.target.matches('.modal-footer .btn') || e.target.closest('.modal-footer .btn')) {
                    const btn = e.target.matches('.modal-footer .btn') ? e.target : e.target.closest('.modal-footer .btn');
                    setTimeout(() => {
                        btn.style.opacity = '';
                    }, 150);
                }
                
                // Handle notification modal button touches only
                if (e.target.matches('.modal-footer .btn-primary')) {
                    console.log('🔧 Mobile: Notification subscribe button touched');
                    e.preventDefault();
                    e.stopPropagation();
                    if (typeof window.subscribeToEventModal === 'function') {
                        window.subscribeToEventModal(e);
                    }
                } else if (e.target.matches('.modal-footer .btn-danger')) {
                    console.log('🔧 Mobile: Notification unsubscribe button touched');
                    e.preventDefault();
                    e.stopPropagation();
                    if (typeof window.unsubscribeFromEventModal === 'function') {
                        window.unsubscribeFromEventModal(e);
                    }
                }
            }
        }, { passive: false });
    }
    
    function ensureViewportMeta() {
        if (!document.querySelector('meta[name="viewport"]')) {
            const viewport = document.createElement('meta');
            viewport.name = 'viewport';
            viewport.content = 'width=device-width, initial-scale=1, shrink-to-fit=no';
            document.head.appendChild(viewport);
            console.log('🔧 Mobile: Added viewport meta tag');
        }
    }
    
    function observeModalCreation() {
        // Use MutationObserver to watch for modal creation - ONLY for notification modal
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    // Only enhance the notification modal, not other modals
                    if (node.nodeType === 1 && node.id === 'notificationModal') {
                        console.log('🔧 Mobile: Notification modal detected, enhancing...');
                        enhanceModal(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    function enhanceModal(modal) {
        // Add mobile-specific classes
        modal.classList.add('mobile-enhanced');
        
        // Enhance modal buttons with direct event listeners
        setTimeout(() => {
            const subscribeBtn = modal.querySelector('.btn-primary');
            const unsubscribeBtn = modal.querySelector('.btn-danger');
            
            if (subscribeBtn) {
                // Remove existing handlers and add new ones
                subscribeBtn.removeAttribute('onclick');
                subscribeBtn.removeAttribute('ontouchend');
                
                subscribeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile: Enhanced subscribe button clicked');
                    handleSubscribeAction(e);
                }, { passive: false });
                
                subscribeBtn.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile: Enhanced subscribe button touched');
                    handleSubscribeAction(e);
                }, { passive: false });
            }
            
            if (unsubscribeBtn) {
                // Remove existing handlers and add new ones
                unsubscribeBtn.removeAttribute('onclick');
                unsubscribeBtn.removeAttribute('ontouchend');
                
                unsubscribeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile: Enhanced unsubscribe button clicked');
                    handleUnsubscribeAction(e);
                }, { passive: false });
                
                unsubscribeBtn.addEventListener('touchend', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔧 Mobile: Enhanced unsubscribe button touched');
                    handleUnsubscribeAction(e);
                }, { passive: false });
            }
        }, 100);
    }
    
    function handleSubscribeAction(e) {
        console.log('🔧 Mobile: Handling subscribe action');
        
        const form = document.getElementById('notificationSubscriptionForm');
        if (!form) {
            console.error('🔧 Mobile: Notification subscription form not found');
            alert('Error: Form not found. Please try again.');
            return;
        }
        
        const formData = new FormData(form);
        
        // Validate that at least one notification time is selected
        const notificationTimes = formData.getAll('notification_times[]');
        if (notificationTimes.length === 0) {
            alert('Please select at least one notification time.');
            return;
        }
        
        // If registration notifications are enabled, validate those times too
        const notifyRegistration = document.getElementById('notify_registration_end');
        if (notifyRegistration && notifyRegistration.checked) {
            const registrationTimes = formData.getAll('registration_times[]');
            if (registrationTimes.length === 0) {
                alert('Please select at least one registration deadline notification time.');
                return;
            }
        }
        
        // Show loading state
        const button = e ? e.target : event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
        button.disabled = true;
        
        // Make request
        const baseUrl = window.BASE_URL || '';
        fetch(`${baseUrl}/notification/subscribe`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Show success message
                alert(data.message);
                
                // Reload page to ensure consistent state
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('🔧 Mobile: Subscribe error:', error);
            alert('An error occurred while subscribing to notifications.');
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
    
    function handleUnsubscribeAction(e) {
        console.log('🔧 Mobile: Handling unsubscribe action');
        
        if (!confirm('Are you sure you want to unsubscribe from notifications for this event?')) {
            return;
        }
        
        const form = document.getElementById('notificationSubscriptionForm');
        if (!form) {
            console.error('🔧 Mobile: Notification subscription form not found');
            alert('Error: Form not found. Please try again.');
            return;
        }
        
        const formData = new FormData(form);
        
        // Show loading state
        const button = e ? e.target : event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Unsubscribing...';
        button.disabled = true;
        
        // Make request
        const baseUrl = window.BASE_URL || '';
        fetch(`${baseUrl}/notification/unsubscribe`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
                if (modal) {
                    modal.hide();
                }
                
                // Show success message
                alert(data.message);
                
                // Reload page to ensure consistent state
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('🔧 Mobile: Unsubscribe error:', error);
            alert('An error occurred while unsubscribing.');
        })
        .finally(() => {
            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileFix);
    } else {
        initMobileFix();
    }
    
    console.log('🔧 Mobile notification fix: Loaded successfully');
})();
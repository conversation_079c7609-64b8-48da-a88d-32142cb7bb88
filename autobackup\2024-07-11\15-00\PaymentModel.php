<?php
/**
 * Payment Model
 * 
 * This model handles all database operations related to payments.
 */
class PaymentModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get all payment methods
     * 
     * @param bool $activeOnly Only return active payment methods
     * @return array
     */
    public function getPaymentMethods($activeOnly = true) {
        // Check if the is_active column exists
        try {
            if ($activeOnly) {
                $this->db->query('SELECT * FROM payment_methods WHERE is_active = TRUE ORDER BY id');
            } else {
                $this->db->query('SELECT * FROM payment_methods ORDER BY id');
            }
            
            return $this->db->resultSet();
        } catch (Exception $e) {
            // If there's an error (likely because is_active doesn't exist), just get all methods
            $this->db->query('SELECT * FROM payment_methods ORDER BY id');
            return $this->db->resultSet();
        }
    }
    
    /**
     * Get payment method by ID
     * 
     * @param int $id Payment method ID
     * @return object|bool
     */
    public function getPaymentMethodById($id) {
        $this->db->query('SELECT * FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payment method by name
     * 
     * @param string $name Payment method name
     * @return object|bool
     */
    public function getPaymentMethodByName($name) {
        $this->db->query('SELECT * FROM payment_methods WHERE name = :name');
        $this->db->bind(':name', $name);
        
        return $this->db->single();
    }
    
    /**
     * Create payment method
     * 
     * @param array $data Payment method data
     * @return bool|int
     */
    public function createPaymentMethod($data) {
        $this->db->query('INSERT INTO payment_methods (name, description, instructions, is_active, requires_approval) 
                          VALUES (:name, :description, :instructions, :is_active, :requires_approval)');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment method
     * 
     * @param array $data Payment method data
     * @return bool
     */
    public function updatePaymentMethod($data) {
        $this->db->query('UPDATE payment_methods 
                          SET name = :name, 
                              description = :description, 
                              instructions = :instructions, 
                              is_active = :is_active, 
                              requires_approval = :requires_approval 
                          WHERE id = :id');
        
        $this->db->bind(':name', $data['name']);
        $this->db->bind(':description', $data['description']);
        $this->db->bind(':instructions', $data['instructions']);
        $this->db->bind(':is_active', $data['is_active']);
        $this->db->bind(':requires_approval', $data['requires_approval']);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
    
    /**
     * Delete payment method
     * 
     * @param int $id Payment method ID
     * @return bool
     */
    public function deletePaymentMethod($id) {
        $this->db->query('DELETE FROM payment_methods WHERE id = :id');
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get payment setting
     * 
     * @param string $key Setting key
     * @return string|null
     */
    public function getPaymentSetting($key) {
        $this->db->query('SELECT setting_value FROM payment_settings WHERE setting_key = :key');
        $this->db->bind(':key', $key);
        
        $result = $this->db->single();
        
        return $result ? $result->setting_value : null;
    }
    
    /**
     * Update payment setting
     * 
     * @param string $key Setting key
     * @param string $value Setting value
     * @return bool
     */
    public function updatePaymentSetting($key, $value) {
        $this->db->query('INSERT INTO payment_settings (setting_key, setting_value) 
                          VALUES (:key, :value) 
                          ON DUPLICATE KEY UPDATE setting_value = :update_value');
        
        $this->db->bind(':key', $key);
        $this->db->bind(':value', $value);
        $this->db->bind(':update_value', $value);
        
        return $this->db->execute();
    }
    
    /**
     * Get all payment settings
     * 
     * @return array
     */
    public function getAllPaymentSettings() {
        $this->db->query('SELECT * FROM payment_settings ORDER BY setting_key');
        
        $results = $this->db->resultSet();
        $settings = [];
        
        foreach ($results as $result) {
            $settings[$result->setting_key] = $result->setting_value;
        }
        
        return $settings;
    }
    
    /**
     * Create payment record
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function createPayment($data) {
        $this->db->query('INSERT INTO payments (user_id, amount, payment_method_id, payment_status, 
                                               payment_reference, payment_type, related_id, notes) 
                          VALUES (:user_id, :amount, :payment_method_id, :payment_status, 
                                 :payment_reference, :payment_type, :related_id, :notes)');
        
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':amount', $data['amount']);
        $this->db->bind(':payment_method_id', $data['payment_method_id']);
        $this->db->bind(':payment_status', $data['payment_status']);
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        $this->db->bind(':payment_type', $data['payment_type']);
        $this->db->bind(':related_id', $data['related_id']);
        $this->db->bind(':notes', $data['notes'] ?? null);
        
        if ($this->db->execute()) {
            return $this->db->lastInsertId();
        } else {
            return false;
        }
    }
    
    /**
     * Update payment status
     * 
     * @param int $id Payment ID
     * @param string $status New status
     * @param string $reference Payment reference
     * @param string $adminNotes Admin notes for the payment
     * @return bool
     */
    public function updatePaymentStatus($id, $status, $reference = null, $adminNotes = null) {
        $this->db->query('UPDATE payments 
                          SET payment_status = :status, 
                              payment_reference = COALESCE(:reference, payment_reference),
                              admin_notes = COALESCE(:admin_notes, admin_notes),
                              updated_at = NOW()
                          WHERE id = :id');
        
        $this->db->bind(':status', $status);
        $this->db->bind(':reference', $reference);
        $this->db->bind(':admin_notes', $adminNotes);
        $this->db->bind(':id', $id);
        
        return $this->db->execute();
    }
    
    /**
     * Get payment by ID
     * 
     * @param int $id Payment ID
     * @return object|bool
     */
    public function getPaymentById($id) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.id = :id');
        
        $this->db->bind(':id', $id);
        
        return $this->db->single();
    }
    
    /**
     * Get payments by user
     * 
     * @param int $userId User ID
     * @return array
     */
    public function getPaymentsByUser($userId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          WHERE p.user_id = :user_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':user_id', $userId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payments by type and related ID
     * 
     * @param string $type Payment type
     * @param int $relatedId Related ID
     * @return array
     */
    public function getPaymentsByTypeAndRelatedId($type, $relatedId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.payment_type = :type AND p.related_id = :related_id
                          ORDER BY p.created_at DESC');
        
        $this->db->bind(':type', $type);
        $this->db->bind(':related_id', $relatedId);
        
        return $this->db->resultSet();
    }
    
    /**
     * Get payment by registration ID
     * 
     * @param int $registrationId Registration ID
     * @return object|bool Payment object or false if not found
     */
    public function getPaymentByRegistration($registrationId) {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.payment_type = "registration" AND p.related_id = :registration_id
                          AND p.payment_status = "completed"
                          ORDER BY p.created_at DESC
                          LIMIT 1');
        
        $this->db->bind(':registration_id', $registrationId);
        
        return $this->db->single();
    }
    
    /**
     * Get all payments with optional filters
     * 
     * @param array $filters Optional filters
     * @return array
     */
    public function getAllPayments($filters = []) {
        $sql = 'SELECT p.*, pm.name as payment_method_name, u.name as user_name
                FROM payments p
                JOIN payment_methods pm ON p.payment_method_id = pm.id
                JOIN users u ON p.user_id = u.id
                WHERE 1=1';
        
        // Apply filters
        if (!empty($filters['payment_status'])) {
            $sql .= ' AND p.payment_status = :payment_status';
        }
        
        if (!empty($filters['payment_type'])) {
            $sql .= ' AND p.payment_type = :payment_type';
        }
        
        if (!empty($filters['payment_method_id'])) {
            $sql .= ' AND p.payment_method_id = :payment_method_id';
        }
        
        if (!empty($filters['start_date'])) {
            $sql .= ' AND p.created_at >= :start_date';
        }
        
        if (!empty($filters['end_date'])) {
            $sql .= ' AND p.created_at <= :end_date';
        }
        
        $sql .= ' ORDER BY p.created_at DESC';
        
        // Add limit if specified
        if (!empty($filters['limit'])) {
            $sql .= ' LIMIT :limit';
        }
        
        $this->db->query($sql);
        
        // Bind filter values
        if (!empty($filters['payment_status'])) {
            $this->db->bind(':payment_status', $filters['payment_status']);
        }
        
        if (!empty($filters['payment_type'])) {
            $this->db->bind(':payment_type', $filters['payment_type']);
        }
        
        if (!empty($filters['payment_method_id'])) {
            $this->db->bind(':payment_method_id', $filters['payment_method_id']);
        }
        
        if (!empty($filters['start_date'])) {
            $this->db->bind(':start_date', $filters['start_date']);
        }
        
        if (!empty($filters['end_date'])) {
            $this->db->bind(':end_date', $filters['end_date']);
        }
        
        if (!empty($filters['limit'])) {
            $this->db->bind(':limit', $filters['limit']);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Check if user is exempt from listing fees
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function isUserExemptFromListingFees($userId) {
        $this->db->query('SELECT exempt_from_listing_fees FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->exempt_from_listing_fees;
    }
    
    /**
     * Check if user can create free shows
     * 
     * @param int $userId User ID
     * @return bool
     */
    public function canUserCreateFreeShows($userId) {
        $this->db->query('SELECT can_create_free_shows FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        
        $result = $this->db->single();
        
        return $result && $result->can_create_free_shows;
    }
    
    /**
     * Update user payment permissions
     * 
     * @param int $userId User ID
     * @param bool $canCreateFreeShows Whether user can create free shows
     * @param bool $exemptFromListingFees Whether user is exempt from listing fees
     * @return bool
     */
    public function updateUserPaymentPermissions($userId, $canCreateFreeShows, $exemptFromListingFees) {
        $this->db->query('UPDATE users 
                          SET can_create_free_shows = :can_create_free_shows, 
                              exempt_from_listing_fees = :exempt_from_listing_fees 
                          WHERE id = :id');
        
        $this->db->bind(':can_create_free_shows', $canCreateFreeShows);
        $this->db->bind(':exempt_from_listing_fees', $exemptFromListingFees);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get pending payments
     * 
     * @return array
     */
    public function getPendingPayments() {
        $this->db->query('SELECT p.*, pm.name as payment_method_name, u.name as user_name, u.email as user_email
                          FROM payments p
                          JOIN payment_methods pm ON p.payment_method_id = pm.id
                          JOIN users u ON p.user_id = u.id
                          WHERE p.payment_status = "pending"
                          ORDER BY p.created_at DESC');
        
        return $this->db->resultSet();
    }
    
    /**
     * Get pending registration payments
     * 
     * @return array
     */
    public function getPendingRegistrationPayments() {
        $this->db->query('SELECT r.*, s.name as show_name, v.year, v.make, v.model, 
                                 u.name as owner_name, u.email as owner_email,
                                 pm.name as payment_method_name
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          JOIN vehicles v ON r.vehicle_id = v.id
                          JOIN users u ON r.owner_id = u.id
                          JOIN payment_methods pm ON r.payment_method_id = pm.id
                          WHERE r.payment_status = "pending"
                          ORDER BY r.created_at DESC');
        
        return $this->db->resultSet();
    }
}
<?php
/**
 * Show Model
 * 
 * Handles database operations for shows
 */
class ShowModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database;
    }
    
    /**
     * Parse location string to extract city and state
     * 
     * This is an advanced location parser that handles various formats of addresses
     * and extracts city and state information.
     * 
     * @param string $location The location string to parse
     * @return array Associative array with 'city' and 'state' keys
     */
    private function parseLocation($location) {
        $result = [
            'city' => '',
            'state' => ''
        ];
        
        if (empty($location)) {
            return $result;
        }
        
        // Common state abbreviations and full names mapping
        $stateMap = [
            'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
            'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
            'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
            'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
            'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
            'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
            'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
            'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
            'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
            'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
            'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
            'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
            'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
        ];
        
        // Flip the map to also search by full state name
        $stateNameToAbbr = array_flip($stateMap);
        
        // Normalize the location string
        $location = trim($location);
        
        // Debug mode check
        $debug = defined('DEBUG_MODE') && DEBUG_MODE;
        if ($debug) {
            error_log("ShowModel::parseLocation - Parsing location: " . $location);
        }
        
        // Special case: Check if the location is just "USA" or contains only "USA"
        if (preg_match('/^USA$/i', $location) || preg_match('/^United\s+States(\s+of\s+America)?$/i', $location)) {
            $result['city'] = '';
            $result['state'] = 'USA';
            if ($debug) error_log("ShowModel::parseLocation - Matched USA only");
            return $result;
        }
        
        // Try to match common patterns in order of specificity
        
        // Pattern 1: Full address with street, city, state, zip
        // Example: "123 Main St, Anytown, CA 12345"
        if (preg_match('/^.+,\s*(.+?),\s*([A-Za-z]{2})\s*\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 1 (Full address)");
        }
        // Pattern 2: City, State Zip
        // Example: "Anytown, CA 12345"
        else if (preg_match('/^(.+?),\s*([A-Za-z]{2})\s*\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 2 (City, State Zip)");
        }
        // Pattern 3: "City, State" or "City, ST"
        // Example: "Anytown, California" or "Anytown, CA"
        else if (preg_match('/^(.+?),\s*([A-Za-z]{2,})$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
            } 
            // Check if it's "USA"
            else if (strtoupper($stateCandidate) == 'USA' || 
                     strtoupper($stateCandidate) == 'UNITED STATES' || 
                     strtoupper($stateCandidate) == 'UNITED STATES OF AMERICA') {
                $result['state'] = 'USA';
            } else {
                $result['state'] = $stateCandidate;
            }
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 3 (City, State)");
        }
        // Pattern 4: "City ST Zip" or "City State Zip"
        // Example: "Anytown CA 12345" or "Anytown California 12345"
        else if (preg_match('/^(.+?)\s+([A-Za-z]{2})\s+\d{5}(-\d{4})?$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $result['state'] = strtoupper(trim($matches[2]));
            if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 4 (City ST Zip)");
        }
        // Pattern 5: "City ST" or "City State"
        // Example: "Anytown CA" or "Anytown California"
        else if (preg_match('/^(.+?)\s+([A-Za-z]{2,})$/i', $location, $matches)) {
            $result['city'] = trim($matches[1]);
            $stateCandidate = trim($matches[2]);
            
            // Check if it's a state abbreviation
            if (strlen($stateCandidate) == 2 && isset($stateMap[strtoupper($stateCandidate)])) {
                $result['state'] = strtoupper($stateCandidate);
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City ST)");
            } 
            // Check if it's a full state name
            else if (isset($stateNameToAbbr[ucwords(strtolower($stateCandidate))])) {
                $result['state'] = $stateNameToAbbr[ucwords(strtolower($stateCandidate))];
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City State)");
            }
            // Check if it's "USA"
            else if (strtoupper($stateCandidate) == 'USA' || 
                     strtoupper($stateCandidate) == 'UNITED STATES' || 
                     strtoupper($stateCandidate) == 'UNITED STATES OF AMERICA') {
                $result['state'] = 'USA';
                if ($debug) error_log("ShowModel::parseLocation - Matched Pattern 5 (City USA)");
            } else {
                // This might be part of the city name, not a state
                $result['city'] = $location;
                $result['state'] = '';
                if ($debug) error_log("ShowModel::parseLocation - Pattern 5 failed state validation, treating as city only");
            }
        }
        // Pattern 6: Look for state abbreviation or name within the string
        else {
            if ($debug) error_log("ShowModel::parseLocation - Trying Pattern 6 (State within string)");
            $foundState = false;
            
            // Check for USA in the string
            if (stripos($location, 'USA') !== false || 
                stripos($location, 'United States') !== false || 
                stripos($location, 'United States of America') !== false) {
                $result['state'] = 'USA';
                $foundState = true;
                
                // Try to extract city by removing USA references
                $cityCandidate = preg_replace('/\b(USA|United\s+States(\s+of\s+America)?)\b/i', '', $location);
                $cityCandidate = trim($cityCandidate, " ,;-");
                
                if (!empty($cityCandidate)) {
                    $result['city'] = $cityCandidate;
                }
                
                if ($debug) error_log("ShowModel::parseLocation - Found USA reference, city candidate: " . $result['city']);
            }
            
            // If we didn't find USA, try to find a state abbreviation or name
            if (!$foundState) {
                // First try to find a state abbreviation
                foreach ($stateMap as $abbr => $fullName) {
                    // Look for state abbreviation surrounded by spaces, commas, or end of string
                    if (preg_match('/(\s|,|^)' . preg_quote($abbr, '/') . '(\s|,|$)/i', $location, $matches)) {
                        $result['state'] = $abbr;
                        $foundState = true;
                        
                        // Try to extract city by looking at what comes before the state
                        if (preg_match('/(.+?)(\s|,)' . preg_quote($abbr, '/') . '(\s|,|$)/i', $location, $cityMatches)) {
                            $result['city'] = trim($cityMatches[1]);
                            // Remove any trailing comma or other punctuation
                            $result['city'] = rtrim($result['city'], " ,;-");
                        }
                        if ($debug) error_log("ShowModel::parseLocation - Found state abbreviation: " . $abbr);
                        break;
                    }
                    
                    // Look for full state name
                    if (preg_match('/(\s|,|^)' . preg_quote($fullName, '/') . '(\s|,|$)/i', $location, $matches)) {
                        $result['state'] = $abbr;
                        $foundState = true;
                        
                        // Try to extract city by looking at what comes before the state
                        if (preg_match('/(.+?)(\s|,)' . preg_quote($fullName, '/') . '(\s|,|$)/i', $location, $cityMatches)) {
                            $result['city'] = trim($cityMatches[1]);
                            // Remove any trailing comma or other punctuation
                            $result['city'] = rtrim($result['city'], " ,;-");
                        }
                        if ($debug) error_log("ShowModel::parseLocation - Found state name: " . $fullName);
                        break;
                    }
                }
            }
            
            // If we still don't have a city or state, try to extract from common formats
            if (!$foundState) {
                // Try to find a comma-separated list and assume the last part might be a state
                $parts = explode(',', $location);
                if (count($parts) >= 2) {
                    // Last part might contain state
                    $lastPart = trim(end($parts));
                    
                    // Check if it contains a state abbreviation
                    foreach ($stateMap as $abbr => $fullName) {
                        if (preg_match('/\b' . preg_quote($abbr, '/') . '\b/i', $lastPart)) {
                            $result['state'] = $abbr;
                            // The part before the last comma is likely the city
                            $result['city'] = trim($parts[count($parts) - 2]);
                            $foundState = true;
                            if ($debug) error_log("ShowModel::parseLocation - Found state in comma-separated list: " . $abbr);
                            break;
                        }
                    }
                    
                    // If we still don't have a state, check for full state names
                    if (!$foundState) {
                        foreach ($stateMap as $abbr => $fullName) {
                            if (preg_match('/\b' . preg_quote($fullName, '/') . '\b/i', $lastPart)) {
                                $result['state'] = $abbr;
                                // The part before the last comma is likely the city
                                $result['city'] = trim($parts[count($parts) - 2]);
                                $foundState = true;
                                if ($debug) error_log("ShowModel::parseLocation - Found state name in comma-separated list: " . $fullName);
                                break;
                            }
                        }
                    }
                    
                    // Check for USA in the last part
                    if (!$foundState && (stripos($lastPart, 'USA') !== false || 
                                        stripos($lastPart, 'United States') !== false || 
                                        stripos($lastPart, 'United States of America') !== false)) {
                        $result['state'] = 'USA';
                        // The part before the last comma is likely the city
                        $result['city'] = trim($parts[count($parts) - 2]);
                        $foundState = true;
                        if ($debug) error_log("ShowModel::parseLocation - Found USA in comma-separated list");
                    }
                }
            }
            
            // If we still don't have a city, use the whole string as the city
            if (empty($result['city'])) {
                $result['city'] = $location;
                if ($debug) error_log("ShowModel::parseLocation - No city found, using entire string as city");
            }
            
            // If we still don't have a state, default to USA
            if (empty($result['state'])) {
                $result['state'] = 'USA';
                if ($debug) error_log("ShowModel::parseLocation - No state found, defaulting to USA");
            }
        }
        
        // Clean up city name - remove any trailing/leading punctuation
        $result['city'] = trim($result['city'], " ,;-");
        
        // If we have a state abbreviation, make sure it's uppercase
        if (strlen($result['state']) == 2) {
            $result['state'] = strtoupper($result['state']);
        }
        
        if ($debug) {
            error_log("ShowModel::parseLocation - Final result: City = '" . $result['city'] . "', State = '" . $result['state'] . "'");
        }
        
        return $result;
    }
    
    /**
     * Get unique states from shows
     * 
     * @param string $selectedState Optional state to filter cities by
     * @return array Array of unique states
     */
    public function getUniqueStatesFromShows($selectedState = '') {
        try {
            // Debug mode check
            $debug = defined('DEBUG_MODE') && DEBUG_MODE;
            
            // Common state abbreviations and full names mapping
            $stateAbbreviations = [
                'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
                'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
                'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
                'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
                'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
                'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
                'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
                'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
                'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
                'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
                'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
                'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
                'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
            ];
            
            // Get all published shows
            $shows = $this->getShows('published');
            $states = [];
            
            if ($debug) {
                error_log("ShowModel::getUniqueStatesFromShows - Processing " . count($shows) . " shows");
            }
            
            // Extract states using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                
                if ($debug) {
                    error_log("ShowModel::getUniqueStatesFromShows - Show ID: " . $show->id . 
                              ", Location: '" . $show->location . "'" .
                              ", Parsed State: '" . $locationData['state'] . "'");
                }
                
                if (!empty($locationData['state'])) {
                    $stateCode = $locationData['state'];
                    
                    // If we have a 2-letter state code, use it with its full name
                    if (strlen($stateCode) == 2 && isset($stateAbbreviations[strtoupper($stateCode)])) {
                        $stateCode = strtoupper($stateCode);
                        if (!isset($states[$stateCode])) {
                            $states[$stateCode] = $stateCode . ' - ' . $stateAbbreviations[$stateCode];
                            if ($debug) {
                                error_log("ShowModel::getUniqueStatesFromShows - Added state: " . $stateCode . 
                                          " (" . $stateAbbreviations[$stateCode] . ")");
                            }
                        }
                    } 
                    // If it's a full state name, try to map it to a code
                    else {
                        $stateFound = false;
                        foreach ($stateAbbreviations as $abbr => $name) {
                            if (strtolower($name) === strtolower($stateCode)) {
                                if (!isset($states[$abbr])) {
                                    $states[$abbr] = $abbr . ' - ' . $name;
                                    if ($debug) {
                                        error_log("ShowModel::getUniqueStatesFromShows - Mapped state name to code: " . 
                                                  $stateCode . " -> " . $abbr);
                                    }
                                }
                                $stateFound = true;
                                break;
                            }
                        }
                        
                        // If it's not a recognized state name, use as-is
                        if (!$stateFound && !isset($states[$stateCode])) {
                            $states[$stateCode] = $stateCode;
                            if ($debug) {
                                error_log("ShowModel::getUniqueStatesFromShows - Added non-standard state: " . $stateCode);
                            }
                        }
                    }
                }
            }
            
            // Sort states alphabetically
            asort($states);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($states as $code => $displayName) {
                $stateObj = new stdClass();
                $stateObj->state = $code;
                $stateObj->display_name = $displayName;
                $result[] = $stateObj;
            }
            
            if ($debug) {
                error_log("ShowModel::getUniqueStatesFromShows - Returning " . count($result) . " unique states");
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueStatesFromShows: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get unique cities from shows
     * 
     * @param string $stateFilter Optional state to filter cities by
     * @return array Array of unique cities
     */
    public function getUniqueCitiesFromShows($stateFilter = '') {
        try {
            // Debug mode check
            $debug = defined('DEBUG_MODE') && DEBUG_MODE;
            
            // Get all published shows
            $shows = $this->getShows('published');
            $cities = [];
            
            if ($debug) {
                error_log("ShowModel::getUniqueCitiesFromShows - Processing " . count($shows) . " shows" . 
                          ($stateFilter ? " for state: " . $stateFilter : ""));
            }
            
            // Common state abbreviations and full names mapping
            $stateAbbreviations = [
                'AL' => 'Alabama', 'AK' => 'Alaska', 'AZ' => 'Arizona', 'AR' => 'Arkansas',
                'CA' => 'California', 'CO' => 'Colorado', 'CT' => 'Connecticut', 'DE' => 'Delaware',
                'FL' => 'Florida', 'GA' => 'Georgia', 'HI' => 'Hawaii', 'ID' => 'Idaho',
                'IL' => 'Illinois', 'IN' => 'Indiana', 'IA' => 'Iowa', 'KS' => 'Kansas',
                'KY' => 'Kentucky', 'LA' => 'Louisiana', 'ME' => 'Maine', 'MD' => 'Maryland',
                'MA' => 'Massachusetts', 'MI' => 'Michigan', 'MN' => 'Minnesota', 'MS' => 'Mississippi',
                'MO' => 'Missouri', 'MT' => 'Montana', 'NE' => 'Nebraska', 'NV' => 'Nevada',
                'NH' => 'New Hampshire', 'NJ' => 'New Jersey', 'NM' => 'New Mexico', 'NY' => 'New York',
                'NC' => 'North Carolina', 'ND' => 'North Dakota', 'OH' => 'Ohio', 'OK' => 'Oklahoma',
                'OR' => 'Oregon', 'PA' => 'Pennsylvania', 'RI' => 'Rhode Island', 'SC' => 'South Carolina',
                'SD' => 'South Dakota', 'TN' => 'Tennessee', 'TX' => 'Texas', 'UT' => 'Utah',
                'VT' => 'Vermont', 'VA' => 'Virginia', 'WA' => 'Washington', 'WV' => 'West Virginia',
                'WI' => 'Wisconsin', 'WY' => 'Wyoming', 'DC' => 'District of Columbia'
            ];
            
            // Flip the map to search by full state name
            $stateNameToAbbr = array_flip($stateAbbreviations);
            
            // Extract cities using the location parser
            foreach ($shows as $show) {
                $locationData = $this->parseLocation($show->location);
                
                if ($debug) {
                    error_log("ShowModel::getUniqueCitiesFromShows - Show ID: " . $show->id . 
                              ", Location: '" . $show->location . "'" .
                              ", Parsed City: '" . $locationData['city'] . "'" . 
                              ", Parsed State: '" . $locationData['state'] . "'");
                }
                
                // Skip if city is empty
                if (empty($locationData['city'])) {
                    continue;
                }
                
                // If state filter is provided, only include cities from that state
                if (!empty($stateFilter)) {
                    $stateMatch = false;
                    
                    // Direct match on state code
                    if (strtoupper($locationData['state']) === strtoupper($stateFilter)) {
                        $stateMatch = true;
                    }
                    // Match state name to code
                    else if (isset($stateAbbreviations[strtoupper($stateFilter)]) && 
                             strtolower($stateAbbreviations[strtoupper($stateFilter)]) === strtolower($locationData['state'])) {
                        $stateMatch = true;
                    }
                    // Match state code to name
                    else if (isset($stateAbbreviations[strtoupper($locationData['state'])]) && 
                             strtolower($stateFilter) === strtolower($stateAbbreviations[strtoupper($locationData['state'])])) {
                        $stateMatch = true;
                    }
                    
                    if ($stateMatch && !in_array($locationData['city'], $cities)) {
                        $cities[] = $locationData['city'];
                        if ($debug) {
                            error_log("ShowModel::getUniqueCitiesFromShows - Added city: " . $locationData['city'] . 
                                      " for state: " . $stateFilter);
                        }
                    }
                } else {
                    // No state filter, include all cities
                    if (!in_array($locationData['city'], $cities)) {
                        $cities[] = $locationData['city'];
                        if ($debug) {
                            error_log("ShowModel::getUniqueCitiesFromShows - Added city: " . $locationData['city']);
                        }
                    }
                }
            }
            
            // Sort cities alphabetically
            sort($cities);
            
            // Convert to objects to match expected format
            $result = [];
            foreach ($cities as $city) {
                $cityObj = new stdClass();
                $cityObj->city = $city;
                $result[] = $cityObj;
            }
            
            if ($debug) {
                error_log("ShowModel::getUniqueCitiesFromShows - Returning " . count($result) . " unique cities" . 
                          ($stateFilter ? " for state: " . $stateFilter : ""));
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error in ShowModel::getUniqueCitiesFromShows: " . $e->getMessage());
            return [];
        }
    }
}
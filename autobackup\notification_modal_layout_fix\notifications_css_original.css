/**
 * Notification System CSS v3.49.0
 * 
 * Styles for the comprehensive notification system including
 * toast notifications, subscription modals, and admin interfaces.
 * 
 * Location: /public/css/notifications.css
 * Dependencies: Bootstrap 5, Font Awesome
 */

/* ==========================================================================
   Toast Notifications
   ========================================================================== */

.toast-container {
    z-index: 99999;
    max-width: 400px;
    pointer-events: none;
}

.toast-container > * {
    pointer-events: auto;
}

.toast-notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #0d6efd;
    animation: slideInRight 0.3s ease-out;
    z-index: 100000;
    position: relative;
    pointer-events: auto;
}

.toast-notification.alert-success {
    border-left-color: #198754;
}

.toast-notification.alert-warning {
    border-left-color: #ffc107;
}

.toast-notification.alert-danger {
    border-left-color: #dc3545;
}

.toast-notification.alert-info {
    border-left-color: #0dcaf0;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.toast-notification.fade-out {
    animation: fadeOut 0.15s ease-out forwards;
}

.toast-notification .btn-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    color: #0d6efd;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230d6efd'%3e%3cpath d='m.235 1.027 1.027-.235 6.738 6.738 6.738-6.738 1.027.235-6.738 6.738 6.738 6.738-1.027.235-6.738-6.738-6.738 6.738-1.027-.235 6.738-6.738-6.738-6.738z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: 0.375rem;
    opacity: 0.7;
    cursor: pointer;
    width: 1em;
    height: 1em;
}

.toast-notification .btn-close:hover {
    color: #0d6efd;
    text-decoration: none;
    opacity: 1;
}

.toast-notification .btn-close:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    opacity: 1;
}

.toast-notification {
    position: relative;
    padding-right: 3rem;
}

/* ==========================================================================
   Notification Buttons
   ========================================================================== */

[data-notification-btn] {
    position: relative;
    transition: all 0.2s ease;
}

[data-notification-btn]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

[data-notification-btn].btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
}

[data-notification-btn].btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   Subscription Modal
   ========================================================================== */

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

.notification-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.form-check {
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Original CSS continues... */
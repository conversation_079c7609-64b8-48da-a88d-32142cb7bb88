<?php
/**
 * Calendar Model
 * 
 * This model handles all database operations related to the calendar system.
 * 
 * Version 1.0.0 - Initial implementation
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarModel {
    private $db;
    private $showModel;
    private $eventTrigger;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize ShowModel
        require_once APPROOT . '/models/ShowModel.php';
        $this->showModel = new ShowModel();
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
    }
}
# Admin Shows Dashboard - Test Plan

## Overview
This document outlines the testing procedures for the new Admin Show Management Dashboard feature.

## Test Environment Setup
1. Ensure you have admin and coordinator user accounts
2. Create test shows with different statuses (upcoming, active, completed, cancelled)
3. Create test registrations for shows
4. Ensure shows have different coordinators assigned

## Test Cases

### 1. Dashboard Access and Authentication
**Test ID**: ASD-001
**Description**: Verify proper access control to the admin shows dashboard
**Steps**:
1. Access `/admin/shows` as an unauthenticated user
2. Access `/admin/shows` as a regular user
3. Access `/admin/shows` as a coordinator
4. Access `/admin/shows` as an admin

**Expected Results**:
- Unauthenticated users should be redirected to login
- Regular users should get access denied
- Coordinators should see only their assigned shows
- Ad<PERSON> should see all shows

### 2. Statistics Dashboard
**Test ID**: ASD-002
**Description**: Verify statistics cards display correct information
**Steps**:
1. Navigate to `/admin/shows`
2. Verify each statistics card shows correct numbers
3. Create a new show and refresh to verify counts update
4. Add a registration and verify registration count updates

**Expected Results**:
- All statistics should reflect actual database values
- Counts should update when data changes

### 3. Show Listing and Display
**Test ID**: ASD-003
**Description**: Verify show listing displays correctly
**Steps**:
1. Navigate to `/admin/shows`
2. Verify all shows are displayed in the table
3. Check that show details are correctly formatted
4. Verify status badges display correct colors
5. Verify registration counts are accurate

**Expected Results**:
- All shows should be visible (based on user role)
- Information should be accurate and well-formatted
- Status badges should use correct colors

### 4. Filtering Functionality
**Test ID**: ASD-004
**Description**: Test status filtering
**Steps**:
1. Click on different status filter buttons (All, Upcoming, Active, etc.)
2. Verify URL updates with correct parameters
3. Verify only shows with selected status are displayed
4. Test with shows that have no matches for a status

**Expected Results**:
- Filtering should work correctly for each status
- URL should reflect current filter
- Empty states should display appropriate messages

### 5. Search Functionality
**Test ID**: ASD-005
**Description**: Test search across show data
**Steps**:
1. Enter show name in search box and press Enter
2. Search for location keywords
3. Search for coordinator names
4. Test partial matches
5. Test case-insensitive search
6. Test search with no results

**Expected Results**:
- Search should find matches in names, locations, descriptions, and coordinators
- Results should update correctly
- No results should show appropriate message

### 6. Sorting Functionality
**Test ID**: ASD-006
**Description**: Test sorting options
**Steps**:
1. Test sorting by each available field (name, date, location, etc.)
2. Test ascending and descending order
3. Verify sort indicators update correctly
4. Test sorting with filtered results

**Expected Results**:
- Sorting should work correctly for all fields
- Sort direction should be indicated visually
- Sorting should work with filters applied

### 7. Bulk Operations
**Test ID**: ASD-007
**Description**: Test bulk actions on shows
**Steps**:
1. Select multiple shows using checkboxes
2. Test "Select All" functionality
3. Try each bulk action (activate, complete, cancel)
4. Test bulk delete (admin only)
5. Verify CSRF protection
6. Test with no shows selected

**Expected Results**:
- Bulk operations should work correctly
- Appropriate confirmation dialogs should appear
- CSRF tokens should be validated
- Error handling should work for invalid operations

### 8. Individual Show Actions
**Test ID**: ASD-008
**Description**: Test individual show action buttons
**Steps**:
1. Click "View Registrations" button
2. Click "Edit Show" button
3. Test dropdown menu actions
4. Verify links open correct pages
5. Test with different user roles

**Expected Results**:
- All action buttons should work correctly
- Links should navigate to appropriate pages
- Role-based restrictions should be enforced

### 9. Recent Activities Sidebar
**Test ID**: ASD-009
**Description**: Test recent activities display
**Steps**:
1. Verify recent activities are displayed
2. Create a new registration and refresh
3. Verify time stamps are accurate
4. Test with no recent activities

**Expected Results**:
- Recent activities should display correctly
- Time stamps should use timeAgo format
- New activities should appear after refresh

### 10. Upcoming Deadlines
**Test ID**: ASD-010
**Description**: Test upcoming deadlines functionality
**Steps**:
1. Create shows with upcoming registration deadlines
2. Create shows with upcoming start dates
3. Verify color coding for urgency
4. Test with no upcoming deadlines

**Expected Results**:
- Deadlines should be calculated correctly
- Color coding should indicate urgency appropriately
- Empty state should display when no deadlines

### 11. Mobile Responsiveness
**Test ID**: ASD-011
**Description**: Test mobile device compatibility
**Steps**:
1. Access dashboard on mobile device or resize browser
2. Test all functionality on mobile
3. Verify touch interactions work
4. Test modal dialogs on mobile

**Expected Results**:
- Dashboard should be fully functional on mobile
- UI should adapt to smaller screens
- Touch interactions should work properly

### 12. Performance Testing
**Test ID**: ASD-012
**Description**: Test performance with large datasets
**Steps**:
1. Create many test shows (100+)
2. Test page load times
3. Test filtering and sorting performance
4. Test bulk operations with many shows

**Expected Results**:
- Page should load within reasonable time
- Operations should remain responsive
- No timeout errors should occur

### 13. Error Handling
**Test ID**: ASD-013
**Description**: Test error scenarios
**Steps**:
1. Test with invalid show IDs
2. Test bulk operations on non-existent shows
3. Test with database connection issues
4. Test CSRF token validation

**Expected Results**:
- Appropriate error messages should be displayed
- System should handle errors gracefully
- No sensitive information should be exposed

### 14. Integration Testing
**Test ID**: ASD-014
**Description**: Test integration with existing features
**Steps**:
1. Navigate from dashboard to existing admin pages
2. Test links to registration management
3. Test links to show editing
4. Verify data consistency across pages

**Expected Results**:
- Navigation should work seamlessly
- Data should be consistent across all pages
- No broken links or missing functionality

## Regression Testing
After implementing the dashboard, verify that existing functionality still works:
- Admin dashboard main page
- Show creation and editing
- Registration management
- User management
- Payment processing

## Browser Compatibility
Test the dashboard on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Android Chrome)

## Performance Benchmarks
- Page load time: < 3 seconds
- Search response time: < 1 second
- Bulk operation response time: < 5 seconds
- Mobile page load time: < 5 seconds

## Security Testing
- Verify CSRF protection on all forms
- Test SQL injection prevention
- Verify role-based access control
- Test XSS prevention in search and display

## Accessibility Testing
- Test keyboard navigation
- Verify screen reader compatibility
- Test color contrast ratios
- Verify ARIA labels and roles
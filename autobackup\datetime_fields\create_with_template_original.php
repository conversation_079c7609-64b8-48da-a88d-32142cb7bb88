                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control' . (!empty($data['start_date_err']) ? ' is-invalid' : '') . '" id="start_date" name="start_date" value="' . $data['start_date'] . '" required>
                            ' . (!empty($data['start_date_err']) ? '<div class="invalid-feedback">' . $data['start_date_err'] . '</div>' : '') . '
                        </div>
                        <div class="col-md-6">
                            <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control' . (!empty($data['end_date_err']) ? ' is-invalid' : '') . '" id="end_date" name="end_date" value="' . $data['end_date'] . '" required>
                            ' . (!empty($data['end_date_err']) ? '<div class="invalid-feedback">' . $data['end_date_err'] . '</div>' : '') . '
                        </div>
                    </div>';
                    
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="registration_start" class="form-label">Registration Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control' . (!empty($data['registration_start_err']) ? ' is-invalid' : '') . '" id="registration_start" name="registration_start" value="' . (isset($data['registration_start']) ? $data['registration_start'] : '') . '" required>
                            ' . (!empty($data['registration_start_err']) ? '<div class="invalid-feedback">' . $data['registration_start_err'] . '</div>' : '') . '
                        </div>
                        <div class="col-md-6">
                            <label for="registration_end" class="form-label">Registration End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control' . (!empty($data['registration_end_err']) ? ' is-invalid' : '') . '" id="registration_end" name="registration_end" value="' . (isset($data['registration_end']) ? $data['registration_end'] : '') . '" required>
                            ' . (!empty($data['registration_end_err']) ? '<div class="invalid-feedback">' . $data['registration_end_err'] . '</div>' : '') . '
                        </div>
                    </div>';
<!DOCTYPE html>
<html>
<head>
    <title>Syntax Check</title>
</head>
<body>
    <h2>Syntax Check</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load and check syntax
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Script loaded, checking syntax...');
                
                try {
                    // Try to create a function with the code to check syntax
                    new Function(code);
                    output('✓ Syntax is valid');
                    
                    // Now try to execute it
                    output('Executing script...');
                    eval(code);
                    
                    // Check what was created
                    output('Checking what exists after execution:');
                    output('- typeof CameraBanner: ' + typeof CameraBanner);
                    output('- typeof window.CameraBanner: ' + typeof window.CameraBanner);
                    output('- window.cameraBanner exists: ' + (typeof window.cameraBanner !== 'undefined'));
                    
                    // Try to find the class in global scope
                    for (let prop in window) {
                        if (prop.includes('Camera') || prop.includes('Banner')) {
                            output('Found related property: ' + prop + ' = ' + typeof window[prop]);
                        }
                    }
                    
                } catch (syntaxError) {
                    output('✗ Syntax error: ' + syntaxError.message);
                    output('Error line info: ' + syntaxError.stack);
                    
                    // Try to find the problematic line
                    const lines = code.split('\n');
                    const errorMatch = syntaxError.message.match(/line (\d+)/);
                    if (errorMatch) {
                        const lineNum = parseInt(errorMatch[1]);
                        output('Problem around line ' + lineNum + ': ' + lines[lineNum - 1]);
                    }
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
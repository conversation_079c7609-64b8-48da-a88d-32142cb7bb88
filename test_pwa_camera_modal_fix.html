<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Camera Modal Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background: #f8f9fa;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .test-button {
            margin: 10px;
            padding: 15px 30px;
            font-size: 1.1rem;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .test-results {
            background: #f1f8e9;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-camera"></i> PWA Camera Modal Fix Test</h1>
        
        <div class="test-info">
            <h5><i class="fas fa-info-circle"></i> Test Information</h5>
            <p>This test verifies that the PWA camera and QR scanner modals close properly using multiple methods:</p>
            <ul>
                <li><strong>Cancel Button:</strong> Click the "Cancel" button at the bottom</li>
                <li><strong>Close Button (X):</strong> Click the X button in the top-right corner</li>
                <li><strong>Backdrop Click:</strong> Click outside the modal content area</li>
                <li><strong>Escape Key:</strong> Press the Escape key on your keyboard</li>
                <li><strong>Force Close:</strong> Use the global function if modal gets stuck</li>
                <li><strong>QR Beep Sound:</strong> Test the audio beep that plays when QR codes are detected (max volume, works even when phone is muted)</li>
            </ul>
        </div>

        <div class="text-center">
            <h3>Test Camera Modal</h3>
            <button class="btn btn-primary test-button" onclick="testCameraModal()">
                <i class="fas fa-camera"></i> Open Camera Modal
            </button>
            
            <h3 class="mt-4">Test QR Scanner Modal</h3>
            <button class="btn btn-success test-button" onclick="testQRModal()">
                <i class="fas fa-qrcode"></i> Open QR Scanner Modal
            </button>

            <h3 class="mt-4">Test QR Beep Sound</h3>
            <button class="btn btn-info test-button" onclick="testQRBeepSound()">
                <i class="fas fa-volume-up"></i> Test QR Beep Sound
            </button>
            
            <h3 class="mt-4">Emergency Controls</h3>
            <button class="btn btn-warning test-button" onclick="forceCloseAllModals()">
                <i class="fas fa-times-circle"></i> Force Close All Modals
            </button>
        </div>

        <div class="test-results" id="testResults">
            <h5><i class="fas fa-check-circle"></i> Test Results</h5>
            <div id="resultsList"></div>
        </div>
    </div>

    <!-- Hidden file input for camera test -->
    <input type="file" name="test_camera_input" style="display: none;" accept="image/*">

    <script src="public/js/pwa-features.js"></script>
    <script>
        // Test functions
        function testCameraModal() {
            logResult('Opening camera modal...');
            if (window.pwaFeatures) {
                window.pwaFeatures.openCamera('test_camera_input');
            } else {
                logResult('ERROR: PWA Features not loaded', 'error');
            }
        }

        function testQRModal() {
            logResult('Opening QR scanner modal...');
            if (window.pwaFeatures) {
                window.pwaFeatures.openQRScanner();
            } else {
                logResult('ERROR: PWA Features not loaded', 'error');
            }
        }

        function testQRBeepSound() {
            logResult('Testing QR beep sound...');
            if (window.pwaFeatures && window.pwaFeatures.playQRBeepSound) {
                window.pwaFeatures.playQRBeepSound();
                logResult('QR beep sound test completed', 'success');
            } else {
                logResult('ERROR: QR beep sound function not available', 'error');
            }
        }

        function forceCloseAllModals() {
            logResult('Force closing all modals...');
            if (window.forceCloseCameraModals) {
                window.forceCloseCameraModals();
                logResult('Force close executed successfully', 'success');
            } else {
                logResult('ERROR: Force close function not available', 'error');
            }
        }

        function logResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultsList = document.getElementById('resultsList');
            
            resultsDiv.style.display = 'block';
            
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 
                        type === 'success' ? 'fas fa-check' : 'fas fa-info';
            const color = type === 'error' ? 'text-danger' : 
                         type === 'success' ? 'text-success' : 'text-info';
            
            const resultItem = document.createElement('div');
            resultItem.className = `${color} mb-1`;
            resultItem.innerHTML = `<i class="${icon}"></i> [${timestamp}] ${message}`;
            
            resultsList.appendChild(resultItem);
            resultsList.scrollTop = resultsList.scrollHeight;
        }

        // Test PWA Features loading
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.pwaFeatures) {
                    logResult('PWA Features loaded successfully', 'success');
                } else {
                    logResult('PWA Features failed to load', 'error');
                }
            }, 1000);
        });

        // Monitor modal events
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('camera-modal') || 
                e.target.classList.contains('qr-scanner-modal')) {
                logResult('Backdrop click detected');
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && 
                (document.querySelector('.camera-modal') || document.querySelector('.qr-scanner-modal'))) {
                logResult('Escape key pressed');
            }
        });
    </script>
</body>
</html>
# Notification Preferences Toggle Layout Fix

## Issue
The toggle switches in `/user/notification_preferences` are positioned too close to the left table edge, with approximately 1/4 of the toggle hanging outside the table edge.

## Root Cause
The Bootstrap form-switch layout places the toggle input to the left of the label, and in constrained containers, this can cause the toggle to extend beyond the container boundaries.

## Solution
1. Modify the form-check layout to use flexbox with proper spacing
2. Add specific CSS rules for notification preferences toggles
3. Ensure mobile-first responsive design is maintained

## Files Modified
- `views/user/notification_preferences.php` - Updated toggle layout structure
- `public/css/notifications.css` - Added specific styles for user preferences toggles

## Changes Made
- Changed form-check structure to use flexbox layout with justify-content-between
- Added proper padding and margins to prevent toggle overflow
- Maintained accessibility and mobile responsiveness
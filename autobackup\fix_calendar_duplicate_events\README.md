# Fix Calendar Duplicate Events Issue

## Problem
When unchecking and rechecking calendars in the advanced filter, events are being duplicated instead of properly replaced.

## Root Cause
The calendar filtering system was not properly clearing existing events before loading new ones, causing events to accumulate.

## Solution
1. Remove remaining references to quick calendar toggles in calendar-filters.js
2. Improve the event clearing mechanism in the applyFilters function
3. Ensure proper DOM cleanup when events are cleared

## Files Modified
- `public/js/calendar-filters.js` - Removed quick calendar toggle synchronization
- Enhanced event clearing mechanism in applyFilters function
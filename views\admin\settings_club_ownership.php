<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Club Ownership Settings</h1>
            <p class="text-muted">Review and manage club ownership verification requests from users</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Club ownership settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="type" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Club Ownership Management Sections -->
    <div class="row g-4 mb-5">
        
        <!-- View Requests Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-list text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">View Requests</h4>
                        <?php
                        // Get pending verification count
                        $pendingCount = 0;
                        try {
                            $calendarModel = new CalendarModel();
                            $stats = $calendarModel->getVerificationStatistics();
                            $pendingCount = $stats['pending'] ?? 0;
                        } catch (Exception $e) {
                            // Silently handle error
                        }
                        ?>
                        <?php if ($pendingCount > 0): ?>
                            <span class="badge bg-danger ms-2">
                                <?php echo $pendingCount; ?> pending
                            </span>
                        <?php endif; ?>
                    </div>
                    <p class="card-text text-muted">Review pending club ownership verification requests and manage approvals.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/clubOwnershipVerifications" class="stretched-link text-decoration-none">
                        <span class="d-none">View Ownership Requests</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Verification Statistics Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-chart-bar text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Statistics</h4>
                    </div>
                    <div class="row text-center">
                        <?php
                        try {
                            $calendarModel = new CalendarModel();
                            $stats = $calendarModel->getVerificationStatistics();
                        } catch (Exception $e) {
                            $stats = ['pending' => 0, 'approved' => 0, 'rejected' => 0, 'total' => 0];
                        }
                        ?>
                        <div class="col-6 mb-3">
                            <div class="text-warning">
                                <i class="fas fa-clock fa-2x"></i>
                                <h5 class="mt-2"><?php echo $stats['pending'] ?? 0; ?></h5>
                                <small>Pending</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="text-success">
                                <i class="fas fa-check-circle fa-2x"></i>
                                <h5 class="mt-2"><?php echo $stats['approved'] ?? 0; ?></h5>
                                <small>Approved</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-danger">
                                <i class="fas fa-times-circle fa-2x"></i>
                                <h5 class="mt-2"><?php echo $stats['rejected'] ?? 0; ?></h5>
                                <small>Rejected</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-primary">
                                <i class="fas fa-list fa-2x"></i>
                                <h5 class="mt-2"><?php echo $stats['total'] ?? 0; ?></h5>
                                <small>Total</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-bolt text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Quick Actions</h4>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="<?php echo BASE_URL; ?>/admin/clubOwnershipVerifications" class="btn btn-outline-warning">
                            <i class="fas fa-list me-2"></i> View All Requests
                        </a>
                        <a href="<?php echo BASE_URL; ?>/admin/clubOwnershipVerifications?status=pending" class="btn btn-outline-danger">
                            <i class="fas fa-clock me-2"></i> Pending Only
                        </a>
                        <a href="<?php echo BASE_URL; ?>/calendar/manageClubs" class="btn btn-outline-info">
                            <i class="fas fa-users me-2"></i> Manage Clubs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Verification Process Information -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Club Ownership Verification Process</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>How It Works</h5>
                            <ol>
                                <li>Users request ownership of existing clubs</li>
                                <li>They provide verification information and documentation</li>
                                <li>Administrators review the request and supporting materials</li>
                                <li>Requests are approved or rejected with feedback</li>
                                <li>Approved users gain management access to the club</li>
                            </ol>
                        </div>
                        <div class="col-md-6">
                            <h5>Review Guidelines</h5>
                            <ul>
                                <li>Verify the user's identity and authority</li>
                                <li>Check provided documentation for authenticity</li>
                                <li>Ensure the club information is accurate</li>
                                <li>Consider the user's history and reputation</li>
                                <li>Provide clear feedback for rejections</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning mt-4">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> Club ownership grants significant permissions including the ability to edit club information, 
                        manage events, and control club membership. Please review all requests carefully.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
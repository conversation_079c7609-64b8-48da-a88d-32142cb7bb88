<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/user/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/user/registrations">My Registrations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">View Scores</li>
                </ol>
            </nav>
            
            <h1><?php echo $data['title']; ?></h1>
            
            <?php flash('score_message'); ?>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Score Details</h5>
                    <div>
                        <a href="<?php echo URLROOT; ?>/user/registrations" class="btn btn-light btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Registrations
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($data['scores'])) : ?>
                        <div class="alert alert-info">
                            <h4 class="alert-heading">No Scores Available</h4>
                            <p>Scores for this registration have not been entered yet. Please check back later.</p>
                            <hr>
                            <p class="mb-0">If you believe this is an error, please contact the show coordinator.</p>
                        </div>
                    <?php else : ?>
                        <!-- Registration and Vehicle Info -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">Registration Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th>Show:</th>
                                                <td><?php echo $data['registration']->show_name; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Category:</th>
                                                <td><?php echo $data['registration']->category_name; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Registration Date:</th>
                                                <td><?php echo date('F j, Y', strtotime($data['registration']->created_at)); ?></td>
                                            </tr>
                                            <tr>
                                                <th>Status:</th>
                                                <td>
                                                    <?php 
                                                    $statusClass = '';
                                                    switch ($data['registration']->status) {
                                                        case 'approved':
                                                            $statusClass = 'badge bg-success';
                                                            break;
                                                        case 'pending':
                                                            $statusClass = 'badge bg-warning text-dark';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'badge bg-danger';
                                                            break;
                                                        default:
                                                            $statusClass = 'badge bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="<?php echo $statusClass; ?>"><?php echo ucfirst($data['registration']->status); ?></span>
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">Vehicle Information</h5>
                                    </div>
                                    <div class="card-body">
                                        <table class="table table-sm">
                                            <tr>
                                                <th>Vehicle:</th>
                                                <td><?php echo $data['vehicle']->year . ' ' . $data['vehicle']->make . ' ' . $data['vehicle']->model; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Color:</th>
                                                <td><?php echo $data['vehicle']->color; ?></td>
                                            </tr>
                                            <tr>
                                                <th>License Plate:</th>
                                                <td><?php echo $data['vehicle']->license_plate; ?></td>
                                            </tr>
                                            <tr>
                                                <th>Modifications:</th>
                                                <td><?php echo !empty($data['vehicle']->modifications) ? $data['vehicle']->modifications : 'None'; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Scoring Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Scoring Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Judge</th>
                                                <th>Category</th>
                                                <th>Score</th>
                                                <th>Comments</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($data['scores'] as $score) : ?>
                                            <tr>
                                                <td><?php echo $score->judge_name; ?></td>
                                                <td><?php echo $score->category_name; ?></td>
                                                <td>
                                                    <?php if (isset($data['scoring_settings']->display_raw_scores) && $data['scoring_settings']->display_raw_scores): ?>
                                                        <?php echo $score->total_score; ?> / <?php echo $score->max_possible; ?>
                                                    <?php else: ?>
                                                        <?php echo number_format(($score->total_score / $score->max_possible) * 100, 1); ?>%
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo !empty($score->comments) ? $score->comments : 'No comments provided'; ?></td>
                                                <td><?php echo date('M j, Y g:i A', strtotime($score->created_at)); ?></td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Detailed Scoring Breakdown -->
                        <?php if (!empty($data['score_details'])) : ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Detailed Scoring Breakdown</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>Judge</th>
                                                <th>Criteria</th>
                                                <th>Score</th>
                                                <th>Max</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($data['score_details'] as $detail) : ?>
                                            <tr>
                                                <td><?php echo $detail->judge_name; ?></td>
                                                <td><?php echo $detail->criteria_name; ?></td>
                                                <td><?php echo $detail->score; ?></td>
                                                <td><?php echo $detail->max_score; ?></td>
                                                <td><?php echo number_format(($detail->score / $detail->max_score) * 100, 1); ?>%</td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Scoring Formula Explanation -->
                        <?php if (isset($data['scoring_settings']->display_formula) && $data['scoring_settings']->display_formula): ?>
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Scoring Formula</h5>
                            </div>
                            <div class="card-body">
                                <p>This show uses the following formula to calculate final scores:</p>
                                <div class="alert alert-secondary">
                                    <code>
                                        <?php echo nl2br(htmlspecialchars($data['scoring_settings']->formula_explanation)); ?>
                                    </code>
                                </div>
                                
                                <?php if (isset($data['formula_breakdown'])): ?>
                                <h6>Your Score Calculation:</h6>
                                <div class="alert alert-info">
                                    <code>
                                        <?php echo nl2br(htmlspecialchars($data['formula_breakdown'])); ?>
                                    </code>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Final Score -->
                        <?php if (isset($data['weighted_total_score'])) : ?>
                        <!-- Score Badge and Formula Visualization Side by Side -->
                        <div class="row mb-4 align-items-stretch">
                            <!-- Final Score Badge - Left Side -->
                            <div class="col-md-4 d-flex align-items-center justify-content-center mb-3 mb-md-0">
                                <div class="p-4 bg-success text-white rounded shadow final-score-badge w-100 text-center">
                                    <h2 class="mb-0">Final Score</h2>
                                    <div class="display-1 fw-bold"><?php echo number_format($data['weighted_total_score'], 1); ?></div>
                                    <?php if (isset($data['scoring_settings']->normalize_scores) && $data['scoring_settings']->normalize_scores): ?>
                                    <div class="small">(Normalized to 100-point scale)</div>
                                    <?php endif; ?>
                                    
                                    <?php if (isset($data['placement'])): ?>
                                    <div class="mt-3">
                                        <h3 class="mb-0">Placement</h3>
                                        <div class="display-4 fw-bold">
                                            <?php 
                                            switch ($data['placement']) {
                                                case 1:
                                                    echo '<i class="fas fa-trophy text-warning"></i> 1st Place';
                                                    break;
                                                case 2:
                                                    echo '<i class="fas fa-trophy text-secondary"></i> 2nd Place';
                                                    break;
                                                case 3:
                                                    echo '<i class="fas fa-trophy" style="color: #cd7f32;"></i> 3rd Place';
                                                    break;
                                                default:
                                                    echo $data['placement'] . 'th Place';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Score Breakdown - Right Side -->
                            <div class="col-md-8">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">Score Breakdown</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php if (isset($data['category_scores']) && !empty($data['category_scores'])): ?>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Category</th>
                                                        <th>Score</th>
                                                        <th>Weight</th>
                                                        <th>Weighted Score</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($data['category_scores'] as $cat): ?>
                                                    <tr>
                                                        <td><?php echo $cat['name']; ?></td>
                                                        <td><?php echo number_format($cat['score'], 1); ?></td>
                                                        <td><?php echo $cat['weight']; ?>%</td>
                                                        <td><?php echo number_format($cat['weighted'], 1); ?></td>
                                                    </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr class="table-primary">
                                                        <th colspan="3">Final Score:</th>
                                                        <th><?php echo number_format($data['weighted_total_score'], 1); ?></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                        <?php else: ?>
                                        <p>Detailed score breakdown is not available for this registration.</p>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- Awards -->
                        <?php if (!empty($data['awards'])) : ?>
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0"><i class="fas fa-award"></i> Awards</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($data['awards'] as $award) : ?>
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-warning">
                                            <div class="card-body text-center">
                                                <h5 class="card-title"><?php echo $award->name; ?></h5>
                                                <p class="card-text"><?php echo $award->description; ?></p>
                                                <div class="badge bg-warning text-dark">Awarded: <?php echo date('F j, Y', strtotime($award->awarded_date)); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="<?php echo URLROOT; ?>/user/registrations" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Registrations
                        </a>
                        <?php if (!empty($data['scores']) && isset($data['registration']->show_id)) : ?>
                        <a href="<?php echo URLROOT; ?>/user/printScoreCard/<?php echo $data['registration']->id; ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-print"></i> Print Score Card
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
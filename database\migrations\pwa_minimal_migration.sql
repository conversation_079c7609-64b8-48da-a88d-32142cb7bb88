-- PWA Minimal Migration - Just the essential tables and settings
-- This is the safest version that should work on any MySQL/MariaDB setup

-- Create push subscriptions table (minimal version)
CREATE TABLE IF NOT EXISTS push_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    user_agent TEXT,
    active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create notification preferences table (minimal version)
CREATE TABLE IF NOT EXISTS notification_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    event_reminders TINYINT(1) DEFAULT 1,
    registration_updates TINYINT(1) DEFAULT 1,
    judging_reminders TINYINT(1) DEFAULT 1,
    payment_notifications TINYINT(1) DEFAULT 1,
    new_events TINYINT(1) DEFAULT 0,
    marketing TINYINT(1) DEFAULT 0,
    push_enabled TINYINT(1) DEFAULT 1,
    email_enabled TINYINT(1) DEFAULT 1,
    sms_enabled TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create offline sync queue table (minimal version)
CREATE TABLE IF NOT EXISTS offline_sync_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    sync_type VARCHAR(50) NOT NULL,
    sync_data TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create PWA metrics table (minimal version)
CREATE TABLE IF NOT EXISTS pwa_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    metric_date DATE NOT NULL,
    total_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    installed_users INT DEFAULT 0,
    push_subscribers INT DEFAULT 0,
    offline_usage_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create notification log table (minimal version)
CREATE TABLE IF NOT EXISTS notification_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    notification_type VARCHAR(20) NOT NULL,
    title VARCHAR(255),
    message TEXT,
    status VARCHAR(20) DEFAULT 'sent',
    metadata TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add basic indexes
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_user_id ON push_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_push_subscriptions_active ON push_subscriptions(active);
CREATE INDEX IF NOT EXISTS idx_notification_preferences_user_id ON notification_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_offline_sync_queue_user_id ON offline_sync_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_offline_sync_queue_status ON offline_sync_queue(status);
CREATE INDEX IF NOT EXISTS idx_notification_log_user_id ON notification_log(user_id);

-- Add PWA settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_enabled', '1', 'Enable Progressive Web App features', 'boolean', NOW()),
('push_notifications_enabled', '1', 'Enable push notifications', 'boolean', NOW()),
('offline_mode_enabled', '1', 'Enable offline functionality', 'boolean', NOW()),
('pwa_app_name', 'RER Events', 'PWA application name', 'text', NOW()),
('pwa_short_name', 'RER Events', 'PWA short name', 'text', NOW()),
('pwa_description', 'Rowan Elite Rides Events & Shows Management', 'PWA description', 'text', NOW()),
('pwa_theme_color', '#dc3545', 'PWA theme color', 'color', NOW()),
('pwa_background_color', '#ffffff', 'PWA background color', 'color', NOW()),
('vapid_public_key', '', 'VAPID public key for push notifications', 'text', NOW()),
('vapid_private_key', '', 'VAPID private key for push notifications (encrypted)', 'password', NOW());

-- Set up default notification preferences for existing users
INSERT IGNORE INTO notification_preferences (user_id, event_reminders, registration_updates, judging_reminders, payment_notifications)
SELECT id, 1, 1, 1, 1 FROM users;

-- Record migration
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_description, setting_type, created_at) VALUES
('pwa_migration_version', '1.0.0', 'PWA features migration version', 'text', NOW());

SELECT 'PWA minimal migration completed successfully!' as result;
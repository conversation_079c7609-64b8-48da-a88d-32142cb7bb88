<?php
// Backup of CalendarController.php before color fix
// Version: Enhanced color debugging
// Date: 2024-12-20

// Key changes made:
// 1. Added comprehensive debug logging for event color handling
// 2. Enhanced the color setting logic in getEvents method around line 315-340
// 3. Added detailed logging to track color values from database to JSON output

// The main enhancement was in the color setting section:
// - Added DEBUG_MODE logging for event color fields
// - Added logging for both event.color and calendar_color values
// - Added logging for final formatted event color properties
// - This helps troubleshoot color issues by showing exactly what colors are being sent to JavaScript

// Debug output includes:
// - Event ID and Title
// - Raw color values from database (event.color, calendar_color)
// - Which color source is being used (event vs calendar)
// - Final formatted color properties (backgroundColor, borderColor, color)
?>
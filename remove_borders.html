<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remove All Borders</title>
</head>
<body>
    <h1>Border Removal Tool</h1>
    <p>This will remove any borders I accidentally added.</p>
    <button onclick="removeBorders()">Remove All Borders</button>
    <div id="status"></div>

    <script>
        function removeBorders() {
            // Remove any borders from body
            document.body.style.border = '';
            document.body.style.outline = '';
            document.body.style.backgroundColor = '';
            
            // Remove borders from html element
            document.documentElement.style.border = '';
            document.documentElement.style.outline = '';
            
            // Remove any inline styles that might have borders
            const allElements = document.querySelectorAll('*');
            allElements.forEach(el => {
                if (el.style.border && el.style.border.includes('green')) {
                    el.style.border = '';
                }
                if (el.style.backgroundColor && el.style.backgroundColor.includes('green')) {
                    el.style.backgroundColor = '';
                }
            });
            
            document.getElementById('status').innerHTML = '<p style="color: green;">✅ All borders removed!</p>';
        }
        
        // Auto-remove on load
        window.addEventListener('load', removeBorders);
    </script>
</body>
</html>

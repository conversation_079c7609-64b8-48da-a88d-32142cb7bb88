# Notification Modal Backdrop Fix

## Issue
When clicking the "Notify Me" button on events/shows and then clicking "Cancel" in the modal, the page remains greyed out with a cover layer. <PERSON>sole shows aria-hidden focus warning.

## Root Cause
- Modal backdrop not properly removed when modal is cancelled
- aria-hidden attribute conflicts with focused elements
- Bootstrap modal event handlers not properly cleaning up

## Solution
1. Add proper modal event listeners for cleanup
2. Fix aria-hidden attribute management
3. Ensure backdrop is removed on all modal close events
4. Add focus management for accessibility

## Files Modified
- public/js/notifications.js

## Changes Made
- Added modal cleanup event listeners
- Fixed backdrop removal on cancel
- Improved aria-hidden attribute handling
- Added proper focus management

## Testing
- Click "Notify Me" button
- Click "Cancel" in modal
- Verify page is not greyed out
- Verify no console errors
<?php
/**
 * Camera Banner Settings API
 * Handles banner rotation settings
 */

// Suppress errors initially to prevent HTML output
error_reporting(0);
ini_set('display_errors', 0);

// Define APPROOT if not already defined
if (!defined('APPROOT')) {
    define('APPROOT', dirname(__DIR__));
}

// Set JSON headers first
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

try {
    require_once APPROOT . '/config/config.php';
    require_once APPROOT . '/core/Database.php';
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load required files',
        'error' => $e->getMessage()
    ]);
    exit;
}

// Debug mode check
$debug_mode = defined('DEBUG_MODE') ? DEBUG_MODE : false;

// Add debug logging if enabled
if ($debug_mode) {
    error_log("Camera banner settings API accessed - Method: " . $_SERVER['REQUEST_METHOD']);
}

try {
    // Add error reporting for debugging
    if ($debug_mode) {
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        error_log("Attempting to create Database instance for settings");
    }
    
    $db = new Database();
    
    if ($debug_mode) {
        error_log("Database instance created successfully for settings");
    }

    $method = $_SERVER['REQUEST_METHOD'];
    
    if ($method === 'GET') {
        // Get current settings with null safety
        $delay = 5000; // Default 5 seconds
        
        try {
            $db->query("SELECT setting_value FROM system_settings WHERE setting_key = 'camera_banner_delay' LIMIT 1");
            $db->execute();
            $row = $db->single();
            
            if ($row && !empty($row->setting_value)) {
                $delay = (int)$row->setting_value;
            }
        } catch (Exception $e) {
            // Use default if setting doesn't exist
            if ($debug_mode) {
                error_log('Camera banner delay setting not found: ' . $e->getMessage());
            }
        }
        
        echo json_encode([
            'success' => true,
            'delay' => $delay
        ]);

    } elseif ($method === 'POST') {
        // Check if user is admin (following the existing codebase pattern)
        session_start();
        if (!isset($_SESSION['user_id']) || ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator')) {
            http_response_code(403);
            echo json_encode(['success' => false, 'message' => 'Access denied. Admin or Coordinator role required.']);
            exit;
        }
        
        // Update settings
        $delay = (int)($_POST['delay'] ?? 5);
        
        // Convert seconds to milliseconds
        $delay_ms = $delay * 1000;
        
        // Validate delay (1-60 seconds)
        if ($delay < 1 || $delay > 60) {
            echo json_encode([
                'success' => false,
                'message' => 'Delay must be between 1 and 60 seconds'
            ]);
            exit;
        }
        
        // Update or insert setting
        $db->query("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, description) 
                   VALUES ('camera_banner_delay', :delay, 'Banner rotation delay in milliseconds for camera modals', 'media', 'Camera banner delay')
                   ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
        $db->bind(':delay', $delay_ms);
        $db->execute();
        
        echo json_encode([
            'success' => true,
            'message' => 'Settings saved successfully',
            'delay' => $delay_ms
        ]);
    }

} catch (PDOException $e) {
    $error_response = [
        'success' => false,
        'message' => 'Database error occurred'
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);

} catch (Exception $e) {
    $error_response = [
        'success' => false,
        'message' => 'An error occurred'
    ];
    
    if ($debug_mode) {
        $error_response['debug'] = [
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ];
    }
    
    http_response_code(500);
    echo json_encode($error_response);
}
?>
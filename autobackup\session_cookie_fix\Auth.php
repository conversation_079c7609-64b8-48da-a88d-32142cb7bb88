<?php
/**
 * Auth Class
 * 
 * This class handles user authentication, including login, registration,
 * password hashing, and role-based access control.
 */
class Auth {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Check for remember me cookie and auto-login if valid
        if (!isset($_SESSION['user_id']) && isset($_COOKIE['remember_token'])) {
            $this->loginWithRememberToken($_COOKIE['remember_token']);
        }
        
        // Check if session has expired
        if (isset($_SESSION['login_time']) && isset($_SESSION['user_id'])) {
            $sessionLifetime = $this->getSessionLifetime();
            
            // Log session information for debugging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Session check - Current time: ' . time());
                error_log('Session check - Login time: ' . $_SESSION['login_time']);
                error_log('Session check - Session lifetime: ' . $sessionLifetime);
                error_log('Session check - Elapsed time: ' . (time() - $_SESSION['login_time']));
                error_log('Session check - Is Facebook login: ' . (isset($_SESSION['facebook_login']) ? 'Yes' : 'No'));
            }
            
            if (time() - $_SESSION['login_time'] > $sessionLifetime) {
                // Session has expired, log the user out
                error_log('Session expired for user ID: ' . $_SESSION['user_id'] . ' after ' . (time() - $_SESSION['login_time']) . ' seconds');
                $this->logout();
                
                // Redirect to login page with expired message
                if (!isset($_SESSION['session_expired_redirect']) && !defined('DOING_AJAX')) {
                    $_SESSION['session_expired_redirect'] = true;
                    if (defined('BASE_URL')) {
                        header('Location: ' . BASE_URL . '/auth/login?expired=1');
                        exit;
                    }
                }
            } else {
                // Refresh the session timestamp to extend the session
                // This is important for long-running browser sessions
                $_SESSION['login_time'] = time();
            }
        }
    }
    
    /**
     * Get session lifetime from settings
     * 
     * @return int Session lifetime in seconds
     */
    public function getSessionLifetime() {
        // Default to 24 hours (86400 seconds)
        $defaultLifetime = 86400;
        
        try {
            // Check if we're using a Facebook session
            $isFacebookSession = isset($_SESSION['facebook_login']) && $_SESSION['facebook_login'] === true;
            
            // Get the appropriate setting key
            $settingKey = $isFacebookSession ? 'facebook_session_lifetime' : 'session_lifetime';
            
            // Try to get the setting from the database
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', $settingKey);
            $result = $this->db->single();
            
            if ($result && is_numeric($result->setting_value)) {
                $lifetime = (int)$result->setting_value;
                
                // Log the retrieved session lifetime for debugging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log('Retrieved ' . ($isFacebookSession ? 'Facebook' : 'regular') . ' session lifetime: ' . $lifetime . ' seconds');
                }
                
                // Ensure the lifetime is at least 5 minutes (300 seconds)
                if ($lifetime < 300) {
                    error_log('Session lifetime too short, using minimum value of 300 seconds');
                    $lifetime = 300;
                }
                
                return $lifetime;
            } else {
                // Log that we couldn't find the setting
                error_log('Could not find ' . $settingKey . ' in system_settings, using default: ' . $defaultLifetime);
            }
        } catch (Exception $e) {
            error_log('Error getting session lifetime: ' . $e->getMessage());
        }
        
        return $defaultLifetime;
    }
}
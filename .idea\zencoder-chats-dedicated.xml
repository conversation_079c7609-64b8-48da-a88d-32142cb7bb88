<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ai.zencoder.plugin.chat.state-dedicated">
    <option name="activeChatId" value="df5a8345-f4a2-4ef3-b74a-eb69b31e8488" />
    <option name="chatSessions" value="{&quot;df5a8345-f4a2-4ef3-b74a-eb69b31e8488&quot;:{&quot;id&quot;:&quot;df5a8345-f4a2-4ef3-b74a-eb69b31e8488&quot;,&quot;createdAt&quot;:1750104639996,&quot;updatedAt&quot;:1750106286049,&quot;title&quot;:&quot;structure.md – Lists the project structure with...&quot;,&quot;messages&quot;:[{&quot;id&quot;:&quot;96dbb730-7f39-4ca0-9841-ff08e0c4be2a&quot;,&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;structure.md – Lists the project structure with short descriptions of each folder/file and its purpose.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;features.md – Lists the main features already developed and their current status.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;task_pending.md – Lists features still to be developed. Only remove items when instructed.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;SYSTEM_INIT.md - outlines how to properly initialize the Events and Shows Management System in scripts and applications. We will continue developing this project, and I want you to read and analyze the *.md files initially, and then the code files that are related...  the fullcalander intergration is not our own calander we want our own custom built . i dont want a simple calendar . i dont like simple . i want advanced custom one with all the bells and whistles . just build it from scratch and make it custom . it needs to look awesum and usable on desktop as well as small view like mobile phones.&quot;}],&quot;context&quot;:{&quot;codebaseEnabled&quot;:true},&quot;createdAt&quot;:1750104639996,&quot;isInternal&quot;:false,&quot;rawContent&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;structure.md – Lists the project structure with short descriptions of each folder/file and its purpose.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;features.md – Lists the main features already developed and their current status.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;task_pending.md – Lists features still to be developed. Only remove items when instructed.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;SYSTEM_INIT.md - outlines how to properly initialize the Events and Shows Management System in scripts and applications. We will continue developing this project, and I want you to read and analyze the *.md files initially, and then the code files that are related...  the fullcalander intergration is not our own calander we want our own custom built . i dont want a simple calendar . i dont like simple . i want advanced custom one with all the bells and whistles . just build it from scratch and make it custom . it needs to look awesum and usable on desktop as well as small view like mobile phones. &quot;}]}]}},{&quot;id&quot;:&quot;1d4a61b1-a8bd-4b30-8832-e69444bd245e&quot;,&quot;role&quot;:&quot;assistant&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;I'll help you develop a custom calendar for your Events and Shows Management System. Let me first analyze the project structure and understand what's already been built by examining the markdown files you mentioned.&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_013H4KRXyFBPHHh4SBBQvJ33&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;structure.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\structure.md&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01KDAtw5oJnULFzDD2btG7it&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;features.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\features.md&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01CifhrWyYED1xbQci1f3Rs6&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;task_pending.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\task_pending.md&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_019mCQi1fQMN3eTxVFuUw7Td&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;SYSTEM_INIT.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\SYSTEM_INIT.md&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Now, let's look at the existing calendar implementation to understand what we need to build:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_012P2UWLrxQUbAgvPxrwmpPp&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;controllers/CalendarController.php&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\controllers\\CalendarController.php&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Let's look at the calendar view files to understand the current implementation:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01AVoxP6CibuMJX1UuW9Pf7W&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;views/calendar/index.php&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\views\\calendar\\index.php&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Let's check the CalendarModel to understand the database structure:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01LJ71nBtZqCcjGgvrdcVniN&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;models/CalendarModel.php&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\models\\CalendarModel.php&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Let's look at the database structure for the calendar:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01Ta4HCTsPN7wvBjqqyJmMdg&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;database/calendar.sql&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\database\\calendar.sql&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Based on my analysis of the project structure and requirements, I'll now create a custom calendar implementation to replace the FullCalendar integration. Let's start by creating the necessary files for our custom calendar.\n\nFirst, let's create a new JavaScript file for our custom calendar:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01Gn8CGWvJoCq7d6LGDQSiw6&quot;,&quot;statusText&quot;:&quot;Change generated&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;autobackup/custom_calendar/original_index.php&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\autobackup\\custom_calendar\\original_index.php&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;codeHunk&quot;,&quot;lineBasedDiff&quot;:&quot;--- autobackup/custom_calendar/original_index.php\n+++ autobackup/custom_calendar/original_index.php\n@@ -0,0 +1,589 @@\n+&lt;?php require APPROOT . '/views/includes/header.php'; ?&gt;\n+\n+&lt;div class=\&quot;container-fluid\&quot;&gt;\n+    &lt;div class=\&quot;row mb-4\&quot;&gt;\n+        &lt;div class=\&quot;col-md-6\&quot;&gt;\n+            &lt;h1&gt;Event Calendar&lt;/h1&gt;\n+        &lt;/div&gt;\n+        &lt;div class=\&quot;col-md-6 text-end\&quot;&gt;\n+            &lt;div class=\&quot;btn-group\&quot;&gt;\n+                &lt;a href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/createEvent\&quot; class=\&quot;btn btn-primary\&quot;&gt;\n+                    &lt;i class=\&quot;fas fa-plus me-2\&quot;&gt;&lt;/i&gt; Add Event\n+                &lt;/a&gt;\n+                &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-primary dropdown-toggle dropdown-toggle-split\&quot; data-bs-toggle=\&quot;dropdown\&quot; aria-expanded=\&quot;false\&quot;&gt;\n+                    &lt;span class=\&quot;visually-hidden\&quot;&gt;Toggle Dropdown&lt;/span&gt;\n+                &lt;/button&gt;\n+                &lt;ul class=\&quot;dropdown-menu dropdown-menu-end\&quot;&gt;\n+                    &lt;li&gt;&lt;a class=\&quot;dropdown-item\&quot; href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/manageCalendars\&quot;&gt;Manage Calendars&lt;/a&gt;&lt;/li&gt;\n+                    &lt;li&gt;&lt;a class=\&quot;dropdown-item\&quot; href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/manageVenues\&quot;&gt;Manage Venues&lt;/a&gt;&lt;/li&gt;\n+                    &lt;li&gt;&lt;a class=\&quot;dropdown-item\&quot; href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/manageClubs\&quot;&gt;Manage Clubs&lt;/a&gt;&lt;/li&gt;\n+                    &lt;li&gt;&lt;hr class=\&quot;dropdown-divider\&quot;&gt;&lt;/li&gt;\n+                    &lt;li&gt;&lt;a class=\&quot;dropdown-item\&quot; href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/import\&quot;&gt;Import Events&lt;/a&gt;&lt;/li&gt;\n+                    &lt;?php if (isAdmin()): ?&gt;\n+                    &lt;li&gt;&lt;hr class=\&quot;dropdown-divider\&quot;&gt;&lt;/li&gt;\n+                    &lt;li&gt;&lt;a class=\&quot;dropdown-item\&quot; href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/settings\&quot;&gt;Calendar Settings&lt;/a&gt;&lt;/li&gt;\n+                    &lt;?php endif; ?&gt;\n+                &lt;/ul&gt;\n+            &lt;/div&gt;\n+        &lt;/div&gt;\n+    &lt;/div&gt;\n+    \n+    &lt;?php flash('calendar_message'); ?&gt;\n+    \n+    &lt;div class=\&quot;row mb-4\&quot;&gt;\n+        &lt;div class=\&quot;col-md-3\&quot;&gt;\n+            &lt;div class=\&quot;card\&quot;&gt;\n+                &lt;div class=\&quot;card-header\&quot;&gt;\n+                    &lt;h5 class=\&quot;card-title mb-0\&quot;&gt;Calendars&lt;/h5&gt;\n+                &lt;/div&gt;\n+                &lt;div class=\&quot;card-body\&quot;&gt;\n+                    &lt;?php if (!empty($data['calendars'])): ?&gt;\n+                        &lt;div class=\&quot;calendar-list\&quot;&gt;\n+                            &lt;?php foreach ($data['calendars'] as $calendar): ?&gt;\n+                                &lt;div class=\&quot;form-check\&quot;&gt;\n+                                    &lt;input class=\&quot;form-check-input calendar-toggle\&quot; type=\&quot;checkbox\&quot; value=\&quot;&lt;?php echo $calendar-&gt;id; ?&gt;\&quot; id=\&quot;calendar-&lt;?php echo $calendar-&gt;id; ?&gt;\&quot; checked data-color=\&quot;&lt;?php echo $calendar-&gt;color; ?&gt;\&quot;&gt;\n+                                    &lt;label class=\&quot;form-check-label\&quot; for=\&quot;calendar-&lt;?php echo $calendar-&gt;id; ?&gt;\&quot;&gt;\n+                                        &lt;span class=\&quot;color-dot\&quot; style=\&quot;background-color: &lt;?php echo $calendar-&gt;color; ?&gt;\&quot;&gt;&lt;/span&gt;\n+                                        &lt;?php echo $calendar-&gt;name; ?&gt;\n+                                    &lt;/label&gt;\n+                                &lt;/div&gt;\n+                            &lt;?php endforeach; ?&gt;\n+                        &lt;/div&gt;\n+                    &lt;?php else: ?&gt;\n+                        &lt;p&gt;No calendars found.&lt;/p&gt;\n+                        &lt;a href=\&quot;&lt;?php echo URLROOT; ?&gt;/calendar/createCalendar\&quot; class=\&quot;btn btn-sm btn-primary\&quot;&gt;Create Calendar&lt;/a&gt;\n+                    &lt;?php endif; ?&gt;\n+                &lt;/div&gt;\n+            &lt;/div&gt;\n+            \n+            &lt;div class=\&quot;card mt-3\&quot;&gt;\n+                &lt;div class=\&quot;card-header\&quot;&gt;\n+                    &lt;h5 class=\&quot;card-title mb-0\&quot;&gt;Upcoming Events&lt;/h5&gt;\n+                &lt;/div&gt;\n+                &lt;div class=\&quot;card-body p-0\&quot;&gt;\n+                    &lt;div id=\&quot;upcoming-events\&quot; class=\&quot;list-group list-group-flush\&quot;&gt;\n+                        &lt;!-- Upcoming events will be loaded here via JavaScript --&gt;\n+                        &lt;div class=\&quot;list-group-item text-center\&quot;&gt;\n+                            &lt;div class=\&quot;spinner-border spinner-border-sm text-primary\&quot; role=\&quot;status\&quot;&gt;\n+                                &lt;span class=\&quot;visually-hidden\&quot;&gt;Loading...&lt;/span&gt;\n+                            &lt;/div&gt;\n+                            &lt;span class=\&quot;ms-2\&quot;&gt;Loading events...&lt;/span&gt;\n+                        &lt;/div&gt;\n+                    &lt;/div&gt;\n+                &lt;/div&gt;\n+            &lt;/div&gt;\n+        &lt;/div&gt;\n+        \n+        &lt;div class=\&quot;col-md-9\&quot;&gt;\n+            &lt;div class=\&quot;card\&quot;&gt;\n+                &lt;div class=\&quot;card-header\&quot;&gt;\n+                    &lt;div class=\&quot;d-flex justify-content-between align-items-center\&quot;&gt;\n+                        &lt;div class=\&quot;btn-group\&quot; role=\&quot;group\&quot; aria-label=\&quot;Calendar views\&quot;&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;view-month\&quot;&gt;Month&lt;/button&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;view-week\&quot;&gt;Week&lt;/button&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;view-day\&quot;&gt;Day&lt;/button&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;view-list\&quot;&gt;List&lt;/button&gt;\n+                        &lt;/div&gt;\n+                        &lt;div&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;prev-btn\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-chevron-left\&quot;&gt;&lt;/i&gt;\n+                            &lt;/button&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;today-btn\&quot;&gt;Today&lt;/button&gt;\n+                            &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-outline-secondary\&quot; id=\&quot;next-btn\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-chevron-right\&quot;&gt;&lt;/i&gt;\n+                            &lt;/button&gt;\n+                        &lt;/div&gt;\n+                    &lt;/div&gt;\n+                &lt;/div&gt;\n+                &lt;div class=\&quot;card-body\&quot;&gt;\n+                    &lt;div id=\&quot;calendar\&quot;&gt;&lt;/div&gt;\n+                &lt;/div&gt;\n+            &lt;/div&gt;\n+        &lt;/div&gt;\n+    &lt;/div&gt;\n+&lt;/div&gt;\n+\n+&lt;!-- Event Details Modal --&gt;\n+&lt;div class=\&quot;modal fade\&quot; id=\&quot;eventModal\&quot; tabindex=\&quot;-1\&quot; aria-labelledby=\&quot;eventModalLabel\&quot; aria-hidden=\&quot;true\&quot;&gt;\n+    &lt;div class=\&quot;modal-dialog modal-lg\&quot;&gt;\n+        &lt;div class=\&quot;modal-content\&quot;&gt;\n+            &lt;div class=\&quot;modal-header\&quot;&gt;\n+                &lt;h5 class=\&quot;modal-title\&quot; id=\&quot;eventModalLabel\&quot;&gt;Event Details&lt;/h5&gt;\n+                &lt;button type=\&quot;button\&quot; class=\&quot;btn-close\&quot; data-bs-dismiss=\&quot;modal\&quot; aria-label=\&quot;Close\&quot;&gt;&lt;/button&gt;\n+            &lt;/div&gt;\n+            &lt;div class=\&quot;modal-body\&quot;&gt;\n+                &lt;div class=\&quot;event-details\&quot;&gt;\n+                    &lt;div class=\&quot;mb-3\&quot;&gt;\n+                        &lt;h4 id=\&quot;event-title\&quot;&gt;&lt;/h4&gt;\n+                        &lt;div class=\&quot;text-muted\&quot;&gt;\n+                            &lt;span id=\&quot;event-date\&quot;&gt;&lt;/span&gt; • \n+                            &lt;span id=\&quot;event-time\&quot;&gt;&lt;/span&gt;\n+                        &lt;/div&gt;\n+                    &lt;/div&gt;\n+                    \n+                    &lt;div class=\&quot;row mb-3\&quot;&gt;\n+                        &lt;div class=\&quot;col-md-6\&quot;&gt;\n+                            &lt;div class=\&quot;mb-2\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-calendar-alt me-2\&quot;&gt;&lt;/i&gt;\n+                                &lt;span id=\&quot;event-calendar\&quot;&gt;&lt;/span&gt;\n+                            &lt;/div&gt;\n+                            &lt;div class=\&quot;mb-2\&quot; id=\&quot;event-location-container\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-map-marker-alt me-2\&quot;&gt;&lt;/i&gt;\n+                                &lt;span id=\&quot;event-location\&quot;&gt;&lt;/span&gt;\n+                            &lt;/div&gt;\n+                        &lt;/div&gt;\n+                        &lt;div class=\&quot;col-md-6\&quot;&gt;\n+                            &lt;div class=\&quot;mb-2\&quot; id=\&quot;event-show-container\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-car me-2\&quot;&gt;&lt;/i&gt;\n+                                &lt;span id=\&quot;event-show\&quot;&gt;&lt;/span&gt;\n+                            &lt;/div&gt;\n+                            &lt;div class=\&quot;mb-2\&quot; id=\&quot;event-url-container\&quot;&gt;\n+                                &lt;i class=\&quot;fas fa-link me-2\&quot;&gt;&lt;/i&gt;\n+                                &lt;a href=\&quot;#\&quot; id=\&quot;event-url\&quot; target=\&quot;_blank\&quot;&gt;&lt;/a&gt;\n+                            &lt;/div&gt;\n+                        &lt;/div&gt;\n+                    &lt;/div&gt;\n+                    \n+                    &lt;div class=\&quot;mb-3\&quot; id=\&quot;event-description-container\&quot;&gt;\n+                        &lt;h5&gt;Description&lt;/h5&gt;\n+                        &lt;div id=\&quot;event-description\&quot;&gt;&lt;/div&gt;\n+                    &lt;/div&gt;\n+                &lt;/div&gt;\n+            &lt;/div&gt;\n+            &lt;div class=\&quot;modal-footer\&quot;&gt;\n+                &lt;a href=\&quot;#\&quot; class=\&quot;btn btn-primary\&quot; id=\&quot;event-view-link\&quot;&gt;View Details&lt;/a&gt;\n+                &lt;button type=\&quot;button\&quot; class=\&quot;btn btn-secondary\&quot; data-bs-dismiss=\&quot;modal\&quot;&gt;Close&lt;/button&gt;\n+            &lt;/div&gt;\n+        &lt;/div&gt;\n+    &lt;/div&gt;\n+&lt;/div&gt;\n+\n+&lt;!-- Add FullCalendar CSS --&gt;\n+&lt;link href=\&quot;https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css\&quot; rel=\&quot;stylesheet\&quot;&gt;\n+\n+&lt;!-- Add custom CSS for calendar --&gt;\n+&lt;style&gt;\n+    .color-dot {\n+        display: inline-block;\n+        width: 12px;\n+        height: 12px;\n+        border-radius: 50%;\n+        margin-right: 5px;\n+    }\n+    \n+    .calendar-list {\n+        max-height: 200px;\n+        overflow-y: auto;\n+    }\n+    \n+    #calendar {\n+        height: 700px;\n+    }\n+    \n+    .fc-event {\n+        cursor: pointer;\n+    }\n+    \n+    .fc-toolbar-title {\n+        font-size: 1.5rem !important;\n+    }\n+    \n+    .upcoming-event {\n+        border-left: 4px solid #3788d8;\n+    }\n+    \n+    .upcoming-event-title {\n+        font-weight: 600;\n+    }\n+    \n+    .upcoming-event-time {\n+        font-size: 0.85rem;\n+    }\n+    \n+    @media (max-width: 768px) {\n+        #calendar {\n+            height: 500px;\n+        }\n+        \n+        .fc-toolbar-title {\n+            font-size: 1.2rem !important;\n+        }\n+        \n+        .fc-toolbar.fc-header-toolbar {\n+            flex-direction: column;\n+        }\n+        \n+        .fc-toolbar.fc-header-toolbar .fc-toolbar-chunk {\n+            margin-bottom: 0.5rem;\n+        }\n+    }\n+&lt;/style&gt;\n+\n+&lt;!-- Add FullCalendar JS with all required plugins --&gt;\n+&lt;script src=\&quot;https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js\&quot;&gt;&lt;/script&gt;\n+&lt;script src=\&quot;https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@5.10.1/main.min.js\&quot;&gt;&lt;/script&gt;\n+&lt;script src=\&quot;https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@5.10.1/main.min.js\&quot;&gt;&lt;/script&gt;\n+&lt;script src=\&quot;https://cdn.jsdelivr.net/npm/@fullcalendar/list@5.10.1/main.min.js\&quot;&gt;&lt;/script&gt;\n+&lt;script src=\&quot;https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@5.10.1/main.min.js\&quot;&gt;&lt;/script&gt;\n+\n+&lt;!-- Calendar initialization script --&gt;\n+&lt;script&gt;\n+    document.addEventListener('DOMContentLoaded', function() {\n+        // Get calendar settings\n+        const settings = {\n+            defaultView: '&lt;?php echo isset($data['settings']['default_view']) ? $data['settings']['default_view'] : 'month'; ?&gt;',\n+            businessHoursStart: '&lt;?php echo isset($data['settings']['business_hours_start']) ? $data['settings']['business_hours_start'] : '09:00:00'; ?&gt;',\n+            businessHoursEnd: '&lt;?php echo isset($data['settings']['business_hours_end']) ? $data['settings']['business_hours_end'] : '17:00:00'; ?&gt;',\n+            weekStartsOn: &lt;?php echo isset($data['settings']['week_starts_on']) ? $data['settings']['week_starts_on'] : 0; ?&gt;,\n+            timeFormat: '&lt;?php echo isset($data['settings']['time_format']) ? $data['settings']['time_format'] : '12'; ?&gt;',\n+            dateFormat: '&lt;?php echo isset($data['settings']['date_format']) ? $data['settings']['date_format'] : 'MM/DD/YYYY'; ?&gt;',\n+            defaultEventDuration: &lt;?php echo isset($data['settings']['default_event_duration']) ? $data['settings']['default_event_duration'] : 60; ?&gt;,\n+            enableDragDrop: &lt;?php echo isset($data['settings']['enable_drag_drop']) &amp;&amp; $data['settings']['enable_drag_drop'] ? 'true' : 'false'; ?&gt;,\n+            enableResize: &lt;?php echo isset($data['settings']['enable_resize']) &amp;&amp; $data['settings']['enable_resize'] ? 'true' : 'false'; ?&gt;,\n+            showWeekends: &lt;?php echo isset($data['settings']['show_weekends']) &amp;&amp; $data['settings']['show_weekends'] ? 'true' : 'false'; ?&gt;,\n+            defaultCalendarColor: '&lt;?php echo isset($data['settings']['default_calendar_color']) ? $data['settings']['default_calendar_color'] : '#3788d8'; ?&gt;'\n+        };\n+        \n+        // Initialize calendar\n+        const calendarEl = document.getElementById('calendar');\n+        const calendar = new FullCalendar.Calendar(calendarEl, {\n+            plugins: [ 'dayGrid', 'timeGrid', 'list', 'interaction' ],\n+            initialView: settings.defaultView,\n+            headerToolbar: false, // We're using custom header buttons\n+            firstDay: settings.weekStartsOn,\n+            weekends: settings.showWeekends,\n+            businessHours: {\n+                daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday\n+                startTime: settings.businessHoursStart,\n+                endTime: settings.businessHoursEnd\n+            },\n+            editable: settings.enableDragDrop,\n+            eventResizableFromStart: settings.enableResize,\n+            eventDurationEditable: settings.enableResize,\n+            selectable: true,\n+            selectMirror: true,\n+            dayMaxEvents: true,\n+            events: {\n+                url: '&lt;?php echo URLROOT; ?&gt;/calendar/getEvents',\n+                failure: function() {\n+                    alert('There was an error loading events!');\n+                }\n+            },\n+            eventTimeFormat: {\n+                hour: 'numeric',\n+                minute: '2-digit',\n+                meridiem: settings.timeFormat === '12'\n+            },\n+            select: function(info) {\n+                // Redirect to create event page with pre-filled dates\n+                window.location.href = '&lt;?php echo URLROOT; ?&gt;/calendar/createEvent?start=' + info.startStr + '&amp;end=' + info.endStr;\n+            },\n+            eventClick: function(info) {\n+                // Show event details in modal\n+                showEventDetails(info.event);\n+            },\n+            eventDrop: function(info) {\n+                // Update event dates via AJAX\n+                updateEventDates(info.event);\n+            },\n+            eventResize: function(info) {\n+                // Update event dates via AJAX\n+                updateEventDates(info.event);\n+            },\n+            loading: function(isLoading) {\n+                // Show/hide loading indicator\n+                if (isLoading) {\n+                    // Add loading indicator if needed\n+                }\n+            }\n+        });\n+        \n+        // Render calendar\n+        calendar.render();\n+        \n+        // Set active view button\n+        setActiveViewButton(settings.defaultView);\n+        \n+        // View buttons\n+        document.getElementById('view-month').addEventListener('click', function() {\n+            calendar.changeView('dayGridMonth');\n+            setActiveViewButton('month');\n+        });\n+        \n+        document.getElementById('view-week').addEventListener('click', function() {\n+            calendar.changeView('timeGridWeek');\n+            setActiveViewButton('week');\n+        });\n+        \n+        document.getElementById('view-day').addEventListener('click', function() {\n+            calendar.changeView('timeGridDay');\n+            setActiveViewButton('day');\n+        });\n+        \n+        document.getElementById('view-list').addEventListener('click', function() {\n+            calendar.changeView('listMonth');\n+            setActiveViewButton('list');\n+        });\n+        \n+        // Navigation buttons\n+        document.getElementById('prev-btn').addEventListener('click', function() {\n+            calendar.prev();\n+        });\n+        \n+        document.getElementById('next-btn').addEventListener('click', function() {\n+            calendar.next();\n+        });\n+        \n+        document.getElementById('today-btn').addEventListener('click', function() {\n+            calendar.today();\n+        });\n+        \n+        // Calendar toggles\n+        const calendarToggles = document.querySelectorAll('.calendar-toggle');\n+        calendarToggles.forEach(function(toggle) {\n+            toggle.addEventListener('change', function() {\n+                const calendarId = this.value;\n+                const isChecked = this.checked;\n+                \n+                // Filter events\n+                filterEvents();\n+                \n+                // Refetch events\n+                calendar.refetchEvents();\n+            });\n+        });\n+        \n+        // Helper function to set active view button\n+        function setActiveViewButton(view) {\n+            const viewButtons = document.querySelectorAll('[id^=\&quot;view-\&quot;]');\n+            viewButtons.forEach(function(button) {\n+                button.classList.remove('active', 'btn-primary');\n+                button.classList.add('btn-outline-secondary');\n+            });\n+            \n+            let buttonId;\n+            switch(view) {\n+                case 'dayGridMonth':\n+                case 'month':\n+                    buttonId = 'view-month';\n+                    break;\n+                case 'timeGridWeek':\n+                case 'week':\n+                    buttonId = 'view-week';\n+                    break;\n+                case 'timeGridDay':\n+                case 'day':\n+                    buttonId = 'view-day';\n+                    break;\n+                case 'listMonth':\n+                case 'list':\n+                    buttonId = 'view-list';\n+                    break;\n+                default:\n+                    buttonId = 'view-month';\n+            }\n+            \n+            const activeButton = document.getElementById(buttonId);\n+            if (activeButton) {\n+                activeButton.classList.remove('btn-outline-secondary');\n+                activeButton.classList.add('active', 'btn-primary');\n+            }\n+        }\n+        \n+        // Helper function to filter events based on selected calendars\n+        function filterEvents() {\n+            const selectedCalendars = [];\n+            calendarToggles.forEach(function(toggle) {\n+                if (toggle.checked) {\n+                    selectedCalendars.push(toggle.value);\n+                }\n+            });\n+            \n+            // Update events source\n+            const eventsSource = calendar.getEventSources()[0];\n+            if (eventsSource) {\n+                eventsSource.remove();\n+            }\n+            \n+            calendar.addEventSource({\n+                url: '&lt;?php echo URLROOT; ?&gt;/calendar/getEvents',\n+                extraParams: {\n+                    calendar_id: selectedCalendars.join(',')\n+                }\n+            });\n+        }\n+        \n+        // Helper function to show event details in modal\n+        function showEventDetails(event) {\n+            const modal = document.getElementById('eventModal');\n+            const modalInstance = new bootstrap.Modal(modal);\n+            \n+            // Set event details\n+            document.getElementById('event-title').textContent = event.title;\n+            \n+            // Format date\n+            const startDate = new Date(event.start);\n+            const endDate = event.end ? new Date(event.end) : null;\n+            \n+            let dateStr = startDate.toLocaleDateString();\n+            if (endDate &amp;&amp; startDate.toDateString() !== endDate.toDateString()) {\n+                dateStr += ' - ' + endDate.toLocaleDateString();\n+            }\n+            document.getElementById('event-date').textContent = dateStr;\n+            \n+            // Format time\n+            if (event.allDay) {\n+                document.getElementById('event-time').textContent = 'All day';\n+            } else {\n+                let timeStr = startDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });\n+                if (endDate) {\n+                    timeStr += ' - ' + endDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });\n+                }\n+                document.getElementById('event-time').textContent = timeStr;\n+            }\n+            \n+            // Set calendar\n+            document.getElementById('event-calendar').textContent = event.extendedProps.calendar_name || 'Unknown Calendar';\n+            \n+            // Set location\n+            const locationContainer = document.getElementById('event-location-container');\n+            if (event.extendedProps.location) {\n+                document.getElementById('event-location').textContent = event.extendedProps.location;\n+                locationContainer.style.display = 'block';\n+            } else {\n+                locationContainer.style.display = 'none';\n+            }\n+            \n+            // Set show\n+            const showContainer = document.getElementById('event-show-container');\n+            if (event.extendedProps.show_name) {\n+                document.getElementById('event-show').textContent = event.extendedProps.show_name;\n+                showContainer.style.display = 'block';\n+            } else {\n+                showContainer.style.display = 'none';\n+            }\n+            \n+            // Set URL\n+            const urlContainer = document.getElementById('event-url-container');\n+            if (event.url) {\n+                const urlElement = document.getElementById('event-url');\n+                urlElement.href = event.url;\n+                urlElement.textContent = event.url;\n+                urlContainer.style.display = 'block';\n+            } else {\n+                urlContainer.style.display = 'none';\n+            }\n+            \n+            // Set description\n+            const descriptionContainer = document.getElementById('event-description-container');\n+            if (event.extendedProps.description) {\n+                document.getElementById('event-description').innerHTML = event.extendedProps.description;\n+                descriptionContainer.style.display = 'block';\n+            } else {\n+                descriptionContainer.style.display = 'none';\n+            }\n+            \n+            // Set view link\n+            document.getElementById('event-view-link').href = '&lt;?php echo URLROOT; ?&gt;/calendar/event/' + event.id;\n+            \n+            // Show modal\n+            modalInstance.show();\n+        }\n+        \n+        // Helper function to update event dates\n+        function updateEventDates(event) {\n+            const eventData = {\n+                id: event.id,\n+                start_date: event.start.toISOString(),\n+                end_date: event.end ? event.end.toISOString() : null,\n+                all_day: event.allDay ? 1 : 0\n+            };\n+            \n+            // Send AJAX request\n+            fetch('&lt;?php echo URLROOT; ?&gt;/calendar/updateEventDates', {\n+                method: 'POST',\n+                headers: {\n+                    'Content-Type': 'application/json',\n+                    'X-Requested-With': 'XMLHttpRequest'\n+                },\n+                body: JSON.stringify(eventData)\n+            })\n+            .then(response =&gt; response.json())\n+            .then(data =&gt; {\n+                if (!data.success) {\n+                    alert('Error updating event: ' + data.message);\n+                    calendar.refetchEvents();\n+                }\n+            })\n+            .catch(error =&gt; {\n+                console.error('Error:', error);\n+                alert('Error updating event. Please try again.');\n+                calendar.refetchEvents();\n+            });\n+        }\n+        \n+        // Load upcoming events\n+        loadUpcomingEvents();\n+        \n+        function loadUpcomingEvents() {\n+            const upcomingEventsContainer = document.getElementById('upcoming-events');\n+            \n+            // Send AJAX request\n+            fetch('&lt;?php echo URLROOT; ?&gt;/calendar/getUpcomingEvents', {\n+                method: 'GET',\n+                headers: {\n+                    'X-Requested-With': 'XMLHttpRequest'\n+                }\n+            })\n+            .then(response =&gt; response.json())\n+            .then(data =&gt; {\n+                // Clear container\n+                upcomingEventsContainer.innerHTML = '';\n+                \n+                if (data.length === 0) {\n+                    upcomingEventsContainer.innerHTML = '&lt;div class=\&quot;list-group-item\&quot;&gt;No upcoming events&lt;/div&gt;';\n+                    return;\n+                }\n+                \n+                // Add events\n+                data.forEach(event =&gt; {\n+                    const startDate = new Date(event.start_date);\n+                    const endDate = event.end_date ? new Date(event.end_date) : null;\n+                    \n+                    let dateStr = startDate.toLocaleDateString();\n+                    let timeStr = '';\n+                    \n+                    if (event.all_day) {\n+                        timeStr = 'All day';\n+                    } else {\n+                        timeStr = startDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });\n+                        if (endDate) {\n+                            timeStr += ' - ' + endDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });\n+                        }\n+                    }\n+                    \n+                    const eventItem = document.createElement('a');\n+                    eventItem.href = '&lt;?php echo URLROOT; ?&gt;/calendar/event/' + event.id;\n+                    eventItem.className = 'list-group-item list-group-item-action upcoming-event';\n+                    eventItem.style.borderLeftColor = event.color || event.calendar_color || settings.defaultCalendarColor;\n+                    \n+                    eventItem.innerHTML = `\n+                        &lt;div class=\&quot;d-flex w-100 justify-content-between\&quot;&gt;\n+                            &lt;h6 class=\&quot;mb-1 upcoming-event-title\&quot;&gt;${event.title}&lt;/h6&gt;\n+                        &lt;/div&gt;\n+                        &lt;p class=\&quot;mb-1 upcoming-event-time\&quot;&gt;${dateStr} • ${timeStr}&lt;/p&gt;\n+                        &lt;small&gt;${event.calendar_name}&lt;/small&gt;\n+                    `;\n+                    \n+                    upcomingEventsContainer.appendChild(eventItem);\n+                });\n+            })\n+            .catch(error =&gt; {\n+                console.error('Error:', error);\n+                upcomingEventsContainer.innerHTML = '&lt;div class=\&quot;list-group-item text-danger\&quot;&gt;Error loading events&lt;/div&gt;';\n+            });\n+        }\n+    });\n+&lt;/script&gt;\n+\n+&lt;?php require APPROOT . '/views/includes/footer.php'; ?&gt;&quot;,&quot;refToolCallId&quot;:&quot;toolu_01Gn8CGWvJoCq7d6LGDQSiw6&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Now, let's create our custom calendar JavaScript file:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01XERt7xcggLN2YvWZjZnr4K&quot;,&quot;title&quot;:&quot;&quot;,&quot;statusText&quot;:&quot;Missing required parameter&quot;,&quot;status&quot;:&quot;WARNING&quot;,&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01VAxRNFDLTDLGqc4xPoQCAR&quot;,&quot;title&quot;:&quot;&quot;,&quot;statusText&quot;:&quot;Missing required parameter&quot;,&quot;status&quot;:&quot;WARNING&quot;,&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01SEYBBrJTJzmD5f8bNEqNdN&quot;,&quot;title&quot;:&quot;&quot;,&quot;statusText&quot;:&quot;Missing required parameter&quot;,&quot;status&quot;:&quot;WARNING&quot;,&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01HwXLrMPkuRzpeEaL4FwiM8&quot;,&quot;statusText&quot;:&quot;Change generated&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;libraries/js/custom-calendar.js&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\libraries\\js\\custom-calendar.js&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;codeHunk&quot;,&quot;lineBasedDiff&quot;:&quot;--- libraries/js/custom-calendar.js\n+++ libraries/js/custom-calendar.js\n@@ -0,0 +1,1376 @@\n+/**\n+ * Custom Calendar Implementation\n+ * \n+ * A fully custom calendar implementation for the Events and Shows Management System.\n+ * This calendar provides month, week, day, and list views with advanced features.\n+ * \n+ * Version: 1.0.0\n+ */\n+\n+class CustomCalendar {\n+    /**\n+     * Constructor\n+     * \n+     * @param {string} containerId - The ID of the container element\n+     * @param {Object} options - Calendar options\n+     */\n+    constructor(containerId, options = {}) {\n+        // Store container element\n+        this.container = document.getElementById(containerId);\n+        if (!this.container) {\n+            throw new Error(`Container element with ID \&quot;${containerId}\&quot; not found`);\n+        }\n+\n+        // Default options\n+        this.defaultOptions = {\n+            defaultView: 'month',\n+            weekStartsOn: 0, // 0 = Sunday, 1 = Monday, etc.\n+            showWeekends: true,\n+            businessHoursStart: '09:00',\n+            businessHoursEnd: '17:00',\n+            timeFormat: '12', // 12 or 24\n+            dateFormat: 'MM/DD/YYYY',\n+            defaultEventDuration: 60, // minutes\n+            enableDragDrop: true,\n+            enableResize: true,\n+            defaultCalendarColor: '#3788d8',\n+            eventSources: [],\n+            eventClick: null,\n+            eventDrop: null,\n+            eventResize: null,\n+            dateSelect: null,\n+            loading: null,\n+            viewChange: null,\n+            dayHeaderFormat: 'short', // 'short', 'narrow', 'long'\n+            monthHeaderFormat: 'long', // 'numeric', '2-digit', 'short', 'long'\n+            timeSlotDuration: 30, // minutes\n+            snapDuration: 15, // minutes\n+            slotLabelInterval: 60, // minutes\n+            slotLabelFormat: 'h:mm a', // 'h:mm a', 'HH:mm'\n+            allDaySlot: true,\n+            allDayText: 'All Day',\n+            nowIndicator: true,\n+            scrollTime: '08:00:00',\n+            height: 'auto',\n+            aspectRatio: 1.35,\n+            firstHour: 8,\n+            minTime: '00:00:00',\n+            maxTime: '24:00:00',\n+            slotEventOverlap: true,\n+            eventLimit: true,\n+            eventLimitText: 'more',\n+            eventLimitClick: 'popover',\n+            navLinks: true,\n+            selectable: true,\n+            selectMirror: true,\n+            unselectAuto: true,\n+            unselectCancel: '',\n+            selectOverlap: true,\n+            selectConstraint: {},\n+            selectAllow: null,\n+            selectMinDistance: 0,\n+            editable: true,\n+            eventStartEditable: true,\n+            eventDurationEditable: true,\n+            eventOverlap: true,\n+            eventConstraint: {},\n+            eventAllow: null,\n+            longPressDelay: 1000,\n+            eventLongPressDelay: 1000,\n+            selectLongPressDelay: 1000\n+        };\n+\n+        // Merge options\n+        this.options = { ...this.defaultOptions, ...options };\n+\n+        // Current date and view\n+        this.currentDate = new Date();\n+        this.currentView = this.options.defaultView;\n+\n+        // Events cache\n+        this.events = [];\n+        this.filteredEvents = [];\n+\n+        // Selected calendars\n+        this.selectedCalendars = [];\n+\n+        // Initialize calendar\n+        this.init();\n+    }\n+\n+    /**\n+     * Initialize calendar\n+     */\n+    init() {\n+        // Create calendar structure\n+        this.createCalendarStructure();\n+\n+        // Set initial view\n+        this.setView(this.currentView);\n+\n+        // Fetch events\n+        this.fetchEvents();\n+\n+        // Set up event listeners\n+        this.setupEventListeners();\n+    }\n+\n+    /**\n+     * Create calendar structure\n+     */\n+    createCalendarStructure() {\n+        // Clear container\n+        this.container.innerHTML = '';\n+        this.container.classList.add('custom-calendar');\n+\n+        // Create header\n+        this.header = document.createElement('div');\n+        this.header.className = 'calendar-header';\n+        this.container.appendChild(this.header);\n+\n+        // Create title\n+        this.title = document.createElement('h2');\n+        this.title.className = 'calendar-title';\n+        this.header.appendChild(this.title);\n+\n+        // Create main calendar grid\n+        this.calendarGrid = document.createElement('div');\n+        this.calendarGrid.className = 'calendar-grid';\n+        this.container.appendChild(this.calendarGrid);\n+\n+        // Create event container for day/week view\n+        this.eventContainer = document.createElement('div');\n+        this.eventContainer.className = 'calendar-events';\n+        this.container.appendChild(this.eventContainer);\n+\n+        // Create event list for list view\n+        this.eventList = document.createElement('div');\n+        this.eventList.className = 'calendar-event-list';\n+        this.container.appendChild(this.eventList);\n+\n+        // Create loading indicator\n+        this.loadingIndicator = document.createElement('div');\n+        this.loadingIndicator.className = 'calendar-loading';\n+        this.loadingIndicator.innerHTML = '&lt;div class=\&quot;spinner\&quot;&gt;&lt;/div&gt;&lt;span&gt;Loading...&lt;/span&gt;';\n+        this.loadingIndicator.style.display = 'none';\n+        this.container.appendChild(this.loadingIndicator);\n+    }\n+\n+    /**\n+     * Set up event listeners\n+     */\n+    setupEventListeners() {\n+        // Event delegation for calendar cells\n+        this.calendarGrid.addEventListener('click', (e) =&gt; {\n+            // Handle day cell click\n+            if (e.target.classList.contains('calendar-day') || e.target.closest('.calendar-day')) {\n+                const cell = e.target.classList.contains('calendar-day') ? e.target : e.target.closest('.calendar-day');\n+                const date = new Date(cell.dataset.date);\n+                \n+                if (this.options.dateSelect) {\n+                    this.options.dateSelect({\n+                        start: date,\n+                        end: new Date(date.getTime() + 24 * 60 * 60 * 1000),\n+                        allDay: true\n+                    });\n+                }\n+            }\n+\n+            // Handle event click\n+            if (e.target.classList.contains('calendar-event') || e.target.closest('.calendar-event')) {\n+                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');\n+                const eventId = eventEl.dataset.eventId;\n+                const event = this.events.find(e =&gt; e.id == eventId);\n+                \n+                if (event &amp;&amp; this.options.eventClick) {\n+                    this.options.eventClick({ event });\n+                }\n+            }\n+        });\n+\n+        // Event delegation for event list\n+        this.eventList.addEventListener('click', (e) =&gt; {\n+            // Handle event click in list view\n+            if (e.target.classList.contains('list-event') || e.target.closest('.list-event')) {\n+                const eventEl = e.target.classList.contains('list-event') ? e.target : e.target.closest('.list-event');\n+                const eventId = eventEl.dataset.eventId;\n+                const event = this.events.find(e =&gt; e.id == eventId);\n+                \n+                if (event &amp;&amp; this.options.eventClick) {\n+                    this.options.eventClick({ event });\n+                }\n+            }\n+        });\n+\n+        // Drag and drop for events\n+        if (this.options.enableDragDrop) {\n+            this.setupDragAndDrop();\n+        }\n+    }\n+\n+    /**\n+     * Set up drag and drop functionality\n+     */\n+    setupDragAndDrop() {\n+        // We'll implement this with a more advanced library or custom implementation\n+        // For now, we'll use a simple implementation\n+        this.container.addEventListener('mousedown', (e) =&gt; {\n+            if (e.target.classList.contains('calendar-event') || e.target.closest('.calendar-event')) {\n+                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');\n+                const eventId = eventEl.dataset.eventId;\n+                const event = this.events.find(e =&gt; e.id == eventId);\n+                \n+                if (!event || !this.options.editable) return;\n+                \n+                // Start drag\n+                const startX = e.clientX;\n+                const startY = e.clientY;\n+                const startTop = eventEl.offsetTop;\n+                const startLeft = eventEl.offsetLeft;\n+                const startDate = new Date(event.start);\n+                \n+                // Create ghost element\n+                const ghost = eventEl.cloneNode(true);\n+                ghost.classList.add('dragging');\n+                ghost.style.position = 'absolute';\n+                ghost.style.top = startTop + 'px';\n+                ghost.style.left = startLeft + 'px';\n+                ghost.style.width = eventEl.offsetWidth + 'px';\n+                ghost.style.zIndex = 1000;\n+                ghost.style.opacity = 0.7;\n+                document.body.appendChild(ghost);\n+                \n+                // Hide original\n+                eventEl.style.opacity = 0.3;\n+                \n+                // Track mouse move\n+                const moveHandler = (e) =&gt; {\n+                    ghost.style.top = (startTop + e.clientY - startY) + 'px';\n+                    ghost.style.left = (startLeft + e.clientX - startX) + 'px';\n+                };\n+                \n+                // Handle drop\n+                const upHandler = (e) =&gt; {\n+                    document.removeEventListener('mousemove', moveHandler);\n+                    document.removeEventListener('mouseup', upHandler);\n+                    \n+                    // Remove ghost\n+                    document.body.removeChild(ghost);\n+                    \n+                    // Show original\n+                    eventEl.style.opacity = 1;\n+                    \n+                    // Find drop target\n+                    const target = document.elementFromPoint(e.clientX, e.clientY);\n+                    const dayCell = target.classList.contains('calendar-day') ? target : target.closest('.calendar-day');\n+                    \n+                    if (dayCell) {\n+                        const newDate = new Date(dayCell.dataset.date);\n+                        const diff = newDate.getTime() - startDate.getTime();\n+                        \n+                        // Update event dates\n+                        const newEvent = { ...event };\n+                        newEvent.start = new Date(new Date(event.start).getTime() + diff);\n+                        newEvent.end = event.end ? new Date(new Date(event.end).getTime() + diff) : null;\n+                        \n+                        // Call eventDrop callback\n+                        if (this.options.eventDrop) {\n+                            this.options.eventDrop({\n+                                event: newEvent,\n+                                oldEvent: event,\n+                                revert: () =&gt; {\n+                                    // Revert is handled by refetching events\n+                                    this.fetchEvents();\n+                                }\n+                            });\n+                        }\n+                    }\n+                };\n+                \n+                document.addEventListener('mousemove', moveHandler);\n+                document.addEventListener('mouseup', upHandler);\n+            }\n+        });\n+    }\n+\n+    /**\n+     * Set calendar view\n+     * \n+     * @param {string} view - The view to set ('month', 'week', 'day', 'list')\n+     */\n+    setView(view) {\n+        this.currentView = view;\n+        \n+        // Update container class\n+        this.container.className = 'custom-calendar';\n+        this.container.classList.add(`calendar-view-${view}`);\n+        \n+        // Hide/show appropriate containers\n+        this.calendarGrid.style.display = ['month', 'week', 'day'].includes(view) ? 'grid' : 'none';\n+        this.eventContainer.style.display = ['week', 'day'].includes(view) ? 'block' : 'none';\n+        this.eventList.style.display = view === 'list' ? 'block' : 'none';\n+        \n+        // Render view\n+        this.render();\n+        \n+        // Call viewChange callback\n+        if (this.options.viewChange) {\n+            this.options.viewChange({ view });\n+        }\n+    }\n+\n+    /**\n+     * Navigate to previous period\n+     */\n+    prev() {\n+        switch (this.currentView) {\n+            case 'month':\n+                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);\n+                break;\n+            case 'week':\n+                this.currentDate = new Date(this.currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);\n+                break;\n+            case 'day':\n+                this.currentDate = new Date(this.currentDate.getTime() - 24 * 60 * 60 * 1000);\n+                break;\n+            case 'list':\n+                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);\n+                break;\n+        }\n+        \n+        this.render();\n+    }\n+\n+    /**\n+     * Navigate to next period\n+     */\n+    next() {\n+        switch (this.currentView) {\n+            case 'month':\n+                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);\n+                break;\n+            case 'week':\n+                this.currentDate = new Date(this.currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);\n+                break;\n+            case 'day':\n+                this.currentDate = new Date(this.currentDate.getTime() + 24 * 60 * 60 * 1000);\n+                break;\n+            case 'list':\n+                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);\n+                break;\n+        }\n+        \n+        this.render();\n+    }\n+\n+    /**\n+     * Navigate to today\n+     */\n+    today() {\n+        this.currentDate = new Date();\n+        this.render();\n+    }\n+\n+    /**\n+     * Render calendar\n+     */\n+    render() {\n+        // Update title\n+        this.updateTitle();\n+        \n+        // Render appropriate view\n+        switch (this.currentView) {\n+            case 'month':\n+                this.renderMonthView();\n+                break;\n+            case 'week':\n+                this.renderWeekView();\n+                break;\n+            case 'day':\n+                this.renderDayView();\n+                break;\n+            case 'list':\n+                this.renderListView();\n+                break;\n+        }\n+    }\n+\n+    /**\n+     * Update calendar title\n+     */\n+    updateTitle() {\n+        const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n+        const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n+        \n+        let title = '';\n+        \n+        switch (this.currentView) {\n+            case 'month':\n+                title = `${months[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;\n+                break;\n+            case 'week':\n+                const weekStart = this.getWeekStart(this.currentDate);\n+                const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);\n+                \n+                if (weekStart.getMonth() === weekEnd.getMonth()) {\n+                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()} - ${weekEnd.getDate()}, ${weekStart.getFullYear()}`;\n+                } else if (weekStart.getFullYear() === weekEnd.getFullYear()) {\n+                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()} - ${months[weekEnd.getMonth()]} ${weekEnd.getDate()}, ${weekStart.getFullYear()}`;\n+                } else {\n+                    title = `${months[weekStart.getMonth()]} ${weekStart.getDate()}, ${weekStart.getFullYear()} - ${months[weekEnd.getMonth()]} ${weekEnd.getDate()}, ${weekEnd.getFullYear()}`;\n+                }\n+                break;\n+            case 'day':\n+                title = `${days[this.currentDate.getDay()]}, ${months[this.currentDate.getMonth()]} ${this.currentDate.getDate()}, ${this.currentDate.getFullYear()}`;\n+                break;\n+            case 'list':\n+                title = `${months[this.currentDate.getMonth()]} ${this.currentDate.getFullYear()}`;\n+                break;\n+        }\n+        \n+        this.title.textContent = title;\n+    }\n+\n+    /**\n+     * Render month view\n+     */\n+    renderMonthView() {\n+        // Clear grid\n+        this.calendarGrid.innerHTML = '';\n+        \n+        // Set grid template\n+        this.calendarGrid.className = 'calendar-grid month-view';\n+        \n+        // Get month details\n+        const year = this.currentDate.getFullYear();\n+        const month = this.currentDate.getMonth();\n+        const firstDay = new Date(year, month, 1);\n+        const lastDay = new Date(year, month + 1, 0);\n+        const daysInMonth = lastDay.getDate();\n+        \n+        // Adjust for week start day\n+        let startOffset = firstDay.getDay() - this.options.weekStartsOn;\n+        if (startOffset &lt; 0) startOffset += 7;\n+        \n+        // Create day headers\n+        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n+        for (let i = 0; i &lt; 7; i++) {\n+            const dayIndex = (i + this.options.weekStartsOn) % 7;\n+            const dayHeader = document.createElement('div');\n+            dayHeader.className = 'calendar-day-header';\n+            dayHeader.textContent = dayNames[dayIndex];\n+            this.calendarGrid.appendChild(dayHeader);\n+        }\n+        \n+        // Create day cells\n+        const today = new Date();\n+        today.setHours(0, 0, 0, 0);\n+        \n+        // Previous month days\n+        for (let i = 0; i &lt; startOffset; i++) {\n+            const day = new Date(year, month, 1 - startOffset + i);\n+            this.createDayCell(day, 'prev-month');\n+        }\n+        \n+        // Current month days\n+        for (let i = 1; i &lt;= daysInMonth; i++) {\n+            const day = new Date(year, month, i);\n+            this.createDayCell(day, 'current-month');\n+        }\n+        \n+        // Next month days\n+        const totalCells = Math.ceil((daysInMonth + startOffset) / 7) * 7;\n+        const remainingCells = totalCells - (daysInMonth + startOffset);\n+        \n+        for (let i = 1; i &lt;= remainingCells; i++) {\n+            const day = new Date(year, month + 1, i);\n+            this.createDayCell(day, 'next-month');\n+        }\n+        \n+        // Render events\n+        this.renderMonthEvents();\n+    }\n+\n+    /**\n+     * Create a day cell for month view\n+     * \n+     * @param {Date} date - The date for the cell\n+     * @param {string} className - Additional class name\n+     */\n+    createDayCell(date, className) {\n+        const cell = document.createElement('div');\n+        cell.className = `calendar-day ${className}`;\n+        cell.dataset.date = date.toISOString().split('T')[0];\n+        \n+        // Check if today\n+        const today = new Date();\n+        if (date.getDate() === today.getDate() &amp;&amp; \n+            date.getMonth() === today.getMonth() &amp;&amp; \n+            date.getFullYear() === today.getFullYear()) {\n+            cell.classList.add('today');\n+        }\n+        \n+        // Check if weekend\n+        if (date.getDay() === 0 || date.getDay() === 6) {\n+            cell.classList.add('weekend');\n+        }\n+        \n+        // Add day number\n+        const dayNumber = document.createElement('div');\n+        dayNumber.className = 'day-number';\n+        dayNumber.textContent = date.getDate();\n+        cell.appendChild(dayNumber);\n+        \n+        // Add events container\n+        const eventsContainer = document.createElement('div');\n+        eventsContainer.className = 'day-events';\n+        cell.appendChild(eventsContainer);\n+        \n+        this.calendarGrid.appendChild(cell);\n+    }\n+\n+    /**\n+     * Render events for month view\n+     */\n+    renderMonthEvents() {\n+        // Get visible date range\n+        const year = this.currentDate.getFullYear();\n+        const month = this.currentDate.getMonth();\n+        const firstDay = new Date(year, month, 1);\n+        const lastDay = new Date(year, month + 1, 0);\n+        \n+        // Adjust for week start and end\n+        let startOffset = firstDay.getDay() - this.options.weekStartsOn;\n+        if (startOffset &lt; 0) startOffset += 7;\n+        \n+        const visibleStart = new Date(year, month, 1 - startOffset);\n+        const totalDays = Math.ceil((lastDay.getDate() + startOffset) / 7) * 7;\n+        const visibleEnd = new Date(year, month, totalDays - startOffset);\n+        \n+        // Filter events for visible range\n+        const visibleEvents = this.filteredEvents.filter(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            return eventStart &lt; visibleEnd &amp;&amp; eventEnd &gt;= visibleStart;\n+        });\n+        \n+        // Sort events by duration (longer events first)\n+        visibleEvents.sort((a, b) =&gt; {\n+            const aStart = new Date(a.start);\n+            const aEnd = a.end ? new Date(a.end) : new Date(aStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            const aDuration = aEnd.getTime() - aStart.getTime();\n+            \n+            const bStart = new Date(b.start);\n+            const bEnd = b.end ? new Date(b.end) : new Date(bStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            const bDuration = bEnd.getTime() - bStart.getTime();\n+            \n+            return bDuration - aDuration;\n+        });\n+        \n+        // Render each event\n+        visibleEvents.forEach(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            let eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            // Adjust all-day events\n+            if (event.allDay) {\n+                eventStart.setHours(0, 0, 0, 0);\n+                eventEnd.setHours(23, 59, 59, 999);\n+            }\n+            \n+            // Clip to visible range\n+            const displayStart = new Date(Math.max(eventStart.getTime(), visibleStart.getTime()));\n+            const displayEnd = new Date(Math.min(eventEnd.getTime(), visibleEnd.getTime()));\n+            \n+            // Calculate day span\n+            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));\n+            \n+            // Find cells for each day\n+            for (let i = 0; i &lt; daySpan; i++) {\n+                const day = new Date(displayStart.getTime() + i * 24 * 60 * 60 * 1000);\n+                const dateStr = day.toISOString().split('T')[0];\n+                const cell = this.calendarGrid.querySelector(`.calendar-day[data-date=\&quot;${dateStr}\&quot;]`);\n+                \n+                if (cell) {\n+                    const eventsContainer = cell.querySelector('.day-events');\n+                    \n+                    // Check if we already have too many events\n+                    if (eventsContainer.children.length &gt;= 3) {\n+                        // Check if we already have a \&quot;more\&quot; indicator\n+                        if (!eventsContainer.querySelector('.more-events')) {\n+                            const moreIndicator = document.createElement('div');\n+                            moreIndicator.className = 'more-events';\n+                            moreIndicator.textContent = '+ more';\n+                            eventsContainer.appendChild(moreIndicator);\n+                        }\n+                        continue;\n+                    }\n+                    \n+                    // Create event element\n+                    const eventEl = document.createElement('div');\n+                    eventEl.className = 'calendar-event';\n+                    eventEl.dataset.eventId = event.id;\n+                    \n+                    // Set event color\n+                    const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+                    eventEl.style.backgroundColor = color;\n+                    eventEl.style.borderColor = color;\n+                    \n+                    // Set event title\n+                    eventEl.textContent = event.title;\n+                    \n+                    // Add to container\n+                    eventsContainer.appendChild(eventEl);\n+                }\n+            }\n+        });\n+    }\n+\n+    /**\n+     * Render week view\n+     */\n+    renderWeekView() {\n+        // Clear grid\n+        this.calendarGrid.innerHTML = '';\n+        this.eventContainer.innerHTML = '';\n+        \n+        // Set grid template\n+        this.calendarGrid.className = 'calendar-grid week-view';\n+        \n+        // Get week start and end\n+        const weekStart = this.getWeekStart(this.currentDate);\n+        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);\n+        \n+        // Create time column\n+        const timeColumn = document.createElement('div');\n+        timeColumn.className = 'time-column';\n+        this.calendarGrid.appendChild(timeColumn);\n+        \n+        // Create time slots\n+        const startHour = parseInt(this.options.minTime.split(':')[0]);\n+        const endHour = parseInt(this.options.maxTime.split(':')[0]);\n+        \n+        for (let hour = startHour; hour &lt; endHour; hour++) {\n+            const timeSlot = document.createElement('div');\n+            timeSlot.className = 'time-slot';\n+            \n+            // Format hour based on time format\n+            let displayHour = hour;\n+            let ampm = '';\n+            \n+            if (this.options.timeFormat === '12') {\n+                ampm = hour &gt;= 12 ? 'PM' : 'AM';\n+                displayHour = hour % 12 || 12;\n+            }\n+            \n+            timeSlot.textContent = `${displayHour}${ampm ? ' ' + ampm : ''}`;\n+            timeColumn.appendChild(timeSlot);\n+        }\n+        \n+        // Create day columns\n+        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];\n+        const today = new Date();\n+        today.setHours(0, 0, 0, 0);\n+        \n+        for (let i = 0; i &lt; 7; i++) {\n+            const day = new Date(weekStart.getTime() + i * 24 * 60 * 60 * 1000);\n+            const dayColumn = document.createElement('div');\n+            dayColumn.className = 'day-column';\n+            dayColumn.dataset.date = day.toISOString().split('T')[0];\n+            \n+            // Check if today\n+            if (day.getDate() === today.getDate() &amp;&amp; \n+                day.getMonth() === today.getMonth() &amp;&amp; \n+                day.getFullYear() === today.getFullYear()) {\n+                dayColumn.classList.add('today');\n+            }\n+            \n+            // Check if weekend\n+            if (day.getDay() === 0 || day.getDay() === 6) {\n+                dayColumn.classList.add('weekend');\n+            }\n+            \n+            // Add day header\n+            const dayHeader = document.createElement('div');\n+            dayHeader.className = 'day-header';\n+            \n+            const dayName = document.createElement('div');\n+            dayName.className = 'day-name';\n+            dayName.textContent = days[day.getDay()];\n+            \n+            const dayNumber = document.createElement('div');\n+            dayNumber.className = 'day-number';\n+            dayNumber.textContent = day.getDate();\n+            \n+            dayHeader.appendChild(dayName);\n+            dayHeader.appendChild(dayNumber);\n+            dayColumn.appendChild(dayHeader);\n+            \n+            // Add time grid\n+            for (let hour = startHour; hour &lt; endHour; hour++) {\n+                const hourCell = document.createElement('div');\n+                hourCell.className = 'hour-cell';\n+                hourCell.dataset.hour = hour;\n+                dayColumn.appendChild(hourCell);\n+            }\n+            \n+            this.calendarGrid.appendChild(dayColumn);\n+        }\n+        \n+        // Render events\n+        this.renderWeekEvents();\n+    }\n+\n+    /**\n+     * Render events for week view\n+     */\n+    renderWeekEvents() {\n+        // Get visible date range\n+        const weekStart = this.getWeekStart(this.currentDate);\n+        const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);\n+        \n+        // Filter events for visible range\n+        const visibleEvents = this.filteredEvents.filter(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            return eventStart &lt; weekEnd &amp;&amp; eventEnd &gt;= weekStart;\n+        });\n+        \n+        // Create all-day events container\n+        const allDayContainer = document.createElement('div');\n+        allDayContainer.className = 'all-day-container';\n+        this.eventContainer.appendChild(allDayContainer);\n+        \n+        // Separate all-day events\n+        const allDayEvents = visibleEvents.filter(event =&gt; event.allDay);\n+        const timeEvents = visibleEvents.filter(event =&gt; !event.allDay);\n+        \n+        // Render all-day events\n+        allDayEvents.forEach(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + 24 * 60 * 60 * 1000);\n+            \n+            // Clip to visible range\n+            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));\n+            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));\n+            \n+            // Calculate day span and offset\n+            const startOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));\n+            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));\n+            \n+            // Create event element\n+            const eventEl = document.createElement('div');\n+            eventEl.className = 'calendar-event all-day-event';\n+            eventEl.dataset.eventId = event.id;\n+            \n+            // Set event color\n+            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+            eventEl.style.backgroundColor = color;\n+            eventEl.style.borderColor = color;\n+            \n+            // Set event position\n+            eventEl.style.gridColumn = `${startOffset + 1} / span ${daySpan}`;\n+            \n+            // Set event title\n+            eventEl.textContent = event.title;\n+            \n+            // Add to container\n+            allDayContainer.appendChild(eventEl);\n+        });\n+        \n+        // Render time-based events\n+        timeEvents.forEach(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            // Clip to visible range\n+            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));\n+            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));\n+            \n+            // Calculate day offset\n+            const dayOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));\n+            \n+            // Find day column\n+            const dayColumn = this.calendarGrid.querySelectorAll('.day-column')[dayOffset];\n+            \n+            if (dayColumn) {\n+                // Calculate time position\n+                const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;\n+                const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;\n+                const duration = endHour - startHour;\n+                \n+                // Create event element\n+                const eventEl = document.createElement('div');\n+                eventEl.className = 'calendar-event time-event';\n+                eventEl.dataset.eventId = event.id;\n+                \n+                // Set event color\n+                const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+                eventEl.style.backgroundColor = color;\n+                eventEl.style.borderColor = color;\n+                \n+                // Set event position\n+                const minHour = parseInt(this.options.minTime.split(':')[0]);\n+                const top = (startHour - minHour) * 60; // 60px per hour\n+                const height = duration * 60;\n+                \n+                eventEl.style.top = `${top}px`;\n+                eventEl.style.height = `${height}px`;\n+                eventEl.style.left = `${dayOffset * 14.28}%`; // 7 days = 14.28% per day\n+                eventEl.style.width = '14.28%';\n+                \n+                // Set event title\n+                const eventTitle = document.createElement('div');\n+                eventTitle.className = 'event-title';\n+                eventTitle.textContent = event.title;\n+                eventEl.appendChild(eventTitle);\n+                \n+                // Set event time\n+                const eventTime = document.createElement('div');\n+                eventTime.className = 'event-time';\n+                \n+                // Format time based on time format\n+                let startTimeStr = this.formatTime(displayStart);\n+                let endTimeStr = this.formatTime(displayEnd);\n+                \n+                eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;\n+                eventEl.appendChild(eventTime);\n+                \n+                // Add to container\n+                this.eventContainer.appendChild(eventEl);\n+            }\n+        });\n+    }\n+\n+    /**\n+     * Render day view\n+     */\n+    renderDayView() {\n+        // Clear grid\n+        this.calendarGrid.innerHTML = '';\n+        this.eventContainer.innerHTML = '';\n+        \n+        // Set grid template\n+        this.calendarGrid.className = 'calendar-grid day-view';\n+        \n+        // Create time column\n+        const timeColumn = document.createElement('div');\n+        timeColumn.className = 'time-column';\n+        this.calendarGrid.appendChild(timeColumn);\n+        \n+        // Create time slots\n+        const startHour = parseInt(this.options.minTime.split(':')[0]);\n+        const endHour = parseInt(this.options.maxTime.split(':')[0]);\n+        \n+        for (let hour = startHour; hour &lt; endHour; hour++) {\n+            const timeSlot = document.createElement('div');\n+            timeSlot.className = 'time-slot';\n+            \n+            // Format hour based on time format\n+            let displayHour = hour;\n+            let ampm = '';\n+            \n+            if (this.options.timeFormat === '12') {\n+                ampm = hour &gt;= 12 ? 'PM' : 'AM';\n+                displayHour = hour % 12 || 12;\n+            }\n+            \n+            timeSlot.textContent = `${displayHour}${ampm ? ' ' + ampm : ''}`;\n+            timeColumn.appendChild(timeSlot);\n+        }\n+        \n+        // Create day column\n+        const dayColumn = document.createElement('div');\n+        dayColumn.className = 'day-column full-width';\n+        dayColumn.dataset.date = this.currentDate.toISOString().split('T')[0];\n+        \n+        // Check if today\n+        const today = new Date();\n+        if (this.currentDate.getDate() === today.getDate() &amp;&amp; \n+            this.currentDate.getMonth() === today.getMonth() &amp;&amp; \n+            this.currentDate.getFullYear() === today.getFullYear()) {\n+            dayColumn.classList.add('today');\n+        }\n+        \n+        // Check if weekend\n+        if (this.currentDate.getDay() === 0 || this.currentDate.getDay() === 6) {\n+            dayColumn.classList.add('weekend');\n+        }\n+        \n+        // Add time grid\n+        for (let hour = startHour; hour &lt; endHour; hour++) {\n+            const hourCell = document.createElement('div');\n+            hourCell.className = 'hour-cell';\n+            hourCell.dataset.hour = hour;\n+            dayColumn.appendChild(hourCell);\n+        }\n+        \n+        this.calendarGrid.appendChild(dayColumn);\n+        \n+        // Render events\n+        this.renderDayEvents();\n+    }\n+\n+    /**\n+     * Render events for day view\n+     */\n+    renderDayEvents() {\n+        // Get visible date range\n+        const dayStart = new Date(this.currentDate);\n+        dayStart.setHours(0, 0, 0, 0);\n+        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);\n+        \n+        // Filter events for visible range\n+        const visibleEvents = this.filteredEvents.filter(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            return eventStart &lt; dayEnd &amp;&amp; eventEnd &gt;= dayStart;\n+        });\n+        \n+        // Create all-day events container\n+        const allDayContainer = document.createElement('div');\n+        allDayContainer.className = 'all-day-container';\n+        this.eventContainer.appendChild(allDayContainer);\n+        \n+        // Separate all-day events\n+        const allDayEvents = visibleEvents.filter(event =&gt; event.allDay);\n+        const timeEvents = visibleEvents.filter(event =&gt; !event.allDay);\n+        \n+        // Render all-day events\n+        allDayEvents.forEach(event =&gt; {\n+            // Create event element\n+            const eventEl = document.createElement('div');\n+            eventEl.className = 'calendar-event all-day-event';\n+            eventEl.dataset.eventId = event.id;\n+            \n+            // Set event color\n+            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+            eventEl.style.backgroundColor = color;\n+            eventEl.style.borderColor = color;\n+            \n+            // Set event title\n+            eventEl.textContent = event.title;\n+            \n+            // Add to container\n+            allDayContainer.appendChild(eventEl);\n+        });\n+        \n+        // Render time-based events\n+        timeEvents.forEach(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            // Clip to visible range\n+            const displayStart = new Date(Math.max(eventStart.getTime(), dayStart.getTime()));\n+            const displayEnd = new Date(Math.min(eventEnd.getTime(), dayEnd.getTime()));\n+            \n+            // Calculate time position\n+            const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;\n+            const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;\n+            const duration = endHour - startHour;\n+            \n+            // Create event element\n+            const eventEl = document.createElement('div');\n+            eventEl.className = 'calendar-event time-event';\n+            eventEl.dataset.eventId = event.id;\n+            \n+            // Set event color\n+            const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+            eventEl.style.backgroundColor = color;\n+            eventEl.style.borderColor = color;\n+            \n+            // Set event position\n+            const minHour = parseInt(this.options.minTime.split(':')[0]);\n+            const top = (startHour - minHour) * 60; // 60px per hour\n+            const height = duration * 60;\n+            \n+            eventEl.style.top = `${top}px`;\n+            eventEl.style.height = `${height}px`;\n+            eventEl.style.width = '100%';\n+            \n+            // Set event title\n+            const eventTitle = document.createElement('div');\n+            eventTitle.className = 'event-title';\n+            eventTitle.textContent = event.title;\n+            eventEl.appendChild(eventTitle);\n+            \n+            // Set event time\n+            const eventTime = document.createElement('div');\n+            eventTime.className = 'event-time';\n+            \n+            // Format time based on time format\n+            let startTimeStr = this.formatTime(displayStart);\n+            let endTimeStr = this.formatTime(displayEnd);\n+            \n+            eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;\n+            eventEl.appendChild(eventTime);\n+            \n+            // Add to container\n+            this.eventContainer.appendChild(eventEl);\n+        });\n+    }\n+\n+    /**\n+     * Render list view\n+     */\n+    renderListView() {\n+        // Clear list\n+        this.eventList.innerHTML = '';\n+        \n+        // Get visible date range\n+        const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);\n+        const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);\n+        \n+        // Filter events for visible range\n+        const visibleEvents = this.filteredEvents.filter(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+            \n+            return eventStart &lt;= monthEnd &amp;&amp; eventEnd &gt;= monthStart;\n+        });\n+        \n+        // Sort events by start date\n+        visibleEvents.sort((a, b) =&gt; {\n+            return new Date(a.start).getTime() - new Date(b.start).getTime();\n+        });\n+        \n+        // Group events by day\n+        const eventsByDay = {};\n+        \n+        visibleEvents.forEach(event =&gt; {\n+            const eventStart = new Date(event.start);\n+            const dateStr = eventStart.toISOString().split('T')[0];\n+            \n+            if (!eventsByDay[dateStr]) {\n+                eventsByDay[dateStr] = [];\n+            }\n+            \n+            eventsByDay[dateStr].push(event);\n+        });\n+        \n+        // Render events by day\n+        if (Object.keys(eventsByDay).length === 0) {\n+            const noEvents = document.createElement('div');\n+            noEvents.className = 'no-events';\n+            noEvents.textContent = 'No events to display';\n+            this.eventList.appendChild(noEvents);\n+        } else {\n+            for (const dateStr in eventsByDay) {\n+                const date = new Date(dateStr);\n+                \n+                // Create day header\n+                const dayHeader = document.createElement('div');\n+                dayHeader.className = 'list-day-header';\n+                \n+                // Format date\n+                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];\n+                const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];\n+                \n+                dayHeader.textContent = `${dayNames[date.getDay()]}, ${monthNames[date.getMonth()]} ${date.getDate()}`;\n+                this.eventList.appendChild(dayHeader);\n+                \n+                // Create events for this day\n+                eventsByDay[dateStr].forEach(event =&gt; {\n+                    const eventEl = document.createElement('div');\n+                    eventEl.className = 'list-event';\n+                    eventEl.dataset.eventId = event.id;\n+                    \n+                    // Set event color\n+                    const color = event.color || event.backgroundColor || event.extendedProps?.calendar_color || this.options.defaultCalendarColor;\n+                    \n+                    // Create color dot\n+                    const colorDot = document.createElement('div');\n+                    colorDot.className = 'event-color-dot';\n+                    colorDot.style.backgroundColor = color;\n+                    eventEl.appendChild(colorDot);\n+                    \n+                    // Create event content\n+                    const eventContent = document.createElement('div');\n+                    eventContent.className = 'event-content';\n+                    \n+                    // Create event title\n+                    const eventTitle = document.createElement('div');\n+                    eventTitle.className = 'event-title';\n+                    eventTitle.textContent = event.title;\n+                    eventContent.appendChild(eventTitle);\n+                    \n+                    // Create event time\n+                    const eventTime = document.createElement('div');\n+                    eventTime.className = 'event-time';\n+                    \n+                    const eventStart = new Date(event.start);\n+                    const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);\n+                    \n+                    if (event.allDay) {\n+                        eventTime.textContent = 'All day';\n+                    } else {\n+                        // Format time based on time format\n+                        let startTimeStr = this.formatTime(eventStart);\n+                        let endTimeStr = this.formatTime(eventEnd);\n+                        \n+                        eventTime.textContent = `${startTimeStr} - ${endTimeStr}`;\n+                    }\n+                    \n+                    eventContent.appendChild(eventTime);\n+                    \n+                    // Create event location if available\n+                    if (event.extendedProps &amp;&amp; event.extendedProps.location) {\n+                        const eventLocation = document.createElement('div');\n+                        eventLocation.className = 'event-location';\n+                        eventLocation.textContent = event.extendedProps.location;\n+                        eventContent.appendChild(eventLocation);\n+                    }\n+                    \n+                    // Create event calendar if available\n+                    if (event.extendedProps &amp;&amp; event.extendedProps.calendar_name) {\n+                        const eventCalendar = document.createElement('div');\n+                        eventCalendar.className = 'event-calendar';\n+                        eventCalendar.textContent = event.extendedProps.calendar_name;\n+                        eventContent.appendChild(eventCalendar);\n+                    }\n+                    \n+                    eventEl.appendChild(eventContent);\n+                    this.eventList.appendChild(eventEl);\n+                });\n+            }\n+        }\n+    }\n+\n+    /**\n+     * Get the start of the week containing the given date\n+     * \n+     * @param {Date} date - The date\n+     * @return {Date} The start of the week\n+     */\n+    getWeekStart(date) {\n+        const day = date.getDay();\n+        const diff = (day - this.options.weekStartsOn + 7) % 7;\n+        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - diff);\n+    }\n+\n+    /**\n+     * Format time based on time format setting\n+     * \n+     * @param {Date} date - The date to format\n+     * @return {string} Formatted time string\n+     */\n+    formatTime(date) {\n+        if (this.options.timeFormat === '12') {\n+            const hours = date.getHours();\n+            const minutes = date.getMinutes();\n+            const ampm = hours &gt;= 12 ? 'PM' : 'AM';\n+            const displayHours = hours % 12 || 12;\n+            \n+            return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;\n+        } else {\n+            return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n+        }\n+    }\n+\n+    /**\n+     * Fetch events from all sources\n+     */\n+    fetchEvents() {\n+        // Show loading indicator\n+        this.showLoading(true);\n+        \n+        // Call loading callback\n+        if (this.options.loading) {\n+            this.options.loading(true);\n+        }\n+        \n+        // Clear events\n+        this.events = [];\n+        \n+        // Create promises for all event sources\n+        const promises = this.options.eventSources.map(source =&gt; {\n+            if (typeof source === 'string') {\n+                // URL source\n+                return fetch(source)\n+                    .then(response =&gt; response.json())\n+                    .then(events =&gt; events);\n+            } else if (typeof source === 'function') {\n+                // Function source\n+                return Promise.resolve(source());\n+            } else if (Array.isArray(source)) {\n+                // Array source\n+                return Promise.resolve(source);\n+            } else if (typeof source === 'object' &amp;&amp; source.url) {\n+                // Object source with URL\n+                const params = new URLSearchParams();\n+                \n+                // Add start and end parameters\n+                const rangeStart = this.getRangeStart();\n+                const rangeEnd = this.getRangeEnd();\n+                \n+                params.append('start', rangeStart.toISOString());\n+                params.append('end', rangeEnd.toISOString());\n+                \n+                // Add extra parameters\n+                if (source.extraParams) {\n+                    for (const key in source.extraParams) {\n+                        params.append(key, source.extraParams[key]);\n+                    }\n+                }\n+                \n+                // Build URL\n+                const url = `${source.url}${source.url.includes('?') ? '&amp;' : '?'}${params.toString()}`;\n+                \n+                // Fetch events\n+                return fetch(url)\n+                    .then(response =&gt; response.json())\n+                    .then(events =&gt; events);\n+            }\n+            \n+            return Promise.resolve([]);\n+        });\n+        \n+        // Wait for all promises to resolve\n+        Promise.all(promises)\n+            .then(results =&gt; {\n+                // Flatten results\n+                this.events = results.flat();\n+                \n+                // Filter events\n+                this.filterEvents();\n+                \n+                // Render calendar\n+                this.render();\n+                \n+                // Hide loading indicator\n+                this.showLoading(false);\n+                \n+                // Call loading callback\n+                if (this.options.loading) {\n+                    this.options.loading(false);\n+                }\n+            })\n+            .catch(error =&gt; {\n+                console.error('Error fetching events:', error);\n+                \n+                // Hide loading indicator\n+                this.showLoading(false);\n+                \n+                // Call loading callback\n+                if (this.options.loading) {\n+                    this.options.loading(false);\n+                }\n+            });\n+    }\n+\n+    /**\n+     * Filter events based on selected calendars\n+     */\n+    filterEvents() {\n+        if (this.selectedCalendars.length === 0) {\n+            this.filteredEvents = this.events;\n+        } else {\n+            this.filteredEvents = this.events.filter(event =&gt; {\n+                const calendarId = event.extendedProps?.calendar_id;\n+                return this.selectedCalendars.includes(calendarId);\n+            });\n+        }\n+    }\n+\n+    /**\n+     * Get the start of the visible range\n+     * \n+     * @return {Date} The start of the visible range\n+     */\n+    getRangeStart() {\n+        switch (this.currentView) {\n+            case 'month':\n+                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);\n+                const startOffset = monthStart.getDay() - this.options.weekStartsOn;\n+                return new Date(monthStart.getFullYear(), monthStart.getMonth(), 1 - (startOffset &lt; 0 ? startOffset + 7 : startOffset));\n+            \n+            case 'week':\n+                return this.getWeekStart(this.currentDate);\n+            \n+            case 'day':\n+                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate());\n+            \n+            case 'list':\n+                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);\n+        }\n+    }\n+\n+    /**\n+     * Get the end of the visible range\n+     * \n+     * @return {Date} The end of the visible range\n+     */\n+    getRangeEnd() {\n+        switch (this.currentView) {\n+            case 'month':\n+                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);\n+                const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);\n+                const startOffset = monthStart.getDay() - this.options.weekStartsOn;\n+                const endOffset = 6 - (monthEnd.getDay() - this.options.weekStartsOn);\n+                return new Date(monthEnd.getFullYear(), monthEnd.getMonth(), monthEnd.getDate() + (endOffset &lt; 0 ? endOffset + 7 : endOffset));\n+            \n+            case 'week':\n+                const weekStart = this.getWeekStart(this.currentDate);\n+                return new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 7);\n+            \n+            case 'day':\n+                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate() + 1);\n+            \n+            case 'list':\n+                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);\n+        }\n+    }\n+\n+    /**\n+     * Show or hide loading indicator\n+     * \n+     * @param {boolean} show - Whether to show or hide the loading indicator\n+     */\n+    showLoading(show) {\n+        this.loadingIndicator.style.display = show ? 'flex' : 'none';\n+    }\n+\n+    /**\n+     * Set selected calendars\n+     * \n+     * @param {Array} calendarIds - Array of calendar IDs\n+     */\n+    setSelectedCalendars(calendarIds) {\n+        this.selectedCalendars = calendarIds;\n+        this.filterEvents();\n+        this.render();\n+    }\n+\n+    /**\n+     * Add event source\n+     * \n+     * @param {string|Object|Array|Function} source - Event source\n+     */\n+    addEventSource(source) {\n+        this.options.eventSources.push(source);\n+        this.fetchEvents();\n+    }\n+\n+    /**\n+     * Remove event source\n+     * \n+     * @param {string|Object|Array|Function} source - Event source to remove\n+     */\n+    removeEventSource(source) {\n+        const index = this.options.eventSources.indexOf(source);\n+        if (index !== -1) {\n+            this.options.eventSources.splice(index, 1);\n+            this.fetchEvents();\n+        }\n+    }\n+\n+    /**\n+     * Refetch events\n+     */\n+    refetchEvents() {\n+        this.fetchEvents();\n+    }\n+}&quot;,&quot;refToolCallId&quot;:&quot;toolu_01HwXLrMPkuRzpeEaL4FwiM8&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;Now, let's create the CSS file for our custom calendar:&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_014GgdVFs6bTsWV9JEPobsYG&quot;,&quot;statusText&quot;:&quot;Analyzing file&quot;,&quot;status&quot;:&quot;IN_PROGRESS&quot;,&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;codePatch&quot;,&quot;diffs&quot;:[{&quot;path&quot;:&quot;autobackup/custom_calendar/original_index.php&quot;,&quot;patch&quot;:&quot;@@ -0,0 +1,26237 @@\n+%3C?php require APPROOT . '/views/includes/header.php'; ?%3E%0D%0A%0D%0A%3Cdiv class=%22container-fluid%22%3E%0D%0A    %3Cdiv class=%22row mb-4%22%3E%0D%0A        %3Cdiv class=%22col-md-6%22%3E%0D%0A            %3Ch1%3EEvent Calendar%3C/h1%3E%0D%0A        %3C/div%3E%0D%0A        %3Cdiv class=%22col-md-6 text-end%22%3E%0D%0A            %3Cdiv class=%22btn-group%22%3E%0D%0A                %3Ca href=%22%3C?php echo URLROOT; ?%3E/calendar/createEvent%22 class=%22btn btn-primary%22%3E%0D%0A                    %3Ci class=%22fas fa-plus me-2%22%3E%3C/i%3E Add Event%0D%0A                %3C/a%3E%0D%0A                %3Cbutton type=%22button%22 class=%22btn btn-primary dropdown-toggle dropdown-toggle-split%22 data-bs-toggle=%22dropdown%22 aria-expanded=%22false%22%3E%0D%0A                    %3Cspan class=%22visually-hidden%22%3EToggle Dropdown%3C/span%3E%0D%0A                %3C/button%3E%0D%0A                %3Cul class=%22dropdown-menu dropdown-menu-end%22%3E%0D%0A                    %3Cli%3E%3Ca class=%22dropdown-item%22 href=%22%3C?php echo URLROOT; ?%3E/calendar/manageCalendars%22%3EManage Calendars%3C/a%3E%3C/li%3E%0D%0A                    %3Cli%3E%3Ca class=%22dropdown-item%22 href=%22%3C?php echo URLROOT; ?%3E/calendar/manageVenues%22%3EManage Venues%3C/a%3E%3C/li%3E%0D%0A                    %3Cli%3E%3Ca class=%22dropdown-item%22 href=%22%3C?php echo URLROOT; ?%3E/calendar/manageClubs%22%3EManage Clubs%3C/a%3E%3C/li%3E%0D%0A                    %3Cli%3E%3Chr class=%22dropdown-divider%22%3E%3C/li%3E%0D%0A                    %3Cli%3E%3Ca class=%22dropdown-item%22 href=%22%3C?php echo URLROOT; ?%3E/calendar/import%22%3EImport Events%3C/a%3E%3C/li%3E%0D%0A                    %3C?php if (isAdmin()): ?%3E%0D%0A                    %3Cli%3E%3Chr class=%22dropdown-divider%22%3E%3C/li%3E%0D%0A                    %3Cli%3E%3Ca class=%22dropdown-item%22 href=%22%3C?php echo URLROOT; ?%3E/calendar/settings%22%3ECalendar Settings%3C/a%3E%3C/li%3E%0D%0A                    %3C?php endif; ?%3E%0D%0A                %3C/ul%3E%0D%0A            %3C/div%3E%0D%0A        %3C/div%3E%0D%0A    %3C/div%3E%0D%0A    %0D%0A    %3C?php flash('calendar_message'); ?%3E%0D%0A    %0D%0A    %3Cdiv class=%22row mb-4%22%3E%0D%0A        %3Cdiv class=%22col-md-3%22%3E%0D%0A            %3Cdiv class=%22card%22%3E%0D%0A                %3Cdiv class=%22card-header%22%3E%0D%0A                    %3Ch5 class=%22card-title mb-0%22%3ECalendars%3C/h5%3E%0D%0A                %3C/div%3E%0D%0A                %3Cdiv class=%22card-body%22%3E%0D%0A                    %3C?php if (!empty($data%5B'calendars'%5D)): ?%3E%0D%0A                        %3Cdiv class=%22calendar-list%22%3E%0D%0A                            %3C?php foreach ($data%5B'calendars'%5D as $calendar): ?%3E%0D%0A                                %3Cdiv class=%22form-check%22%3E%0D%0A                                    %3Cinput class=%22form-check-input calendar-toggle%22 type=%22checkbox%22 value=%22%3C?php echo $calendar-%3Eid; ?%3E%22 id=%22calendar-%3C?php echo $calendar-%3Eid; ?%3E%22 checked data-color=%22%3C?php echo $calendar-%3Ecolor; ?%3E%22%3E%0D%0A                                    %3Clabel class=%22form-check-label%22 for=%22calendar-%3C?php echo $calendar-%3Eid; ?%3E%22%3E%0D%0A                                        %3Cspan class=%22color-dot%22 style=%22background-color: %3C?php echo $calendar-%3Ecolor; ?%3E%22%3E%3C/span%3E%0D%0A                                        %3C?php echo $calendar-%3Ename; ?%3E%0D%0A                                    %3C/label%3E%0D%0A                                %3C/div%3E%0D%0A                            %3C?php endforeach; ?%3E%0D%0A                        %3C/div%3E%0D%0A                    %3C?php else: ?%3E%0D%0A                        %3Cp%3ENo calendars found.%3C/p%3E%0D%0A                        %3Ca href=%22%3C?php echo URLROOT; ?%3E/calendar/createCalendar%22 class=%22btn btn-sm btn-primary%22%3ECreate Calendar%3C/a%3E%0D%0A                    %3C?php endif; ?%3E%0D%0A                %3C/div%3E%0D%0A            %3C/div%3E%0D%0A            %0D%0A            %3Cdiv class=%22card mt-3%22%3E%0D%0A                %3Cdiv class=%22card-header%22%3E%0D%0A                    %3Ch5 class=%22card-title mb-0%22%3EUpcoming Events%3C/h5%3E%0D%0A                %3C/div%3E%0D%0A                %3Cdiv class=%22card-body p-0%22%3E%0D%0A                    %3Cdiv id=%22upcoming-events%22 class=%22list-group list-group-flush%22%3E%0D%0A                        %3C!-- Upcoming events will be loaded here via JavaScript --%3E%0D%0A                        %3Cdiv class=%22list-group-item text-center%22%3E%0D%0A                            %3Cdiv class=%22spinner-border spinner-border-sm text-primary%22 role=%22status%22%3E%0D%0A                                %3Cspan class=%22visually-hidden%22%3ELoading...%3C/span%3E%0D%0A                            %3C/div%3E%0D%0A                            %3Cspan class=%22ms-2%22%3ELoading events...%3C/span%3E%0D%0A                        %3C/div%3E%0D%0A                    %3C/div%3E%0D%0A                %3C/div%3E%0D%0A            %3C/div%3E%0D%0A        %3C/div%3E%0D%0A        %0D%0A        %3Cdiv class=%22col-md-9%22%3E%0D%0A            %3Cdiv class=%22card%22%3E%0D%0A                %3Cdiv class=%22card-header%22%3E%0D%0A                    %3Cdiv class=%22d-flex justify-content-between align-items-center%22%3E%0D%0A                        %3Cdiv class=%22btn-group%22 role=%22group%22 aria-label=%22Calendar views%22%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22view-month%22%3EMonth%3C/button%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22view-week%22%3EWeek%3C/button%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22view-day%22%3EDay%3C/button%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22view-list%22%3EList%3C/button%3E%0D%0A                        %3C/div%3E%0D%0A                        %3Cdiv%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22prev-btn%22%3E%0D%0A                                %3Ci class=%22fas fa-chevron-left%22%3E%3C/i%3E%0D%0A                            %3C/button%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22today-btn%22%3EToday%3C/button%3E%0D%0A                            %3Cbutton type=%22button%22 class=%22btn btn-outline-secondary%22 id=%22next-btn%22%3E%0D%0A                                %3Ci class=%22fas fa-chevron-right%22%3E%3C/i%3E%0D%0A                            %3C/button%3E%0D%0A                        %3C/div%3E%0D%0A                    %3C/div%3E%0D%0A                %3C/div%3E%0D%0A                %3Cdiv class=%22card-body%22%3E%0D%0A                    %3Cdiv id=%22calendar%22%3E%3C/div%3E%0D%0A                %3C/div%3E%0D%0A            %3C/div%3E%0D%0A        %3C/div%3E%0D%0A    %3C/div%3E%0D%0A%3C/div%3E%0D%0A%0D%0A%3C!-- Event Details Modal --%3E%0D%0A%3Cdiv class=%22modal fade%22 id=%22eventModal%22 tabindex=%22-1%22 aria-labelledby=%22eventModalLabel%22 aria-hidden=%22true%22%3E%0D%0A    %3Cdiv class=%22modal-dialog modal-lg%22%3E%0D%0A        %3Cdiv class=%22modal-content%22%3E%0D%0A            %3Cdiv class=%22modal-header%22%3E%0D%0A                %3Ch5 class=%22modal-title%22 id=%22eventModalLabel%22%3EEvent Details%3C/h5%3E%0D%0A                %3Cbutton type=%22button%22 class=%22btn-close%22 data-bs-dismiss=%22modal%22 aria-label=%22Close%22%3E%3C/button%3E%0D%0A            %3C/div%3E%0D%0A            %3Cdiv class=%22modal-body%22%3E%0D%0A                %3Cdiv class=%22event-details%22%3E%0D%0A                    %3Cdiv class=%22mb-3%22%3E%0D%0A                        %3Ch4 id=%22event-title%22%3E%3C/h4%3E%0D%0A                        %3Cdiv class=%22text-muted%22%3E%0D%0A                            %3Cspan id=%22event-date%22%3E%3C/span%3E %E2%80%A2 %0D%0A                            %3Cspan id=%22event-time%22%3E%3C/span%3E%0D%0A                        %3C/div%3E%0D%0A                    %3C/div%3E%0D%0A                    %0D%0A                    %3Cdiv class=%22row mb-3%22%3E%0D%0A                        %3Cdiv class=%22col-md-6%22%3E%0D%0A                            %3Cdiv class=%22mb-2%22%3E%0D%0A                                %3Ci class=%22fas fa-calendar-alt me-2%22%3E%3C/i%3E%0D%0A                                %3Cspan id=%22event-calendar%22%3E%3C/span%3E%0D%0A                            %3C/div%3E%0D%0A                            %3Cdiv class=%22mb-2%22 id=%22event-location-container%22%3E%0D%0A                                %3Ci class=%22fas fa-map-marker-alt me-2%22%3E%3C/i%3E%0D%0A                                %3Cspan id=%22event-location%22%3E%3C/span%3E%0D%0A                            %3C/div%3E%0D%0A                        %3C/div%3E%0D%0A                        %3Cdiv class=%22col-md-6%22%3E%0D%0A                            %3Cdiv class=%22mb-2%22 id=%22event-show-container%22%3E%0D%0A                                %3Ci class=%22fas fa-car me-2%22%3E%3C/i%3E%0D%0A                                %3Cspan id=%22event-show%22%3E%3C/span%3E%0D%0A                            %3C/div%3E%0D%0A                            %3Cdiv class=%22mb-2%22 id=%22event-url-container%22%3E%0D%0A                                %3Ci class=%22fas fa-link me-2%22%3E%3C/i%3E%0D%0A                                %3Ca href=%22#%22 id=%22event-url%22 target=%22_blank%22%3E%3C/a%3E%0D%0A                            %3C/div%3E%0D%0A                        %3C/div%3E%0D%0A                    %3C/div%3E%0D%0A                    %0D%0A                    %3Cdiv class=%22mb-3%22 id=%22event-description-container%22%3E%0D%0A                        %3Ch5%3EDescription%3C/h5%3E%0D%0A                        %3Cdiv id=%22event-description%22%3E%3C/div%3E%0D%0A                    %3C/div%3E%0D%0A                %3C/div%3E%0D%0A            %3C/div%3E%0D%0A            %3Cdiv class=%22modal-footer%22%3E%0D%0A                %3Ca href=%22#%22 class=%22btn btn-primary%22 id=%22event-view-link%22%3EView Details%3C/a%3E%0D%0A                %3Cbutton type=%22button%22 class=%22btn btn-secondary%22 data-bs-dismiss=%22modal%22%3EClose%3C/button%3E%0D%0A            %3C/div%3E%0D%0A        %3C/div%3E%0D%0A    %3C/div%3E%0D%0A%3C/div%3E%0D%0A%0D%0A%3C!-- Add FullCalendar CSS --%3E%0D%0A%3Clink href=%22https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css%22 rel=%22stylesheet%22%3E%0D%0A%0D%0A%3C!-- Add custom CSS for calendar --%3E%0D%0A%3Cstyle%3E%0D%0A    .color-dot %7B%0D%0A        display: inline-block;%0D%0A        width: 12px;%0D%0A        height: 12px;%0D%0A        border-radius: 50%25;%0D%0A        margin-right: 5px;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-list %7B%0D%0A        max-height: 200px;%0D%0A        overflow-y: auto;%0D%0A    %7D%0D%0A    %0D%0A    #calendar %7B%0D%0A        height: 700px;%0D%0A    %7D%0D%0A    %0D%0A    .fc-event %7B%0D%0A        cursor: pointer;%0D%0A    %7D%0D%0A    %0D%0A    .fc-toolbar-title %7B%0D%0A        font-size: 1.5rem !important;%0D%0A    %7D%0D%0A    %0D%0A    .upcoming-event %7B%0D%0A        border-left: 4px solid #3788d8;%0D%0A    %7D%0D%0A    %0D%0A    .upcoming-event-title %7B%0D%0A        font-weight: 600;%0D%0A    %7D%0D%0A    %0D%0A    .upcoming-event-time %7B%0D%0A        font-size: 0.85rem;%0D%0A    %7D%0D%0A    %0D%0A    @media (max-width: 768px) %7B%0D%0A        #calendar %7B%0D%0A            height: 500px;%0D%0A        %7D%0D%0A        %0D%0A        .fc-toolbar-title %7B%0D%0A            font-size: 1.2rem !important;%0D%0A        %7D%0D%0A        %0D%0A        .fc-toolbar.fc-header-toolbar %7B%0D%0A            flex-direction: column;%0D%0A        %7D%0D%0A        %0D%0A        .fc-toolbar.fc-header-toolbar .fc-toolbar-chunk %7B%0D%0A            margin-bottom: 0.5rem;%0D%0A        %7D%0D%0A    %7D%0D%0A%3C/style%3E%0D%0A%0D%0A%3C!-- Add FullCalendar JS with all required plugins --%3E%0D%0A%3Cscript src=%22https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js%22%3E%3C/script%3E%0D%0A%3Cscript src=%22https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@5.10.1/main.min.js%22%3E%3C/script%3E%0D%0A%3Cscript src=%22https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@5.10.1/main.min.js%22%3E%3C/script%3E%0D%0A%3Cscript src=%22https://cdn.jsdelivr.net/npm/@fullcalendar/list@5.10.1/main.min.js%22%3E%3C/script%3E%0D%0A%3Cscript src=%22https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@5.10.1/main.min.js%22%3E%3C/script%3E%0D%0A%0D%0A%3C!-- Calendar initialization script --%3E%0D%0A%3Cscript%3E%0D%0A    document.addEventListener('DOMContentLoaded', function() %7B%0D%0A        // Get calendar settings%0D%0A        const settings = %7B%0D%0A            defaultView: '%3C?php echo isset($data%5B'settings'%5D%5B'default_view'%5D) ? $data%5B'settings'%5D%5B'default_view'%5D : 'month'; ?%3E',%0D%0A            businessHoursStart: '%3C?php echo isset($data%5B'settings'%5D%5B'business_hours_start'%5D) ? $data%5B'settings'%5D%5B'business_hours_start'%5D : '09:00:00'; ?%3E',%0D%0A            businessHoursEnd: '%3C?php echo isset($data%5B'settings'%5D%5B'business_hours_end'%5D) ? $data%5B'settings'%5D%5B'business_hours_end'%5D : '17:00:00'; ?%3E',%0D%0A            weekStartsOn: %3C?php echo isset($data%5B'settings'%5D%5B'week_starts_on'%5D) ? $data%5B'settings'%5D%5B'week_starts_on'%5D : 0; ?%3E,%0D%0A            timeFormat: '%3C?php echo isset($data%5B'settings'%5D%5B'time_format'%5D) ? $data%5B'settings'%5D%5B'time_format'%5D : '12'; ?%3E',%0D%0A            dateFormat: '%3C?php echo isset($data%5B'settings'%5D%5B'date_format'%5D) ? $data%5B'settings'%5D%5B'date_format'%5D : 'MM/DD/YYYY'; ?%3E',%0D%0A            defaultEventDuration: %3C?php echo isset($data%5B'settings'%5D%5B'default_event_duration'%5D) ? $data%5B'settings'%5D%5B'default_event_duration'%5D : 60; ?%3E,%0D%0A            enableDragDrop: %3C?php echo isset($data%5B'settings'%5D%5B'enable_drag_drop'%5D) &amp;&amp; $data%5B'settings'%5D%5B'enable_drag_drop'%5D ? 'true' : 'false'; ?%3E,%0D%0A            enableResize: %3C?php echo isset($data%5B'settings'%5D%5B'enable_resize'%5D) &amp;&amp; $data%5B'settings'%5D%5B'enable_resize'%5D ? 'true' : 'false'; ?%3E,%0D%0A            showWeekends: %3C?php echo isset($data%5B'settings'%5D%5B'show_weekends'%5D) &amp;&amp; $data%5B'settings'%5D%5B'show_weekends'%5D ? 'true' : 'false'; ?%3E,%0D%0A            defaultCalendarColor: '%3C?php echo isset($data%5B'settings'%5D%5B'default_calendar_color'%5D) ? $data%5B'settings'%5D%5B'default_calendar_color'%5D : '#3788d8'; ?%3E'%0D%0A        %7D;%0D%0A        %0D%0A        // Initialize calendar%0D%0A        const calendarEl = document.getElementById('calendar');%0D%0A        const calendar = new FullCalendar.Calendar(calendarEl, %7B%0D%0A            plugins: %5B 'dayGrid', 'timeGrid', 'list', 'interaction' %5D,%0D%0A            initialView: settings.defaultView,%0D%0A            headerToolbar: false, // We're using custom header buttons%0D%0A            firstDay: settings.weekStartsOn,%0D%0A            weekends: settings.showWeekends,%0D%0A            businessHours: %7B%0D%0A                daysOfWeek: %5B1, 2, 3, 4, 5%5D, // Monday - Friday%0D%0A                startTime: settings.businessHoursStart,%0D%0A                endTime: settings.businessHoursEnd%0D%0A            %7D,%0D%0A            editable: settings.enableDragDrop,%0D%0A            eventResizableFromStart: settings.enableResize,%0D%0A            eventDurationEditable: settings.enableResize,%0D%0A            selectable: true,%0D%0A            selectMirror: true,%0D%0A            dayMaxEvents: true,%0D%0A            events: %7B%0D%0A                url: '%3C?php echo URLROOT; ?%3E/calendar/getEvents',%0D%0A                failure: function() %7B%0D%0A                    alert('There was an error loading events!');%0D%0A                %7D%0D%0A            %7D,%0D%0A            eventTimeFormat: %7B%0D%0A                hour: 'numeric',%0D%0A                minute: '2-digit',%0D%0A                meridiem: settings.timeFormat === '12'%0D%0A            %7D,%0D%0A            select: function(info) %7B%0D%0A                // Redirect to create event page with pre-filled dates%0D%0A                window.location.href = '%3C?php echo URLROOT; ?%3E/calendar/createEvent?start=' + info.startStr + '&amp;end=' + info.endStr;%0D%0A            %7D,%0D%0A            eventClick: function(info) %7B%0D%0A                // Show event details in modal%0D%0A                showEventDetails(info.event);%0D%0A            %7D,%0D%0A            eventDrop: function(info) %7B%0D%0A                // Update event dates via AJAX%0D%0A                updateEventDates(info.event);%0D%0A            %7D,%0D%0A            eventResize: function(info) %7B%0D%0A                // Update event dates via AJAX%0D%0A                updateEventDates(info.event);%0D%0A            %7D,%0D%0A            loading: function(isLoading) %7B%0D%0A                // Show/hide loading indicator%0D%0A                if (isLoading) %7B%0D%0A                    // Add loading indicator if needed%0D%0A                %7D%0D%0A            %7D%0D%0A        %7D);%0D%0A        %0D%0A        // Render calendar%0D%0A        calendar.render();%0D%0A        %0D%0A        // Set active view button%0D%0A        setActiveViewButton(settings.defaultView);%0D%0A        %0D%0A        // View buttons%0D%0A        document.getElementById('view-month').addEventListener('click', function() %7B%0D%0A            calendar.changeView('dayGridMonth');%0D%0A            setActiveViewButton('month');%0D%0A        %7D);%0D%0A        %0D%0A        document.getElementById('view-week').addEventListener('click', function() %7B%0D%0A            calendar.changeView('timeGridWeek');%0D%0A            setActiveViewButton('week');%0D%0A        %7D);%0D%0A        %0D%0A        document.getElementById('view-day').addEventListener('click', function() %7B%0D%0A            calendar.changeView('timeGridDay');%0D%0A            setActiveViewButton('day');%0D%0A        %7D);%0D%0A        %0D%0A        document.getElementById('view-list').addEventListener('click', function() %7B%0D%0A            calendar.changeView('listMonth');%0D%0A            setActiveViewButton('list');%0D%0A        %7D);%0D%0A        %0D%0A        // Navigation buttons%0D%0A        document.getElementById('prev-btn').addEventListener('click', function() %7B%0D%0A            calendar.prev();%0D%0A        %7D);%0D%0A        %0D%0A        document.getElementById('next-btn').addEventListener('click', function() %7B%0D%0A            calendar.next();%0D%0A        %7D);%0D%0A        %0D%0A        document.getElementById('today-btn').addEventListener('click', function() %7B%0D%0A            calendar.today();%0D%0A        %7D);%0D%0A        %0D%0A        // Calendar toggles%0D%0A        const calendarToggles = document.querySelectorAll('.calendar-toggle');%0D%0A        calendarToggles.forEach(function(toggle) %7B%0D%0A            toggle.addEventListener('change', function() %7B%0D%0A                const calendarId = this.value;%0D%0A                const isChecked = this.checked;%0D%0A                %0D%0A                // Filter events%0D%0A                filterEvents();%0D%0A                %0D%0A                // Refetch events%0D%0A                calendar.refetchEvents();%0D%0A            %7D);%0D%0A        %7D);%0D%0A        %0D%0A        // Helper function to set active view button%0D%0A        function setActiveViewButton(view) %7B%0D%0A            const viewButtons = document.querySelectorAll('%5Bid%5E=%22view-%22%5D');%0D%0A            viewButtons.forEach(function(button) %7B%0D%0A                button.classList.remove('active', 'btn-primary');%0D%0A                button.classList.add('btn-outline-secondary');%0D%0A            %7D);%0D%0A            %0D%0A            let buttonId;%0D%0A            switch(view) %7B%0D%0A                case 'dayGridMonth':%0D%0A                case 'month':%0D%0A                    buttonId = 'view-month';%0D%0A                    break;%0D%0A                case 'timeGridWeek':%0D%0A                case 'week':%0D%0A                    buttonId = 'view-week';%0D%0A                    break;%0D%0A                case 'timeGridDay':%0D%0A                case 'day':%0D%0A                    buttonId = 'view-day';%0D%0A                    break;%0D%0A                case 'listMonth':%0D%0A                case 'list':%0D%0A                    buttonId = 'view-list';%0D%0A                    break;%0D%0A                default:%0D%0A                    buttonId = 'view-month';%0D%0A            %7D%0D%0A            %0D%0A            const activeButton = document.getElementById(buttonId);%0D%0A            if (activeButton) %7B%0D%0A                activeButton.classList.remove('btn-outline-secondary');%0D%0A                activeButton.classList.add('active', 'btn-primary');%0D%0A            %7D%0D%0A        %7D%0D%0A        %0D%0A        // Helper function to filter events based on selected calendars%0D%0A        function filterEvents() %7B%0D%0A            const selectedCalendars = %5B%5D;%0D%0A            calendarToggles.forEach(function(toggle) %7B%0D%0A                if (toggle.checked) %7B%0D%0A                    selectedCalendars.push(toggle.value);%0D%0A                %7D%0D%0A            %7D);%0D%0A            %0D%0A            // Update events source%0D%0A            const eventsSource = calendar.getEventSources()%5B0%5D;%0D%0A            if (eventsSource) %7B%0D%0A                eventsSource.remove();%0D%0A            %7D%0D%0A            %0D%0A            calendar.addEventSource(%7B%0D%0A                url: '%3C?php echo URLROOT; ?%3E/calendar/getEvents',%0D%0A                extraParams: %7B%0D%0A                    calendar_id: selectedCalendars.join(',')%0D%0A                %7D%0D%0A            %7D);%0D%0A        %7D%0D%0A        %0D%0A        // Helper function to show event details in modal%0D%0A        function showEventDetails(event) %7B%0D%0A            const modal = document.getElementById('eventModal');%0D%0A            const modalInstance = new bootstrap.Modal(modal);%0D%0A            %0D%0A            // Set event details%0D%0A            document.getElementById('event-title').textContent = event.title;%0D%0A            %0D%0A            // Format date%0D%0A            const startDate = new Date(event.start);%0D%0A            const endDate = event.end ? new Date(event.end) : null;%0D%0A            %0D%0A            let dateStr = startDate.toLocaleDateString();%0D%0A            if (endDate &amp;&amp; startDate.toDateString() !== endDate.toDateString()) %7B%0D%0A                dateStr += ' - ' + endDate.toLocaleDateString();%0D%0A            %7D%0D%0A            document.getElementById('event-date').textContent = dateStr;%0D%0A            %0D%0A            // Format time%0D%0A            if (event.allDay) %7B%0D%0A                document.getElementById('event-time').textContent = 'All day';%0D%0A            %7D else %7B%0D%0A                let timeStr = startDate.toLocaleTimeString(%5B%5D, %7B hour: 'numeric', minute: '2-digit' %7D);%0D%0A                if (endDate) %7B%0D%0A                    timeStr += ' - ' + endDate.toLocaleTimeString(%5B%5D, %7B hour: 'numeric', minute: '2-digit' %7D);%0D%0A                %7D%0D%0A                document.getElementById('event-time').textContent = timeStr;%0D%0A            %7D%0D%0A            %0D%0A            // Set calendar%0D%0A            document.getElementById('event-calendar').textContent = event.extendedProps.calendar_name %7C%7C 'Unknown Calendar';%0D%0A            %0D%0A            // Set location%0D%0A            const locationContainer = document.getElementById('event-location-container');%0D%0A            if (event.extendedProps.location) %7B%0D%0A                document.getElementById('event-location').textContent = event.extendedProps.location;%0D%0A                locationContainer.style.display = 'block';%0D%0A            %7D else %7B%0D%0A                locationContainer.style.display = 'none';%0D%0A            %7D%0D%0A            %0D%0A            // Set show%0D%0A            const showContainer = document.getElementById('event-show-container');%0D%0A            if (event.extendedProps.show_name) %7B%0D%0A                document.getElementById('event-show').textContent = event.extendedProps.show_name;%0D%0A                showContainer.style.display = 'block';%0D%0A            %7D else %7B%0D%0A                showContainer.style.display = 'none';%0D%0A            %7D%0D%0A            %0D%0A            // Set URL%0D%0A            const urlContainer = document.getElementById('event-url-container');%0D%0A            if (event.url) %7B%0D%0A                const urlElement = document.getElementById('event-url');%0D%0A                urlElement.href = event.url;%0D%0A                urlElement.textContent = event.url;%0D%0A                urlContainer.style.display = 'block';%0D%0A            %7D else %7B%0D%0A                urlContainer.style.display = 'none';%0D%0A            %7D%0D%0A            %0D%0A            // Set description%0D%0A            const descriptionContainer = document.getElementById('event-description-container');%0D%0A            if (event.extendedProps.description) %7B%0D%0A                document.getElementById('event-description').innerHTML = event.extendedProps.description;%0D%0A                descriptionContainer.style.display = 'block';%0D%0A            %7D else %7B%0D%0A                descriptionContainer.style.display = 'none';%0D%0A            %7D%0D%0A            %0D%0A            // Set view link%0D%0A            document.getElementById('event-view-link').href = '%3C?php echo URLROOT; ?%3E/calendar/event/' + event.id;%0D%0A            %0D%0A            // Show modal%0D%0A            modalInstance.show();%0D%0A        %7D%0D%0A        %0D%0A        // Helper function to update event dates%0D%0A        function updateEventDates(event) %7B%0D%0A            const eventData = %7B%0D%0A                id: event.id,%0D%0A                start_date: event.start.toISOString(),%0D%0A                end_date: event.end ? event.end.toISOString() : null,%0D%0A                all_day: event.allDay ? 1 : 0%0D%0A            %7D;%0D%0A            %0D%0A            // Send AJAX request%0D%0A            fetch('%3C?php echo URLROOT; ?%3E/calendar/updateEventDates', %7B%0D%0A                method: 'POST',%0D%0A                headers: %7B%0D%0A                    'Content-Type': 'application/json',%0D%0A                    'X-Requested-With': 'XMLHttpRequest'%0D%0A                %7D,%0D%0A                body: JSON.stringify(eventData)%0D%0A            %7D)%0D%0A            .then(response =%3E response.json())%0D%0A            .then(data =%3E %7B%0D%0A                if (!data.success) %7B%0D%0A                    alert('Error updating event: ' + data.message);%0D%0A                    calendar.refetchEvents();%0D%0A                %7D%0D%0A            %7D)%0D%0A            .catch(error =%3E %7B%0D%0A                console.error('Error:', error);%0D%0A                alert('Error updating event. Please try again.');%0D%0A                calendar.refetchEvents();%0D%0A            %7D);%0D%0A        %7D%0D%0A        %0D%0A        // Load upcoming events%0D%0A        loadUpcomingEvents();%0D%0A        %0D%0A        function loadUpcomingEvents() %7B%0D%0A            const upcomingEventsContainer = document.getElementById('upcoming-events');%0D%0A            %0D%0A            // Send AJAX request%0D%0A            fetch('%3C?php echo URLROOT; ?%3E/calendar/getUpcomingEvents', %7B%0D%0A                method: 'GET',%0D%0A                headers: %7B%0D%0A                    'X-Requested-With': 'XMLHttpRequest'%0D%0A                %7D%0D%0A            %7D)%0D%0A            .then(response =%3E response.json())%0D%0A            .then(data =%3E %7B%0D%0A                // Clear container%0D%0A                upcomingEventsContainer.innerHTML = '';%0D%0A                %0D%0A                if (data.length === 0) %7B%0D%0A                    upcomingEventsContainer.innerHTML = '%3Cdiv class=%22list-group-item%22%3ENo upcoming events%3C/div%3E';%0D%0A                    return;%0D%0A                %7D%0D%0A                %0D%0A                // Add events%0D%0A                data.forEach(event =%3E %7B%0D%0A                    const startDate = new Date(event.start_date);%0D%0A                    const endDate = event.end_date ? new Date(event.end_date) : null;%0D%0A                    %0D%0A                    let dateStr = startDate.toLocaleDateString();%0D%0A                    let timeStr = '';%0D%0A                    %0D%0A                    if (event.all_day) %7B%0D%0A                        timeStr = 'All day';%0D%0A                    %7D else %7B%0D%0A                        timeStr = startDate.toLocaleTimeString(%5B%5D, %7B hour: 'numeric', minute: '2-digit' %7D);%0D%0A                        if (endDate) %7B%0D%0A                            timeStr += ' - ' + endDate.toLocaleTimeString(%5B%5D, %7B hour: 'numeric', minute: '2-digit' %7D);%0D%0A                        %7D%0D%0A                    %7D%0D%0A                    %0D%0A                    const eventItem = document.createElement('a');%0D%0A                    eventItem.href = '%3C?php echo URLROOT; ?%3E/calendar/event/' + event.id;%0D%0A                    eventItem.className = 'list-group-item list-group-item-action upcoming-event';%0D%0A                    eventItem.style.borderLeftColor = event.color %7C%7C event.calendar_color %7C%7C settings.defaultCalendarColor;%0D%0A                    %0D%0A                    eventItem.innerHTML = %60%0D%0A                        %3Cdiv class=%22d-flex w-100 justify-content-between%22%3E%0D%0A                            %3Ch6 class=%22mb-1 upcoming-event-title%22%3E$%7Bevent.title%7D%3C/h6%3E%0D%0A                        %3C/div%3E%0D%0A                        %3Cp class=%22mb-1 upcoming-event-time%22%3E$%7BdateStr%7D %E2%80%A2 $%7BtimeStr%7D%3C/p%3E%0D%0A                        %3Csmall%3E$%7Bevent.calendar_name%7D%3C/small%3E%0D%0A                    %60;%0D%0A                    %0D%0A                    upcomingEventsContainer.appendChild(eventItem);%0D%0A                %7D);%0D%0A            %7D)%0D%0A            .catch(error =%3E %7B%0D%0A                console.error('Error:', error);%0D%0A                upcomingEventsContainer.innerHTML = '%3Cdiv class=%22list-group-item text-danger%22%3EError loading events%3C/div%3E';%0D%0A            %7D);%0D%0A        %7D%0D%0A    %7D);%0D%0A%3C/script%3E%0D%0A%0D%0A%3C?php require APPROOT . '/views/includes/footer.php'; ?%3E\n&quot;,&quot;isNew&quot;:true,&quot;isApplied&quot;:true,&quot;isUnread&quot;:true},{&quot;path&quot;:&quot;libraries/js/custom-calendar.js&quot;,&quot;patch&quot;:&quot;@@ -0,0 +1,55649 @@\n+/**%0D%0A * Custom Calendar Implementation%0D%0A * %0D%0A * A fully custom calendar implementation for the Events and Shows Management System.%0D%0A * This calendar provides month, week, day, and list views with advanced features.%0D%0A * %0D%0A * Version: 1.0.0%0D%0A */%0D%0A%0D%0Aclass CustomCalendar %7B%0D%0A    /**%0D%0A     * Constructor%0D%0A     * %0D%0A     * @param %7Bstring%7D containerId - The ID of the container element%0D%0A     * @param %7BObject%7D options - Calendar options%0D%0A     */%0D%0A    constructor(containerId, options = %7B%7D) %7B%0D%0A        // Store container element%0D%0A        this.container = document.getElementById(containerId);%0D%0A        if (!this.container) %7B%0D%0A            throw new Error(%60Container element with ID %22$%7BcontainerId%7D%22 not found%60);%0D%0A        %7D%0D%0A%0D%0A        // Default options%0D%0A        this.defaultOptions = %7B%0D%0A            defaultView: 'month',%0D%0A            weekStartsOn: 0, // 0 = Sunday, 1 = Monday, etc.%0D%0A            showWeekends: true,%0D%0A            businessHoursStart: '09:00',%0D%0A            businessHoursEnd: '17:00',%0D%0A            timeFormat: '12', // 12 or 24%0D%0A            dateFormat: 'MM/DD/YYYY',%0D%0A            defaultEventDuration: 60, // minutes%0D%0A            enableDragDrop: true,%0D%0A            enableResize: true,%0D%0A            defaultCalendarColor: '#3788d8',%0D%0A            eventSources: %5B%5D,%0D%0A            eventClick: null,%0D%0A            eventDrop: null,%0D%0A            eventResize: null,%0D%0A            dateSelect: null,%0D%0A            loading: null,%0D%0A            viewChange: null,%0D%0A            dayHeaderFormat: 'short', // 'short', 'narrow', 'long'%0D%0A            monthHeaderFormat: 'long', // 'numeric', '2-digit', 'short', 'long'%0D%0A            timeSlotDuration: 30, // minutes%0D%0A            snapDuration: 15, // minutes%0D%0A            slotLabelInterval: 60, // minutes%0D%0A            slotLabelFormat: 'h:mm a', // 'h:mm a', 'HH:mm'%0D%0A            allDaySlot: true,%0D%0A            allDayText: 'All Day',%0D%0A            nowIndicator: true,%0D%0A            scrollTime: '08:00:00',%0D%0A            height: 'auto',%0D%0A            aspectRatio: 1.35,%0D%0A            firstHour: 8,%0D%0A            minTime: '00:00:00',%0D%0A            maxTime: '24:00:00',%0D%0A            slotEventOverlap: true,%0D%0A            eventLimit: true,%0D%0A            eventLimitText: 'more',%0D%0A            eventLimitClick: 'popover',%0D%0A            navLinks: true,%0D%0A            selectable: true,%0D%0A            selectMirror: true,%0D%0A            unselectAuto: true,%0D%0A            unselectCancel: '',%0D%0A            selectOverlap: true,%0D%0A            selectConstraint: %7B%7D,%0D%0A            selectAllow: null,%0D%0A            selectMinDistance: 0,%0D%0A            editable: true,%0D%0A            eventStartEditable: true,%0D%0A            eventDurationEditable: true,%0D%0A            eventOverlap: true,%0D%0A            eventConstraint: %7B%7D,%0D%0A            eventAllow: null,%0D%0A            longPressDelay: 1000,%0D%0A            eventLongPressDelay: 1000,%0D%0A            selectLongPressDelay: 1000%0D%0A        %7D;%0D%0A%0D%0A        // Merge options%0D%0A        this.options = %7B ...this.defaultOptions, ...options %7D;%0D%0A%0D%0A        // Current date and view%0D%0A        this.currentDate = new Date();%0D%0A        this.currentView = this.options.defaultView;%0D%0A%0D%0A        // Events cache%0D%0A        this.events = %5B%5D;%0D%0A        this.filteredEvents = %5B%5D;%0D%0A%0D%0A        // Selected calendars%0D%0A        this.selectedCalendars = %5B%5D;%0D%0A%0D%0A        // Initialize calendar%0D%0A        this.init();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Initialize calendar%0D%0A     */%0D%0A    init() %7B%0D%0A        // Create calendar structure%0D%0A        this.createCalendarStructure();%0D%0A%0D%0A        // Set initial view%0D%0A        this.setView(this.currentView);%0D%0A%0D%0A        // Fetch events%0D%0A        this.fetchEvents();%0D%0A%0D%0A        // Set up event listeners%0D%0A        this.setupEventListeners();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Create calendar structure%0D%0A     */%0D%0A    createCalendarStructure() %7B%0D%0A        // Clear container%0D%0A        this.container.innerHTML = '';%0D%0A        this.container.classList.add('custom-calendar');%0D%0A%0D%0A        // Create header%0D%0A        this.header = document.createElement('div');%0D%0A        this.header.className = 'calendar-header';%0D%0A        this.container.appendChild(this.header);%0D%0A%0D%0A        // Create title%0D%0A        this.title = document.createElement('h2');%0D%0A        this.title.className = 'calendar-title';%0D%0A        this.header.appendChild(this.title);%0D%0A%0D%0A        // Create main calendar grid%0D%0A        this.calendarGrid = document.createElement('div');%0D%0A        this.calendarGrid.className = 'calendar-grid';%0D%0A        this.container.appendChild(this.calendarGrid);%0D%0A%0D%0A        // Create event container for day/week view%0D%0A        this.eventContainer = document.createElement('div');%0D%0A        this.eventContainer.className = 'calendar-events';%0D%0A        this.container.appendChild(this.eventContainer);%0D%0A%0D%0A        // Create event list for list view%0D%0A        this.eventList = document.createElement('div');%0D%0A        this.eventList.className = 'calendar-event-list';%0D%0A        this.container.appendChild(this.eventList);%0D%0A%0D%0A        // Create loading indicator%0D%0A        this.loadingIndicator = document.createElement('div');%0D%0A        this.loadingIndicator.className = 'calendar-loading';%0D%0A        this.loadingIndicator.innerHTML = '%3Cdiv class=%22spinner%22%3E%3C/div%3E%3Cspan%3ELoading...%3C/span%3E';%0D%0A        this.loadingIndicator.style.display = 'none';%0D%0A        this.container.appendChild(this.loadingIndicator);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Set up event listeners%0D%0A     */%0D%0A    setupEventListeners() %7B%0D%0A        // Event delegation for calendar cells%0D%0A        this.calendarGrid.addEventListener('click', (e) =%3E %7B%0D%0A            // Handle day cell click%0D%0A            if (e.target.classList.contains('calendar-day') %7C%7C e.target.closest('.calendar-day')) %7B%0D%0A                const cell = e.target.classList.contains('calendar-day') ? e.target : e.target.closest('.calendar-day');%0D%0A                const date = new Date(cell.dataset.date);%0D%0A                %0D%0A                if (this.options.dateSelect) %7B%0D%0A                    this.options.dateSelect(%7B%0D%0A                        start: date,%0D%0A                        end: new Date(date.getTime() + 24 * 60 * 60 * 1000),%0D%0A                        allDay: true%0D%0A                    %7D);%0D%0A                %7D%0D%0A            %7D%0D%0A%0D%0A            // Handle event click%0D%0A            if (e.target.classList.contains('calendar-event') %7C%7C e.target.closest('.calendar-event')) %7B%0D%0A                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');%0D%0A                const eventId = eventEl.dataset.eventId;%0D%0A                const event = this.events.find(e =%3E e.id == eventId);%0D%0A                %0D%0A                if (event &amp;&amp; this.options.eventClick) %7B%0D%0A                    this.options.eventClick(%7B event %7D);%0D%0A                %7D%0D%0A            %7D%0D%0A        %7D);%0D%0A%0D%0A        // Event delegation for event list%0D%0A        this.eventList.addEventListener('click', (e) =%3E %7B%0D%0A            // Handle event click in list view%0D%0A            if (e.target.classList.contains('list-event') %7C%7C e.target.closest('.list-event')) %7B%0D%0A                const eventEl = e.target.classList.contains('list-event') ? e.target : e.target.closest('.list-event');%0D%0A                const eventId = eventEl.dataset.eventId;%0D%0A                const event = this.events.find(e =%3E e.id == eventId);%0D%0A                %0D%0A                if (event &amp;&amp; this.options.eventClick) %7B%0D%0A                    this.options.eventClick(%7B event %7D);%0D%0A                %7D%0D%0A            %7D%0D%0A        %7D);%0D%0A%0D%0A        // Drag and drop for events%0D%0A        if (this.options.enableDragDrop) %7B%0D%0A            this.setupDragAndDrop();%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Set up drag and drop functionality%0D%0A     */%0D%0A    setupDragAndDrop() %7B%0D%0A        // We'll implement this with a more advanced library or custom implementation%0D%0A        // For now, we'll use a simple implementation%0D%0A        this.container.addEventListener('mousedown', (e) =%3E %7B%0D%0A            if (e.target.classList.contains('calendar-event') %7C%7C e.target.closest('.calendar-event')) %7B%0D%0A                const eventEl = e.target.classList.contains('calendar-event') ? e.target : e.target.closest('.calendar-event');%0D%0A                const eventId = eventEl.dataset.eventId;%0D%0A                const event = this.events.find(e =%3E e.id == eventId);%0D%0A                %0D%0A                if (!event %7C%7C !this.options.editable) return;%0D%0A                %0D%0A                // Start drag%0D%0A                const startX = e.clientX;%0D%0A                const startY = e.clientY;%0D%0A                const startTop = eventEl.offsetTop;%0D%0A                const startLeft = eventEl.offsetLeft;%0D%0A                const startDate = new Date(event.start);%0D%0A                %0D%0A                // Create ghost element%0D%0A                const ghost = eventEl.cloneNode(true);%0D%0A                ghost.classList.add('dragging');%0D%0A                ghost.style.position = 'absolute';%0D%0A                ghost.style.top = startTop + 'px';%0D%0A                ghost.style.left = startLeft + 'px';%0D%0A                ghost.style.width = eventEl.offsetWidth + 'px';%0D%0A                ghost.style.zIndex = 1000;%0D%0A                ghost.style.opacity = 0.7;%0D%0A                document.body.appendChild(ghost);%0D%0A                %0D%0A                // Hide original%0D%0A                eventEl.style.opacity = 0.3;%0D%0A                %0D%0A                // Track mouse move%0D%0A                const moveHandler = (e) =%3E %7B%0D%0A                    ghost.style.top = (startTop + e.clientY - startY) + 'px';%0D%0A                    ghost.style.left = (startLeft + e.clientX - startX) + 'px';%0D%0A                %7D;%0D%0A                %0D%0A                // Handle drop%0D%0A                const upHandler = (e) =%3E %7B%0D%0A                    document.removeEventListener('mousemove', moveHandler);%0D%0A                    document.removeEventListener('mouseup', upHandler);%0D%0A                    %0D%0A                    // Remove ghost%0D%0A                    document.body.removeChild(ghost);%0D%0A                    %0D%0A                    // Show original%0D%0A                    eventEl.style.opacity = 1;%0D%0A                    %0D%0A                    // Find drop target%0D%0A                    const target = document.elementFromPoint(e.clientX, e.clientY);%0D%0A                    const dayCell = target.classList.contains('calendar-day') ? target : target.closest('.calendar-day');%0D%0A                    %0D%0A                    if (dayCell) %7B%0D%0A                        const newDate = new Date(dayCell.dataset.date);%0D%0A                        const diff = newDate.getTime() - startDate.getTime();%0D%0A                        %0D%0A                        // Update event dates%0D%0A                        const newEvent = %7B ...event %7D;%0D%0A                        newEvent.start = new Date(new Date(event.start).getTime() + diff);%0D%0A                        newEvent.end = event.end ? new Date(new Date(event.end).getTime() + diff) : null;%0D%0A                        %0D%0A                        // Call eventDrop callback%0D%0A                        if (this.options.eventDrop) %7B%0D%0A                            this.options.eventDrop(%7B%0D%0A                                event: newEvent,%0D%0A                                oldEvent: event,%0D%0A                                revert: () =%3E %7B%0D%0A                                    // Revert is handled by refetching events%0D%0A                                    this.fetchEvents();%0D%0A                                %7D%0D%0A                            %7D);%0D%0A                        %7D%0D%0A                    %7D%0D%0A                %7D;%0D%0A                %0D%0A                document.addEventListener('mousemove', moveHandler);%0D%0A                document.addEventListener('mouseup', upHandler);%0D%0A            %7D%0D%0A        %7D);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Set calendar view%0D%0A     * %0D%0A     * @param %7Bstring%7D view - The view to set ('month', 'week', 'day', 'list')%0D%0A     */%0D%0A    setView(view) %7B%0D%0A        this.currentView = view;%0D%0A        %0D%0A        // Update container class%0D%0A        this.container.className = 'custom-calendar';%0D%0A        this.container.classList.add(%60calendar-view-$%7Bview%7D%60);%0D%0A        %0D%0A        // Hide/show appropriate containers%0D%0A        this.calendarGrid.style.display = %5B'month', 'week', 'day'%5D.includes(view) ? 'grid' : 'none';%0D%0A        this.eventContainer.style.display = %5B'week', 'day'%5D.includes(view) ? 'block' : 'none';%0D%0A        this.eventList.style.display = view === 'list' ? 'block' : 'none';%0D%0A        %0D%0A        // Render view%0D%0A        this.render();%0D%0A        %0D%0A        // Call viewChange callback%0D%0A        if (this.options.viewChange) %7B%0D%0A            this.options.viewChange(%7B view %7D);%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Navigate to previous period%0D%0A     */%0D%0A    prev() %7B%0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);%0D%0A                break;%0D%0A            case 'week':%0D%0A                this.currentDate = new Date(this.currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);%0D%0A                break;%0D%0A            case 'day':%0D%0A                this.currentDate = new Date(this.currentDate.getTime() - 24 * 60 * 60 * 1000);%0D%0A                break;%0D%0A            case 'list':%0D%0A                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);%0D%0A                break;%0D%0A        %7D%0D%0A        %0D%0A        this.render();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Navigate to next period%0D%0A     */%0D%0A    next() %7B%0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);%0D%0A                break;%0D%0A            case 'week':%0D%0A                this.currentDate = new Date(this.currentDate.getTime() + 7 * 24 * 60 * 60 * 1000);%0D%0A                break;%0D%0A            case 'day':%0D%0A                this.currentDate = new Date(this.currentDate.getTime() + 24 * 60 * 60 * 1000);%0D%0A                break;%0D%0A            case 'list':%0D%0A                this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);%0D%0A                break;%0D%0A        %7D%0D%0A        %0D%0A        this.render();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Navigate to today%0D%0A     */%0D%0A    today() %7B%0D%0A        this.currentDate = new Date();%0D%0A        this.render();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render calendar%0D%0A     */%0D%0A    render() %7B%0D%0A        // Update title%0D%0A        this.updateTitle();%0D%0A        %0D%0A        // Render appropriate view%0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                this.renderMonthView();%0D%0A                break;%0D%0A            case 'week':%0D%0A                this.renderWeekView();%0D%0A                break;%0D%0A            case 'day':%0D%0A                this.renderDayView();%0D%0A                break;%0D%0A            case 'list':%0D%0A                this.renderListView();%0D%0A                break;%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Update calendar title%0D%0A     */%0D%0A    updateTitle() %7B%0D%0A        const months = %5B'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'%5D;%0D%0A        const days = %5B'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'%5D;%0D%0A        %0D%0A        let title = '';%0D%0A        %0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                title = %60$%7Bmonths%5Bthis.currentDate.getMonth()%5D%7D $%7Bthis.currentDate.getFullYear()%7D%60;%0D%0A                break;%0D%0A            case 'week':%0D%0A                const weekStart = this.getWeekStart(this.currentDate);%0D%0A                const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);%0D%0A                %0D%0A                if (weekStart.getMonth() === weekEnd.getMonth()) %7B%0D%0A                    title = %60$%7Bmonths%5BweekStart.getMonth()%5D%7D $%7BweekStart.getDate()%7D - $%7BweekEnd.getDate()%7D, $%7BweekStart.getFullYear()%7D%60;%0D%0A                %7D else if (weekStart.getFullYear() === weekEnd.getFullYear()) %7B%0D%0A                    title = %60$%7Bmonths%5BweekStart.getMonth()%5D%7D $%7BweekStart.getDate()%7D - $%7Bmonths%5BweekEnd.getMonth()%5D%7D $%7BweekEnd.getDate()%7D, $%7BweekStart.getFullYear()%7D%60;%0D%0A                %7D else %7B%0D%0A                    title = %60$%7Bmonths%5BweekStart.getMonth()%5D%7D $%7BweekStart.getDate()%7D, $%7BweekStart.getFullYear()%7D - $%7Bmonths%5BweekEnd.getMonth()%5D%7D $%7BweekEnd.getDate()%7D, $%7BweekEnd.getFullYear()%7D%60;%0D%0A                %7D%0D%0A                break;%0D%0A            case 'day':%0D%0A                title = %60$%7Bdays%5Bthis.currentDate.getDay()%5D%7D, $%7Bmonths%5Bthis.currentDate.getMonth()%5D%7D $%7Bthis.currentDate.getDate()%7D, $%7Bthis.currentDate.getFullYear()%7D%60;%0D%0A                break;%0D%0A            case 'list':%0D%0A                title = %60$%7Bmonths%5Bthis.currentDate.getMonth()%5D%7D $%7Bthis.currentDate.getFullYear()%7D%60;%0D%0A                break;%0D%0A        %7D%0D%0A        %0D%0A        this.title.textContent = title;%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render month view%0D%0A     */%0D%0A    renderMonthView() %7B%0D%0A        // Clear grid%0D%0A        this.calendarGrid.innerHTML = '';%0D%0A        %0D%0A        // Set grid template%0D%0A        this.calendarGrid.className = 'calendar-grid month-view';%0D%0A        %0D%0A        // Get month details%0D%0A        const year = this.currentDate.getFullYear();%0D%0A        const month = this.currentDate.getMonth();%0D%0A        const firstDay = new Date(year, month, 1);%0D%0A        const lastDay = new Date(year, month + 1, 0);%0D%0A        const daysInMonth = lastDay.getDate();%0D%0A        %0D%0A        // Adjust for week start day%0D%0A        let startOffset = firstDay.getDay() - this.options.weekStartsOn;%0D%0A        if (startOffset %3C 0) startOffset += 7;%0D%0A        %0D%0A        // Create day headers%0D%0A        const dayNames = %5B'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'%5D;%0D%0A        for (let i = 0; i %3C 7; i++) %7B%0D%0A            const dayIndex = (i + this.options.weekStartsOn) %25 7;%0D%0A            const dayHeader = document.createElement('div');%0D%0A            dayHeader.className = 'calendar-day-header';%0D%0A            dayHeader.textContent = dayNames%5BdayIndex%5D;%0D%0A            this.calendarGrid.appendChild(dayHeader);%0D%0A        %7D%0D%0A        %0D%0A        // Create day cells%0D%0A        const today = new Date();%0D%0A        today.setHours(0, 0, 0, 0);%0D%0A        %0D%0A        // Previous month days%0D%0A        for (let i = 0; i %3C startOffset; i++) %7B%0D%0A            const day = new Date(year, month, 1 - startOffset + i);%0D%0A            this.createDayCell(day, 'prev-month');%0D%0A        %7D%0D%0A        %0D%0A        // Current month days%0D%0A        for (let i = 1; i %3C= daysInMonth; i++) %7B%0D%0A            const day = new Date(year, month, i);%0D%0A            this.createDayCell(day, 'current-month');%0D%0A        %7D%0D%0A        %0D%0A        // Next month days%0D%0A        const totalCells = Math.ceil((daysInMonth + startOffset) / 7) * 7;%0D%0A        const remainingCells = totalCells - (daysInMonth + startOffset);%0D%0A        %0D%0A        for (let i = 1; i %3C= remainingCells; i++) %7B%0D%0A            const day = new Date(year, month + 1, i);%0D%0A            this.createDayCell(day, 'next-month');%0D%0A        %7D%0D%0A        %0D%0A        // Render events%0D%0A        this.renderMonthEvents();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Create a day cell for month view%0D%0A     * %0D%0A     * @param %7BDate%7D date - The date for the cell%0D%0A     * @param %7Bstring%7D className - Additional class name%0D%0A     */%0D%0A    createDayCell(date, className) %7B%0D%0A        const cell = document.createElement('div');%0D%0A        cell.className = %60calendar-day $%7BclassName%7D%60;%0D%0A        cell.dataset.date = date.toISOString().split('T')%5B0%5D;%0D%0A        %0D%0A        // Check if today%0D%0A        const today = new Date();%0D%0A        if (date.getDate() === today.getDate() &amp;&amp; %0D%0A            date.getMonth() === today.getMonth() &amp;&amp; %0D%0A            date.getFullYear() === today.getFullYear()) %7B%0D%0A            cell.classList.add('today');%0D%0A        %7D%0D%0A        %0D%0A        // Check if weekend%0D%0A        if (date.getDay() === 0 %7C%7C date.getDay() === 6) %7B%0D%0A            cell.classList.add('weekend');%0D%0A        %7D%0D%0A        %0D%0A        // Add day number%0D%0A        const dayNumber = document.createElement('div');%0D%0A        dayNumber.className = 'day-number';%0D%0A        dayNumber.textContent = date.getDate();%0D%0A        cell.appendChild(dayNumber);%0D%0A        %0D%0A        // Add events container%0D%0A        const eventsContainer = document.createElement('div');%0D%0A        eventsContainer.className = 'day-events';%0D%0A        cell.appendChild(eventsContainer);%0D%0A        %0D%0A        this.calendarGrid.appendChild(cell);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render events for month view%0D%0A     */%0D%0A    renderMonthEvents() %7B%0D%0A        // Get visible date range%0D%0A        const year = this.currentDate.getFullYear();%0D%0A        const month = this.currentDate.getMonth();%0D%0A        const firstDay = new Date(year, month, 1);%0D%0A        const lastDay = new Date(year, month + 1, 0);%0D%0A        %0D%0A        // Adjust for week start and end%0D%0A        let startOffset = firstDay.getDay() - this.options.weekStartsOn;%0D%0A        if (startOffset %3C 0) startOffset += 7;%0D%0A        %0D%0A        const visibleStart = new Date(year, month, 1 - startOffset);%0D%0A        const totalDays = Math.ceil((lastDay.getDate() + startOffset) / 7) * 7;%0D%0A        const visibleEnd = new Date(year, month, totalDays - startOffset);%0D%0A        %0D%0A        // Filter events for visible range%0D%0A        const visibleEvents = this.filteredEvents.filter(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            return eventStart %3C visibleEnd &amp;&amp; eventEnd %3E= visibleStart;%0D%0A        %7D);%0D%0A        %0D%0A        // Sort events by duration (longer events first)%0D%0A        visibleEvents.sort((a, b) =%3E %7B%0D%0A            const aStart = new Date(a.start);%0D%0A            const aEnd = a.end ? new Date(a.end) : new Date(aStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            const aDuration = aEnd.getTime() - aStart.getTime();%0D%0A            %0D%0A            const bStart = new Date(b.start);%0D%0A            const bEnd = b.end ? new Date(b.end) : new Date(bStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            const bDuration = bEnd.getTime() - bStart.getTime();%0D%0A            %0D%0A            return bDuration - aDuration;%0D%0A        %7D);%0D%0A        %0D%0A        // Render each event%0D%0A        visibleEvents.forEach(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            let eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            // Adjust all-day events%0D%0A            if (event.allDay) %7B%0D%0A                eventStart.setHours(0, 0, 0, 0);%0D%0A                eventEnd.setHours(23, 59, 59, 999);%0D%0A            %7D%0D%0A            %0D%0A            // Clip to visible range%0D%0A            const displayStart = new Date(Math.max(eventStart.getTime(), visibleStart.getTime()));%0D%0A            const displayEnd = new Date(Math.min(eventEnd.getTime(), visibleEnd.getTime()));%0D%0A            %0D%0A            // Calculate day span%0D%0A            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));%0D%0A            %0D%0A            // Find cells for each day%0D%0A            for (let i = 0; i %3C daySpan; i++) %7B%0D%0A                const day = new Date(displayStart.getTime() + i * 24 * 60 * 60 * 1000);%0D%0A                const dateStr = day.toISOString().split('T')%5B0%5D;%0D%0A                const cell = this.calendarGrid.querySelector(%60.calendar-day%5Bdata-date=%22$%7BdateStr%7D%22%5D%60);%0D%0A                %0D%0A                if (cell) %7B%0D%0A                    const eventsContainer = cell.querySelector('.day-events');%0D%0A                    %0D%0A                    // Check if we already have too many events%0D%0A                    if (eventsContainer.children.length %3E= 3) %7B%0D%0A                        // Check if we already have a %22more%22 indicator%0D%0A                        if (!eventsContainer.querySelector('.more-events')) %7B%0D%0A                            const moreIndicator = document.createElement('div');%0D%0A                            moreIndicator.className = 'more-events';%0D%0A                            moreIndicator.textContent = '+ more';%0D%0A                            eventsContainer.appendChild(moreIndicator);%0D%0A                        %7D%0D%0A                        continue;%0D%0A                    %7D%0D%0A                    %0D%0A                    // Create event element%0D%0A                    const eventEl = document.createElement('div');%0D%0A                    eventEl.className = 'calendar-event';%0D%0A                    eventEl.dataset.eventId = event.id;%0D%0A                    %0D%0A                    // Set event color%0D%0A                    const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A                    eventEl.style.backgroundColor = color;%0D%0A                    eventEl.style.borderColor = color;%0D%0A                    %0D%0A                    // Set event title%0D%0A                    eventEl.textContent = event.title;%0D%0A                    %0D%0A                    // Add to container%0D%0A                    eventsContainer.appendChild(eventEl);%0D%0A                %7D%0D%0A            %7D%0D%0A        %7D);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render week view%0D%0A     */%0D%0A    renderWeekView() %7B%0D%0A        // Clear grid%0D%0A        this.calendarGrid.innerHTML = '';%0D%0A        this.eventContainer.innerHTML = '';%0D%0A        %0D%0A        // Set grid template%0D%0A        this.calendarGrid.className = 'calendar-grid week-view';%0D%0A        %0D%0A        // Get week start and end%0D%0A        const weekStart = this.getWeekStart(this.currentDate);%0D%0A        const weekEnd = new Date(weekStart.getTime() + 6 * 24 * 60 * 60 * 1000);%0D%0A        %0D%0A        // Create time column%0D%0A        const timeColumn = document.createElement('div');%0D%0A        timeColumn.className = 'time-column';%0D%0A        this.calendarGrid.appendChild(timeColumn);%0D%0A        %0D%0A        // Create time slots%0D%0A        const startHour = parseInt(this.options.minTime.split(':')%5B0%5D);%0D%0A        const endHour = parseInt(this.options.maxTime.split(':')%5B0%5D);%0D%0A        %0D%0A        for (let hour = startHour; hour %3C endHour; hour++) %7B%0D%0A            const timeSlot = document.createElement('div');%0D%0A            timeSlot.className = 'time-slot';%0D%0A            %0D%0A            // Format hour based on time format%0D%0A            let displayHour = hour;%0D%0A            let ampm = '';%0D%0A            %0D%0A            if (this.options.timeFormat === '12') %7B%0D%0A                ampm = hour %3E= 12 ? 'PM' : 'AM';%0D%0A                displayHour = hour %25 12 %7C%7C 12;%0D%0A            %7D%0D%0A            %0D%0A            timeSlot.textContent = %60$%7BdisplayHour%7D$%7Bampm ? ' ' + ampm : ''%7D%60;%0D%0A            timeColumn.appendChild(timeSlot);%0D%0A        %7D%0D%0A        %0D%0A        // Create day columns%0D%0A        const days = %5B'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'%5D;%0D%0A        const today = new Date();%0D%0A        today.setHours(0, 0, 0, 0);%0D%0A        %0D%0A        for (let i = 0; i %3C 7; i++) %7B%0D%0A            const day = new Date(weekStart.getTime() + i * 24 * 60 * 60 * 1000);%0D%0A            const dayColumn = document.createElement('div');%0D%0A            dayColumn.className = 'day-column';%0D%0A            dayColumn.dataset.date = day.toISOString().split('T')%5B0%5D;%0D%0A            %0D%0A            // Check if today%0D%0A            if (day.getDate() === today.getDate() &amp;&amp; %0D%0A                day.getMonth() === today.getMonth() &amp;&amp; %0D%0A                day.getFullYear() === today.getFullYear()) %7B%0D%0A                dayColumn.classList.add('today');%0D%0A            %7D%0D%0A            %0D%0A            // Check if weekend%0D%0A            if (day.getDay() === 0 %7C%7C day.getDay() === 6) %7B%0D%0A                dayColumn.classList.add('weekend');%0D%0A            %7D%0D%0A            %0D%0A            // Add day header%0D%0A            const dayHeader = document.createElement('div');%0D%0A            dayHeader.className = 'day-header';%0D%0A            %0D%0A            const dayName = document.createElement('div');%0D%0A            dayName.className = 'day-name';%0D%0A            dayName.textContent = days%5Bday.getDay()%5D;%0D%0A            %0D%0A            const dayNumber = document.createElement('div');%0D%0A            dayNumber.className = 'day-number';%0D%0A            dayNumber.textContent = day.getDate();%0D%0A            %0D%0A            dayHeader.appendChild(dayName);%0D%0A            dayHeader.appendChild(dayNumber);%0D%0A            dayColumn.appendChild(dayHeader);%0D%0A            %0D%0A            // Add time grid%0D%0A            for (let hour = startHour; hour %3C endHour; hour++) %7B%0D%0A                const hourCell = document.createElement('div');%0D%0A                hourCell.className = 'hour-cell';%0D%0A                hourCell.dataset.hour = hour;%0D%0A                dayColumn.appendChild(hourCell);%0D%0A            %7D%0D%0A            %0D%0A            this.calendarGrid.appendChild(dayColumn);%0D%0A        %7D%0D%0A        %0D%0A        // Render events%0D%0A        this.renderWeekEvents();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render events for week view%0D%0A     */%0D%0A    renderWeekEvents() %7B%0D%0A        // Get visible date range%0D%0A        const weekStart = this.getWeekStart(this.currentDate);%0D%0A        const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);%0D%0A        %0D%0A        // Filter events for visible range%0D%0A        const visibleEvents = this.filteredEvents.filter(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            return eventStart %3C weekEnd &amp;&amp; eventEnd %3E= weekStart;%0D%0A        %7D);%0D%0A        %0D%0A        // Create all-day events container%0D%0A        const allDayContainer = document.createElement('div');%0D%0A        allDayContainer.className = 'all-day-container';%0D%0A        this.eventContainer.appendChild(allDayContainer);%0D%0A        %0D%0A        // Separate all-day events%0D%0A        const allDayEvents = visibleEvents.filter(event =%3E event.allDay);%0D%0A        const timeEvents = visibleEvents.filter(event =%3E !event.allDay);%0D%0A        %0D%0A        // Render all-day events%0D%0A        allDayEvents.forEach(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + 24 * 60 * 60 * 1000);%0D%0A            %0D%0A            // Clip to visible range%0D%0A            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));%0D%0A            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));%0D%0A            %0D%0A            // Calculate day span and offset%0D%0A            const startOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));%0D%0A            const daySpan = Math.ceil((displayEnd.getTime() - displayStart.getTime()) / (24 * 60 * 60 * 1000));%0D%0A            %0D%0A            // Create event element%0D%0A            const eventEl = document.createElement('div');%0D%0A            eventEl.className = 'calendar-event all-day-event';%0D%0A            eventEl.dataset.eventId = event.id;%0D%0A            %0D%0A            // Set event color%0D%0A            const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A            eventEl.style.backgroundColor = color;%0D%0A            eventEl.style.borderColor = color;%0D%0A            %0D%0A            // Set event position%0D%0A            eventEl.style.gridColumn = %60$%7BstartOffset + 1%7D / span $%7BdaySpan%7D%60;%0D%0A            %0D%0A            // Set event title%0D%0A            eventEl.textContent = event.title;%0D%0A            %0D%0A            // Add to container%0D%0A            allDayContainer.appendChild(eventEl);%0D%0A        %7D);%0D%0A        %0D%0A        // Render time-based events%0D%0A        timeEvents.forEach(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            // Clip to visible range%0D%0A            const displayStart = new Date(Math.max(eventStart.getTime(), weekStart.getTime()));%0D%0A            const displayEnd = new Date(Math.min(eventEnd.getTime(), weekEnd.getTime()));%0D%0A            %0D%0A            // Calculate day offset%0D%0A            const dayOffset = Math.floor((displayStart.getTime() - weekStart.getTime()) / (24 * 60 * 60 * 1000));%0D%0A            %0D%0A            // Find day column%0D%0A            const dayColumn = this.calendarGrid.querySelectorAll('.day-column')%5BdayOffset%5D;%0D%0A            %0D%0A            if (dayColumn) %7B%0D%0A                // Calculate time position%0D%0A                const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;%0D%0A                const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;%0D%0A                const duration = endHour - startHour;%0D%0A                %0D%0A                // Create event element%0D%0A                const eventEl = document.createElement('div');%0D%0A                eventEl.className = 'calendar-event time-event';%0D%0A                eventEl.dataset.eventId = event.id;%0D%0A                %0D%0A                // Set event color%0D%0A                const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A                eventEl.style.backgroundColor = color;%0D%0A                eventEl.style.borderColor = color;%0D%0A                %0D%0A                // Set event position%0D%0A                const minHour = parseInt(this.options.minTime.split(':')%5B0%5D);%0D%0A                const top = (startHour - minHour) * 60; // 60px per hour%0D%0A                const height = duration * 60;%0D%0A                %0D%0A                eventEl.style.top = %60$%7Btop%7Dpx%60;%0D%0A                eventEl.style.height = %60$%7Bheight%7Dpx%60;%0D%0A                eventEl.style.left = %60$%7BdayOffset * 14.28%7D%25%60; // 7 days = 14.28%25 per day%0D%0A                eventEl.style.width = '14.28%25';%0D%0A                %0D%0A                // Set event title%0D%0A                const eventTitle = document.createElement('div');%0D%0A                eventTitle.className = 'event-title';%0D%0A                eventTitle.textContent = event.title;%0D%0A                eventEl.appendChild(eventTitle);%0D%0A                %0D%0A                // Set event time%0D%0A                const eventTime = document.createElement('div');%0D%0A                eventTime.className = 'event-time';%0D%0A                %0D%0A                // Format time based on time format%0D%0A                let startTimeStr = this.formatTime(displayStart);%0D%0A                let endTimeStr = this.formatTime(displayEnd);%0D%0A                %0D%0A                eventTime.textContent = %60$%7BstartTimeStr%7D - $%7BendTimeStr%7D%60;%0D%0A                eventEl.appendChild(eventTime);%0D%0A                %0D%0A                // Add to container%0D%0A                this.eventContainer.appendChild(eventEl);%0D%0A            %7D%0D%0A        %7D);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render day view%0D%0A     */%0D%0A    renderDayView() %7B%0D%0A        // Clear grid%0D%0A        this.calendarGrid.innerHTML = '';%0D%0A        this.eventContainer.innerHTML = '';%0D%0A        %0D%0A        // Set grid template%0D%0A        this.calendarGrid.className = 'calendar-grid day-view';%0D%0A        %0D%0A        // Create time column%0D%0A        const timeColumn = document.createElement('div');%0D%0A        timeColumn.className = 'time-column';%0D%0A        this.calendarGrid.appendChild(timeColumn);%0D%0A        %0D%0A        // Create time slots%0D%0A        const startHour = parseInt(this.options.minTime.split(':')%5B0%5D);%0D%0A        const endHour = parseInt(this.options.maxTime.split(':')%5B0%5D);%0D%0A        %0D%0A        for (let hour = startHour; hour %3C endHour; hour++) %7B%0D%0A            const timeSlot = document.createElement('div');%0D%0A            timeSlot.className = 'time-slot';%0D%0A            %0D%0A            // Format hour based on time format%0D%0A            let displayHour = hour;%0D%0A            let ampm = '';%0D%0A            %0D%0A            if (this.options.timeFormat === '12') %7B%0D%0A                ampm = hour %3E= 12 ? 'PM' : 'AM';%0D%0A                displayHour = hour %25 12 %7C%7C 12;%0D%0A            %7D%0D%0A            %0D%0A            timeSlot.textContent = %60$%7BdisplayHour%7D$%7Bampm ? ' ' + ampm : ''%7D%60;%0D%0A            timeColumn.appendChild(timeSlot);%0D%0A        %7D%0D%0A        %0D%0A        // Create day column%0D%0A        const dayColumn = document.createElement('div');%0D%0A        dayColumn.className = 'day-column full-width';%0D%0A        dayColumn.dataset.date = this.currentDate.toISOString().split('T')%5B0%5D;%0D%0A        %0D%0A        // Check if today%0D%0A        const today = new Date();%0D%0A        if (this.currentDate.getDate() === today.getDate() &amp;&amp; %0D%0A            this.currentDate.getMonth() === today.getMonth() &amp;&amp; %0D%0A            this.currentDate.getFullYear() === today.getFullYear()) %7B%0D%0A            dayColumn.classList.add('today');%0D%0A        %7D%0D%0A        %0D%0A        // Check if weekend%0D%0A        if (this.currentDate.getDay() === 0 %7C%7C this.currentDate.getDay() === 6) %7B%0D%0A            dayColumn.classList.add('weekend');%0D%0A        %7D%0D%0A        %0D%0A        // Add time grid%0D%0A        for (let hour = startHour; hour %3C endHour; hour++) %7B%0D%0A            const hourCell = document.createElement('div');%0D%0A            hourCell.className = 'hour-cell';%0D%0A            hourCell.dataset.hour = hour;%0D%0A            dayColumn.appendChild(hourCell);%0D%0A        %7D%0D%0A        %0D%0A        this.calendarGrid.appendChild(dayColumn);%0D%0A        %0D%0A        // Render events%0D%0A        this.renderDayEvents();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render events for day view%0D%0A     */%0D%0A    renderDayEvents() %7B%0D%0A        // Get visible date range%0D%0A        const dayStart = new Date(this.currentDate);%0D%0A        dayStart.setHours(0, 0, 0, 0);%0D%0A        const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);%0D%0A        %0D%0A        // Filter events for visible range%0D%0A        const visibleEvents = this.filteredEvents.filter(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            return eventStart %3C dayEnd &amp;&amp; eventEnd %3E= dayStart;%0D%0A        %7D);%0D%0A        %0D%0A        // Create all-day events container%0D%0A        const allDayContainer = document.createElement('div');%0D%0A        allDayContainer.className = 'all-day-container';%0D%0A        this.eventContainer.appendChild(allDayContainer);%0D%0A        %0D%0A        // Separate all-day events%0D%0A        const allDayEvents = visibleEvents.filter(event =%3E event.allDay);%0D%0A        const timeEvents = visibleEvents.filter(event =%3E !event.allDay);%0D%0A        %0D%0A        // Render all-day events%0D%0A        allDayEvents.forEach(event =%3E %7B%0D%0A            // Create event element%0D%0A            const eventEl = document.createElement('div');%0D%0A            eventEl.className = 'calendar-event all-day-event';%0D%0A            eventEl.dataset.eventId = event.id;%0D%0A            %0D%0A            // Set event color%0D%0A            const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A            eventEl.style.backgroundColor = color;%0D%0A            eventEl.style.borderColor = color;%0D%0A            %0D%0A            // Set event title%0D%0A            eventEl.textContent = event.title;%0D%0A            %0D%0A            // Add to container%0D%0A            allDayContainer.appendChild(eventEl);%0D%0A        %7D);%0D%0A        %0D%0A        // Render time-based events%0D%0A        timeEvents.forEach(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            // Clip to visible range%0D%0A            const displayStart = new Date(Math.max(eventStart.getTime(), dayStart.getTime()));%0D%0A            const displayEnd = new Date(Math.min(eventEnd.getTime(), dayEnd.getTime()));%0D%0A            %0D%0A            // Calculate time position%0D%0A            const startHour = displayStart.getHours() + displayStart.getMinutes() / 60;%0D%0A            const endHour = displayEnd.getHours() + displayEnd.getMinutes() / 60;%0D%0A            const duration = endHour - startHour;%0D%0A            %0D%0A            // Create event element%0D%0A            const eventEl = document.createElement('div');%0D%0A            eventEl.className = 'calendar-event time-event';%0D%0A            eventEl.dataset.eventId = event.id;%0D%0A            %0D%0A            // Set event color%0D%0A            const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A            eventEl.style.backgroundColor = color;%0D%0A            eventEl.style.borderColor = color;%0D%0A            %0D%0A            // Set event position%0D%0A            const minHour = parseInt(this.options.minTime.split(':')%5B0%5D);%0D%0A            const top = (startHour - minHour) * 60; // 60px per hour%0D%0A            const height = duration * 60;%0D%0A            %0D%0A            eventEl.style.top = %60$%7Btop%7Dpx%60;%0D%0A            eventEl.style.height = %60$%7Bheight%7Dpx%60;%0D%0A            eventEl.style.width = '100%25';%0D%0A            %0D%0A            // Set event title%0D%0A            const eventTitle = document.createElement('div');%0D%0A            eventTitle.className = 'event-title';%0D%0A            eventTitle.textContent = event.title;%0D%0A            eventEl.appendChild(eventTitle);%0D%0A            %0D%0A            // Set event time%0D%0A            const eventTime = document.createElement('div');%0D%0A            eventTime.className = 'event-time';%0D%0A            %0D%0A            // Format time based on time format%0D%0A            let startTimeStr = this.formatTime(displayStart);%0D%0A            let endTimeStr = this.formatTime(displayEnd);%0D%0A            %0D%0A            eventTime.textContent = %60$%7BstartTimeStr%7D - $%7BendTimeStr%7D%60;%0D%0A            eventEl.appendChild(eventTime);%0D%0A            %0D%0A            // Add to container%0D%0A            this.eventContainer.appendChild(eventEl);%0D%0A        %7D);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Render list view%0D%0A     */%0D%0A    renderListView() %7B%0D%0A        // Clear list%0D%0A        this.eventList.innerHTML = '';%0D%0A        %0D%0A        // Get visible date range%0D%0A        const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);%0D%0A        const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);%0D%0A        %0D%0A        // Filter events for visible range%0D%0A        const visibleEvents = this.filteredEvents.filter(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A            %0D%0A            return eventStart %3C= monthEnd &amp;&amp; eventEnd %3E= monthStart;%0D%0A        %7D);%0D%0A        %0D%0A        // Sort events by start date%0D%0A        visibleEvents.sort((a, b) =%3E %7B%0D%0A            return new Date(a.start).getTime() - new Date(b.start).getTime();%0D%0A        %7D);%0D%0A        %0D%0A        // Group events by day%0D%0A        const eventsByDay = %7B%7D;%0D%0A        %0D%0A        visibleEvents.forEach(event =%3E %7B%0D%0A            const eventStart = new Date(event.start);%0D%0A            const dateStr = eventStart.toISOString().split('T')%5B0%5D;%0D%0A            %0D%0A            if (!eventsByDay%5BdateStr%5D) %7B%0D%0A                eventsByDay%5BdateStr%5D = %5B%5D;%0D%0A            %7D%0D%0A            %0D%0A            eventsByDay%5BdateStr%5D.push(event);%0D%0A        %7D);%0D%0A        %0D%0A        // Render events by day%0D%0A        if (Object.keys(eventsByDay).length === 0) %7B%0D%0A            const noEvents = document.createElement('div');%0D%0A            noEvents.className = 'no-events';%0D%0A            noEvents.textContent = 'No events to display';%0D%0A            this.eventList.appendChild(noEvents);%0D%0A        %7D else %7B%0D%0A            for (const dateStr in eventsByDay) %7B%0D%0A                const date = new Date(dateStr);%0D%0A                %0D%0A                // Create day header%0D%0A                const dayHeader = document.createElement('div');%0D%0A                dayHeader.className = 'list-day-header';%0D%0A                %0D%0A                // Format date%0D%0A                const dayNames = %5B'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'%5D;%0D%0A                const monthNames = %5B'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'%5D;%0D%0A                %0D%0A                dayHeader.textContent = %60$%7BdayNames%5Bdate.getDay()%5D%7D, $%7BmonthNames%5Bdate.getMonth()%5D%7D $%7Bdate.getDate()%7D%60;%0D%0A                this.eventList.appendChild(dayHeader);%0D%0A                %0D%0A                // Create events for this day%0D%0A                eventsByDay%5BdateStr%5D.forEach(event =%3E %7B%0D%0A                    const eventEl = document.createElement('div');%0D%0A                    eventEl.className = 'list-event';%0D%0A                    eventEl.dataset.eventId = event.id;%0D%0A                    %0D%0A                    // Set event color%0D%0A                    const color = event.color %7C%7C event.backgroundColor %7C%7C event.extendedProps?.calendar_color %7C%7C this.options.defaultCalendarColor;%0D%0A                    %0D%0A                    // Create color dot%0D%0A                    const colorDot = document.createElement('div');%0D%0A                    colorDot.className = 'event-color-dot';%0D%0A                    colorDot.style.backgroundColor = color;%0D%0A                    eventEl.appendChild(colorDot);%0D%0A                    %0D%0A                    // Create event content%0D%0A                    const eventContent = document.createElement('div');%0D%0A                    eventContent.className = 'event-content';%0D%0A                    %0D%0A                    // Create event title%0D%0A                    const eventTitle = document.createElement('div');%0D%0A                    eventTitle.className = 'event-title';%0D%0A                    eventTitle.textContent = event.title;%0D%0A                    eventContent.appendChild(eventTitle);%0D%0A                    %0D%0A                    // Create event time%0D%0A                    const eventTime = document.createElement('div');%0D%0A                    eventTime.className = 'event-time';%0D%0A                    %0D%0A                    const eventStart = new Date(event.start);%0D%0A                    const eventEnd = event.end ? new Date(event.end) : new Date(eventStart.getTime() + this.options.defaultEventDuration * 60 * 1000);%0D%0A                    %0D%0A                    if (event.allDay) %7B%0D%0A                        eventTime.textContent = 'All day';%0D%0A                    %7D else %7B%0D%0A                        // Format time based on time format%0D%0A                        let startTimeStr = this.formatTime(eventStart);%0D%0A                        let endTimeStr = this.formatTime(eventEnd);%0D%0A                        %0D%0A                        eventTime.textContent = %60$%7BstartTimeStr%7D - $%7BendTimeStr%7D%60;%0D%0A                    %7D%0D%0A                    %0D%0A                    eventContent.appendChild(eventTime);%0D%0A                    %0D%0A                    // Create event location if available%0D%0A                    if (event.extendedProps &amp;&amp; event.extendedProps.location) %7B%0D%0A                        const eventLocation = document.createElement('div');%0D%0A                        eventLocation.className = 'event-location';%0D%0A                        eventLocation.textContent = event.extendedProps.location;%0D%0A                        eventContent.appendChild(eventLocation);%0D%0A                    %7D%0D%0A                    %0D%0A                    // Create event calendar if available%0D%0A                    if (event.extendedProps &amp;&amp; event.extendedProps.calendar_name) %7B%0D%0A                        const eventCalendar = document.createElement('div');%0D%0A                        eventCalendar.className = 'event-calendar';%0D%0A                        eventCalendar.textContent = event.extendedProps.calendar_name;%0D%0A                        eventContent.appendChild(eventCalendar);%0D%0A                    %7D%0D%0A                    %0D%0A                    eventEl.appendChild(eventContent);%0D%0A                    this.eventList.appendChild(eventEl);%0D%0A                %7D);%0D%0A            %7D%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Get the start of the week containing the given date%0D%0A     * %0D%0A     * @param %7BDate%7D date - The date%0D%0A     * @return %7BDate%7D The start of the week%0D%0A     */%0D%0A    getWeekStart(date) %7B%0D%0A        const day = date.getDay();%0D%0A        const diff = (day - this.options.weekStartsOn + 7) %25 7;%0D%0A        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - diff);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Format time based on time format setting%0D%0A     * %0D%0A     * @param %7BDate%7D date - The date to format%0D%0A     * @return %7Bstring%7D Formatted time string%0D%0A     */%0D%0A    formatTime(date) %7B%0D%0A        if (this.options.timeFormat === '12') %7B%0D%0A            const hours = date.getHours();%0D%0A            const minutes = date.getMinutes();%0D%0A            const ampm = hours %3E= 12 ? 'PM' : 'AM';%0D%0A            const displayHours = hours %25 12 %7C%7C 12;%0D%0A            %0D%0A            return %60$%7BdisplayHours%7D:$%7Bminutes.toString().padStart(2, '0')%7D $%7Bampm%7D%60;%0D%0A        %7D else %7B%0D%0A            return %60$%7Bdate.getHours().toString().padStart(2, '0')%7D:$%7Bdate.getMinutes().toString().padStart(2, '0')%7D%60;%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Fetch events from all sources%0D%0A     */%0D%0A    fetchEvents() %7B%0D%0A        // Show loading indicator%0D%0A        this.showLoading(true);%0D%0A        %0D%0A        // Call loading callback%0D%0A        if (this.options.loading) %7B%0D%0A            this.options.loading(true);%0D%0A        %7D%0D%0A        %0D%0A        // Clear events%0D%0A        this.events = %5B%5D;%0D%0A        %0D%0A        // Create promises for all event sources%0D%0A        const promises = this.options.eventSources.map(source =%3E %7B%0D%0A            if (typeof source === 'string') %7B%0D%0A                // URL source%0D%0A                return fetch(source)%0D%0A                    .then(response =%3E response.json())%0D%0A                    .then(events =%3E events);%0D%0A            %7D else if (typeof source === 'function') %7B%0D%0A                // Function source%0D%0A                return Promise.resolve(source());%0D%0A            %7D else if (Array.isArray(source)) %7B%0D%0A                // Array source%0D%0A                return Promise.resolve(source);%0D%0A            %7D else if (typeof source === 'object' &amp;&amp; source.url) %7B%0D%0A                // Object source with URL%0D%0A                const params = new URLSearchParams();%0D%0A                %0D%0A                // Add start and end parameters%0D%0A                const rangeStart = this.getRangeStart();%0D%0A                const rangeEnd = this.getRangeEnd();%0D%0A                %0D%0A                params.append('start', rangeStart.toISOString());%0D%0A                params.append('end', rangeEnd.toISOString());%0D%0A                %0D%0A                // Add extra parameters%0D%0A                if (source.extraParams) %7B%0D%0A                    for (const key in source.extraParams) %7B%0D%0A                        params.append(key, source.extraParams%5Bkey%5D);%0D%0A                    %7D%0D%0A                %7D%0D%0A                %0D%0A                // Build URL%0D%0A                const url = %60$%7Bsource.url%7D$%7Bsource.url.includes('?') ? '&amp;' : '?'%7D$%7Bparams.toString()%7D%60;%0D%0A                %0D%0A                // Fetch events%0D%0A                return fetch(url)%0D%0A                    .then(response =%3E response.json())%0D%0A                    .then(events =%3E events);%0D%0A            %7D%0D%0A            %0D%0A            return Promise.resolve(%5B%5D);%0D%0A        %7D);%0D%0A        %0D%0A        // Wait for all promises to resolve%0D%0A        Promise.all(promises)%0D%0A            .then(results =%3E %7B%0D%0A                // Flatten results%0D%0A                this.events = results.flat();%0D%0A                %0D%0A                // Filter events%0D%0A                this.filterEvents();%0D%0A                %0D%0A                // Render calendar%0D%0A                this.render();%0D%0A                %0D%0A                // Hide loading indicator%0D%0A                this.showLoading(false);%0D%0A                %0D%0A                // Call loading callback%0D%0A                if (this.options.loading) %7B%0D%0A                    this.options.loading(false);%0D%0A                %7D%0D%0A            %7D)%0D%0A            .catch(error =%3E %7B%0D%0A                console.error('Error fetching events:', error);%0D%0A                %0D%0A                // Hide loading indicator%0D%0A                this.showLoading(false);%0D%0A                %0D%0A                // Call loading callback%0D%0A                if (this.options.loading) %7B%0D%0A                    this.options.loading(false);%0D%0A                %7D%0D%0A            %7D);%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Filter events based on selected calendars%0D%0A     */%0D%0A    filterEvents() %7B%0D%0A        if (this.selectedCalendars.length === 0) %7B%0D%0A            this.filteredEvents = this.events;%0D%0A        %7D else %7B%0D%0A            this.filteredEvents = this.events.filter(event =%3E %7B%0D%0A                const calendarId = event.extendedProps?.calendar_id;%0D%0A                return this.selectedCalendars.includes(calendarId);%0D%0A            %7D);%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Get the start of the visible range%0D%0A     * %0D%0A     * @return %7BDate%7D The start of the visible range%0D%0A     */%0D%0A    getRangeStart() %7B%0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);%0D%0A                const startOffset = monthStart.getDay() - this.options.weekStartsOn;%0D%0A                return new Date(monthStart.getFullYear(), monthStart.getMonth(), 1 - (startOffset %3C 0 ? startOffset + 7 : startOffset));%0D%0A            %0D%0A            case 'week':%0D%0A                return this.getWeekStart(this.currentDate);%0D%0A            %0D%0A            case 'day':%0D%0A                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate());%0D%0A            %0D%0A            case 'list':%0D%0A                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Get the end of the visible range%0D%0A     * %0D%0A     * @return %7BDate%7D The end of the visible range%0D%0A     */%0D%0A    getRangeEnd() %7B%0D%0A        switch (this.currentView) %7B%0D%0A            case 'month':%0D%0A                const monthStart = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);%0D%0A                const monthEnd = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);%0D%0A                const startOffset = monthStart.getDay() - this.options.weekStartsOn;%0D%0A                const endOffset = 6 - (monthEnd.getDay() - this.options.weekStartsOn);%0D%0A                return new Date(monthEnd.getFullYear(), monthEnd.getMonth(), monthEnd.getDate() + (endOffset %3C 0 ? endOffset + 7 : endOffset));%0D%0A            %0D%0A            case 'week':%0D%0A                const weekStart = this.getWeekStart(this.currentDate);%0D%0A                return new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate() + 7);%0D%0A            %0D%0A            case 'day':%0D%0A                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), this.currentDate.getDate() + 1);%0D%0A            %0D%0A            case 'list':%0D%0A                return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 0);%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Show or hide loading indicator%0D%0A     * %0D%0A     * @param %7Bboolean%7D show - Whether to show or hide the loading indicator%0D%0A     */%0D%0A    showLoading(show) %7B%0D%0A        this.loadingIndicator.style.display = show ? 'flex' : 'none';%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Set selected calendars%0D%0A     * %0D%0A     * @param %7BArray%7D calendarIds - Array of calendar IDs%0D%0A     */%0D%0A    setSelectedCalendars(calendarIds) %7B%0D%0A        this.selectedCalendars = calendarIds;%0D%0A        this.filterEvents();%0D%0A        this.render();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Add event source%0D%0A     * %0D%0A     * @param %7Bstring%7CObject%7CArray%7CFunction%7D source - Event source%0D%0A     */%0D%0A    addEventSource(source) %7B%0D%0A        this.options.eventSources.push(source);%0D%0A        this.fetchEvents();%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Remove event source%0D%0A     * %0D%0A     * @param %7Bstring%7CObject%7CArray%7CFunction%7D source - Event source to remove%0D%0A     */%0D%0A    removeEventSource(source) %7B%0D%0A        const index = this.options.eventSources.indexOf(source);%0D%0A        if (index !== -1) %7B%0D%0A            this.options.eventSources.splice(index, 1);%0D%0A            this.fetchEvents();%0D%0A        %7D%0D%0A    %7D%0D%0A%0D%0A    /**%0D%0A     * Refetch events%0D%0A     */%0D%0A    refetchEvents() %7B%0D%0A        this.fetchEvents();%0D%0A    %7D%0D%0A%7D\n&quot;,&quot;isNew&quot;:true,&quot;isApplied&quot;:true,&quot;isUnread&quot;:true},{&quot;path&quot;:&quot;libraries/css/custom-calendar.css&quot;,&quot;patch&quot;:&quot;@@ -0,0 +1,11879 @@\n+/**%0D%0A * Custom Calendar Styles%0D%0A * %0D%0A * Styles for the custom calendar implementation.%0D%0A * %0D%0A * Version: 1.0.0%0D%0A */%0D%0A%0D%0A/* Calendar Container */%0D%0A.custom-calendar %7B%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    width: 100%25;%0D%0A    height: 100%25;%0D%0A    min-height: 600px;%0D%0A    background-color: #fff;%0D%0A    border-radius: 8px;%0D%0A    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);%0D%0A    overflow: hidden;%0D%0A    position: relative;%0D%0A    font-family: -apple-system, BlinkMacSystemFont, %22Segoe UI%22, Roboto, Helvetica, Arial, sans-serif;%0D%0A%7D%0D%0A%0D%0A/* Calendar Header */%0D%0A.calendar-header %7B%0D%0A    display: flex;%0D%0A    justify-content: space-between;%0D%0A    align-items: center;%0D%0A    padding: 15px 20px;%0D%0A    background-color: #f8f9fa;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.calendar-title %7B%0D%0A    font-size: 1.5rem;%0D%0A    font-weight: 600;%0D%0A    color: #343a40;%0D%0A    margin: 0;%0D%0A%7D%0D%0A%0D%0A/* Calendar Grid - Month View */%0D%0A.calendar-grid.month-view %7B%0D%0A    display: grid;%0D%0A    grid-template-columns: repeat(7, 1fr);%0D%0A    grid-auto-rows: minmax(100px, 1fr);%0D%0A    flex: 1;%0D%0A    border-top: 1px solid #e9ecef;%0D%0A    border-left: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.calendar-day-header %7B%0D%0A    padding: 10px;%0D%0A    text-align: center;%0D%0A    font-weight: 600;%0D%0A    color: #495057;%0D%0A    background-color: #f8f9fa;%0D%0A    border-right: 1px solid #e9ecef;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.calendar-day %7B%0D%0A    padding: 5px;%0D%0A    border-right: 1px solid #e9ecef;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A    overflow: hidden;%0D%0A    position: relative;%0D%0A%7D%0D%0A%0D%0A.calendar-day.prev-month,%0D%0A.calendar-day.next-month %7B%0D%0A    background-color: #f8f9fa;%0D%0A    color: #adb5bd;%0D%0A%7D%0D%0A%0D%0A.calendar-day.today %7B%0D%0A    background-color: #fff8e6;%0D%0A%7D%0D%0A%0D%0A.calendar-day.weekend %7B%0D%0A    background-color: #f8f9fa;%0D%0A%7D%0D%0A%0D%0A.day-number %7B%0D%0A    font-size: 0.9rem;%0D%0A    font-weight: 600;%0D%0A    color: #495057;%0D%0A    margin-bottom: 5px;%0D%0A%7D%0D%0A%0D%0A.calendar-day.today .day-number %7B%0D%0A    display: inline-block;%0D%0A    width: 24px;%0D%0A    height: 24px;%0D%0A    line-height: 24px;%0D%0A    text-align: center;%0D%0A    background-color: #007bff;%0D%0A    color: #fff;%0D%0A    border-radius: 50%25;%0D%0A%7D%0D%0A%0D%0A.day-events %7B%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    gap: 2px;%0D%0A%7D%0D%0A%0D%0A/* Calendar Grid - Week View */%0D%0A.calendar-grid.week-view %7B%0D%0A    display: grid;%0D%0A    grid-template-columns: 60px repeat(7, 1fr);%0D%0A    flex: 1;%0D%0A    border-top: 1px solid #e9ecef;%0D%0A    border-left: 1px solid #e9ecef;%0D%0A    overflow-y: auto;%0D%0A%7D%0D%0A%0D%0A.time-column %7B%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    border-right: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.time-slot %7B%0D%0A    height: 60px;%0D%0A    padding: 5px;%0D%0A    text-align: right;%0D%0A    font-size: 0.8rem;%0D%0A    color: #6c757d;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.day-column %7B%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    border-right: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.day-column.full-width %7B%0D%0A    grid-column: 2 / span 7;%0D%0A%7D%0D%0A%0D%0A.day-header %7B%0D%0A    padding: 10px;%0D%0A    text-align: center;%0D%0A    background-color: #f8f9fa;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A%7D%0D%0A%0D%0A.day-name %7B%0D%0A    font-weight: 600;%0D%0A    color: #495057;%0D%0A%7D%0D%0A%0D%0A.hour-cell %7B%0D%0A    height: 60px;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A    position: relative;%0D%0A%7D%0D%0A%0D%0A.day-column.today %7B%0D%0A    background-color: #fff8e6;%0D%0A%7D%0D%0A%0D%0A.day-column.weekend %7B%0D%0A    background-color: #f8f9fa;%0D%0A%7D%0D%0A%0D%0A/* Calendar Events Container */%0D%0A.calendar-events %7B%0D%0A    position: relative;%0D%0A    margin-top: 10px;%0D%0A%7D%0D%0A%0D%0A.all-day-container %7B%0D%0A    display: grid;%0D%0A    grid-template-columns: repeat(7, 1fr);%0D%0A    min-height: 30px;%0D%0A    margin-bottom: 10px;%0D%0A    border: 1px solid #e9ecef;%0D%0A    border-radius: 4px;%0D%0A    background-color: #f8f9fa;%0D%0A    padding: 5px;%0D%0A%7D%0D%0A%0D%0A/* Calendar Event List */%0D%0A.calendar-event-list %7B%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    flex: 1;%0D%0A    overflow-y: auto;%0D%0A%7D%0D%0A%0D%0A.list-day-header %7B%0D%0A    padding: 10px 15px;%0D%0A    font-weight: 600;%0D%0A    color: #495057;%0D%0A    background-color: #f8f9fa;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A    position: sticky;%0D%0A    top: 0;%0D%0A    z-index: 1;%0D%0A%7D%0D%0A%0D%0A.list-event %7B%0D%0A    display: flex;%0D%0A    align-items: center;%0D%0A    padding: 10px 15px;%0D%0A    border-bottom: 1px solid #e9ecef;%0D%0A    cursor: pointer;%0D%0A    transition: background-color 0.2s;%0D%0A%7D%0D%0A%0D%0A.list-event:hover %7B%0D%0A    background-color: #f8f9fa;%0D%0A%7D%0D%0A%0D%0A.event-color-dot %7B%0D%0A    width: 12px;%0D%0A    height: 12px;%0D%0A    border-radius: 50%25;%0D%0A    margin-right: 10px;%0D%0A    flex-shrink: 0;%0D%0A%7D%0D%0A%0D%0A.event-content %7B%0D%0A    flex: 1;%0D%0A%7D%0D%0A%0D%0A.no-events %7B%0D%0A    padding: 20px;%0D%0A    text-align: center;%0D%0A    color: #6c757d;%0D%0A%7D%0D%0A%0D%0A/* Calendar Events */%0D%0A.calendar-event %7B%0D%0A    padding: 2px 4px;%0D%0A    font-size: 0.8rem;%0D%0A    color: #fff;%0D%0A    background-color: #007bff;%0D%0A    border-radius: 3px;%0D%0A    overflow: hidden;%0D%0A    text-overflow: ellipsis;%0D%0A    white-space: nowrap;%0D%0A    cursor: pointer;%0D%0A    margin-bottom: 1px;%0D%0A    border-left: 3px solid rgba(0, 0, 0, 0.2);%0D%0A%7D%0D%0A%0D%0A.calendar-event.time-event %7B%0D%0A    position: absolute;%0D%0A    left: 0;%0D%0A    width: 100%25;%0D%0A    z-index: 1;%0D%0A    padding: 4px 6px;%0D%0A    border-radius: 4px;%0D%0A    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);%0D%0A%7D%0D%0A%0D%0A.calendar-event.all-day-event %7B%0D%0A    height: 24px;%0D%0A    display: flex;%0D%0A    align-items: center;%0D%0A%7D%0D%0A%0D%0A.calendar-event .event-title %7B%0D%0A    font-weight: 600;%0D%0A    margin-bottom: 2px;%0D%0A%7D%0D%0A%0D%0A.calendar-event .event-time %7B%0D%0A    font-size: 0.75rem;%0D%0A    opacity: 0.9;%0D%0A%7D%0D%0A%0D%0A.more-events %7B%0D%0A    font-size: 0.75rem;%0D%0A    color: #6c757d;%0D%0A    text-align: center;%0D%0A    cursor: pointer;%0D%0A%7D%0D%0A%0D%0A/* Event Details */%0D%0A.event-location,%0D%0A.event-calendar %7B%0D%0A    font-size: 0.8rem;%0D%0A    color: #6c757d;%0D%0A    margin-top: 2px;%0D%0A%7D%0D%0A%0D%0A/* Loading Indicator */%0D%0A.calendar-loading %7B%0D%0A    position: absolute;%0D%0A    top: 0;%0D%0A    left: 0;%0D%0A    width: 100%25;%0D%0A    height: 100%25;%0D%0A    background-color: rgba(255, 255, 255, 0.8);%0D%0A    display: flex;%0D%0A    flex-direction: column;%0D%0A    justify-content: center;%0D%0A    align-items: center;%0D%0A    z-index: 10;%0D%0A%7D%0D%0A%0D%0A.calendar-loading .spinner %7B%0D%0A    width: 40px;%0D%0A    height: 40px;%0D%0A    border: 4px solid #f3f3f3;%0D%0A    border-top: 4px solid #007bff;%0D%0A    border-radius: 50%25;%0D%0A    animation: spin 1s linear infinite;%0D%0A    margin-bottom: 10px;%0D%0A%7D%0D%0A%0D%0A@keyframes spin %7B%0D%0A    0%25 %7B transform: rotate(0deg); %7D%0D%0A    100%25 %7B transform: rotate(360deg); %7D%0D%0A%7D%0D%0A%0D%0A/* Dragging */%0D%0A.calendar-event.dragging %7B%0D%0A    opacity: 0.7;%0D%0A    z-index: 1000;%0D%0A%7D%0D%0A%0D%0A/* Responsive Styles */%0D%0A@media (max-width: 768px) %7B%0D%0A    .calendar-title %7B%0D%0A        font-size: 1.2rem;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-grid.month-view %7B%0D%0A        grid-auto-rows: minmax(60px, 1fr);%0D%0A    %7D%0D%0A    %0D%0A    .day-number %7B%0D%0A        font-size: 0.8rem;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-event %7B%0D%0A        font-size: 0.7rem;%0D%0A    %7D%0D%0A    %0D%0A    .time-slot %7B%0D%0A        font-size: 0.7rem;%0D%0A        height: 50px;%0D%0A    %7D%0D%0A    %0D%0A    .hour-cell %7B%0D%0A        height: 50px;%0D%0A    %7D%0D%0A%7D%0D%0A%0D%0A/* Dark Mode Support */%0D%0A@media (prefers-color-scheme: dark) %7B%0D%0A    .custom-calendar %7B%0D%0A        background-color: #212529;%0D%0A        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);%0D%0A    %7D%0D%0A    %0D%0A    .calendar-header %7B%0D%0A        background-color: #343a40;%0D%0A        border-bottom-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-title %7B%0D%0A        color: #f8f9fa;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day-header %7B%0D%0A        background-color: #343a40;%0D%0A        color: #e9ecef;%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day %7B%0D%0A        border-color: #495057;%0D%0A        background-color: #212529;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day.prev-month,%0D%0A    .calendar-day.next-month %7B%0D%0A        background-color: #2c3034;%0D%0A        color: #6c757d;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day.today %7B%0D%0A        background-color: #2b3035;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day.weekend %7B%0D%0A        background-color: #2c3034;%0D%0A    %7D%0D%0A    %0D%0A    .day-number %7B%0D%0A        color: #e9ecef;%0D%0A    %7D%0D%0A    %0D%0A    .time-slot %7B%0D%0A        color: #adb5bd;%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .day-header %7B%0D%0A        background-color: #343a40;%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .day-name %7B%0D%0A        color: #e9ecef;%0D%0A    %7D%0D%0A    %0D%0A    .hour-cell %7B%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .all-day-container %7B%0D%0A        background-color: #343a40;%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .list-day-header %7B%0D%0A        background-color: #343a40;%0D%0A        color: #e9ecef;%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .list-event %7B%0D%0A        border-color: #495057;%0D%0A    %7D%0D%0A    %0D%0A    .list-event:hover %7B%0D%0A        background-color: #343a40;%0D%0A    %7D%0D%0A    %0D%0A    .no-events %7B%0D%0A        color: #adb5bd;%0D%0A    %7D%0D%0A    %0D%0A    .more-events %7B%0D%0A        color: #adb5bd;%0D%0A    %7D%0D%0A    %0D%0A    .event-location,%0D%0A    .event-calendar %7B%0D%0A        color: #adb5bd;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-loading %7B%0D%0A        background-color: rgba(33, 37, 41, 0.8);%0D%0A    %7D%0D%0A    %0D%0A    .calendar-loading .spinner %7B%0D%0A        border-color: #343a40;%0D%0A        border-top-color: #007bff;%0D%0A    %7D%0D%0A%7D%0D%0A%0D%0A/* Additional Styles for Enhanced Visual Appeal */%0D%0A%0D%0A/* Calendar Day Hover Effect */%0D%0A.calendar-day:hover %7B%0D%0A    background-color: rgba(0, 123, 255, 0.05);%0D%0A%7D%0D%0A%0D%0A/* Event Hover Effect */%0D%0A.calendar-event:hover %7B%0D%0A    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);%0D%0A    transform: translateY(-1px);%0D%0A    transition: all 0.2s ease;%0D%0A%7D%0D%0A%0D%0A/* Today Indicator Animation */%0D%0A@keyframes pulse %7B%0D%0A    0%25 %7B box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4); %7D%0D%0A    70%25 %7B box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); %7D%0D%0A    100%25 %7B box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); %7D%0D%0A%7D%0D%0A%0D%0A.calendar-day.today .day-number %7B%0D%0A    animation: pulse 2s infinite;%0D%0A%7D%0D%0A%0D%0A/* Current Time Indicator */%0D%0A.current-time-indicator %7B%0D%0A    position: absolute;%0D%0A    left: 0;%0D%0A    width: 100%25;%0D%0A    height: 2px;%0D%0A    background-color: #dc3545;%0D%0A    z-index: 2;%0D%0A%7D%0D%0A%0D%0A.current-time-indicator::before %7B%0D%0A    content: '';%0D%0A    position: absolute;%0D%0A    left: -5px;%0D%0A    top: -4px;%0D%0A    width: 10px;%0D%0A    height: 10px;%0D%0A    background-color: #dc3545;%0D%0A    border-radius: 50%25;%0D%0A%7D%0D%0A%0D%0A/* Event Categories */%0D%0A.calendar-event.important %7B%0D%0A    background-color: #dc3545;%0D%0A%7D%0D%0A%0D%0A.calendar-event.holiday %7B%0D%0A    background-color: #28a745;%0D%0A%7D%0D%0A%0D%0A.calendar-event.meeting %7B%0D%0A    background-color: #6f42c1;%0D%0A%7D%0D%0A%0D%0A.calendar-event.personal %7B%0D%0A    background-color: #fd7e14;%0D%0A%7D%0D%0A%0D%0A/* Month Navigation Animation */%0D%0A@keyframes slideRight %7B%0D%0A    from %7B transform: translateX(-20px); opacity: 0; %7D%0D%0A    to %7B transform: translateX(0); opacity: 1; %7D%0D%0A%7D%0D%0A%0D%0A@keyframes slideLeft %7B%0D%0A    from %7B transform: translateX(20px); opacity: 0; %7D%0D%0A    to %7B transform: translateX(0); opacity: 1; %7D%0D%0A%7D%0D%0A%0D%0A.slide-right %7B%0D%0A    animation: slideRight 0.3s ease forwards;%0D%0A%7D%0D%0A%0D%0A.slide-left %7B%0D%0A    animation: slideLeft 0.3s ease forwards;%0D%0A%7D%0D%0A%0D%0A/* Event Selection */%0D%0A.calendar-event.selected %7B%0D%0A    box-shadow: 0 0 0 2px #007bff;%0D%0A%7D%0D%0A%0D%0A/* Mobile Optimizations */%0D%0A@media (max-width: 576px) %7B%0D%0A    .calendar-grid.month-view %7B%0D%0A        grid-auto-rows: minmax(50px, 1fr);%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day %7B%0D%0A        padding: 2px;%0D%0A    %7D%0D%0A    %0D%0A    .day-number %7B%0D%0A        font-size: 0.7rem;%0D%0A        margin-bottom: 2px;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-event %7B%0D%0A        padding: 1px 2px;%0D%0A        font-size: 0.65rem;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-day.today .day-number %7B%0D%0A        width: 20px;%0D%0A        height: 20px;%0D%0A        line-height: 20px;%0D%0A    %7D%0D%0A    %0D%0A    .time-column %7B%0D%0A        width: 40px;%0D%0A    %7D%0D%0A    %0D%0A    .time-slot %7B%0D%0A        padding: 2px;%0D%0A        height: 40px;%0D%0A    %7D%0D%0A    %0D%0A    .hour-cell %7B%0D%0A        height: 40px;%0D%0A    %7D%0D%0A%7D%0D%0A%0D%0A/* Print Styles */%0D%0A@media print %7B%0D%0A    .custom-calendar %7B%0D%0A        box-shadow: none;%0D%0A        height: auto;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-grid.month-view %7B%0D%0A        grid-auto-rows: 120px;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-event %7B%0D%0A        break-inside: avoid;%0D%0A        color: #000 !important;%0D%0A        border: 1px solid #000;%0D%0A        background-color: transparent !important;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-event::after %7B%0D%0A        content: attr(title);%0D%0A        color: #000;%0D%0A    %7D%0D%0A    %0D%0A    .calendar-loading %7B%0D%0A        display: none !important;%0D%0A    %7D%0D%0A%7D\n&quot;,&quot;isNew&quot;:true,&quot;isApplied&quot;:true,&quot;isUnread&quot;:true}]}],&quot;context&quot;:{&quot;author&quot;:&quot;Coding Agent&quot;,&quot;operationId&quot;:&quot;d5c586d8-c627-4c1a-a2ea-e5d454773b1e&quot;},&quot;createdAt&quot;:1750104642308,&quot;isInternal&quot;:false},{&quot;id&quot;:&quot;429fec71-b6d8-41c1-b222-6cd16877513d&quot;,&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;continue&quot;}],&quot;context&quot;:{&quot;codebaseEnabled&quot;:true},&quot;createdAt&quot;:1750106257112,&quot;isInternal&quot;:false,&quot;rawContent&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;continue&quot;}]}]}},{&quot;id&quot;:&quot;aab6e051-69c7-4a25-8ac3-25ece45a9ad8&quot;,&quot;role&quot;:&quot;assistant&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;I understanI understand that all the files we wered that all the files we were working with have been deleted. working with have been deleted. Let's start fresh by examining Let's start fresh by examining what files are available in the project what files are available in the project structure to understand the calendar implementation structure to understand the calendar implementation we need to work with. we need to work with.&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01MZNeVkcCXsWZRtDQBJqwyv&quot;,&quot;statusText&quot;:&quot;Viewed folder&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;.&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\.&quot;}]},&quot;isHidden&quot;:false}],&quot;context&quot;:{&quot;author&quot;:&quot;Coding Agent&quot;,&quot;operationId&quot;:&quot;bfaa9f9e-593c-4a84-a949-eb827e8e7127&quot;},&quot;createdAt&quot;:1750106264718,&quot;isInternal&quot;:false}],&quot;isAgent&quot;:true,&quot;isCustomAgent&quot;:false,&quot;isUnitTestsAgent&quot;:false,&quot;isE2EAgent&quot;:false,&quot;autoApply&quot;:true},&quot;16a2cc8e-10d6-494e-bf75-ec84c4ebdd82&quot;:{&quot;id&quot;:&quot;16a2cc8e-10d6-494e-bf75-ec84c4ebdd82&quot;,&quot;createdAt&quot;:1750104369326,&quot;updatedAt&quot;:1750104620275,&quot;title&quot;:&quot;the fullcalander intergration is not our own ca...&quot;,&quot;messages&quot;:[{&quot;id&quot;:&quot;6a188dad-8a4f-4287-942d-f9c1a094c346&quot;,&quot;role&quot;:&quot;user&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;structure.md – Lists the project structure with short descriptions of each folder/file and its purpose.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;features.md – Lists the main features already developed and their current status.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;task_pending.md – Lists features still to be developed. Only remove items when instructed.&quot;},{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;SYSTEM_INIT.md - outlines how to properly initialize the Events and Shows Management System in scripts and applications. We will continue developing this project, and I want you to read and analyze the *.md files initially, and then the code files that are related...  the fullcalander intergration is not our own calander we want our own custom built . i dont want a simple calendar . i dont like simple . i want advanced custom one with all the bells and whistles . just build it from scratch and make it custom . it needs to look awesum and usable on desktop as well as small view like mobile phones.&quot;}],&quot;context&quot;:{&quot;codebaseEnabled&quot;:true},&quot;createdAt&quot;:1750104576355,&quot;isInternal&quot;:false,&quot;rawContent&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;structure.md – Lists the project structure with short descriptions of each folder/file and its purpose.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;features.md – Lists the main features already developed and their current status.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;task_pending.md – Lists features still to be developed. Only remove items when instructed.&quot;}]},{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;SYSTEM_INIT.md - outlines how to properly initialize the Events and Shows Management System in scripts and applications. We will continue developing this project, and I want you to read and analyze the *.md files initially, and then the code files that are related...  the fullcalander intergration is not our own calander we want our own custom built . i dont want a simple calendar . i dont like simple . i want advanced custom one with all the bells and whistles . just build it from scratch and make it custom . it needs to look awesum and usable on desktop as well as small view like mobile phones. &quot;}]}]}},{&quot;id&quot;:&quot;fdbc929c-5188-43ff-a758-25175cfc44b2&quot;,&quot;role&quot;:&quot;assistant&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;I'll help you build a custom calendar system from scratch for your Events and Shows Management System. Let's start by analyzing the project structure and understanding the requirements better.&quot;},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_017srjWvvRLkbAtQLqxt8gTu&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;structure.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\structure.md&quot;}]},&quot;isHidden&quot;:false},{&quot;type&quot;:&quot;toolCall&quot;,&quot;toolCallId&quot;:&quot;toolu_01WGUJY4pXZtLkwfWNB55msU&quot;,&quot;statusText&quot;:&quot;Viewed file&quot;,&quot;status&quot;:&quot;SUCCESS&quot;,&quot;context&quot;:{&quot;files&quot;:[{&quot;path&quot;:&quot;features.md&quot;,&quot;fsPath&quot;:&quot;D:\\Downloads\\events and shows\\features.md&quot;}]},&quot;isHidden&quot;:false}],&quot;context&quot;:{&quot;author&quot;:&quot;Coding Agent&quot;,&quot;operationId&quot;:&quot;dec6a286-d273-4f06-82ea-3c0fdf585977&quot;},&quot;createdAt&quot;:1750104583427,&quot;isInternal&quot;:false}],&quot;isAgent&quot;:true,&quot;isCustomAgent&quot;:false,&quot;isUnitTestsAgent&quot;:false,&quot;isE2EAgent&quot;:false,&quot;autoApply&quot;:false}}" />
  </component>
</project>
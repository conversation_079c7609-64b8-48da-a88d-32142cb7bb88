<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Map</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group me-2">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-outline-primary">
                    <i class="fas fa-calendar me-2"></i> Event View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-primary active">
                    <i class="fas fa-map-marker-alt me-2"></i> Map View
                </a>
            </div>
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <?php if (isAdmin()): ?>
                <a href="<?php echo URLROOT; ?>/calendar/mapSettings" class="btn btn-outline-secondary">
                    <i class="fas fa-cog me-2"></i> Map Settings
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content Column (Full Width) -->
        <div class="col-12 mb-4">
            <!-- Advanced Filter -->
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
            
            <!-- Map Container -->
            <div class="card mb-4">
                <div class="card-body p-0">
                    <div id="map" style="height: 500px;"></div>
                </div>
            </div>

            <!-- Event List -->
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Events</h5>
                    <div class="d-flex align-items-center gap-3">
                        <span id="event-count" class="badge bg-light text-dark">0 events</span>
                        <!-- Pagination Mode Toggle -->
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="pagination-mode-toggle" checked>
                            <label class="form-check-label text-white" for="pagination-mode-toggle" style="font-size: 0.85rem;">
                                Show all pins
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Pagination Controls Top -->
                    <div id="pagination-controls-top" class="d-flex justify-content-between align-items-center mb-3" style="display: none !important;">
                        <div class="d-flex align-items-center gap-2">
                            <label for="page-size-select" class="form-label mb-0">Show:</label>
                            <select id="page-size-select" class="form-select form-select-sm" style="width: auto;">
                                <option value="10">10</option>
                                <option value="25" selected>25</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                            <span class="text-muted">per page</span>
                        </div>
                        <div id="pagination-info" class="text-muted">
                            <!-- Pagination info will be populated via JavaScript -->
                        </div>
                    </div>
                    
                    <div id="event-list" class="list-group">
                        <!-- Events will be populated via JavaScript -->
                        <div class="text-center py-5" id="no-events-message">
                            <i class="fas fa-map-marker-alt fa-3x mb-3 text-muted"></i>
                            <p class="lead text-muted">No events to display. Try adjusting your filters.</p>
                        </div>
                    </div>
                    
                    <!-- Pagination Controls Bottom -->
                    <div id="pagination-controls-bottom" class="d-flex justify-content-center mt-3" style="display: none !important;">
                        <nav aria-label="Event pagination">
                            <ul id="pagination-list" class="pagination pagination-sm mb-0">
                                <!-- Pagination buttons will be populated via JavaScript -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
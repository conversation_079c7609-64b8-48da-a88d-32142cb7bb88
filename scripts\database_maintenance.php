<?php
/**
 * Database Maintenance and Monitoring Script
 * 
 * This script performs regular database maintenance tasks and monitors
 * database health, relationships, and integrity.
 * 
 * Usage: Access via admin dashboard or direct URL (admin only)
 * Tasks: check, clean, optimize, report, all
 */

// Define APPROOT (go up one level from /scripts/ to root)
define('APPROOT', dirname(__FILE__, 2));

// Load configuration
require_once APPROOT . '/config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

class DatabaseMaintenance {
    private $db;
    private $results = [];
    
    public function __construct() {
        try {
            $this->db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->db->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage());
        }
    }
    
    public function runTask($task) {
        echo "=== Database Maintenance Tool ===\n";
        echo "Started at: " . date('Y-m-d H:i:s') . "\n\n";
        
        switch ($task) {
            case 'check':
                $this->checkIntegrity();
                break;
            case 'clean':
                $this->cleanOrphanedRecords();
                break;
            case 'optimize':
                $this->optimizeTables();
                break;
            case 'report':
                $this->generateReport();
                break;
            case 'all':
                $this->checkIntegrity();
                $this->cleanOrphanedRecords();
                $this->optimizeTables();
                $this->generateReport();
                break;
            default:
                $this->showUsage();
                return;
        }
        
        echo "\n=== Maintenance Completed ===\n";
        echo "Finished at: " . date('Y-m-d H:i:s') . "\n";
        
        // Save results to log file
        $this->saveResults();
    }
    
    private function checkIntegrity() {
        echo "--- Checking Database Integrity ---\n";
        
        // Check for orphaned vehicles
        $orphanedVehicles = $this->db->query("
            SELECT v.id, v.make, v.model, v.year, v.owner_id
            FROM vehicles v
            LEFT JOIN users u ON v.owner_id = u.id
            WHERE u.id IS NULL
        ")->fetchAll();
        
        if (!empty($orphanedVehicles)) {
            echo "⚠️  Found " . count($orphanedVehicles) . " orphaned vehicles\n";
            $this->results['orphaned_vehicles'] = $orphanedVehicles;
        } else {
            echo "✅ No orphaned vehicles found\n";
        }
        
        // Check for orphaned registrations - broken down by type for better reporting
        echo "Checking registration integrity...\n";
        
        // Registrations with invalid shows
        $orphanedByShow = $this->db->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, 'invalid_show' as issue_type
            FROM registrations r
            LEFT JOIN shows s ON r.show_id = s.id
            WHERE s.id IS NULL
        ")->fetchAll();
        
        // Registrations with invalid vehicles
        $orphanedByVehicle = $this->db->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, 'invalid_vehicle' as issue_type
            FROM registrations r
            LEFT JOIN vehicles v ON r.vehicle_id = v.id
            WHERE v.id IS NULL
        ")->fetchAll();
        
        // Registrations with invalid owners
        $orphanedByOwner = $this->db->query("
            SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, 'invalid_owner' as issue_type
            FROM registrations r
            LEFT JOIN users u ON r.owner_id = u.id
            WHERE u.id IS NULL
        ")->fetchAll();
        
        // Registrations with invalid categories (using show_categories table)
        $orphanedByCategory = [];
        try {
            $orphanedByCategory = $this->db->query("
                SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, r.category_id, 'invalid_category' as issue_type
                FROM registrations r
                LEFT JOIN show_categories sc ON r.category_id = sc.id
                WHERE r.category_id IS NOT NULL AND sc.id IS NULL
            ")->fetchAll();
        } catch (Exception $e) {
            // Category check might fail if show_categories table doesn't exist or category_id column doesn't exist
            echo "  Note: Could not check category references: " . $e->getMessage() . "\n";
        }
        
        // Combine all orphaned registrations
        $allOrphanedRegistrations = array_merge($orphanedByShow, $orphanedByVehicle, $orphanedByOwner, $orphanedByCategory);
        
        // Report results
        if (!empty($orphanedByShow)) {
            echo "  ⚠️  Found " . count($orphanedByShow) . " registrations with invalid shows\n";
        }
        if (!empty($orphanedByVehicle)) {
            echo "  ⚠️  Found " . count($orphanedByVehicle) . " registrations with invalid vehicles\n";
        }
        if (!empty($orphanedByOwner)) {
            echo "  ⚠️  Found " . count($orphanedByOwner) . " registrations with invalid owners\n";
        }
        if (!empty($orphanedByCategory)) {
            echo "  ⚠️  Found " . count($orphanedByCategory) . " registrations with invalid categories\n";
        }
        
        if (!empty($allOrphanedRegistrations)) {
            echo "⚠️  Total orphaned registrations: " . count($allOrphanedRegistrations) . "\n";
            $this->results['orphaned_registrations'] = $allOrphanedRegistrations;
            $this->results['orphaned_registrations_by_show'] = $orphanedByShow;
            $this->results['orphaned_registrations_by_vehicle'] = $orphanedByVehicle;
            $this->results['orphaned_registrations_by_owner'] = $orphanedByOwner;
            $this->results['orphaned_registrations_by_category'] = $orphanedByCategory;
        } else {
            echo "✅ No orphaned registrations found\n";
        }
        
        // Check for orphaned payments
        $orphanedPayments = $this->db->query("
            SELECT p.id, p.user_id, p.amount, p.payment_status
            FROM payments p
            LEFT JOIN users u ON p.user_id = u.id
            WHERE u.id IS NULL
        ")->fetchAll();
        
        if (!empty($orphanedPayments)) {
            echo "⚠️  Found " . count($orphanedPayments) . " orphaned payments\n";
            $this->results['orphaned_payments'] = $orphanedPayments;
        } else {
            echo "✅ No orphaned payments found\n";
        }
        
        // Check for calendar events without valid calendars
        $orphanedEvents = $this->db->query("
            SELECT ce.id, ce.title, ce.calendar_id
            FROM calendar_events ce
            LEFT JOIN calendars c ON ce.calendar_id = c.id
            WHERE c.id IS NULL
        ")->fetchAll();
        
        if (!empty($orphanedEvents)) {
            echo "⚠️  Found " . count($orphanedEvents) . " orphaned calendar events\n";
            $this->results['orphaned_events'] = $orphanedEvents;
        } else {
            echo "✅ No orphaned calendar events found\n";
        }
        
        // Check for duplicate registrations
        $duplicateRegistrations = $this->db->query("
            SELECT show_id, vehicle_id, COUNT(*) as count
            FROM registrations
            GROUP BY show_id, vehicle_id
            HAVING COUNT(*) > 1
        ")->fetchAll();
        
        if (!empty($duplicateRegistrations)) {
            echo "⚠️  Found " . count($duplicateRegistrations) . " duplicate registrations\n";
            $this->results['duplicate_registrations'] = $duplicateRegistrations;
        } else {
            echo "✅ No duplicate registrations found\n";
        }
        
        echo "\n";
    }
    
    private function cleanOrphanedRecords() {
        echo "--- Cleaning Orphaned Records ---\n";
        
        if (!empty($this->results['orphaned_vehicles'])) {
            echo "Cleaning orphaned vehicles...\n";
            $count = $this->db->query("
                DELETE v FROM vehicles v
                LEFT JOIN users u ON v.owner_id = u.id
                WHERE u.id IS NULL
            ")->rowCount();
            echo "Deleted $count orphaned vehicles\n";
        }
        
        if (!empty($this->results['orphaned_registrations'])) {
            echo "Cleaning orphaned registrations...\n";
            $totalDeleted = 0;
            
            // Clean registrations with invalid shows
            if (!empty($this->results['orphaned_registrations_by_show'])) {
                $count = $this->db->query("
                    DELETE r FROM registrations r
                    LEFT JOIN shows s ON r.show_id = s.id
                    WHERE s.id IS NULL
                ")->rowCount();
                echo "  Deleted $count registrations with invalid shows\n";
                $totalDeleted += $count;
            }
            
            // Clean registrations with invalid vehicles
            if (!empty($this->results['orphaned_registrations_by_vehicle'])) {
                $count = $this->db->query("
                    DELETE r FROM registrations r
                    LEFT JOIN vehicles v ON r.vehicle_id = v.id
                    WHERE v.id IS NULL
                ")->rowCount();
                echo "  Deleted $count registrations with invalid vehicles\n";
                $totalDeleted += $count;
            }
            
            // Clean registrations with invalid owners
            if (!empty($this->results['orphaned_registrations_by_owner'])) {
                $count = $this->db->query("
                    DELETE r FROM registrations r
                    LEFT JOIN users u ON r.owner_id = u.id
                    WHERE u.id IS NULL
                ")->rowCount();
                echo "  Deleted $count registrations with invalid owners\n";
                $totalDeleted += $count;
            }
            
            // Clean registrations with invalid categories (if any)
            if (!empty($this->results['orphaned_registrations_by_category'])) {
                try {
                    $count = $this->db->query("
                        DELETE r FROM registrations r
                        LEFT JOIN show_categories sc ON r.category_id = sc.id
                        WHERE r.category_id IS NOT NULL AND sc.id IS NULL
                    ")->rowCount();
                    echo "  Deleted $count registrations with invalid categories\n";
                    $totalDeleted += $count;
                } catch (Exception $e) {
                    echo "  Could not clean category orphans: " . $e->getMessage() . "\n";
                }
            }
            
            echo "Total orphaned registrations deleted: $totalDeleted\n";
        }
        
        if (!empty($this->results['orphaned_payments'])) {
            echo "Cleaning orphaned payments...\n";
            $count = $this->db->query("
                DELETE p FROM payments p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE u.id IS NULL
            ")->rowCount();
            echo "Deleted $count orphaned payments\n";
        }
        
        if (!empty($this->results['orphaned_events'])) {
            echo "Cleaning orphaned calendar events...\n";
            $count = $this->db->query("
                DELETE ce FROM calendar_events ce
                LEFT JOIN calendars c ON ce.calendar_id = c.id
                WHERE c.id IS NULL
            ")->rowCount();
            echo "Deleted $count orphaned calendar events\n";
        }
        
        echo "\n";
    }
    
    private function optimizeTables() {
        echo "--- Optimizing Tables ---\n";
        
        $tables = [
            'users', 'shows', 'vehicles', 'registrations', 'payments',
            'calendars', 'calendar_events', 'calendar_clubs', 'categories',
            'age_weights', 'awards'
        ];
        
        foreach ($tables as $table) {
            echo "Optimizing $table... ";
            try {
                $this->db->query("OPTIMIZE TABLE `$table`");
                echo "✅\n";
            } catch (Exception $e) {
                echo "❌ Error: " . $e->getMessage() . "\n";
            }
        }
        
        // Update table statistics
        echo "Updating table statistics... ";
        try {
            $this->db->query("ANALYZE TABLE " . implode(', ', array_map(function($t) { return "`$t`"; }, $tables)));
            echo "✅\n";
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    private function generateReport() {
        echo "--- Database Health Report ---\n";
        
        // Table sizes
        $tableSizes = $this->db->query("
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
                table_rows
            FROM information_schema.TABLES 
            WHERE table_schema = DATABASE()
            ORDER BY (data_length + index_length) DESC
        ")->fetchAll();
        
        echo "Table Sizes:\n";
        foreach ($tableSizes as $table) {
            echo sprintf("  %-25s %8s MB  %8d rows\n", 
                $table['table_name'], 
                $table['Size (MB)'], 
                $table['table_rows']
            );
        }
        echo "\n";
        
        // Foreign key constraints
        $foreignKeys = $this->db->query("
            SELECT 
                TABLE_NAME,
                COLUMN_NAME,
                REFERENCED_TABLE_NAME,
                REFERENCED_COLUMN_NAME
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY TABLE_NAME
        ")->fetchAll();
        
        echo "Foreign Key Constraints: " . count($foreignKeys) . "\n";
        foreach ($foreignKeys as $fk) {
            echo sprintf("  %s.%s → %s.%s\n",
                $fk['TABLE_NAME'],
                $fk['COLUMN_NAME'],
                $fk['REFERENCED_TABLE_NAME'],
                $fk['REFERENCED_COLUMN_NAME']
            );
        }
        echo "\n";
        
        // Index usage
        $indexes = $this->db->query("
            SELECT 
                TABLE_NAME,
                INDEX_NAME,
                COUNT(*) as column_count
            FROM INFORMATION_SCHEMA.STATISTICS
            WHERE TABLE_SCHEMA = DATABASE()
            AND INDEX_NAME != 'PRIMARY'
            GROUP BY TABLE_NAME, INDEX_NAME
            ORDER BY TABLE_NAME, INDEX_NAME
        ")->fetchAll();
        
        echo "Indexes: " . count($indexes) . "\n";
        foreach ($indexes as $index) {
            echo sprintf("  %s.%s (%d columns)\n",
                $index['TABLE_NAME'],
                $index['INDEX_NAME'],
                $index['column_count']
            );
        }
        echo "\n";
        
        // Recent activity
        $recentUsers = $this->db->query("
            SELECT COUNT(*) as count
            FROM users 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ")->fetch()['count'];
        
        $recentShows = $this->db->query("
            SELECT COUNT(*) as count
            FROM shows 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ")->fetch()['count'];
        
        $recentRegistrations = $this->db->query("
            SELECT COUNT(*) as count
            FROM registrations 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ")->fetch()['count'];
        
        echo "Recent Activity (Last 30 days):\n";
        echo "  New Users: $recentUsers\n";
        echo "  New Shows: $recentShows\n";
        echo "  New Registrations: $recentRegistrations\n";
        echo "\n";
        
        // System totals
        $totalUsers = $this->db->query("SELECT COUNT(*) as count FROM users")->fetch()['count'];
        $totalShows = $this->db->query("SELECT COUNT(*) as count FROM shows")->fetch()['count'];
        $totalVehicles = $this->db->query("SELECT COUNT(*) as count FROM vehicles")->fetch()['count'];
        $totalRegistrations = $this->db->query("SELECT COUNT(*) as count FROM registrations")->fetch()['count'];
        
        echo "System Totals:\n";
        echo "  Total Users: $totalUsers\n";
        echo "  Total Shows: $totalShows\n";
        echo "  Total Vehicles: $totalVehicles\n";
        echo "  Total Registrations: $totalRegistrations\n";
        echo "\n";
    }
    
    private function saveResults() {
        $logFile = APPROOT . '/logs/database_maintenance_' . date('Y-m-d_H-i-s') . '.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logContent = [
            'timestamp' => date('Y-m-d H:i:s'),
            'results' => $this->results
        ];
        
        file_put_contents($logFile, json_encode($logContent, JSON_PRETTY_PRINT));
        echo "Results saved to: $logFile\n";
    }
    
    private function showUsage() {
        echo "Usage: php database_maintenance.php [task]\n";
        echo "Tasks:\n";
        echo "  check    - Check database integrity\n";
        echo "  clean    - Clean orphaned records\n";
        echo "  optimize - Optimize tables and update statistics\n";
        echo "  report   - Generate database health report\n";
        echo "  all      - Run all tasks\n";
    }
}

// Main execution
// Get task from URL parameter or command line argument
$task = 'check'; // default task

if (php_sapi_name() === 'cli') {
    // Command line execution
    $task = isset($argv[1]) ? $argv[1] : 'check';
} else {
    // Web execution
    $task = isset($_GET['task']) ? $_GET['task'] : 'check';
}

$maintenance = new DatabaseMaintenance();
$maintenance->runTask($task);
?>
<?php
/**
 * DEPRECATED: This API endpoint has been moved to controllers
 * 
 * Old endpoint: /api/camera-banners.php
 * New endpoint: /api/cameraBanners (handled by ApiController::cameraBanners)
 * 
 * This file is kept for backward compatibility but will be removed in future versions.
 * Backup available at: /autobackup/camera-banners-api-removed.php
 */

http_response_code(410); // Gone
header('Content-Type: application/json');
echo json_encode([
    'error' => 'This API endpoint has been moved',
    'message' => 'Please use /api/cameraBanners instead',
    'status' => 'deprecated',
    'redirect' => '/api/cameraBanners'
]);
?>
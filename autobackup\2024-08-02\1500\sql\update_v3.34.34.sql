-- Update script for version 3.34.34
-- Adds API integration for Cash App Pay and Venmo

-- Add new payment settings for Cash App Pay API
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'payment_settings' AND COLUMN_NAME = 'setting_key' AND COLUMN_NAME = 'setting_value';

-- Only proceed if the payment_settings table exists with the expected structure
SET @add_settings_sql = IF(@column_exists > 0, 
    'INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("cashapp_api_client_id", "") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
    
    INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("cashapp_api_client_secret", "") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
    
    INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("cashapp_api_sandbox", "true") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
    
    INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("venmo_api_client_id", "") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
    
    INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("venmo_api_client_secret", "") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
    
    INSERT INTO payment_settings (setting_key, setting_value) VALUES 
    ("venmo_api_sandbox", "true") ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);',
    'SELECT "Payment settings table not found or has unexpected structure"');

PREPARE stmt FROM @add_settings_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update payment_methods table to add API integration flags
SET @column_exists = 0;
SELECT COUNT(*) INTO @column_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'payment_methods' AND COLUMN_NAME = 'api_integration';

-- Add api_integration column if it doesn't exist
SET @add_column_sql = IF(@column_exists = 0, 
    'ALTER TABLE payment_methods ADD COLUMN api_integration TINYINT(1) NOT NULL DEFAULT 0',
    'SELECT "Column api_integration already exists"');
PREPARE stmt FROM @add_column_sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing payment methods to set API integration flags
UPDATE payment_methods SET api_integration = 1 WHERE name IN ('PayPal', 'CashApp', 'Venmo');

-- Log the update
INSERT INTO system_logs (type, message, created_at) 
VALUES ('system', 'Updated database to version 3.34.34 - Added API integration for Cash App Pay and Venmo', NOW());
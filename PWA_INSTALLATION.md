# PWA Installation Guide for RER Events & Shows

## Overview
This guide covers the installation and configuration of Progressive Web App (PWA) features for the Rowan Elite Rides Events & Shows management system.

## Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- HTTPS enabled (required for PWA features)
- Modern web browser with PWA support

## Installation Steps

### 1. Database Migration
Run the PWA database migration to add required tables and columns:

```sql
-- Execute the migration file
SOURCE database/migrations/add_pwa_features.sql;
```

Or run it through your database management tool by importing the file:
`database/migrations/add_pwa_features.sql`

### 2. VAPID Keys Generation
Generate VAPID keys for push notifications:

```bash
# Using web-push CLI (if available)
npx web-push generate-vapid-keys

# Or use online generator: https://vapidkeys.com/
```

Update the VAPID keys in your system settings:
1. Go to Admin Dashboard → Settings
2. Find PWA Settings section
3. Enter your VAPID public and private keys

### 3. HTTPS Configuration
PWA features require HTTPS. Ensure your server has SSL/TLS configured:

```apache
# Apache example
<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /path/to/your/app
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    # Additional SSL configuration...
</VirtualHost>
```

### 4. Service Worker Registration
The service worker is automatically registered. Verify it's working:

1. Open browser developer tools
2. Go to Application tab
3. Check Service Workers section
4. Verify `sw.js` is registered and active

### 5. Web App Manifest
The manifest is automatically generated. Verify it's accessible:
- Visit: `https://yourdomain.com/public/manifest.json`
- Should return valid JSON with app metadata

### 6. Icon Generation
Ensure all required icons are present in `/public/images/icons/`:

Required icons:
- `icon-72x72.png`
- `icon-96x96.png`
- `icon-128x128.png`
- `icon-144x144.png`
- `icon-152x152.png`
- `icon-192x192.png`
- `icon-384x384.png`
- `icon-512x512.png`
- `apple-touch-icon.png`
- `favicon-32x32.png`
- `favicon-16x16.png`

### 7. Configuration Verification
Check PWA settings in Admin Dashboard:

1. **PWA Enabled**: ✓ Enabled
2. **Push Notifications**: ✓ Enabled
3. **Offline Mode**: ✓ Enabled
4. **VAPID Keys**: ✓ Configured

## Features Included

### 1. App Installation
- **Add to Home Screen** prompt on supported devices
- **Standalone mode** for app-like experience
- **Custom splash screen** with branding

### 2. Push Notifications
- **Event reminders** (24h, 1h before events)
- **Registration updates** (confirmations, changes)
- **Judging reminders** for judges
- **Payment notifications**
- **User preference management**

### 3. Offline Functionality
- **Cached pages** for offline viewing
- **Offline form submissions** (synced when online)
- **Service worker** handles network requests
- **Background sync** for pending actions

### 4. Enhanced Mobile Features
- **Camera integration** for vehicle photos
- **QR code scanning** for quick actions
- **Mobile-first navigation**
- **Touch-optimized interface**

### 5. Performance Optimizations
- **Resource caching** for faster loading
- **Background updates** for fresh content
- **Lazy loading** for images and components
- **Optimized asset delivery**

## API Endpoints

### PWA Management
- `GET /api/pwa/vapid-key` - Get VAPID public key
- `POST /api/pwa/subscribe` - Subscribe to push notifications
- `POST /api/pwa/sync` - Sync offline data
- `GET /api/pwa/cached-data` - Get cached data
- `POST /api/pwa/qr-scan` - Process QR scan results
- `GET /api/pwa/metrics` - Get installation metrics
- `POST /api/pwa/usage` - Update usage data

### Notifications
- `POST /api/notifications/send` - Send push notification
- `GET /api/notifications/preferences` - Get user preferences
- `POST /api/notifications/preferences` - Update preferences

## Testing PWA Features

### 1. Installation Test
1. Visit site on mobile device
2. Look for "Add to Home Screen" prompt
3. Install the app
4. Verify standalone mode works

### 2. Push Notifications Test
1. Enable notifications in browser
2. Subscribe to push notifications
3. Send test notification from admin panel
4. Verify notification appears

### 3. Offline Test
1. Load a page while online
2. Disconnect from internet
3. Navigate to cached pages
4. Verify offline functionality works

### 4. Camera Integration Test
1. Go to vehicle registration
2. Click camera button
3. Take photo
4. Verify image is captured and uploaded

### 5. QR Code Test
1. Click QR scanner button
2. Scan a QR code
3. Verify appropriate action is taken

## Troubleshooting

### Common Issues

#### 1. Service Worker Not Registering
- **Check HTTPS**: PWA requires HTTPS
- **Check console errors**: Look for JavaScript errors
- **Clear cache**: Hard refresh (Ctrl+Shift+R)

#### 2. Push Notifications Not Working
- **Check VAPID keys**: Ensure they're properly configured
- **Check permissions**: User must grant notification permission
- **Check subscription**: Verify subscription is saved in database

#### 3. Offline Mode Not Working
- **Check service worker**: Must be active and running
- **Check cache**: Verify resources are being cached
- **Check network**: Test with airplane mode

#### 4. Icons Not Displaying
- **Check file paths**: Ensure icons exist in correct location
- **Check manifest**: Verify manifest.json is valid
- **Check sizes**: Ensure all required icon sizes are present

### Debug Mode
Enable debug mode in `config/config.php`:

```php
define('DEBUG_MODE', true);
```

This will log PWA-related activities to help with troubleshooting.

## Performance Monitoring

### Metrics Available
- **Installation rate**: Users who installed the PWA
- **Push subscription rate**: Users subscribed to notifications
- **Offline usage**: How often offline features are used
- **Performance metrics**: Load times, cache hit rates

### Analytics Integration
PWA usage data is automatically collected and can be viewed in:
1. Admin Dashboard → PWA Analytics
2. Database table: `pwa_metrics`
3. Browser DevTools → Application → Storage

## Security Considerations

### 1. VAPID Keys
- Store private key securely (environment variables)
- Never expose private key in client-side code
- Rotate keys periodically

### 2. Push Notifications
- Validate all notification content
- Implement rate limiting
- Respect user preferences

### 3. Offline Data
- Encrypt sensitive cached data
- Implement data expiration
- Validate sync data before processing

## Maintenance

### Regular Tasks
1. **Clean up old subscriptions** (automated via cron job)
2. **Update PWA metrics** (automated daily)
3. **Monitor error logs** for PWA-related issues
4. **Update service worker** when deploying changes

### Database Maintenance
The system includes automated procedures:
- `CleanupSyncQueue()` - Removes old sync items
- `UpdatePWAMetrics()` - Updates daily metrics
- Event scheduler runs these daily

## Browser Support

### Fully Supported
- Chrome 67+
- Firefox 62+
- Safari 11.1+
- Edge 79+

### Partially Supported
- Safari iOS 11.3+ (limited PWA features)
- Samsung Internet 7.2+
- Opera 54+

### Not Supported
- Internet Explorer (any version)
- Chrome < 67
- Firefox < 62

## Deployment Checklist

Before deploying PWA features:

- [ ] Database migration completed
- [ ] HTTPS configured and working
- [ ] VAPID keys generated and configured
- [ ] All required icons present
- [ ] Service worker accessible
- [ ] Manifest.json valid and accessible
- [ ] Push notifications tested
- [ ] Offline functionality tested
- [ ] Mobile installation tested
- [ ] Debug mode disabled in production

## Support

For technical support or questions about PWA implementation:

1. Check the troubleshooting section above
2. Review browser console for errors
3. Check server error logs
4. Verify all prerequisites are met

## Version History

- **v1.0.0** - Initial PWA implementation
  - Service worker with caching
  - Push notifications
  - App installation
  - Offline functionality
  - Mobile enhancements
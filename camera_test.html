<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="public/css/pwa-features.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Camera Test Page</h1>
        <p>This page is for testing the camera modal without needing to login.</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Regular Camera Test</h5>
                        <p class="card-text">Test the regular photo camera modal.</p>
                        <button id="test-camera-btn" class="btn btn-primary">
                            <i class="fas fa-camera"></i> Test Camera
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">QR Scanner Test</h5>
                        <p class="card-text">Test the QR scanner camera modal.</p>
                        <button id="test-qr-btn" class="btn btn-success">
                            <i class="fas fa-qrcode"></i> Test QR Scanner
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Instructions:</h3>
            <ol>
                <li>Click either button to test the camera</li>
                <li>Look for the red debug box at the top of the screen</li>
                <li>Watch for messages about the backdrop</li>
                <li>Report what you see</li>
            </ol>
        </div>
    </div>

    <!-- Hidden input for camera testing -->
    <input type="file" id="test-camera-input" style="display: none;">

    <!-- Load all required scripts in correct order -->
    <script src="public/js/main.js"></script>
    <script src="public/js/image-viewer.js"></script>
    <script src="public/js/notifications.js"></script>
    <script src="public/js/mobile-notifications-fix.js"></script>
    <script src="public/js/camera-banner.js"></script>
    <script src="public/js/pwa-features.js"></script>

    <script>
        // Enable debug mode
        localStorage.setItem('camera_banner_debug', 'true');

        document.addEventListener('DOMContentLoaded', function() {
            console.log('Camera test page loaded');

            // Wait for PWA features to initialize
            setTimeout(() => {
                if (window.pwaFeatures) {
                    console.log('PWA Features loaded successfully');
                } else {
                    console.error('PWA Features failed to load');
                }

                if (window.cameraBanner) {
                    console.log('Camera Banner system loaded');
                } else {
                    console.error('Camera Banner system failed to load');
                }
            }, 1000);

            // Test camera button
            document.getElementById('test-camera-btn').addEventListener('click', function() {
                console.log('Camera button clicked');
                if (window.pwaFeatures) {
                    window.pwaFeatures.openCamera('test-camera-input');
                } else {
                    alert('PWA Features not loaded');
                }
            });

            // Test QR scanner button
            document.getElementById('test-qr-btn').addEventListener('click', function() {
                console.log('QR button clicked');
                if (window.pwaFeatures) {
                    window.pwaFeatures.openQRScanner();
                } else {
                    alert('PWA Features not loaded');
                }
            });
        });
    </script>
</body>
</html>

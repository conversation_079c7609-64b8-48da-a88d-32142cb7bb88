<?php
// Try multiple approaches to include the header
$headerIncluded = false;

// Approach 1: Using APPROOT constant
if (defined('APPROOT') && file_exists(APPROOT . '/views/includes/header.php')) {
    require APPROOT . '/views/includes/header.php';
    $headerIncluded = true;
}

// Approach 2: Using relative path from current file
if (!$headerIncluded && file_exists(dirname(dirname(__FILE__)) . '/includes/header.php')) {
    require dirname(dirname(__FILE__)) . '/includes/header.php';
    $headerIncluded = true;
}

// Approach 3: Using server document root
if (!$headerIncluded && isset($_SERVER['DOCUMENT_ROOT']) && file_exists($_SERVER['DOCUMENT_ROOT'] . '/views/includes/header.php')) {
    require $_SERVER['DOCUMENT_ROOT'] . '/views/includes/header.php';
    $headerIncluded = true;
}

// If header still not included, create a basic header
if (!$headerIncluded) {
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Events and Shows</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .facebook-notice {
            background: linear-gradient(135deg, #4267B2 0%, #365899 100%) !important;
            border-radius: 15px !important;
            box-shadow: 0 8px 25px rgba(66, 103, 178, 0.4) !important;
            border: 2px solid rgba(255, 255, 255, 0.2) !important;
            padding: 2rem !important;
            margin-bottom: 1.5rem !important;
            width: 100% !important;
            display: block !important;
        }
        
        .facebook-icon-large {
            color: white !important;
            font-size: 3rem !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
            display: block !important;
            margin-bottom: 1rem !important;
        }
        
        .facebook-title {
            color: white !important;
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
            margin-bottom: 1rem !important;
        }
        
        .facebook-text {
            color: white !important;
            font-size: 1.1rem !important;
            opacity: 0.95 !important;
            line-height: 1.5 !important;
            margin-bottom: 1.5rem !important;
        }
        
        .btn-facebook {
            background-color: #4267B2 !important;
            border-color: #4267B2 !important;
            color: white !important;
            font-weight: 600 !important;
            font-size: 1rem !important;
            padding: 12px 24px !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 8px rgba(66, 103, 178, 0.3) !important;
            border: none !important;
            text-decoration: none !important;
            display: inline-block !important;
            cursor: pointer !important;
        }
        
        .btn-facebook:hover {
            background-color: #365899 !important;
            border-color: #365899 !important;
            color: white !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 15px rgba(66, 103, 178, 0.4) !important;
            text-decoration: none !important;
        }
        
        .btn-facebook:focus {
            background-color: #365899 !important;
            border-color: #365899 !important;
            color: white !important;
            box-shadow: 0 0 0 0.2rem rgba(66, 103, 178, 0.5) !important;
            text-decoration: none !important;
        }
        
        .btn-facebook:active {
            background-color: #2d4373 !important;
            border-color: #2d4373 !important;
            color: white !important;
            transform: translateY(0) !important;
            text-decoration: none !important;
        }
        
        .btn-facebook:visited {
            background-color: #4267B2 !important;
            color: white !important;
            text-decoration: none !important;
        }
        
        .btn-facebook i {
            color: white !important;
            font-size: 1.1rem !important;
        }
        
        .facebook-loading-text {
            color: white !important;
            font-weight: 500 !important;
        }
        
        /* Force text center alignment */
        .facebook-notice .text-center {
            text-align: center !important;
            display: block !important;
        }
        
        /* Force all child elements to be visible */
        .facebook-notice * {
            visibility: visible !important;
        }
        
        /* CSS Hover effects as backup - multiple selectors to ensure it works */
        #facebook-login-btn:hover,
        button[id="facebook-login-btn"]:hover,
        .btn-facebook:hover {
            background-color: #f8f9fa !important;
            border-color: #f8f9fa !important;
            color: #365899 !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
            transition: all 0.3s ease !important;
        }
        
        #facebook-login-btn:hover i,
        button[id="facebook-login-btn"]:hover i,
        .btn-facebook:hover i {
            color: #365899 !important;
        }
        
        #facebook-login-btn:active,
        button[id="facebook-login-btn"]:active,
        .btn-facebook:active {
            transform: translateY(0) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
        }
        
        /* Force hover to work even with inline styles */
        button:hover[style*="background-color: white"] {
            background-color: #f8f9fa !important;
            color: #365899 !important;
            transform: translateY(-2px) !important;
        }
        
        @media (max-width: 768px) {
            .facebook-notice {
                padding: 1.5rem !important;
                margin-left: -15px !important;
                margin-right: -15px !important;
                border-radius: 0 !important;
            }
            
            .facebook-icon-large {
                font-size: 2.5rem !important;
                margin-bottom: 1rem !important;
            }
            
            .facebook-title {
                font-size: 1.3rem !important;
                margin-bottom: 1rem !important;
            }
            
            .facebook-text {
                font-size: 1rem !important;
                margin-bottom: 1.5rem !important;
            }
            
            .btn-facebook {
                font-size: 1.1rem !important;
                padding: 14px 28px !important;
                width: 100% !important;
                max-width: 300px !important;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">Events and Shows</a>
        </div>
    </nav>
    <main class="container py-4">
<?php
}
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0 rounded-lg mt-5">
                <div class="card-header">
                    <h3 class="text-center font-weight-light my-4">Create Account</h3>
                </div>
                <div class="card-body">
                    <!-- Facebook Login Notice -->
                    <div class="facebook-notice mb-4" style="background: linear-gradient(135deg, #4267B2 0%, #365899 100%); border-radius: 15px; box-shadow: 0 8px 25px rgba(66, 103, 178, 0.4); border: 2px solid rgba(255, 255, 255, 0.2); padding: 2rem; margin-bottom: 1.5rem;">
                        <div class="text-center" style="text-align: center;">
                            <i class="fab fa-facebook-f facebook-icon-large mb-3" style="color: white; font-size: 3rem; text-shadow: 0 2px 4px rgba(0,0,0,0.2); display: block; margin-bottom: 1rem;"></i>
                            <h4 class="facebook-title mb-3" style="color: white; font-size: 1.5rem; font-weight: 700; text-shadow: 0 1px 2px rgba(0,0,0,0.1); margin-bottom: 1rem;">Skip Registration - Use Facebook!</h4>
                            <p class="facebook-text mb-4" style="color: white; font-size: 1.1rem; opacity: 0.95; line-height: 1.5; margin-bottom: 1.5rem;">
                                <strong style="color: white;">Already have a Facebook account?</strong><br>
                                <span style="color: white;">No need to fill out this form! Simply click below to login with Facebook and get started instantly.</span>
                            </p>
                            <button type="button" id="facebook-login-btn" class="btn btn-facebook" 
                                onclick="handleFacebookLogin()" 
                                onmouseover="this.style.cssText = 'background-color: #28a745 !important; border: 2px solid #28a745 !important; color: white !important; font-weight: 600; font-size: 1rem; padding: 12px 24px; border-radius: 8px; transition: all 0.3s ease; box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important; text-decoration: none; display: inline-block; cursor: pointer; transform: translateY(-3px) scale(1.02) !important;'; this.querySelector('i').style.cssText = 'color: white !important; font-size: 1.1rem; margin-right: 0.5rem;';"
                                onmouseout="this.style.cssText = 'background-color: white !important; border: 2px solid white !important; color: #4267B2 !important; font-weight: 600; font-size: 1rem; padding: 12px 24px; border-radius: 8px; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important; text-decoration: none; display: inline-block; cursor: pointer; transform: translateY(0) scale(1) !important;'; this.querySelector('i').style.cssText = 'color: #4267B2 !important; font-size: 1.1rem; margin-right: 0.5rem;';"
                                style="background-color: white; border: 2px solid white; color: #4267B2; font-weight: 600; font-size: 1rem; padding: 12px 24px; border-radius: 8px; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); text-decoration: none; display: inline-block; cursor: pointer;">
                                <i class="fab fa-facebook-f me-2" style="color: #4267B2; font-size: 1.1rem;"></i> Login with Facebook - No Registration Required
                            </button>
                            <div id="facebook-login-status" class="mt-3" style="display: none;">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="spinner-border spinner-border-sm me-2 text-white" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span id="facebook-status-text" class="facebook-loading-text" style="color: white; font-weight: 500;">Connecting to Facebook...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <div class="position-relative">
                            <hr class="my-4">
                            <span class="position-absolute top-50 start-50 translate-middle bg-white px-3 text-muted" style="font-size: 0.9rem; font-weight: 500;">
                                OR create a new account below
                            </span>
                        </div>
                    </div>

                    <?php if (isset($error_message)) : ?>
                        <div class="alert alert-danger"><?php echo $error_message; ?></div>
                    <?php endif; ?>
                    
                    <form action="<?php echo BASE_URL; ?>/auth/register" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="name" name="name" type="text" placeholder="Enter your full name" value="<?php echo isset($name) ? $name : ''; ?>" required />
                                    <label for="name">Full Name</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input class="form-control" id="email" name="email" type="email" placeholder="<EMAIL>" value="<?php echo isset($email) ? $email : ''; ?>" required />
                            <label for="email">Email address</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input class="form-control" id="phone" name="phone" type="tel" placeholder="Phone number" value="<?php echo isset($phone) ? $phone : ''; ?>" />
                            <label for="phone">Phone Number</label>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input class="form-control" id="address" name="address" type="text" placeholder="Address" value="<?php echo isset($address) ? $address : ''; ?>" />
                            <label for="address">Address</label>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="city" name="city" type="text" placeholder="City" value="<?php echo isset($city) ? $city : ''; ?>" />
                                    <label for="city">City</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="state" name="state" type="text" placeholder="State" value="<?php echo isset($state) ? $state : ''; ?>" />
                                    <label for="state">State</label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="zip" name="zip" type="text" placeholder="ZIP Code" value="<?php echo isset($zip) ? $zip : ''; ?>" />
                                    <label for="zip">ZIP Code</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-floating mb-3 mb-md-0">
                                    <input class="form-control" id="password" name="password" type="password" placeholder="Create a password" required />
                                    <label for="password">Password</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating mb-3 mb-md-0">
                                    <input class="form-control" id="confirm_password" name="confirm_password" type="password" placeholder="Confirm password" required />
                                    <label for="confirm_password">Confirm Password</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 mb-0">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        <a href="<?php echo BASE_URL; ?>/auth/login">Have an account? Go to login</a>
                        <br>
                        <span class="text-muted">or</span>
                        <br>
                        <a href="<?php echo BASE_URL; ?>/auth/facebook" class="text-primary">
                            <i class="fab fa-facebook-f me-1"></i>Login with Facebook (No registration needed)
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
// Try multiple approaches to include the footer
$footerIncluded = false;

// Approach 1: Using APPROOT constant
if (defined('APPROOT') && file_exists(APPROOT . '/views/includes/footer.php')) {
    require APPROOT . '/views/includes/footer.php';
    $footerIncluded = true;
}

// Approach 2: Using relative path from current file
if (!$footerIncluded && file_exists(dirname(dirname(__FILE__)) . '/includes/footer.php')) {
    require dirname(dirname(__FILE__)) . '/includes/footer.php';
    $footerIncluded = true;
}

// Approach 3: Using server document root
if (!$footerIncluded && isset($_SERVER['DOCUMENT_ROOT']) && file_exists($_SERVER['DOCUMENT_ROOT'] . '/views/includes/footer.php')) {
    require $_SERVER['DOCUMENT_ROOT'] . '/views/includes/footer.php';
    $footerIncluded = true;
}

// If footer still not included, create a basic footer
if (!$footerIncluded) {
?>
    </main>
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="text-center">
                <p>&copy; <?php echo gmdate('Y'); ?> Events and Shows. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Facebook login functionality for register page
        window.handleFacebookLogin = function() {
            // Prevent multiple simultaneous calls
            if (window.facebookLoginInProgress) {
                console.log('Facebook login already in progress, ignoring duplicate click');
                return;
            }

            console.log('Starting Facebook login process');
            window.facebookLoginInProgress = true;

            const statusDiv = document.getElementById('facebook-login-status');
            const statusText = document.getElementById('facebook-status-text');
            const loginBtn = document.getElementById('facebook-login-btn');

            // Show loading status first
            if (statusDiv) {
                statusDiv.style.display = 'block';
            }
            if (statusText) {
                statusText.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Please wait, connecting to Facebook...';
            }

            // Update button but keep it visible with loading state
            if (loginBtn) {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting to Facebook...';
                // Keep the button styled properly, just disable it
                loginBtn.style.opacity = '0.8';
                loginBtn.style.cursor = 'wait';
            }

            // Get base URL - try multiple methods
            const baseUrl = '<?php echo isset($GLOBALS["BASE_URL"]) ? $GLOBALS["BASE_URL"] : (defined("BASE_URL") ? BASE_URL : ""); ?>' || '';

            // Add a small delay to show the loading state before redirect
            setTimeout(() => {
                console.log('Redirecting to Facebook login');
                window.location.href = baseUrl + '/auth/facebook';
            }, 500); // Small delay to show loading state

            // Clear the flag after a timeout as a safety measure
            setTimeout(() => {
                window.facebookLoginInProgress = false;
                // Reset button if redirect failed
                if (loginBtn) {
                    loginBtn.disabled = false;
                    loginBtn.innerHTML = '<i class="fab fa-facebook-f me-2" style="color: #4267B2; font-size: 1.1rem;"></i> Login with Facebook - No Registration Required';
                    loginBtn.style.opacity = '1';
                    loginBtn.style.cursor = 'pointer';
                    loginBtn.style.backgroundColor = 'white';
                    loginBtn.style.borderColor = 'white';
                    loginBtn.style.color = '#4267B2';
                    loginBtn.className = 'btn btn-facebook'; // Reset the class
                }
                if (statusDiv) {
                    statusDiv.style.display = 'none';
                }
            }, 10000); // 10 seconds timeout
        };

        // Ensure the function is available when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Facebook login functionality loaded');
            
            // Add click event listener as backup
            const facebookBtn = document.getElementById('facebook-login-btn');
            console.log('Looking for Facebook button with ID: facebook-login-btn');
            console.log('Facebook button found:', facebookBtn);
            
            if (facebookBtn) {
                console.log('Facebook button exists, setting up events');
                facebookBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleFacebookLogin();
                });
                
                // Force hover effects with JavaScript - with debugging
                console.log('Facebook button found, adding hover effects');
                
                facebookBtn.addEventListener('mouseenter', function(e) {
                    console.log('HOVER IN - Mouse entered Facebook button');
                    if (!this.disabled) {
                        console.log('Button not disabled, applying hover styles');
                        this.style.setProperty('background-color', '#f8f9fa', 'important');
                        this.style.setProperty('border-color', '#f8f9fa', 'important');
                        this.style.setProperty('color', '#365899', 'important');
                        this.style.setProperty('transform', 'translateY(-2px)', 'important');
                        this.style.setProperty('box-shadow', '0 4px 15px rgba(0, 0, 0, 0.3)', 'important');
                        // Update icon color
                        const icon = this.querySelector('i');
                        if (icon) {
                            icon.style.setProperty('color', '#365899', 'important');
                            console.log('Icon color changed to hover state');
                        }
                    } else {
                        console.log('Button is disabled, not applying hover');
                    }
                });
                
                facebookBtn.addEventListener('mouseleave', function(e) {
                    console.log('HOVER OUT - Mouse left Facebook button');
                    if (!this.disabled) {
                        console.log('Button not disabled, resetting styles');
                        this.style.setProperty('background-color', 'white', 'important');
                        this.style.setProperty('border-color', 'white', 'important');
                        this.style.setProperty('color', '#4267B2', 'important');
                        this.style.setProperty('transform', 'translateY(0)', 'important');
                        this.style.setProperty('box-shadow', '0 2px 8px rgba(0, 0, 0, 0.2)', 'important');
                        // Reset icon color
                        const icon = this.querySelector('i');
                        if (icon) {
                            icon.style.setProperty('color', '#4267B2', 'important');
                            console.log('Icon color reset to normal state');
                        }
                    }
                });
                
                // Also try mouseover/mouseout as backup
                facebookBtn.addEventListener('mouseover', function(e) {
                    console.log('MOUSEOVER event triggered');
                });
                
                facebookBtn.addEventListener('mouseout', function(e) {
                    console.log('MOUSEOUT event triggered');
                });
            } else {
                console.log('ERROR: Facebook button not found! Checking all buttons on page...');
                const allButtons = document.querySelectorAll('button');
                console.log('All buttons found:', allButtons.length);
                allButtons.forEach((btn, index) => {
                    console.log(`Button ${index}:`, btn.id, btn.className, btn.textContent.substring(0, 50));
                });
            }
        });
    </script>
</body>
</html>
<?php
}
?>
<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Calendar & Map Settings</h1>
            <p class="text-muted">Configure calendar display, event chart, map provider, and image settings</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <!-- Calendar & Map Settings Sections -->
    <div class="row g-4 mb-5">
        
        <!-- Calendar Display Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-calendar-alt text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Calendar Display</h4>
                    </div>
                    <p class="card-text text-muted">Configure basic calendar display options, date formats, and view settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_calendar_display" class="stretched-link text-decoration-none">
                        <span class="d-none">View Calendar Display Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Event Chart Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-chart-bar text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Event Chart</h4>
                    </div>
                    <p class="card-text text-muted">Configure Monthly Event Chart timeline view, drag & drop, and mobile settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_chart" class="stretched-link text-decoration-none">
                        <span class="d-none">View Event Chart Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Map Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-map-marked-alt text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Map Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure map provider, API keys, default location, and zoom settings.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_map" class="stretched-link text-decoration-none">
                        <span class="d-none">View Map Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Map Tools Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-tools text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Map Tools</h4>
                    </div>
                    <p class="card-text text-muted">Access geocoding tools, batch processing, and map verification utilities.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_map_tools" class="stretched-link text-decoration-none">
                        <span class="d-none">View Map Tools</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Event Image Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-images text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Event Images</h4>
                    </div>
                    <p class="card-text text-muted">Configure event image upload limits, file types, and social sharing options.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_images" class="stretched-link text-decoration-none">
                        <span class="d-none">View Event Image Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-bolt text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Quick Actions</h4>
                    </div>
                    <div class="d-grid gap-2">
                        <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-calendar me-1"></i> View Calendar
                        </a>
                        <a href="<?php echo BASE_URL; ?>/calendar/map" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-map me-1"></i> View Map
                        </a>
                        <a href="<?php echo BASE_URL; ?>/admin/geocodeEvents" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-map-marker-alt me-1"></i> Geocode Events
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Calendar System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Current Calendar System</h5>
                            <p>The system uses a <strong>Monthly Event Chart</strong> as the primary calendar view, which displays events in a timeline format for easy viewing and management.</p>
                            
                            <h5>Key Features</h5>
                            <ul>
                                <li><strong>Timeline View:</strong> Events displayed horizontally across dates</li>
                                <li><strong>Mobile Responsive:</strong> Automatic card view on smaller screens</li>
                                <li><strong>Interactive Maps:</strong> Location-based event display</li>
                                <li><strong>Image Support:</strong> Event images with social sharing</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Settings Organization</h5>
                            <p>Calendar and map settings are organized into focused sections for easier management:</p>
                            
                            <ul>
                                <li><strong>Calendar Display:</strong> Basic calendar formatting and view options</li>
                                <li><strong>Event Chart:</strong> Timeline-specific settings and behavior</li>
                                <li><strong>Map Settings:</strong> Provider configuration and location defaults</li>
                                <li><strong>Map Tools:</strong> Geocoding and location utilities</li>
                                <li><strong>Event Images:</strong> Upload limits and display options</li>
                            </ul>
                            
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb me-2"></i>
                                <strong>Tip:</strong> Start with Map Settings to configure your provider, then use Map Tools to geocode existing events.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
-- Add PWA columns to users table - Run individually if needed
-- Only run the ALTER statements for columns that don't exist

-- Check existing columns first:
SELECT 'Checking existing PWA columns...' as status;
SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'users' 
AND COLUMN_NAME IN ('pwa_installed', 'pwa_standalone', 'push_supported', 'last_seen');

-- If pwa_installed doesn't exist, run this:
-- ALTER TABLE users ADD COLUMN pwa_installed TINYINT(1) DEFAULT 0 COMMENT 'Whether user has installed the PWA';

-- If pwa_standalone doesn't exist, run this:
-- ALTER TABLE users ADD COLUMN pwa_standalone TINYINT(1) DEFAULT 0 COMMENT 'Whether user is using PWA in standalone mode';

-- If push_supported doesn't exist, run this:
-- ALTER TABLE users ADD COLUMN push_supported TINYINT(1) DEFAULT 0 COMMENT 'Whether user device supports push notifications';

-- If last_seen doesn't exist, run this:
-- ALTER TABLE users ADD COLUMN last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last time user was active';

SELECT 'Instructions:' as message;
SELECT 'Uncomment and run only the ALTER statements for columns that do not exist in the results above.' as instruction;
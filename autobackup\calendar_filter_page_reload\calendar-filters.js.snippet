                    // The most direct approach - force a page reload with the filter parameters
                    // This ensures the calendar is completely reinitialized with the new filters
                    const filterParams = getFilterParams().join('&');
                    
                    if (DEBUG_MODE) {
                        console.log('Reloading page with filter parameters:', filterParams);
                    }
                    
                    // Store the current filters in sessionStorage so they persist across the reload
                    sessionStorage.setItem('calendarFilters', JSON.stringify(activeFilters));
                    
                    // Reload the page with the filter parameters
                    window.location.href = `${window.location.pathname}?${filterParams}`;
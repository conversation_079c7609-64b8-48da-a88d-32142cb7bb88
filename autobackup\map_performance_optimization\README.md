# Map Performance Optimization

## Issue
Browser console showing performance warning:
```
[Violation] Forced reflow while executing JavaScript took 33ms
```

This indicates that JavaScript was causing the browser to recalculate layout/styling synchronously, causing UI lag.

## Root Causes Identified

### 1. **Multiple DOM Appends in Loop**
- **Problem**: Adding event list items one by one with `appendChild()` in a loop
- **Impact**: Each append caused a reflow, multiplied by number of events
- **Location**: `displayEvents()` function event list creation

### 2. **Synchronous DOM Style Changes**
- **Problem**: Multiple style property changes in sequence
- **Impact**: Each style change triggered layout recalculation
- **Location**: `updatePaginationControls()` function

### 3. **Inefficient DOM Clearing**
- **Problem**: Using `innerHTML = ''` to clear event list
- **Impact**: Forces parser to process empty string and rebuild DOM
- **Location**: Event list clearing in `displayEvents()`

### 4. **Synchronous Scroll Operations**
- **Problem**: `scrollIntoView()` called immediately after DOM changes
- **Impact**: Forces layout calculation before scroll
- **Location**: `highlightEventInList()` function

## Optimizations Applied

### 1. **DocumentFragment for Batch DOM Operations**
```javascript
// Before: Multiple reflows
sortedListEvents.forEach(event => {
    const eventItem = document.createElement('div');
    // ... setup eventItem
    eventList.appendChild(eventItem); // Reflow on each iteration
});

// After: Single reflow
const fragment = document.createDocumentFragment();
sortedListEvents.forEach(event => {
    const eventItem = document.createElement('div');
    // ... setup eventItem
    fragment.appendChild(eventItem); // No reflow
});
eventList.appendChild(fragment); // Single reflow
```

### 2. **RequestAnimationFrame for Style Changes**
```javascript
// Before: Immediate style changes
topControls.style.display = 'none';
bottomControls.style.display = 'none';
topNavigation.style.display = 'none';
bottomNavigation.style.display = 'none';

// After: Batched in next frame
requestAnimationFrame(() => {
    topControls.style.display = 'none';
    bottomControls.style.display = 'none';
    topNavigation.style.display = 'none';
    bottomNavigation.style.display = 'none';
});
```

### 3. **Efficient DOM Clearing**
```javascript
// Before: Parser overhead
eventList.innerHTML = '';

// After: Direct DOM method
eventList.replaceChildren();
```

### 4. **Deferred Scroll Operations**
```javascript
// Before: Immediate scroll after DOM changes
eventItem.classList.add('highlighted');
eventItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

// After: Deferred to avoid forced reflow
requestAnimationFrame(() => {
    eventItem.classList.add('highlighted');
    requestAnimationFrame(() => {
        eventItem.scrollIntoView({ behavior: 'smooth', block: 'center' });
    });
});
```

### 5. **Optimized Timing**
```javascript
// Before: Long timeout
setTimeout(() => {
    captureCurrentFilteredView();
}, 500);

// After: Optimized with requestAnimationFrame
requestAnimationFrame(() => {
    setTimeout(() => {
        captureCurrentFilteredView();
    }, 100);
});
```

## Performance Benefits

1. **Reduced Reflows**: From multiple reflows per event to single reflow per batch
2. **Better Frame Timing**: Operations aligned with browser's rendering cycle
3. **Smoother Animations**: Reduced jank during DOM updates
4. **Lower CPU Usage**: Less forced layout calculations
5. **Better User Experience**: More responsive interface during event loading

## Technical Details

### Browser Reflow Triggers Eliminated
- Multiple `appendChild()` calls in loops
- Sequential style property changes
- `innerHTML` assignments for clearing
- Immediate scroll operations after DOM changes

### Modern APIs Utilized
- `DocumentFragment` for batch DOM operations
- `requestAnimationFrame()` for frame-aligned operations
- `replaceChildren()` for efficient DOM clearing

## Files Modified
- `views/calendar/map.php` - All performance optimizations

## Testing
- Monitor browser console for reflow warnings
- Test with large numbers of events (100+)
- Verify smooth scrolling and highlighting
- Check pagination control updates
- Confirm map view capture timing
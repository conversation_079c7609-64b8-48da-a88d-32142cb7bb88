<?php require APPROOT . '/views/includes/header.php'; ?>



<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1><?php echo $data['title']; ?></h1>
            <p class="lead">Welcome to your coordinator dashboard. Here you can manage your shows and events.</p>
            
            <?php flash('coordinator_message'); ?>
            
            <!-- Quick Links -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/createShow" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <span>Create New Show</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/payment/myPaymentSettings" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-credit-card fa-2x mb-2"></i>
                                <span>My Payment Settings</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-user-cog fa-2x mb-2"></i>
                                <span>My Profile</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/coordinator/reports" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-chart-bar fa-2x mb-2"></i>
                                <span>Reports</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Your Shows</h5>
                    <a href="<?php echo URLROOT; ?>/coordinator/createShow" class="btn btn-light btn-sm">
                        <i class="fas fa-plus"></i> Create New Show
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($data['shows'])) : ?>
                        <div class="alert alert-info">
                            You don't have any shows yet. Click the "Create New Show" button to get started.
                        </div>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Show Name</th>
                                        <th>Date</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Registrations</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['shows'] as $show) : ?>
                                        <tr>
                                            <td><?php echo $show->name; ?></td>
                                            <td><?php echo isset($show->start_date) ? date('F j, Y', strtotime($show->start_date)) : 'Not set'; ?></td>
                                            <td><?php echo $show->location; ?></td>
                                            <td>
                                                <?php if (isset($show->start_date) && strtotime($show->start_date) > time()) : ?>
                                                    <span class="badge bg-info">Upcoming</span>
                                                <?php elseif (isset($show->start_date) && strtotime($show->start_date) == strtotime(date('Y-m-d'))) : ?>
                                                    <span class="badge bg-success">Today</span>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary">Past</span>
                                                <?php endif; ?>
                                                
                                                <?php if (isset($show->status) && $show->status == 'published') : ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else : ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo $show->registration_count ?? 0; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo URLROOT; ?>/coordinator/show/<?php echo $show->id; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <a href="<?php echo URLROOT; ?>/coordinator/editShow/<?php echo $show->id; ?>" class="btn btn-sm btn-secondary">
                                                        <i class="fas fa-edit"></i> Edit
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteShowModal<?php echo $show->id; ?>">
                                                        <i class="fas fa-trash"></i> Delete
                                                    </button>
                                                    <?php if (isset($show->fan_voting_enabled) && $show->fan_voting_enabled): ?>
                                                        <div class="btn-group">
                                                            <button type="button" class="btn btn-sm btn-success dropdown-toggle" data-bs-toggle="dropdown" data-bs-boundary="viewport" aria-expanded="false">
                                                                <i class="fas fa-thumbs-up"></i> Fan Voting
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/coordinator/fanVotes/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-chart-bar"></i> View Votes
                                                                </a></li>
                                                                <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/coordinator/qrCodes/<?php echo $show->id; ?>">
                                                                    <i class="fas fa-id-card"></i> Registration Cards
                                                                </a></li>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                                
                                                <!-- Delete Show Modal -->
                                                <div class="modal fade" id="deleteShowModal<?php echo $show->id; ?>" tabindex="-1" aria-labelledby="deleteShowModalLabel<?php echo $show->id; ?>" aria-hidden="true">
                                                    <div class="modal-dialog">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h5 class="modal-title" id="deleteShowModalLabel<?php echo $show->id; ?>">Confirm Delete</h5>
                                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                            </div>
                                                            <div class="modal-body">
                                                                Are you sure you want to delete the show "<?php echo $show->name; ?>"? This action cannot be undone.
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <form action="<?php echo URLROOT; ?>/coordinator/deleteShow/<?php echo $show->id; ?>" method="post">
                                                                    <?php echo csrfTokenField(); ?>
                                                                    <button type="submit" class="btn btn-danger">Delete</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Payment Settings -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Payment Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p>Configure your default payment credentials for receiving registration fees from car owners.</p>
                            <p>These settings will be used for all your shows unless you specify different credentials for a specific show.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?php echo URLROOT; ?>/payment/coordinatorSettings" class="btn btn-primary">
                                <i class="fas fa-credit-card me-2"></i> Manage Payment Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Registration Card Management -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Registration Card Management</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <p>Print registration cards for vehicles with completed registration and payment.</p>
                            <p>Registration cards provide vehicle information and QR codes that <strong>must be scanned for voting</strong>.</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <a href="<?php echo URLROOT; ?>/coordinator/manageQrCodes" class="btn btn-primary">
                                <i class="fas fa-id-card me-2"></i> Manage Registration Cards
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Test Category Fix
 * 
 * Quick test to verify the category orphan check now uses the correct tables
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Check if user is admin (following your site's pattern)
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>🧪 Category Fix Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
    .error { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0; }
    .info { background: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 10px 0; }
    .warning { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>";

try {
    // Test database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "</div>";
    
    // Check what category tables exist
    echo "<h2>🔍 Checking Category Tables</h2>";
    
    $tables = $pdo->query("SHOW TABLES LIKE '%categor%'")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='info'>";
    echo "<h3>📋 Category-related Tables Found</h3>";
    if (!empty($tables)) {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li><strong>$table</strong></li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No category tables found.</p>";
    }
    echo "</div>";
    
    // Test each category table
    foreach ($tables as $table) {
        echo "<h3>📊 Table: $table</h3>";
        
        try {
            $count = $pdo->query("SELECT COUNT(*) as count FROM `$table`")->fetch()['count'];
            $columns = $pdo->query("DESCRIBE `$table`")->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='success'>";
            echo "<h4>✅ $table accessible</h4>";
            echo "<p><strong>Row count:</strong> $count</p>";
            echo "<p><strong>Columns:</strong> ";
            $columnNames = array_column($columns, 'Field');
            echo implode(', ', $columnNames);
            echo "</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h4>❌ Error accessing $table</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    // Test the fixed category orphan check
    echo "<h2>🔧 Testing Fixed Category Orphan Check</h2>";
    
    // Test if registrations table has category_id
    try {
        $regColumns = $pdo->query("DESCRIBE registrations")->fetchAll(PDO::FETCH_ASSOC);
        $hasCategoryId = false;
        
        foreach ($regColumns as $col) {
            if ($col['Field'] === 'category_id') {
                $hasCategoryId = true;
                break;
            }
        }
        
        if ($hasCategoryId) {
            echo "<div class='success'>";
            echo "<h3>✅ Registrations table has category_id column</h3>";
            echo "</div>";
            
            // Test the fixed orphan check query
            if (in_array('show_categories', $tables)) {
                echo "<h3>🔍 Testing Fixed Orphan Check Query</h3>";
                
                try {
                    $orphanedByCategory = $pdo->query("
                        SELECT r.id, r.show_id, r.vehicle_id, r.owner_id, r.category_id
                        FROM registrations r
                        LEFT JOIN show_categories sc ON r.category_id = sc.id
                        WHERE r.category_id IS NOT NULL AND sc.id IS NULL
                        LIMIT 5
                    ")->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<div class='success'>";
                    echo "<h4>✅ Fixed Category Orphan Check Works</h4>";
                    echo "<p>Found " . count($orphanedByCategory) . " registrations with invalid categories</p>";
                    
                    if (!empty($orphanedByCategory)) {
                        echo "<table>";
                        echo "<tr><th>Registration ID</th><th>Show ID</th><th>Vehicle ID</th><th>Owner ID</th><th>Category ID</th></tr>";
                        foreach ($orphanedByCategory as $reg) {
                            echo "<tr>";
                            echo "<td>{$reg['id']}</td>";
                            echo "<td>{$reg['show_id']}</td>";
                            echo "<td>{$reg['vehicle_id']}</td>";
                            echo "<td>{$reg['owner_id']}</td>";
                            echo "<td>{$reg['category_id']} (missing)</td>";
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "<div class='error'>";
                    echo "<h4>❌ Fixed query still has issues</h4>";
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "</div>";
                }
                
            } else {
                echo "<div class='warning'>";
                echo "<h3>⚠️ show_categories table not found</h3>";
                echo "<p>The registrations table has category_id but show_categories table doesn't exist.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div class='info'>";
            echo "<h3>ℹ️ Registrations table doesn't have category_id column</h3>";
            echo "<p>Category orphan checking is not applicable.</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Error checking registrations table</h3>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    // Test the old broken query for comparison
    echo "<h2>🔄 Comparing Old vs Fixed Query</h2>";
    
    echo "<h3>❌ Old Broken Query (should fail)</h3>";
    try {
        $oldResult = $pdo->query("
            SELECT COUNT(*) as count
            FROM registrations r
            LEFT JOIN categories c ON r.category_id = c.id
            WHERE r.category_id IS NOT NULL AND c.id IS NULL
        ")->fetch()['count'];
        
        echo "<div class='warning'>";
        echo "<h4>⚠️ Old query unexpectedly worked</h4>";
        echo "<p>Found $oldResult orphaned categories using old query</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='success'>";
        echo "<h4>✅ Old query correctly failed</h4>";
        echo "<p>Error (expected): " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<h3>✅ New Fixed Query</h3>";
    if (in_array('show_categories', $tables)) {
        try {
            $newResult = $pdo->query("
                SELECT COUNT(*) as count
                FROM registrations r
                LEFT JOIN show_categories sc ON r.category_id = sc.id
                WHERE r.category_id IS NOT NULL AND sc.id IS NULL
            ")->fetch()['count'];
            
            echo "<div class='success'>";
            echo "<h4>✅ New query works correctly</h4>";
            echo "<p>Found $newResult registrations with invalid categories</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='error'>";
            echo "<h4>❌ New query has issues</h4>";
            echo "<p>Error: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div class='info'>";
        echo "<h4>ℹ️ Cannot test new query</h4>";
        echo "<p>show_categories table not found</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Test Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<div class='info'>";
echo "<p><strong>Issue Fixed:</strong> The category orphan check now uses 'show_categories' instead of the non-existent 'categories' table.</p>";
echo "<p><strong>Your Category Structure:</strong></p>";
echo "<ul>";
echo "<li><strong>default_categories</strong> - Template categories for reuse across shows</li>";
echo "<li><strong>show_categories</strong> - Specific categories for each show (what registrations reference)</li>";
echo "</ul>";
echo "<p><strong>Next Step:</strong> <a href='scripts/database_maintenance.php?task=check'>Run the fixed maintenance check</a></p>";
echo "</div>";

echo "<p><a href='test_registration_orphan_check.php'>← Back to Registration Orphan Test</a></p>";
?>
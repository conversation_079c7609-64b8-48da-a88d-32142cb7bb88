<?php
/**
 * Add PWA Settings to System Settings
 * Run this script after creating the PWA tables to add the necessary settings
 */

// Include the database configuration
require_once dirname(dirname(__FILE__)) . '/config/config.php';
require_once dirname(dirname(__FILE__)) . '/libraries/Database.php';

try {
    $db = new Database();
    
    // First, check the structure of system_settings table
    echo "Checking system_settings table structure...\n";
    $db->query("DESCRIBE system_settings");
    $columns = $db->resultSet();
    
    $hasSettingType = false;
    $hasCreatedAt = false;
    $hasUpdatedAt = false;
    
    foreach ($columns as $column) {
        echo "Column: {$column->Field} ({$column->Type})\n";
        if ($column->Field === 'setting_type') {
            $hasSettingType = true;
        }
        if ($column->Field === 'created_at') {
            $hasCreatedAt = true;
        }
        if ($column->Field === 'updated_at') {
            $hasUpdatedAt = true;
        }
    }
    
    // Define PWA settings
    $pwaSettings = [
        'pwa_enabled' => ['1', 'Enable Progressive Web App features'],
        'push_notifications_enabled' => ['1', 'Enable push notifications'],
        'offline_mode_enabled' => ['1', 'Enable offline functionality'],
        'pwa_app_name' => ['RER Events', 'PWA application name'],
        'pwa_short_name' => ['RER Events', 'PWA short name'],
        'pwa_description' => ['Rowan Elite Rides Events & Shows Management', 'PWA description'],
        'pwa_theme_color' => ['#dc3545', 'PWA theme color'],
        'pwa_background_color' => ['#ffffff', 'PWA background color'],
        'vapid_public_key' => ['', 'VAPID public key for push notifications'],
        'vapid_private_key' => ['', 'VAPID private key for push notifications (encrypted)'],
        'pwa_migration_version' => ['1.0.0', 'PWA features migration version']
    ];
    
    echo "\nAdding PWA settings...\n";
    
    foreach ($pwaSettings as $key => $data) {
        $value = $data[0];
        $description = $data[1];
        
        // Build the INSERT query based on available columns
        $columns = ['setting_key', 'setting_value'];
        $values = [':setting_key', ':setting_value'];
        $bindings = [
            ':setting_key' => $key,
            ':setting_value' => $value
        ];
        
        if (isset($columns) && in_array('setting_description', array_column($columns, 'Field'))) {
            $columns[] = 'setting_description';
            $values[] = ':setting_description';
            $bindings[':setting_description'] = $description;
        }
        
        if ($hasSettingType) {
            $columns[] = 'setting_type';
            $values[] = ':setting_type';
            $bindings[':setting_type'] = 'text';
        }
        
        if ($hasCreatedAt) {
            $columns[] = 'created_at';
            $values[] = 'NOW()';
        }
        
        $sql = "INSERT IGNORE INTO system_settings (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ")";
        
        $db->query($sql);
        foreach ($bindings as $param => $val) {
            $db->bind($param, $val);
        }
        
        if ($db->execute()) {
            echo "✓ Added setting: $key\n";
        } else {
            echo "✗ Failed to add setting: $key\n";
        }
    }
    
    echo "\nPWA settings installation completed!\n";
    echo "You can now use the PWA features.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration and try again.\n";
}
?>
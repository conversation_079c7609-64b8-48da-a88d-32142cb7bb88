<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Map Tools</h1>
            <p class="text-muted">Geocoding tools and map utilities for event locations</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings_map" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Map Settings
            </a>
        </div>
    </div>

    <!-- Map Tools -->
    <div class="row g-4 mb-5">
        
        <!-- Enhanced Geocoding Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-map-marker-alt text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Enhanced Geocoding</h4>
                    </div>
                    <p class="card-text text-muted">Use the enhanced geocoding tool to add coordinates to events that don't have them.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/geocodeEvents" class="stretched-link text-decoration-none">
                        <span class="d-none">Enhanced Geocoding Tool</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Batch Geocoding Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-map-marker-alt text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Batch Geocoding</h4>
                    </div>
                    <p class="card-text text-muted">Use the batch geocoding tool to add coordinates to venues and events that don't have them.</p>
                    <a href="<?php echo BASE_URL; ?>/calendar/batchGeocode" class="stretched-link text-decoration-none">
                        <span class="d-none">Batch Geocoding Tool</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Map View Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-map text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Map View</h4>
                    </div>
                    <p class="card-text text-muted">View all events on the map to verify locations and settings.</p>
                    <a href="<?php echo BASE_URL; ?>/calendar/map" class="stretched-link text-decoration-none">
                        <span class="d-none">View Map</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> About Map Tools</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Geocoding Tools</h5>
                            <p>Geocoding converts addresses into geographic coordinates (latitude and longitude) that can be used to place markers on maps.</p>
                            
                            <h6>Enhanced Geocoding</h6>
                            <ul>
                                <li>Individual event geocoding</li>
                                <li>Manual coordinate entry</li>
                                <li>Address validation</li>
                                <li>Preview before saving</li>
                            </ul>
                            
                            <h6>Batch Geocoding</h6>
                            <ul>
                                <li>Process multiple events at once</li>
                                <li>Venue coordinate assignment</li>
                                <li>Bulk address processing</li>
                                <li>Progress tracking</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Map Verification</h5>
                            <p>Use the map view to verify that events are properly located and that your map settings are working correctly.</p>
                            
                            <h6>What to Check</h6>
                            <ul>
                                <li>Event markers appear correctly</li>
                                <li>Map provider is working</li>
                                <li>Zoom levels are appropriate</li>
                                <li>Location accuracy</li>
                            </ul>
                            
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Note:</strong> Geocoding requires a properly configured map provider with valid API keys.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
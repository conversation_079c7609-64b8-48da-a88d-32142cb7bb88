<?php
/**
 * Form Designer Model
 * 
 * This model handles all database operations related to form design.
 */
class FormDesignerModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Get default fields by type (internal method)
     * 
     * @param string $type Form type (show, vehicle, admin_show)
     * @return array Default fields
     */
    private function getDefaultFieldsByType($type) {
        switch ($type) {
            case 'admin_show':
                return [
                    [
                        'id' => 'name',
                        'type' => 'text',
                        'label' => 'Show Name',
                        'required' => true
                    ],
                    [
                        'id' => 'location',
                        'type' => 'text',
                        'label' => 'Location',
                        'required' => true
                    ],
                    [
                        'id' => 'description',
                        'type' => 'textarea',
                        'label' => 'Description',
                        'required' => false
                    ],
                    [
                        'id' => 'start_date',
                        'type' => 'date',
                        'label' => 'Start Date',
                        'required' => true
                    ],
                    [
                        'id' => 'end_date',
                        'type' => 'date',
                        'label' => 'End Date',
                        'required' => true
                    ],
                    [
                        'id' => 'registration_start',
                        'type' => 'date',
                        'label' => 'Registration Start',
                        'required' => true
                    ],
                    [
                        'id' => 'registration_end',
                        'type' => 'date',
                        'label' => 'Registration End',
                        'required' => true
                    ],
                ];
        }
    }
}
<?php
/**
 * Setup Test Banners - Creates exact same banners for testing
 */

// Define the application root directory
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load required classes
require_once APPROOT . '/core/Database.php';

try {
    $database = new Database();
    $db = $database;
    
    echo "<h2>Setting Up Test Banners</h2>";
    
    // First, clear existing banners
    echo "<p>Clearing existing banners...</p>";
    $db->query("DELETE FROM camera_banners");
    $db->execute();

    // Insert exactly 3 test banners
    echo "<p>Adding 3 test banners...</p>";

    $banners = [
        ['text' => 'Banner 1: Welcome to our Event Platform!', 'sort_order' => 1],
        ['text' => 'Banner 2: Check out our upcoming events!', 'sort_order' => 2],
        ['text' => 'Banner 3: Register your vehicle today!', 'sort_order' => 3]
    ];

    foreach ($banners as $banner) {
        $db->query("INSERT INTO camera_banners (type, text, active, sort_order) VALUES ('text', :text, 1, :sort_order)");
        $db->bind(':text', $banner['text']);
        $db->bind(':sort_order', $banner['sort_order']);
        $db->execute();
        echo "<p>✅ Added: {$banner['text']}</p>";
    }

    // Set delay to 3 seconds for faster testing
    echo "<p>Setting banner delay to 3 seconds...</p>";
    $db->query("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, description) VALUES ('camera_banner_delay', '3000', 'Banner rotation delay in milliseconds for camera modals', 'media', 'Camera banner delay') ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)");
    $db->execute();
    
    echo "<h3>✅ Setup Complete!</h3>";
    echo "<p><strong>We now have:</strong></p>";
    echo "<ul>";
    echo "<li>3 text banners with clear names</li>";
    echo "<li>3 second rotation delay (faster testing)</li>";
    echo "<li>All banners active</li>";
    echo "</ul>";
    
    echo "<h3>Expected Behavior:</h3>";
    echo "<ol>";
    echo "<li><strong>Logo shows first</strong> (5 seconds)</li>";
    echo "<li><strong>Banner 1</strong> appears (3 seconds)</li>";
    echo "<li><strong>Banner 2</strong> appears (3 seconds)</li>";
    echo "<li><strong>Banner 3</strong> appears (3 seconds)</li>";
    echo "<li><strong>Back to Banner 1</strong> (cycle repeats)</li>";
    echo "</ol>";
    
    echo "<h3>Debug Messages You Should See:</h3>";
    echo "<p><strong>When camera opens:</strong></p>";
    echo "<ul>";
    echo "<li>\"Banners loaded: 4 total, 3 for rotation\"</li>";
    echo "<li>\"Starting rotation in 5 seconds for 3 banners\"</li>";
    echo "</ul>";
    
    echo "<p><strong>After 5 seconds:</strong></p>";
    echo "<ul>";
    echo "<li>\"Rotation started! Cycling through 3 banners every 3000ms\"</li>";
    echo "<li>\"🚀 showBanner() CALLED!\"</li>";
    echo "<li>\"🔄 Displaying: text - 'Banner 1: Welcome...'\"</li>";
    echo "<li>\"📝 Showing text: 'Banner 1: Welcome...'\"</li>";
    echo "</ul>";
    
    echo "<p><strong>Every 3 seconds after that:</strong></p>";
    echo "<ul>";
    echo "<li>\"🚀 showBanner() CALLED!\"</li>";
    echo "<li>\"🔄 Displaying: text - 'Banner 2: Check out...'\"</li>";
    echo "<li>\"📝 Showing text: 'Banner 2: Check out...'\"</li>";
    echo "<li>(then Banner 3, then back to Banner 1, etc.)</li>";
    echo "</ul>";
    
    echo "<hr>";
    echo "<p><strong>Now test the camera and tell me:</strong></p>";
    echo "<ol>";
    echo "<li>Do you see the logo first?</li>";
    echo "<li>Do you see \"Banners loaded: 4 total, 3 for rotation\"?</li>";
    echo "<li>After 5 seconds, do you see \"🚀 showBanner() CALLED!\"?</li>";
    echo "<li>Do you see the text change from logo to \"Banner 1: Welcome...\"?</li>";
    echo "<li>Every 3 seconds, do you see more \"🚀 showBanner() CALLED!\" messages?</li>";
    echo "<li>Does the text actually change on screen to Banner 2, Banner 3, etc.?</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h2, h3 { color: #333; }
    ul, ol { margin: 10px 0; }
    li { margin: 5px 0; }
    p { margin: 8px 0; }
</style>

<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Payment Settings</h1>
            <p class="text-muted">Configure payment methods and settings for <?php echo $data['show']->name; ?></p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/show/manage/<?php echo $data['show']->id; ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Show
            </a>
        </div>
    </div>

    <?php flash('settings_success'); ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary bg-opacity-10 py-3">
                    <h5 class="mb-0 text-primary"><i class="fas fa-credit-card me-2"></i> Payment Methods</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/payment/showSettings/<?php echo $data['show']->id; ?>" method="post">
                        <?php echo generateCsrfToken(); ?>
                        
                        <p class="mb-3">Select which payment methods will be available for this show:</p>
                        
                        <div class="row mb-4">
                            <?php foreach ($data['all_payment_methods'] as $method): ?>
                                <div class="col-md-4 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="payment_methods[]" 
                                            value="<?php echo $method->id; ?>" id="method_<?php echo $method->id; ?>"
                                            <?php 
                                            $selected = false;
                                            foreach ($data['show_payment_methods'] as $showMethod) {
                                                if ($showMethod->id == $method->id) {
                                                    $selected = true;
                                                    break;
                                                }
                                            }
                                            echo $selected ? 'checked' : '';
                                            ?>>
                                        <label class="form-check-label" for="method_<?php echo $method->id; ?>">
                                            <?php echo $method->name; ?>
                                            <?php if (!$method->is_active): ?>
                                                <span class="badge bg-warning text-dark">Inactive</span>
                                            <?php endif; ?>
                                        </label>
                                        <p class="text-muted small mb-0"><?php echo $method->description; ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <div class="accordion mb-4" id="paymentSettingsAccordion">
                            <!-- PayPal Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="paypalHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#paypalCollapse" aria-expanded="false" aria-controls="paypalCollapse">
                                        <i class="fab fa-paypal me-2"></i> PayPal Settings
                                    </button>
                                </h2>
                                <div id="paypalCollapse" class="accordion-collapse collapse" aria-labelledby="paypalHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                            <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" 
                                                value="<?php echo isset($data['payment_settings']['paypal_client_id']) ? $data['payment_settings']['paypal_client_id'] : ''; ?>">
                                            <div class="form-text">Your PayPal Client ID for processing registration payments.</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="paypal_secret" class="form-label">PayPal Secret</label>
                                            <input type="password" class="form-control" id="paypal_secret" name="paypal_secret" 
                                                value="<?php echo isset($data['payment_settings']['paypal_secret']) ? $data['payment_settings']['paypal_secret'] : ''; ?>">
                                            <div class="form-text">Your PayPal Secret for processing registration payments.</div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="paypal_sandbox" name="paypal_sandbox" value="true"
                                                    <?php echo (isset($data['payment_settings']['paypal_sandbox']) && $data['payment_settings']['paypal_sandbox'] === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="paypal_sandbox">Use PayPal Sandbox (Testing Mode)</label>
                                            </div>
                                            <div class="form-text">Enable this for testing payments without real money.</div>
                                        </div>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> These PayPal credentials will be used to receive registration payments from car owners. They are separate from the admin's credentials used for listing fees.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cash App Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="cashappHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#cashappCollapse" aria-expanded="false" aria-controls="cashappCollapse">
                                        <i class="fas fa-dollar-sign me-2"></i> Cash App Settings
                                    </button>
                                </h2>
                                <div id="cashappCollapse" class="accordion-collapse collapse" aria-labelledby="cashappHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="cashapp_id" class="form-label">Cash App ID</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="text" class="form-control" id="cashapp_id" name="cashapp_id" 
                                                    value="<?php echo isset($data['payment_settings']['cashapp_id']) ? $data['payment_settings']['cashapp_id'] : ''; ?>">
                                            </div>
                                            <div class="form-text">Your Cash App $Cashtag for receiving payments.</div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Cash App by sending money to this $Cashtag.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Venmo Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="venmoHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#venmoCollapse" aria-expanded="false" aria-controls="venmoCollapse">
                                        <i class="fab fa-vimeo-v me-2"></i> Venmo Settings
                                    </button>
                                </h2>
                                <div id="venmoCollapse" class="accordion-collapse collapse" aria-labelledby="venmoHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="venmo_id" class="form-label">Venmo ID</label>
                                            <div class="input-group">
                                                <span class="input-group-text">@</span>
                                                <input type="text" class="form-control" id="venmo_id" name="venmo_id" 
                                                    value="<?php echo isset($data['payment_settings']['venmo_id']) ? $data['payment_settings']['venmo_id'] : ''; ?>">
                                            </div>
                                            <div class="form-text">Your Venmo username for receiving payments.</div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Venmo by sending money to this username.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> If you don't set specific payment credentials for this show, the system will use the global payment settings.
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Payment Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
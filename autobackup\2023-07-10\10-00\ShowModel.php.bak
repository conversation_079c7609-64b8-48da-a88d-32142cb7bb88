<?php
/**
 * Show Model
 * 
 * This model handles all database operations related to car shows.
 * 
 * Version 3.34.10 - Fixed PHP 8.4 deprecation warning
 * - Added proper declaration for $eventTrigger property to fix PHP 8.4 deprecation warning
 * 
 * Version 3.34.9 - Added event trigger support
 * - Added integration with EventTrigger for scheduled tasks
 * - Modified updateStatus method to trigger events on status changes
 * 
 * Version 2.19.95 - Fixed duplicate method declaration
 * - Fixed fatal error: "Cannot redeclare ShowModel::tableExists()" in ShowModel.php
 * - Removed duplicate tableExists() method declaration at the end of ShowModel class
 * - Maintained consistent database table existence checking functionality
 * 
 * Version 2.19.90 - Added age weights management functionality
 * - Added getAgeWeights method to retrieve age weights for a show
 * - Added createAgeWeight method to create new age weights
 * - Added updateAgeWeight method to update existing age weights
 * - Added deleteAgeWeight method to delete age weights
 * - Added createAgeWeightsTable method to create the age_weights table if it doesn't exist
 * - Added tableExists method to check if a table exists in the database
 * 
 * Version 2.19.94 - Fixed issues with missing methods and deprecated properties
 * - Added assignJudge method to fix fatal error in CoordinatorController
 * - Added removeJudgeAssignment method to fix fatal error in CoordinatorController
 * - Added category support to judging metrics
 * - Enhanced createJudgingMetricsTable to add category_id column
 * - Updated createMetric and updateMetric to handle category_id
 * - Added getMetricById method to fix fatal error in CoordinatorController
 * - Added updateMetric method to support editing metrics
 * - Added createMetric method to support adding new metrics
 * - Added getJudgeAssignments method to fix fatal error in CoordinatorController
 * - Added getShowCategories and getJudgingMetrics methods
 * - Replaced calls to EmergencyFormFieldManager::detectFieldType with our own implementation
 * - Fixed all occurrences of $standardFields and $systemFields
 * - Added proper variable initialization
 * - Fixed duplicate method declaration for detectFieldType
 * - Added missing property declaration for $formFieldManager
 * - Added missing getUserCompletedShows method with proper error handling
 */
class ShowModel {
    private $db;
    private $customFieldValuesModel;
    private $emergencyFormFieldManager;
    private $formFieldManager; // Added to fix deprecation warning
    private $eventTrigger; // Added to fix PHP 8.4 deprecation of dynamic properties
    private $systemFields = ['id', 'created_at', 'updated_at'];
    private $standardFields = [
        'name', 'description', 'location', 'start_date', 'end_date', 
        'registration_start', 'registration_end', 'coordinator_id', 
        'status', 'fan_voting_enabled', 'registration_fee', 'is_free', 
        'featured_image_id'
    ];
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize CustomFieldValuesModel if it exists
        if (file_exists(APPROOT . '/models/CustomFieldValuesModel.php')) {
            require_once APPROOT . '/models/CustomFieldValuesModel.php';
            $this->customFieldValuesModel = new CustomFieldValuesModel();
        }
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
        
        // Initialize DynamicFormFieldManager
        if (file_exists(APPROOT . '/models/DynamicFormFieldManager.php')) {
            require_once APPROOT . '/models/DynamicFormFieldManager.php';
            $this->formFieldManager = new DynamicFormFieldManager();
        } else {
            // Fallback to FormFieldManager if needed
            require_once APPROOT . '/models/FormFieldManager.php';
            $this->formFieldManager = new FormFieldManager();
        }
    }
    
    /**
     * Get shows assigned to a judge
     * 
     * @param int $judgeId Judge user ID
     * @return array List of shows
     */
    public function getShowsByJudge($judgeId) {
        try {
            // Check if the judge_assignments table exists
            if (!$this->tableExists('judge_assignments')) {
                error_log("judge_assignments table does not exist");
                return [];
            }
            
            $this->db->query('SELECT s.* FROM shows s 
                              JOIN judge_assignments ja ON s.id = ja.show_id 
                              WHERE ja.judge_id = :judge_id AND s.status = :status 
                              ORDER BY s.start_date DESC');
            $this->db->bind(':judge_id', $judgeId);
            $this->db->bind(':status', 'published');
            
            $result = $this->db->resultSet();
            return is_array($result) ? $result : [];
        } catch (Exception $e) {
            error_log("Error in ShowModel::getShowsByJudge: " . $e->getMessage());
            return [];
        }
    }
}
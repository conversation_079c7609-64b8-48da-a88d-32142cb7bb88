<!DOCTYPE html>
<html>
<head>
    <title>Test Camera Modal Banners</title>
    <style>
        .camera-banner {
            width: 300px;
            height: 100px;
            border: 2px solid #ccc;
            margin: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
        }
        
        .test-container {
            margin: 20px;
            padding: 20px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h2>Test Camera Modal Banners</h2>
    <div id="output"></div>

    <div class="test-container">
        <h3>Simulated Camera Banner Container</h3>
        <div class="camera-banner" id="camera-banner-content">
            <img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Static Logo" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">
        </div>
        <button onclick="testBannerRotation()">Start Banner Rotation</button>
        <button onclick="stopBannerRotation()">Stop Banner Rotation</button>
    </div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        function testBannerRotation() {
            output('=== Testing Banner Rotation ===');
            
            if (window.cameraBanner) {
                output('✓ CameraBanner instance found');
                output('- Version: ' + window.cameraBanner.version);
                output('- Banners loaded: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'none'));
                
                if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                    output('- Starting rotation...');
                    window.cameraBanner.startRotation('camera-banner-content');
                    output('✓ Rotation started');
                } else {
                    output('✗ No banners loaded, trying to load them first...');
                    window.cameraBanner.loadBanners().then(() => {
                        output('✓ Banners loaded: ' + window.cameraBanner.banners.length);
                        output('- Starting rotation...');
                        window.cameraBanner.startRotation('camera-banner-content');
                        output('✓ Rotation started');
                    }).catch(error => {
                        output('✗ Failed to load banners: ' + error.message);
                    });
                }
            } else {
                output('✗ CameraBanner instance not found');
                output('- Checking if script is loaded...');
                
                // Try to load the script
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now();
                script.onload = function() {
                    output('✓ Script loaded, waiting for initialization...');
                    setTimeout(() => {
                        if (window.cameraBanner) {
                            output('✓ CameraBanner now available');
                            testBannerRotation(); // Retry
                        } else {
                            output('✗ CameraBanner still not available');
                        }
                    }, 2000);
                };
                script.onerror = function() {
                    output('✗ Failed to load camera-banner.js');
                };
                document.head.appendChild(script);
            }
        }
        
        function stopBannerRotation() {
            if (window.cameraBanner && typeof window.cameraBanner.stopRotation === 'function') {
                window.cameraBanner.stopRotation();
                output('✓ Banner rotation stopped');
            } else {
                output('✗ Cannot stop rotation - cameraBanner not available');
            }
        }

        // Auto-test when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                output('Page loaded, checking banner system...');
                if (window.cameraBanner) {
                    output('✓ CameraBanner already available');
                    output('- Version: ' + window.cameraBanner.version);
                    output('- Banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'loading...'));
                } else {
                    output('⚠ CameraBanner not yet available, will be loaded on demand');
                }
            }, 1000);
        });
    </script>
</body>
</html>
<?php
/**
 * Payment Model
 * 
 * This model handles all database operations related to payments.
 */
class PaymentModel {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Process a manual payment
     * 
     * @param array $data Payment data
     * @return bool|int
     */
    public function processManualPayment($data) {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Create payment record
            $paymentId = $this->createPayment([
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'payment_method_id' => $data['payment_method_id'],
                'payment_status' => 'completed',
                'payment_reference' => $data['payment_reference'] ?? null,
                'payment_type' => $data['payment_type'],
                'related_id' => $data['related_id'],
                'notes' => $data['notes'] ?? null,
                'is_manual' => 1,
                'processed_by' => $data['processed_by'],
                'admin_notes' => $data['admin_notes'] ?? null
            ]);
            
            if (!$paymentId) {
                throw new Exception('Failed to create payment record');
            }
            
            // Update related record based on payment type
            if ($data['payment_type'] == 'registration') {
                // Update registration payment status
                $this->db->query('UPDATE registrations SET payment_status = :status WHERE id = :id');
                $this->db->bind(':status', 'paid');
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update registration payment status');
                }
            } else if ($data['payment_type'] == 'show_listing') {
                // Update show payment status
                $this->db->query('UPDATE shows SET payment_status = :status WHERE id = :id');
                $this->db->bind(':status', 'paid');
                $this->db->bind(':id', $data['related_id']);
                
                if (!$this->db->execute()) {
                    throw new Exception('Failed to update show payment status');
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return $paymentId;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollback();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error processing manual payment: ' . $e->getMessage());
            }
            return false;
        }
    }
}
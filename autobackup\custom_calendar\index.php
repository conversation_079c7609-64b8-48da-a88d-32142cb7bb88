<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Calendar</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">Manage Calendars</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">Manage Venues</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">Manage Clubs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">Import Events</a></li>
                    <?php if (isAdmin()): ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">Calendar Settings</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Calendars</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['calendars'])): ?>
                        <div class="calendar-list">
                            <?php foreach ($data['calendars'] as $calendar): ?>
                                <div class="form-check">
                                    <input class="form-check-input calendar-toggle" type="checkbox" value="<?php echo $calendar->id; ?>" id="calendar-<?php echo $calendar->id; ?>" checked data-color="<?php echo $calendar->color; ?>">
                                    <label class="form-check-label" for="calendar-<?php echo $calendar->id; ?>">
                                        <span class="color-dot" style="background-color: <?php echo $calendar->color; ?>"></span>
                                        <?php echo $calendar->name; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p>No calendars found.</p>
                        <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-sm btn-primary">Create Calendar</a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body p-0">
                    <div id="upcoming-events" class="list-group list-group-flush">
                        <!-- Upcoming events will be loaded here via JavaScript -->
                        <div class="list-group-item text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group" aria-label="Calendar views">
                            <button type="button" class="btn btn-outline-secondary" id="view-month">Month</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-week">Week</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-day">Day</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-list">List</button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary" id="prev-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="today-btn">Today</button>
                            <button type="button" class="btn btn-outline-secondary" id="next-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="event-details">
                    <div class="mb-3">
                        <h4 id="event-title"></h4>
                        <div class="text-muted">
                            <span id="event-date"></span> • 
                            <span id="event-time"></span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span id="event-calendar"></span>
                            </div>
                            <div class="mb-2" id="event-location-container">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span id="event-location"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2" id="event-show-container">
                                <i class="fas fa-car me-2"></i>
                                <span id="event-show"></span>
                            </div>
                            <div class="mb-2" id="event-url-container">
                                <i class="fas fa-link me-2"></i>
                                <a href="#" id="event-url" target="_blank"></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="event-description-container">
                        <h5>Description</h5>
                        <div id="event-description"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-primary" id="event-view-link">View Details</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">

<!-- Add custom CSS for calendar -->
<style>
    .color-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .calendar-list {
        max-height: 200px;
        overflow-y: auto;
    }
    
    #calendar {
        height: 700px;
    }
    
    .fc-event {
        cursor: pointer;
    }
    
    .fc-toolbar-title {
        font-size: 1.5rem !important;
    }
    
    .upcoming-event {
        border-left: 4px solid #3788d8;
    }
    
    .upcoming-event-title {
        font-weight: 600;
    }
    
    .upcoming-event-time {
        font-size: 0.85rem;
    }
    
    @media (max-width: 768px) {
        #calendar {
            height: 500px;
        }
        
        .fc-toolbar-title {
            font-size: 1.2rem !important;
        }
        
        .fc-toolbar.fc-header-toolbar {
            flex-direction: column;
        }
        
        .fc-toolbar.fc-header-toolbar .fc-toolbar-chunk {
            margin-bottom: 0.5rem;
        }
    }
</style>

<!-- Add FullCalendar JS with all required plugins -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@5.10.1/main.min.js"></script>

<!-- Calendar initialization script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get calendar settings
        const settings = {
            defaultView: '<?php echo isset($data['settings']['default_view']) ? $data['settings']['default_view'] : 'month'; ?>',
            businessHoursStart: '<?php echo isset($data['settings']['business_hours_start']) ? $data['settings']['business_hours_start'] : '09:00:00'; ?>',
            businessHoursEnd: '<?php echo isset($data['settings']['business_hours_end']) ? $data['settings']['business_hours_end'] : '17:00:00'; ?>',
            weekStartsOn: <?php echo isset($data['settings']['week_starts_on']) ? $data['settings']['week_starts_on'] : 0; ?>,
            timeFormat: '<?php echo isset($data['settings']['time_format']) ? $data['settings']['time_format'] : '12'; ?>',
            dateFormat: '<?php echo isset($data['settings']['date_format']) ? $data['settings']['date_format'] : 'MM/DD/YYYY'; ?>',
            defaultEventDuration: <?php echo isset($data['settings']['default_event_duration']) ? $data['settings']['default_event_duration'] : 60; ?>,
            enableDragDrop: <?php echo isset($data['settings']['enable_drag_drop']) && $data['settings']['enable_drag_drop'] ? 'true' : 'false'; ?>,
            enableResize: <?php echo isset($data['settings']['enable_resize']) && $data['settings']['enable_resize'] ? 'true' : 'false'; ?>,
            showWeekends: <?php echo isset($data['settings']['show_weekends']) && $data['settings']['show_weekends'] ? 'true' : 'false'; ?>,
            defaultCalendarColor: '<?php echo isset($data['settings']['default_calendar_color']) ? $data['settings']['default_calendar_color'] : '#3788d8'; ?>'
        };
        
        // Initialize calendar
        const calendarEl = document.getElementById('calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            plugins: [ 'dayGrid', 'timeGrid', 'list', 'interaction' ],
            initialView: settings.defaultView,
            headerToolbar: false, // We're using custom header buttons
            firstDay: settings.weekStartsOn,
            weekends: settings.showWeekends,
            businessHours: {
                daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
                startTime: settings.businessHoursStart,
                endTime: settings.businessHoursEnd
            },
            editable: settings.enableDragDrop,
            eventResizableFromStart: settings.enableResize,
            eventDurationEditable: settings.enableResize,
            selectable: true,
            selectMirror: true,
            dayMaxEvents: true,
            events: {
                url: '<?php echo URLROOT; ?>/calendar/getEvents',
                failure: function() {
                    alert('There was an error loading events!');
                }
            },
            eventTimeFormat: {
                hour: 'numeric',
                minute: '2-digit',
                meridiem: settings.timeFormat === '12'
            },
            select: function(info) {
                // Redirect to create event page with pre-filled dates
                window.location.href = '<?php echo URLROOT; ?>/calendar/createEvent?start=' + info.startStr + '&end=' + info.endStr;
            },
            eventClick: function(info) {
                // Show event details in modal
                showEventDetails(info.event);
            },
            eventDrop: function(info) {
                // Update event dates via AJAX
                updateEventDates(info.event);
            },
            eventResize: function(info) {
                // Update event dates via AJAX
                updateEventDates(info.event);
            },
            loading: function(isLoading) {
                // Show/hide loading indicator
                if (isLoading) {
                    // Add loading indicator if needed
                }
            }
        });
        
        // Render calendar
        calendar.render();
        
        // Set active view button
        const viewButtons = {
            'dayGridMonth': 'view-month',
            'timeGridWeek': 'view-week',
            'timeGridDay': 'view-day',
            'listMonth': 'view-list'
        };
        
        const activeViewButton = viewButtons[calendar.view.type];
        if (activeViewButton) {
            document.getElementById(activeViewButton).classList.add('active');
        }
        
        // Add event listeners to view buttons
        document.getElementById('view-month').addEventListener('click', function() {
            calendar.changeView('dayGridMonth');
            updateActiveViewButton('view-month');
        });
        
        document.getElementById('view-week').addEventListener('click', function() {
            calendar.changeView('timeGridWeek');
            updateActiveViewButton('view-week');
        });
        
        document.getElementById('view-day').addEventListener('click', function() {
            calendar.changeView('timeGridDay');
            updateActiveViewButton('view-day');
        });
        
        document.getElementById('view-list').addEventListener('click', function() {
            calendar.changeView('listMonth');
            updateActiveViewButton('view-list');
        });
        
        // Add event listeners to navigation buttons
        document.getElementById('prev-btn').addEventListener('click', function() {
            calendar.prev();
        });
        
        document.getElementById('today-btn').addEventListener('click', function() {
            calendar.today();
        });
        
        document.getElementById('next-btn').addEventListener('click', function() {
            calendar.next();
        });
        
        // Update active view button
        function updateActiveViewButton(activeId) {
            const viewButtons = ['view-month', 'view-week', 'view-day', 'view-list'];
            
            viewButtons.forEach(function(buttonId) {
                const button = document.getElementById(buttonId);
                if (buttonId === activeId) {
                    button.classList.add('active');
                } else {
                    button.classList.remove('active');
                }
            });
        }
        
        // Handle calendar toggles
        document.querySelectorAll('.calendar-toggle').forEach(function(toggle) {
            toggle.addEventListener('change', function() {
                const calendarId = this.value;
                const isChecked = this.checked;
                
                // Get all visible calendars
                const visibleCalendars = [];
                document.querySelectorAll('.calendar-toggle:checked').forEach(function(checkedToggle) {
                    visibleCalendars.push(checkedToggle.value);
                });
                
                // Reload events with visible calendars
                calendar.removeAllEventSources();
                
                if (visibleCalendars.length > 0) {
                    calendar.addEventSource({
                        url: '<?php echo URLROOT; ?>/calendar/getEvents',
                        extraParams: {
                            calendar_id: visibleCalendars.join(',')
                        }
                    });
                }
                
                // Update upcoming events
                loadUpcomingEvents(visibleCalendars);
            });
        });
        
        // Load upcoming events
        loadUpcomingEvents();
        
        // Function to load upcoming events
        function loadUpcomingEvents(calendarIds = null) {
            const upcomingEventsEl = document.getElementById('upcoming-events');
            
            // Show loading indicator
            upcomingEventsEl.innerHTML = `
                <div class="list-group-item text-center">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span class="ms-2">Loading events...</span>
                </div>
            `;
            
            // Get visible calendars if not provided
            if (!calendarIds) {
                calendarIds = [];
                document.querySelectorAll('.calendar-toggle:checked').forEach(function(toggle) {
                    calendarIds.push(toggle.value);
                });
            }
            
            // Get current date
            const now = new Date();
            const start = now.toISOString().slice(0, 10) + ' 00:00:00';
            const end = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10) + ' 23:59:59';
            
            // Fetch upcoming events
            fetch(`<?php echo URLROOT; ?>/calendar/getEvents?start=${start}&end=${end}&calendar_id=${calendarIds.join(',')}`)
                .then(response => response.json())
                .then(events => {
                    if (events.length === 0) {
                        upcomingEventsEl.innerHTML = `
                            <div class="list-group-item">
                                <p class="mb-0">No upcoming events</p>
                            </div>
                        `;
                        return;
                    }
                    
                    // Sort events by start date
                    events.sort((a, b) => new Date(a.start) - new Date(b.start));
                    
                    // Limit to 10 events
                    events = events.slice(0, 10);
                    
                    let html = '';
                    
                    events.forEach(event => {
                        const startDate = new Date(event.start);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        
                        let dateText;
                        if (startDate.toDateString() === today.toDateString()) {
                            dateText = 'Today';
                        } else if (startDate.toDateString() === new Date(today.getTime() + 86400000).toDateString()) {
                            dateText = 'Tomorrow';
                        } else {
                            dateText = startDate.toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' });
                        }
                        
                        let timeText;
                        if (event.allDay) {
                            timeText = 'All day';
                        } else {
                            timeText = startDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' });
                        }
                        
                        html += `
                            <a href="<?php echo URLROOT; ?>/calendar/event/${event.id}" class="list-group-item list-group-item-action upcoming-event">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1 upcoming-event-title">${event.title}</h6>
                                    <small class="text-muted">${dateText}</small>
                                </div>
                                <div class="d-flex w-100 justify-content-between">
                                    <small class="upcoming-event-time">${timeText}</small>
                                    <small class="text-muted">${event.extendedProps.calendar_name}</small>
                                </div>
                            </a>
                        `;
                    });
                    
                    upcomingEventsEl.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error loading upcoming events:', error);
                    upcomingEventsEl.innerHTML = `
                        <div class="list-group-item">
                            <p class="mb-0 text-danger">Error loading events</p>
                        </div>
                    `;
                });
        }
        
        // Function to show event details in modal
        function showEventDetails(event) {
            // Set event details in modal
            document.getElementById('event-title').textContent = event.title;
            
            // Format date
            const startDate = new Date(event.start);
            const endDate = event.end ? new Date(event.end) : new Date(startDate.getTime() + (settings.defaultEventDuration * 60 * 1000));
            
            let dateText;
            if (startDate.toDateString() === endDate.toDateString()) {
                dateText = startDate.toLocaleDateString(undefined, { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
            } else {
                dateText = `${startDate.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })} - ${endDate.toLocaleDateString(undefined, { month: 'short', day: 'numeric', year: 'numeric' })}`;
            }
            
            document.getElementById('event-date').textContent = dateText;
            
            // Format time
            let timeText;
            if (event.allDay) {
                timeText = 'All day';
            } else {
                timeText = `${startDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })} - ${endDate.toLocaleTimeString(undefined, { hour: 'numeric', minute: '2-digit' })}`;
            }
            
            document.getElementById('event-time').textContent = timeText;
            
            // Set calendar
            document.getElementById('event-calendar').textContent = event.extendedProps.calendar_name || '';
            
            // Set location
            const locationContainer = document.getElementById('event-location-container');
            if (event.extendedProps.location) {
                document.getElementById('event-location').textContent = event.extendedProps.location;
                locationContainer.style.display = 'block';
            } else {
                locationContainer.style.display = 'none';
            }
            
            // Set show
            const showContainer = document.getElementById('event-show-container');
            if (event.extendedProps.show_id) {
                document.getElementById('event-show').textContent = event.extendedProps.show_name || '';
                showContainer.style.display = 'block';
            } else {
                showContainer.style.display = 'none';
            }
            
            // Set URL
            const urlContainer = document.getElementById('event-url-container');
            if (event.url) {
                const urlEl = document.getElementById('event-url');
                urlEl.href = event.url;
                urlEl.textContent = event.url;
                urlContainer.style.display = 'block';
            } else {
                urlContainer.style.display = 'none';
            }
            
            // Set description
            const descriptionContainer = document.getElementById('event-description-container');
            if (event.extendedProps.description) {
                document.getElementById('event-description').innerHTML = event.extendedProps.description;
                descriptionContainer.style.display = 'block';
            } else {
                descriptionContainer.style.display = 'none';
            }
            
            // Set view link
            document.getElementById('event-view-link').href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('eventModal'));
            modal.show();
        }
        
        // Function to update event dates
        function updateEventDates(event) {
            // Format dates for API
            const startStr = event.start.toISOString().slice(0, 19).replace('T', ' ');
            const endStr = event.end ? event.end.toISOString().slice(0, 19).replace('T', ' ') : new Date(event.start.getTime() + (settings.defaultEventDuration * 60 * 1000)).toISOString().slice(0, 19).replace('T', ' ');
            
            // Send update request
            fetch('<?php echo URLROOT; ?>/calendar/updateEventDates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `id=${event.id}&start_date=${startStr}&end_date=${endStr}&<?php echo generateCsrfToken(); ?>`
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Error updating event: ' + data.message);
                    calendar.refetchEvents(); // Reload events to revert changes
                }
            })
            .catch(error => {
                console.error('Error updating event:', error);
                alert('Error updating event. Please try again.');
                calendar.refetchEvents(); // Reload events to revert changes
            });
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
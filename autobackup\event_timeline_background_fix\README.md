# Event Timeline Background Fix

## Issue
The event-timeline area was completely filled with light grey background (#f8f9fa), instead of having only thin colored bars with white space above and below.

## Root Cause
The `.event-timeline` CSS class had `background-color: #f8f9fa` which filled the entire timeline container with grey background.

## Solution
- Remove background-color from `.event-timeline` containers
- Create thin grey bar using CSS ::before pseudo-element
- Bar spans entire week width with 32px height (same as event bars)
- Position bar centered vertically with white space above/below
- Layer event bars on top using z-index

## Final Implementation
- `.event-timeline` background: transparent
- `::before` pseudo-element: creates 32px grey bar spanning week width
- Event bars: z-index 2 to appear above timeline bar
- Result: Thin timeline structure with proper layering

## Files Modified
- public/css/monthly-event-chart.css

## Version
v3.43.1 - Timeline Background Fix
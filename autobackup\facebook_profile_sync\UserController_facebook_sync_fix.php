<?php
/**
 * Backup of the UserController.php file with the Facebook sync button fix
 * 
 * This file contains the syncFacebookImage method that was modified to fix
 * the issue with the Facebook sync button not working correctly.
 */

public function syncFacebookImage() {
    // Always enable debugging for this method
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
    
    // Force error logging to file
    error_log('======= FACEBOOK SYNC STARTED ======= ' . date('Y-m-d H:i:s'));
    
    error_log('UserController::syncFacebookImage - Method called');
    
    // Check if user is logged in
    if (!$this->auth->isLoggedIn()) {
        error_log('UserController::syncFacebookImage - User not logged in');
        $this->redirect('auth/login');
        return;
    }
    
    // Get user ID
    $userId = $this->auth->getCurrentUserId();
    error_log('UserController::syncFacebookImage - User ID: ' . $userId);
    
    // Check if form was submitted
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        error_log('UserController::syncFacebookImage - POST request received');
        
        // Debug CSRF token
        if (isset($_POST[CSRF_TOKEN_NAME])) {
            error_log('UserController::syncFacebookImage - CSRF token in request: ' . $_POST[CSRF_TOKEN_NAME]);
        } else {
            error_log('UserController::syncFacebookImage - No CSRF token in request');
        }
        
        if (isset($_SESSION[CSRF_TOKEN_NAME])) {
            error_log('UserController::syncFacebookImage - CSRF token in session: ' . $_SESSION[CSRF_TOKEN_NAME]);
        } else {
            error_log('UserController::syncFacebookImage - No CSRF token in session');
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            error_log('UserController::syncFacebookImage - Invalid CSRF token');
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        error_log('UserController::syncFacebookImage - CSRF token verified');
        
        // Set a flag in the session to indicate we're in a sync operation
        $_SESSION['facebook_sync_in_progress'] = $userId;
        error_log('UserController::syncFacebookImage - Set facebook_sync_in_progress flag for user ' . $userId);
        
        // Get user data
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            error_log('UserController::syncFacebookImage - User not found: ' . $userId);
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'User not found.');
            }
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - User found: ' . $user->name . ' (ID: ' . $userId . ')');
        
        // Check if user has a Facebook ID
        if (empty($user->facebook_id)) {
            error_log('UserController::syncFacebookImage - User does not have a Facebook ID: ' . $userId);
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Your account is not linked with Facebook.');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - User has Facebook ID: ' . $user->facebook_id);
        
        // Load Facebook service
        require_once APPROOT . '/libraries/facebook/FacebookService.php';
        $fbService = new FacebookService();
        
        // Check if Facebook SDK is available
        if (!$fbService->isSdkAvailable()) {
            error_log('UserController::syncFacebookImage - Facebook SDK not available');
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Facebook integration is not properly configured.');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - Facebook SDK is available');
        
        // Get user's Facebook access token
        $this->db->query('SELECT facebook_token FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        $tokenResult = $this->db->single();
        
        $accessToken = $tokenResult && isset($tokenResult->facebook_token) ? $tokenResult->facebook_token : null;
        
        error_log('UserController::syncFacebookImage - Facebook token for user ' . $userId . ': ' . ($accessToken ? 'Found' : 'Not found'));
        
        // If no token is stored, we need to use the Facebook API to get a new one
        if (!$accessToken) {
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('warning', 'Please log in with Facebook again to update your profile image.');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
            return;
        }
        
        try {
            // Use the Facebook ID directly to get the profile image
            $fbId = $user->facebook_id;
            error_log('UserController::syncFacebookImage - Getting profile picture for Facebook ID: ' . $fbId);
            
            // Construct the Facebook Graph API URL for the profile picture
            $imageUrl = "https://graph.facebook.com/{$fbId}/picture?type=large&width=500&height=500";
            error_log('UserController::syncFacebookImage - Image URL: ' . $imageUrl);
            
            // Load image editor model for storing the image
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();
            
            // First, clear the profile_image field in the users table
            $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
            $this->db->bind(':id', $userId);
            $this->db->execute();
            error_log('UserController::syncFacebookImage - Cleared profile_image field in users table for user ID: ' . $userId);
            
            // Second, clear the profile image cache in the helper if it exists
            if (isset($GLOBALS['profileImageCache']) && isset($GLOBALS['profileImageCache'][$userId])) {
                unset($GLOBALS['profileImageCache'][$userId]);
                error_log('UserController::syncFacebookImage - Cleared profile image cache for user ID: ' . $userId);
            }
            
            // Third, delete any existing profile images from the images table
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $existingImages = $this->db->resultSet();
            
            // Delete each existing image
            foreach ($existingImages as $image) {
                $imageEditorModel->deleteImage($image->id);
                error_log('UserController::syncFacebookImage - Deleted existing image: ' . $image->id);
            }
            
            // Fourth, clear the Facebook download cache for this user to force a new download
            $cacheFile = APPROOT . '/uploads/temp/fb_download_' . $userId . '.txt';
            if (file_exists($cacheFile)) {
                if (unlink($cacheFile)) {
                    error_log('UserController::syncFacebookImage - Deleted Facebook download cache file: ' . $cacheFile);
                } else {
                    error_log('UserController::syncFacebookImage - Failed to delete Facebook download cache file: ' . $cacheFile);
                }
            }
            
            // Download the image
            error_log('UserController::syncFacebookImage - Downloading image from: ' . $imageUrl);
            $imageContent = $this->downloadImage($imageUrl);
            
            if (!$imageContent) {
                error_log('UserController::syncFacebookImage - Failed to download image');
                throw new Exception('Failed to download profile image from Facebook');
            }
            
            // Check if the image content is valid
            error_log('UserController::syncFacebookImage - Image content length: ' . strlen($imageContent) . ' bytes');
            
            // Save the image content to a temporary file for debugging
            $tempDebugFile = APPROOT . '/temp_facebook_image_' . time() . '.jpg';
            file_put_contents($tempDebugFile, $imageContent);
            error_log('UserController::syncFacebookImage - Saved debug image to: ' . $tempDebugFile);
            
            // Verify the image content is valid
            $tempImageCheck = @imagecreatefromstring($imageContent);
            if ($tempImageCheck === false) {
                error_log('UserController::syncFacebookImage - Downloaded content is not a valid image');
                throw new Exception('Downloaded content is not a valid image');
            }
            imagedestroy($tempImageCheck);
            
            // Process the downloaded image directly
            // Use absolute path for upload directory
            $uploadDir = APPROOT . '/uploads/users/';
            error_log('UserController::syncFacebookImage - Upload directory: ' . $uploadDir);
            error_log('UserController::syncFacebookImage - APPROOT: ' . APPROOT);
            
            // Make sure the upload directory exists
            if (!file_exists($uploadDir)) {
                error_log('UserController::syncFacebookImage - Creating upload directory: ' . $uploadDir);
                if (!mkdir($uploadDir, 0755, true)) {
                    error_log('UserController::syncFacebookImage - Failed to create upload directory: ' . $uploadDir);
                    throw new Exception('Failed to create upload directory: ' . $uploadDir);
                }
            }
            
            // Check if the directory is writable
            if (!is_writable($uploadDir)) {
                error_log('UserController::syncFacebookImage - Upload directory is not writable: ' . $uploadDir);
                throw new Exception('Upload directory is not writable: ' . $uploadDir);
            }
            
            // Process the image using our new method for downloaded images
            $result = $imageEditorModel->processDownloadedImage(
                $imageContent,
                'facebook_profile.jpg',
                'user', 
                $userId, 
                $uploadDir,
                $userId,
                true // Set as primary image
            );
            
            error_log('UserController::syncFacebookImage - processDownloadedImage result: ' . ($result ? json_encode($result) : 'false'));
            
            if ($result) {
                error_log('UserController::syncFacebookImage - Successfully synchronized profile image with Facebook');
                // Set success message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('success', 'Profile image synchronized with Facebook successfully.');
                }
            } else {
                error_log('UserController::syncFacebookImage - Failed to synchronize profile image with Facebook');
                throw new Exception('Failed to synchronize profile image with Facebook');
            }
            
        } catch (Exception $e) {
            // Log the error
            error_log('Error synchronizing Facebook profile image: ' . $e->getMessage());
            error_log('Error trace: ' . $e->getTraceAsString());
            
            // Clear the sync flag
            if (isset($_SESSION['facebook_sync_in_progress'])) {
                unset($_SESSION['facebook_sync_in_progress']);
                error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag due to error');
            }
            
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Failed to synchronize profile image with Facebook: ' . $e->getMessage());
            }
        }
        
        // Clear the sync flag if it's still set
        if (isset($_SESSION['facebook_sync_in_progress'])) {
            unset($_SESSION['facebook_sync_in_progress']);
            error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag at end of method');
        }
        
        // Redirect back to profile page
        $this->redirect('user/profile');
    } else {
        // Redirect to profile page if not a POST request
        error_log('UserController::syncFacebookImage - Not a POST request');
        
        // Clear the sync flag if it's somehow set
        if (isset($_SESSION['facebook_sync_in_progress'])) {
            unset($_SESSION['facebook_sync_in_progress']);
            error_log('UserController::syncFacebookImage - Cleared facebook_sync_in_progress flag (not a POST request)');
        }
        
        $this->redirect('user/profile');
    }
}
<?php require APPROOT . '/views/includes/header.php'; ?>

<?php if (isset($data['debug_mode']) && $data['debug_mode']): ?>
<div class="container-fluid px-2 px-md-4 mb-4">
    <div class="card">
        <div class="card-header bg-warning">
            <h3>Debug Information (Admin Only)</h3>
        </div>
        <div class="card-body">
            <h4>Payment Settings Array</h4>
            <pre><?php print_r($data['payment_settings']); ?></pre>
            
            <h4>Full Data Array</h4>
            <pre><?php print_r($data); ?></pre>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">
                <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
                    <span class="badge bg-primary me-2">Admin View</span>
                <?php endif; ?>
                Coordinator Payment Settings
            </h1>
            <p class="text-muted">
                <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
                    View and manage the default payment credentials for coordinators
                <?php else: ?>
                    Configure your default payment credentials for all shows
                <?php endif; ?>
            </p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/user/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <?php flash('settings_success'); ?>
    
    <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="alert-heading">Administrator Notice</h5>
                <p class="mb-0">You are viewing the <strong>coordinator payment settings</strong> as an administrator. Any changes you make here will affect the default settings for all coordinators.</p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary bg-opacity-10 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-credit-card me-2"></i>
                        <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
                            Coordinator Payment Credentials
                        <?php else: ?>
                            Your Payment Credentials
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Coordinator Payment Settings</h5>
                                <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
                                    <p class="mb-0">These settings are for <strong>coordinator payment credentials</strong> to receive registration fees from car owners. They will be used as defaults for all coordinator shows unless different credentials are specified for a specific show.</p>
                                <?php else: ?>
                                    <p class="mb-0">These settings are for <strong>your payment credentials</strong> to receive registration fees from car owners. They will be used as defaults for all your shows unless you specify different credentials for a specific show.</p>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <form action="<?php echo BASE_URL; ?>/payment/coordinatorSettings" method="post">
                        <?php echo generateCsrfToken(); ?>
                        
                        <div class="accordion mb-4" id="paymentSettingsAccordion">
                            <!-- PayPal Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="paypalHeading">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#paypalCollapse" aria-expanded="true" aria-controls="paypalCollapse">
                                        <i class="fab fa-paypal me-2"></i> PayPal Settings
                                    </button>
                                </h2>
                                <div id="paypalCollapse" class="accordion-collapse collapse show" aria-labelledby="paypalHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="paypal_client_id" class="form-label">PayPal Client ID</label>
                                            <input type="text" class="form-control" id="paypal_client_id" name="paypal_client_id" 
                                                value="<?php echo isset($data['payment_settings']['paypal_client_id']) ? $data['payment_settings']['paypal_client_id'] : ''; ?>">
                                            <div class="form-text">Your PayPal Client ID for processing registration payments.</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="paypal_secret" class="form-label">PayPal Secret</label>
                                            <input type="password" class="form-control" id="paypal_secret" name="paypal_secret" 
                                                value="<?php echo isset($data['payment_settings']['paypal_secret']) ? $data['payment_settings']['paypal_secret'] : ''; ?>">
                                            <div class="form-text">Your PayPal Secret for processing registration payments.</div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="paypal_sandbox" name="paypal_sandbox" value="true"
                                                    <?php echo (isset($data['payment_settings']['paypal_sandbox']) && $data['payment_settings']['paypal_sandbox'] === 'true') ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="paypal_sandbox">Use PayPal Sandbox (Testing Mode)</label>
                                            </div>
                                            <div class="form-text">Enable this for testing payments without real money.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Cash App Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="cashappHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#cashappCollapse" aria-expanded="false" aria-controls="cashappCollapse">
                                        <i class="fas fa-dollar-sign me-2"></i> Cash App Settings
                                    </button>
                                </h2>
                                <div id="cashappCollapse" class="accordion-collapse collapse" aria-labelledby="cashappHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="cashapp_id" class="form-label">Cash App ID</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="text" class="form-control" id="cashapp_id" name="cashapp_id" 
                                                    value="<?php echo isset($data['payment_settings']['cashapp_id']) ? $data['payment_settings']['cashapp_id'] : ''; ?>">
                                            </div>
                                            <div class="form-text">Your Cash App $Cashtag for receiving payments.</div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Cash App by sending money to this $Cashtag.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Venmo Settings -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="venmoHeading">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#venmoCollapse" aria-expanded="false" aria-controls="venmoCollapse">
                                        <i class="fab fa-vimeo-v me-2"></i> Venmo Settings
                                    </button>
                                </h2>
                                <div id="venmoCollapse" class="accordion-collapse collapse" aria-labelledby="venmoHeading" data-bs-parent="#paymentSettingsAccordion">
                                    <div class="accordion-body">
                                        <div class="mb-3">
                                            <label for="venmo_id" class="form-label">Venmo ID</label>
                                            <div class="input-group">
                                                <span class="input-group-text">@</span>
                                                <input type="text" class="form-control" id="venmo_id" name="venmo_id" 
                                                    value="<?php echo isset($data['payment_settings']['venmo_id']) ? $data['payment_settings']['venmo_id'] : ''; ?>">
                                            </div>
                                            <div class="form-text">Your Venmo username for receiving payments.</div>
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i> Participants will be able to pay using Venmo by sending money to this username.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                <?php if (isset($data['is_admin_view']) && $data['is_admin_view']): ?>
                                    Save Coordinator Payment Settings
                                <?php else: ?>
                                    Save Default Payment Settings
                                <?php endif; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
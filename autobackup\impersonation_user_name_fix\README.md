# Impersonation User Name Fix

## Issue
When using the impersonate user feature in the admin panel, the impersonation banner was showing "You are currently viewing the site as Unknown User" instead of displaying the actual impersonated user's name.

## Root Cause
The `startImpersonation` method in `AdminController.php` was not setting the `$_SESSION['user_name']` and `$_SESSION['user_email']` variables when switching to the impersonated user's session. The header.php file was looking for `$_SESSION['user_name']` to display the impersonated user's name, but this session variable was never set during impersonation.

## Solution
1. **Fixed startImpersonation method**: Added `$_SESSION['user_name']` and `$_SESSION['user_email']` assignments when switching to the impersonated user's session.

2. **Fixed endImpersonation method**: Added proper restoration of admin's `user_name` and `user_email` session variables when ending impersonation.

## Files Modified
- `controllers/AdminController.php`
  - Line 520: Added `$_SESSION['user_name'] = $user->name;`
  - Line 521: Added `$_SESSION['user_email'] = $user->email;`
  - Line 606: Added `$_SESSION['user_name'] = $admin->name;`
  - Line 607: Added `$_SESSION['user_email'] = $admin->email;`

## Testing
After applying this fix:
1. Go to Admin → Impersonate User
2. Select any user to impersonate
3. The banner should now show "You are currently viewing the site as [User Name] (Role)" instead of "Unknown User"
4. Click "Return to Admin Account" to verify the admin session is properly restored

## Version
Applied in version 3.35.1 (increment from previous version)
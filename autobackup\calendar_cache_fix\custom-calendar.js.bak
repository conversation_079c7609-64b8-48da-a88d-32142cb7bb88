/**
 * Custom Calendar JavaScript
 * 
 * A fully responsive, custom-built calendar system for Events and Shows Management System
 * 
 * Version 1.0.0
 */

class CustomCalendar {
    /**
     * Initialize the calendar
     * 
     * @param {string} containerId - The ID of the container element
     * @param {Object} options - Calendar options
     */
    constructor(containerId, options = {}) {
        // Store container element
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Container element with ID "${containerId}" not found.`);
            return;
        }

        // Default options
        this.options = {
            defaultView: 'month',
            firstDayOfWeek: 0, // 0 = Sunday, 1 = Monday, etc.
            timeFormat: '12', // '12' or '24'
            showWeekends: true,
            businessHoursStart: '09:00',
            businessHoursEnd: '17:00',
            defaultEventDuration: 60, // in minutes
            enableDragDrop: true,
            enableResize: true,
            maxEventsPerDay: 5,
            eventSources: [],
            onEventClick: null,
            onDateClick: null,
            onEventDrop: null,
            onEventResize: null,
            onViewChange: null,
            ...options
        };

        // Set current date and view
        this.currentDate = new Date();
        this.currentView = this.options.defaultView;
        
        // Event data
        this.events = [];
        
        // Calendar elements
        this.calendarEl = null;
        this.headerEl = null;
        this.viewSelectorEl = null;
        this.contentEl = null;
        
        // Initialize calendar
        this.init();
    }

    /**
     * Initialize the calendar
     */
    init() {
        console.log('Initializing calendar');
        console.log(`Default view: ${this.options.defaultView}`);
        console.log(`Current date: ${this.currentDate.toDateString()}`);
        
        // Create calendar structure
        this.createCalendarStructure();
        
        // Load events
        this.loadEvents();
        
        // Render initial view
        this.renderView();
        
        // Add event listeners
        this.addEventListeners();
        
        console.log('Calendar initialization complete');
    }
}
# Google Maps Marker Modernization

## Issues
1. Google Maps is showing a deprecation warning:
```
As of February 21st, 2024, google.maps.Marker is deprecated. Please use google.maps.marker.AdvancedMarkerElement instead.
```

2. The Google Maps implementation doesn't respect the marker type settings from `/calendar/mapSettings`, while OpenStreetMap does.

3. AdvancedMarkerElement uses different event system:
```
Please use addEventListener('gmp-click', ...) instead of addEventListener('click', ...).
```

## Changes Made
1. Updated Google Maps marker creation to use `google.maps.marker.AdvancedMarkerElement`
2. Added support for marker settings configuration (marker type, size, colors, borders)
3. Implemented different marker types: default (circle), pin, and custom image
4. Made Google Maps implementation consistent with OpenStreetMap implementation
5. Fixed event listeners to use 'gmp-click' for AdvancedMarkerElement and 'click' for legacy Marker
6. Updated event triggering in centerMapOnMarker function to use correct event type

## Files Modified
- `views/calendar/map.php` - Updated `createGoogleMarker` function

## Testing
- Test with different marker types in map settings
- Verify markers display correctly on Google Maps
- Confirm deprecation warning is resolved
- Test marker click functionality
- Verify marker colors and sizes work correctly
- Test custom marker images
- Verify border settings work correctly
- Test calendar color integration

## Implementation Details

### API Changes
- **Old**: `new google.maps.Marker()` (deprecated Feb 21, 2024)
- **New**: `new google.maps.marker.AdvancedMarkerElement()` (modern API)

### Event System Changes
- **Old**: `addListener('click', ...)` for legacy Marker
- **New**: `addListener('gmp-click', ...)` for AdvancedMarkerElement
- **Implementation**: Dynamic event type detection based on marker instance

### Library Loading
- **Old**: `libraries=places`
- **New**: `libraries=places,marker` (added marker library)

### Map Configuration
- **Added**: `mapId: 'DEMO_MAP_ID'` (required for AdvancedMarkerElement)

### Marker Types Supported
1. **Default (Circle)**: Circular markers with event numbers
2. **Pin**: Map pin style markers with event numbers
3. **Custom Image**: User-provided image markers with number overlay

### Settings Integration
- Marker type selection from map settings
- Marker size configuration (8-48 pixels)
- Calendar color usage toggle
- Default marker color setting
- Border display and styling options
- Custom marker image URL support

## Backup Location
Original function backed up to: `autobackup/google_maps_marker_modernization/createGoogleMarker_original.js`
<?php
/**
 * Facebook Login Initiator
 * 
 * This file initiates the Facebook login process by redirecting to Facebook's OAuth dialog.
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(dirname(__FILE__)));

// Make APPROOT available globally
global $GLOBALS;
$GLOBALS['APPROOT'] = APPROOT;

// Load configuration
require_once APPROOT . '/config/config.php';

// Load helpers
require_once APPROOT . '/helpers/csrf_helper.php';
require_once APPROOT . '/helpers/url_helper.php';
require_once APPROOT . '/helpers/session_helper.php';
require_once APPROOT . '/helpers/auth_helper.php';

// Load Facebook service
require_once APPROOT . '/libraries/facebook/FacebookService.php';

// Check if Facebook App ID is configured
if (empty(FB_APP_ID)) {
    // Redirect to error page
    header('Location: ' . BASE_URL . '/home/<USER>/Facebook%20login%20not%20configured');
    exit;
}

// Generate and store a CSRF state token for security
$state = bin2hex(random_bytes(16));
$_SESSION['fb_state'] = $state;

// Create Facebook service
$fbService = new FacebookService();

// Get login URL
$fbLoginUrl = $fbService->getLoginUrl($state);

// Redirect to Facebook login dialog
header('Location: ' . $fbLoginUrl);
exit;
?>
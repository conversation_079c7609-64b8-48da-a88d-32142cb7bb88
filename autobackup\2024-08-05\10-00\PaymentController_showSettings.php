    public function showSettings($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is the coordinator or admin
        if ($show->coordinator_id != $this->auth->getCurrentUserId() && !$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get all payment methods
        $allPaymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        // Get show payment methods
        $showPaymentMethods = $this->paymentModel->getShowPaymentMethods($showId, false);
        
        // Get show payment settings for coordinator
        $showPaymentSettings = $this->paymentModel->getAllShowPaymentSettings($showId, true, false);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update show payment methods
            $selectedMethods = isset($_POST['payment_methods']) ? $_POST['payment_methods'] : [];
            $this->paymentModel->setShowPaymentMethods($showId, $selectedMethods);
            
            // Update show payment settings for coordinator
            $settings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id'
            ];
            
            foreach ($settings as $setting) {
                if (isset($_POST[$setting])) {
                    $this->paymentModel->updateShowPaymentSetting($showId, $setting, $_POST[$setting], false);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Show payment settings updated successfully', 'success');
            $this->redirect('payment/showSettings/' . $showId);
        } else {
            // Display settings form
            $data = [
                'title' => 'Show Payment Settings',
                'show' => $show,
                'all_payment_methods' => $allPaymentMethods,
                'show_payment_methods' => $showPaymentMethods,
                'payment_settings' => $showPaymentSettings
            ];
            
            $this->view('coordinator/payment_settings', $data);
        }
    }
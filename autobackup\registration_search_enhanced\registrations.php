<?php require APPROOT . '/views/includes/header.php'; ?>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Registrations</li>
                </ol>
            </nav>
            
            <h1><?php echo $data['show']->name; ?> - Registrations</h1>
            
            <?php flash('staff_message'); ?>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registrations</h5>
                    <div class="d-flex">
                        <a href="<?php echo URLROOT; ?>/staff/createRegistration/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                            <i class="fas fa-plus"></i> Create Registration
                        </a>
                    </div>
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="registrationSearch" class="form-control" 
                               placeholder="Search by display #, owner, email, phone, license plate, make, model, year..." 
                               aria-label="Search registrations">
                        <button type="button" class="btn btn-light" id="clearSearch" title="Clear search">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="mt-1 text-light small">
                        <i class="fas fa-info-circle"></i> Live search: Results update as you type. Searches all text including hidden fields (email, phone, license plate, etc.)
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($data['registrations'])) : ?>
                        <p class="text-muted">No registrations found for this show.</p>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="registrationsTable">
                                <thead>
                                    <tr>
                                        <th class="d-none">Search Data</th>
                                        <th>Car #</th>
                                        <th>Vehicle</th>
                                        <th>Owner</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Check-In</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['registrations'] as $registration) : ?>
                                        <?php 
                                        // Prepare hidden search data
                                        $email = isset($registration->email) ? $registration->email : '';
                                        $phone = isset($registration->phone) ? $registration->phone : '';
                                        $license_plate = isset($registration->license_plate) ? $registration->license_plate : '';
                                        $make = isset($registration->make) ? $registration->make : '';
                                        $model = isset($registration->model) ? $registration->model : '';
                                        $year = isset($registration->year) ? $registration->year : '';
                                        
                                        // Combine all searchable data
                                        $search_data = array_filter([
                                            $email, $phone, $license_plate, $make, $model, $year
                                        ]);
                                        ?>
                                        <tr>
                                            <!-- Hidden div with all searchable data -->
                                            <td class="d-none search-data"><?php echo htmlspecialchars(implode(' ', $search_data)); ?></td>
                                            <td>
                                                <?php if (!empty($registration->display_number)) : ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 1rem;">
                                                        #<?php echo $registration->display_number; ?>
                                                    </span>
                                                <?php else : ?>
                                                    <small class="text-muted">ID: <?php echo $registration->id; ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php 
                                                    echo $registration->year . ' ' . 
                                                         $registration->make . ' ' . 
                                                         $registration->model;
                                                ?>
                                            </td>
                                            <td><?php echo $registration->owner_name; ?></td>
                                            <td><?php echo $registration->category_name; ?></td>
                                            <td>
                                                <?php 
                                                    $statusClass = 'secondary';
                                                    switch ($registration->status) {
                                                        case 'approved':
                                                            $statusClass = 'success';
                                                            break;
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'danger';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'danger';
                                                            break;
                                                    }
                                                ?>
                                                <span class="badge bg-<?php echo $statusClass; ?>">
                                                    <?php echo ucfirst($registration->status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php 
                                                    $paymentClass = 'secondary';
                                                    $paymentStatus = $registration->payment_status ?? 'unpaid';
                                                    
                                                    switch ($paymentStatus) {
                                                        case 'paid':
                                                            $paymentClass = 'success';
                                                            break;
                                                        case 'pending':
                                                            $paymentClass = 'warning';
                                                            break;
                                                        case 'refunded':
                                                            $paymentClass = 'info';
                                                            break;
                                                        case 'unpaid':
                                                            $paymentClass = 'danger';
                                                            break;
                                                    }
                                                ?>
                                                <span class="badge bg-<?php echo $paymentClass; ?>">
                                                    <?php echo ucfirst($paymentStatus); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($registration->checked_in) : ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check"></i> Checked In
                                                        <br>
                                                        <small><?php echo date('m/d/y g:i a', strtotime($registration->check_in_time)); ?></small>
                                                    </span>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary">Not Checked In</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo URLROOT; ?>/staff/registration/<?php echo $registration->id; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <span class="visually-hidden">Toggle Dropdown</span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/editRegistration/<?php echo $registration->id; ?>">
                                                                <i class="fas fa-edit"></i> Edit
                                                            </a>
                                                        </li>
                                                        <?php if ($paymentStatus != 'paid' && $data['show']->registration_fee > 0) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/processPayment/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-money-bill"></i> Process Payment
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if (!$registration->checked_in && ($registration->payment_status == 'paid' || $data['show']->is_free)) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/checkIn/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-clipboard-check"></i> Check In
                                                                </a>
                                                            </li>
                                                        <?php elseif ($registration->checked_in) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/undoCheckIn/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-undo"></i> Undo Check-In
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/printRegistration/<?php echo $registration->id; ?>" target="_blank">
                                                                <i class="fas fa-print"></i> Print Card
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize DataTable for better sorting and filtering
    $(document).ready(function() {
        try {
            // Initialize DataTable
            $('#registrationsTable').DataTable({
                "order": [[ 1, "asc" ]], // Sort by the Car # column (index 1 now)
                "pageLength": 25,
                "columnDefs": [
                    { "visible": false, "targets": 0 }, // Hide the search data column
                    { "type": "html", "targets": 1 } // Properly sort the display number column that contains HTML
                ]
            });
            console.log('DataTable initialized successfully');
        } catch (error) {
            console.error('Error initializing DataTable:', error);
        }
    });
</script>
<script src="<?php echo URLROOT; ?>/public/js/registration-search.js"></script>

<!-- Fallback script in case DataTables fails to load -->
<script>
    // Check if DataTables loaded properly
    setTimeout(function() {
        if (typeof $.fn.DataTable === 'undefined') {
            console.error('DataTables not loaded. Loading from CDN...');
            
            // Create script elements to load DataTables
            var script1 = document.createElement('script');
            script1.src = 'https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js';
            
            var script2 = document.createElement('script');
            script2.src = 'https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js';
            
            // Add to document
            document.body.appendChild(script1);
            document.body.appendChild(script2);
            
            // Initialize DataTable after scripts are loaded
            script2.onload = function() {
                try {
                    $('#registrationsTable').DataTable({
                        "order": [[ 1, "asc" ]],
                        "pageLength": 25,
                        "columnDefs": [
                            { "visible": false, "targets": 0 }, // Hide the search data column
                            { "type": "html", "targets": 1 }
                        ]
                    });
                    console.log('DataTable initialized via fallback');
                    
                    // Reinitialize search functionality
                    if (typeof initRegistrationSearch === 'function') {
                        initRegistrationSearch();
                    }
                } catch (error) {
                    console.error('Error initializing DataTable via fallback:', error);
                }
            };
        }
    }, 1000);
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Facebook Callback Handler
 * 
 * This file handles the callback from Facebook after user authorization.
 */

// Start session
session_start();

// Define the application root directory
define('APPROOT', dirname(dirname(__FILE__)));

// Make APPROOT available globally
global $GLOBALS;
$GLOBALS['APPROOT'] = APPROOT;

// Load configuration
require_once APPROOT . '/config/config.php';

// Load helpers
require_once APPROOT . '/helpers/csrf_helper.php';
require_once APPROOT . '/helpers/url_helper.php';
require_once APPROOT . '/helpers/session_helper.php';
require_once APPROOT . '/helpers/auth_helper.php';
require_once APPROOT . '/helpers/format_helper.php';
require_once APPROOT . '/helpers/form_helper.php';
require_once APPROOT . '/helpers/image_helper.php';

// Load core classes
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/core/Auth.php';
require_once APPROOT . '/core/EventTrigger.php';
require_once APPROOT . '/core/Controller.php';
require_once APPROOT . '/core/App.php';

// Load models
require_once APPROOT . '/models/UserModel.php';

// Load Facebook service
require_once APPROOT . '/libraries/facebook/FacebookService.php';

// Create a temporary controller to handle the callback
class FacebookCallbackController extends Controller {
    private $auth;
    private $userModel;
    
    public function __construct() {
        $this->auth = new Auth();
        $this->userModel = new UserModel();
    }
    
    public function handleCallback() {
        // Check if Facebook App ID and Secret are configured
        if (empty(FB_APP_ID) || empty(FB_APP_SECRET)) {
            $this->redirect('home/error/Facebook%20login%20not%20configured');
            return;
        }
        
        // Verify state parameter to prevent CSRF attacks
        if (!isset($_GET['state']) || !isset($_SESSION['fb_state']) || $_GET['state'] !== $_SESSION['fb_state']) {
            error_log('Facebook callback: Invalid state parameter');
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        // Clear the state from session
        unset($_SESSION['fb_state']);
        
        // Check for error response from Facebook
        if (isset($_GET['error'])) {
            error_log('Facebook login error: ' . $_GET['error'] . ' - ' . ($_GET['error_description'] ?? 'No description'));
            $this->redirect('auth/login');
            return;
        }
        
        // Check for authorization code
        if (!isset($_GET['code'])) {
            error_log('Facebook callback: No authorization code received');
            $this->redirect('home/error/Authentication%20failed');
            return;
        }
        
        $code = $_GET['code'];
        
        try {
            // Create Facebook service
            $fbService = new FacebookService();
            
            // Exchange authorization code for access token
            $accessToken = $fbService->getAccessToken($code);
            
            if (!$accessToken) {
                throw new Exception('Failed to get access token');
            }
            
            // Get user data from Facebook
            $fbUserData = $fbService->getUserData($accessToken);
            
            if (!$fbUserData) {
                throw new Exception('Failed to get user data from Facebook');
            }
            
            // Register or login with Facebook
            $userId = $this->auth->facebookAuth($fbUserData, $accessToken);
            
            if ($userId) {
                // Get user role
                $user = $this->userModel->getUserById($userId);
                
                // Create session
                $_SESSION['user_id'] = $userId;
                $_SESSION['user_role'] = $user->role;
                $_SESSION['user_name'] = $user->name;
                
                // Redirect to dashboard
                $this->redirect('home/dashboard');
            } else {
                throw new Exception('Failed to authenticate user with Facebook data');
            }
            
        } catch (Exception $e) {
            error_log('Facebook authentication error: ' . $e->getMessage());
            $this->redirect('home/error/Facebook%20login%20failed:%20' . urlencode($e->getMessage()));
        }
    }
}

// Process the callback
$controller = new FacebookCallbackController();
$controller->handleCallback();
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Fixed Constructor</title>
</head>
<body>
    <h2>Test Fixed Constructor</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the fixed script
        const script = document.createElement('script');
        script.src = '/public/js/camera-banner.js?v=' + Date.now(); // Cache bust
        script.onload = function() {
            output('✓ Script loaded successfully');
            
            // Test creating instance
            try {
                output('Creating CameraBanner instance...');
                const banner = new CameraBanner();
                
                output('✓ Instance created successfully');
                output('- Version: ' + banner.version);
                output('- Banners length: ' + banner.banners.length);
                output('- Constructor name: ' + banner.constructor.name);
                
                // Wait a bit for async init to complete
                setTimeout(() => {
                    output('=== After 3 seconds (async init should be done) ===');
                    output('- Version: ' + banner.version);
                    output('- Banners length: ' + banner.banners.length);
                    output('- First banner: ' + JSON.stringify(banner.banners[0]));
                    
                    if (banner.banners.length > 0) {
                        output('✓ SUCCESS: Banners loaded correctly!');
                        output('- Total banners: ' + banner.banners.length);
                        banner.banners.forEach((b, i) => {
                            output(`  ${i+1}. ${b.type} - ${b.text || b.alt_text} (Logo: ${b.is_logo})`);
                        });
                    } else {
                        output('✗ ISSUE: No banners loaded');
                    }
                }, 3000);
                
            } catch (error) {
                output('✗ Error creating instance: ' + error.message);
                output('Stack: ' + error.stack);
            }
        };
        
        script.onerror = function() {
            output('✗ Failed to load script');
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
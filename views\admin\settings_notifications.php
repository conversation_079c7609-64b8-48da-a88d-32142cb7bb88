<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Notification Settings</h1>
            <p class="text-muted">Manage notification settings, SMS providers, queue, testing, and installation</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Settings
            </a>
        </div>
    </div>

    <?php if (isset($success) && $success) : ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <strong>Success!</strong> Notification settings have been updated.
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (isset($error) && !empty($error)) : ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <strong>Error!</strong> <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Notification Settings Sections -->
    <div class="row g-4 mb-5">
        
        <!-- Notification Settings Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-warning bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-cog text-warning fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Settings</h4>
                    </div>
                    <p class="card-text text-muted">Configure notification providers, email settings, and SMS configuration.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/notification_settings" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification Queue Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-list text-info fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Queue</h4>
                    </div>
                    <p class="card-text text-muted">Monitor notification queue, pending notifications, and delivery status.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/notification_queue" class="stretched-link text-decoration-none">
                        <span class="d-none">View Notification Queue</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Test Notifications Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-vial text-success fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Test</h4>
                    </div>
                    <p class="card-text text-muted">Test notification delivery, send test messages, and verify configuration.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/test_notifications" class="stretched-link text-decoration-none">
                        <span class="d-none">Test Notifications</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Install Notifications Card -->
        <div class="col-12 col-md-6 col-xl-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary bg-opacity-10 rounded-circle p-3 me-3">
                            <i class="fas fa-download text-primary fa-2x"></i>
                        </div>
                        <h4 class="card-title mb-0">Install</h4>
                    </div>
                    <p class="card-text text-muted">Install notification system components, database tables, and dependencies.</p>
                    <a href="<?php echo BASE_URL; ?>/admin/installNotifications" class="stretched-link text-decoration-none">
                        <span class="d-none">Install Notifications</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information Section -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white py-3">
                    <h3 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Notification System Information</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>System Status</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check-circle text-success me-2"></i> Notification system installed</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Database tables created</li>
                                <li><i class="fas fa-check-circle text-success me-2"></i> Cron job configured</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Quick Actions</h5>
                            <div class="d-flex flex-wrap gap-2">
                                <a href="<?php echo BASE_URL; ?>/admin/notification_settings" class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-cog me-1"></i> Settings
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/notification_queue" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-list me-1"></i> Queue
                                </a>
                                <a href="<?php echo BASE_URL; ?>/admin/test_notifications" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-vial me-1"></i> Test
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
# Session Lifetime and Remember Me Functionality

This directory contains backups of files modified to implement:

1. Configurable session lifetime for regular and Facebook logins
2. Fixed "Remember Me" functionality to properly store and retrieve user login information

## Changes Made

- Added session lifetime settings to the admin developer settings page
- Fixed the "Remember Me" functionality to properly store tokens in the database
- Added a new database table for remember tokens
- Updated the Auth class to properly handle session expiration and remember me tokens
- Updated the login process to respect the configured session lifetime
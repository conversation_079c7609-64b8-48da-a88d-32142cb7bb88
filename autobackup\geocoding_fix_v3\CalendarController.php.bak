<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.3 - Fixed geocoding issues with consistent implementation
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 * - Fixed geocoding to use enhanced method with fallbacks
 * - Improved geocoding consistency between batch and individual operations
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $showModel;
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('auth/login');
        }
        
        // Initialize models
        $this->calendarModel = $this->model('CalendarModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        
        // Check if calendar tables exist
        if (!$this->calendarModel->tablesExist()) {
            // Create tables
            if (!$this->calendarModel->createTables()) {
                die('Error creating calendar tables');
            }
            
            // Create default calendar
            $defaultCalendar = [
                'name' => 'Main Calendar',
                'description' => 'Default calendar for all events',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $_SESSION['user_id']
            ];
            
            $calendarId = $this->calendarModel->createCalendar($defaultCalendar);
            
            // Sync with existing shows
            if ($calendarId) {
                $this->calendarModel->syncEventsWithShows($calendarId);
            }
        }
    }
    
    /**
     * Calendar index page
     * 
     * @return void
     */
    public function index() {
        // Get user's calendars
        $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
        
        // Get calendar settings
        $settings = $this->calendarModel->getCalendarSettings(null);
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars,
            'settings' => $settings
        ];
        
        // Use fixed custom calendar view instead of the FullCalendar implementation
        $this->view('calendar/custom_index_fixed', $data);
    }
    
    /**
     * Get events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getEvents() {
        // Set content type to JSON first to ensure proper response format
        header('Content-Type: application/json');
        
        // Skip AJAX check for direct API calls from fetch()
        // Modern fetch() API doesn't always set X-Requested-With header
        
        // Get filter parameters
        $start = isset($_GET['start']) ? $_GET['start'] : null;
        $end = isset($_GET['end']) ? $_GET['end'] : null;
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        
        $filters = [];
        
        if ($start) {
            $filters['start_date'] = $start;
        }
        
        if ($end) {
            $filters['end_date'] = $end;
        }
        
        if ($calendarId) {
            // Handle multiple calendar IDs (comma-separated)
            if (strpos($calendarId, ',') !== false) {
                $calendarIds = explode(',', $calendarId);
                $filters['calendar_ids'] = $calendarIds;
            } else {
                $filters['calendar_id'] = $calendarId;
            }
        }
        
        try {
            // Get events
            $events = $this->calendarModel->getEvents($filters, $_SESSION['user_id']);
            
            // Format events for custom calendar
            $formattedEvents = [];
            
            foreach ($events as $event) {
                $formattedEvent = [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $event->start_date,
                    'end' => $event->end_date,
                    'allDay' => (bool)$event->all_day,
                    'location' => $event->location,
                    'url' => URLROOT . '/calendar/event/' . $event->id,
                    'extendedProps' => [
                        'description' => $event->description,
                        'location' => $event->location,
                        'calendar_id' => $event->calendar_id,
                        'calendar_name' => $event->calendar_name,
                        'show_id' => $event->show_id,
                        'show_name' => $event->show_name,
                        'privacy' => $event->privacy
                    ]
                ];
                
                // Set color
                if (!empty($event->color)) {
                    $formattedEvent['backgroundColor'] = $event->color;
                    $formattedEvent['borderColor'] = $event->color;
                    $formattedEvent['color'] = $event->color;
                } else if (!empty($event->calendar_color)) {
                    $formattedEvent['backgroundColor'] = $event->calendar_color;
                    $formattedEvent['borderColor'] = $event->calendar_color;
                    $formattedEvent['color'] = $event->calendar_color;
                }
                
                $formattedEvents[] = $formattedEvent;
            }
            
            // Return JSON response
            echo json_encode($formattedEvents);
        } catch (Exception $e) {
            // Return error as JSON
            echo json_encode(['error' => 'Error loading events: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Get upcoming events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getUpcomingEvents() {
        // Set content type to JSON first to ensure proper response format
        header('Content-Type: application/json');
        
        // Skip AJAX check for direct API calls from fetch()
        // Modern fetch() API doesn't always set X-Requested-With header
        
        // Get calendar ID parameter
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        
        $filters = [
            'start_date' => date('Y-m-d H:i:s'),
            'end_date' => date('Y-m-d H:i:s', strtotime('+30 days')),
            'limit' => 10,
            'order_by' => 'start_date ASC'
        ];
        
        if ($calendarId) {
            // Handle multiple calendar IDs (comma-separated)
            if (strpos($calendarId, ',') !== false) {
                $calendarIds = explode(',', $calendarId);
                $filters['calendar_ids'] = $calendarIds;
            } else {
                $filters['calendar_id'] = $calendarId;
            }
        }
        
        try {
            // Get events
            $events = $this->calendarModel->getEvents($filters, $_SESSION['user_id']);
            
            // Format events
            $formattedEvents = [];
            
            foreach ($events as $event) {
                $formattedEvent = [
                    'id' => $event->id,
                    'title' => $event->title,
                    'start' => $event->start_date,
                    'end' => $event->end_date,
                    'allDay' => (bool)$event->all_day,
                    'location' => $event->location,
                    'calendar_name' => $event->calendar_name,
                    'show_name' => $event->show_name
                ];
                
                $formattedEvents[] = $formattedEvent;
            }
            
            // Return JSON response
            echo json_encode($formattedEvents);
        } catch (Exception $e) {
            // Return error as JSON
            echo json_encode(['error' => 'Error loading upcoming events: ' . $e->getMessage()]);
        }
    }
    
    /**
     * Update event dates via AJAX
     * 
     * @return void
     */
    public function updateEventDates() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }
        
        // Validate CSRF token
        if (!validateCsrfToken()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
            return;
        }
        
        // Get POST data
        $id = isset($_POST['id']) ? filter_var($_POST['id'], FILTER_SANITIZE_NUMBER_INT) : null;
        $startDate = isset($_POST['start_date']) ? $_POST['start_date'] : null;
        $endDate = isset($_POST['end_date']) ? $_POST['end_date'] : null;
        
        // Validate data
        if (!$id || !$startDate || !$endDate) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Missing required parameters']);
            return;
        }
        
        // Get event
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Event not found']);
            return;
        }
        
        // Check if user has permission to edit this event
        if ($event->created_by != $_SESSION['user_id'] && !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'You do not have permission to edit this event']);
            return;
        }
        
        // Update event dates
        $data = [
            'id' => $id,
            'start_date' => $startDate,
            'end_date' => $endDate
        ];
        
        if ($this->calendarModel->updateEventDates($data)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
        } else {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Failed to update event dates']);
        }
    }
    
    /**
     * View event details
     * 
     * @param int $id Event ID
     * @return void
     */
    public function event($id) {
        // Get event details
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            flash('calendar_message', 'Event not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if user has permission to view this event
        if ($event->privacy == 'private' && $event->created_by != $_SESSION['user_id']) {
            flash('calendar_message', 'You do not have permission to view this event', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Get venue details if applicable
        $venue = null;
        if ($event->venue_id) {
            $venue = $this->calendarModel->getVenueById($event->venue_id);
        }
        
        // Get show details if applicable
        $show = null;
        if ($event->show_id) {
            $show = $this->showModel->getShowById($event->show_id);
        }
        
        $data = [
            'title' => 'Event Details',
            'event' => $event,
            'venue' => $venue,
            'show' => $show
        ];
        
        $this->view('calendar/event', $data);
    }
    
    /**
     * Create new event
     * 
     * @return void
     */
    public function createEvent() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $data = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'start_date' => $_POST['start_date'],
                'end_date' => $_POST['end_date'],
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1'] ?? ''),
                'address2' => trim($_POST['address2'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zipcode' => trim($_POST['zipcode'] ?? ''),
                'lat' => !empty($_POST['lat']) ? $_POST['lat'] : null,
                'lng' => !empty($_POST['lng']) ? $_POST['lng'] : null,
                'venue_id' => !empty($_POST['venue_id']) ? $_POST['venue_id'] : null,
                'show_id' => !empty($_POST['show_id']) ? $_POST['show_id'] : null,
                'calendar_id' => $_POST['calendar_id'],
                'color' => !empty($_POST['color']) ? $_POST['color'] : null,
                'privacy' => $_POST['privacy'],
                'url' => !empty($_POST['url']) ? $_POST['url'] : null,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'createEvent');
   
                // Log the geocoding attempt
                error_log("Geocoding attempt for new event: " . 
                ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Create event
                if ($this->calendarModel->createEvent($data)) {
                    flash('calendar_message', 'Event created successfully', 'alert alert-success');
                    redirect('calendar');
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
                $venues = $this->calendarModel->getVenues();
                $shows = $this->showModel->getShows();
                
                $data['calendars'] = $calendars;
                $data['venues'] = $venues;
                $data['shows'] = $shows;
                $data['title'] = 'Create Event';
                
                $this->view('calendar/create_event', $data);
            }
        } else {
            // Get calendars
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            // Get venues
            $venues = $this->calendarModel->getVenues();
            
            // Get shows
            $shows = $this->showModel->getShows();
            
            // Get pre-filled data from URL parameters
            $startDate = isset($_GET['start']) ? $_GET['start'] : date('Y-m-d H:i:s');
            $endDate = isset($_GET['end']) ? $_GET['end'] : date('Y-m-d H:i:s', strtotime('+1 hour'));
            $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : '';
            $showId = isset($_GET['show_id']) ? $_GET['show_id'] : '';
            
            // Initialize data
            $data = [
                'title' => 'Create Event',
                'calendars' => $calendars,
                'venues' => $venues,
                'shows' => $shows,
                'title_value' => '',
                'description' => '',
                'start_date' => $startDate,
                'end_date' => $endDate,
                'all_day' => 0,
                'location' => '',
                'address1' => '',
                'address2' => '',
                'city' => '',
                'state' => '',
                'zipcode' => '',
                'lat' => null,
                'lng' => null,
                'venue_id' => null,
                'show_id' => $showId,
                'calendar_id' => $calendarId,
                'color' => null,
                'privacy' => 'public',
                'url' => null,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // If show ID is provided, pre-fill show data
            if ($showId) {
                $show = $this->showModel->getShowById($showId);
                
                if ($show) {
                    $data['title_value'] = $show->title;
                    $data['description'] = $show->description;
                    $data['location'] = $show->venue_name;
                    $data['address1'] = $show->address1;
                    $data['address2'] = $show->address2;
                    $data['city'] = $show->city;
                    $data['state'] = $show->state;
                    $data['zipcode'] = $show->zipcode;
                    $data['lat'] = $show->lat;
                    $data['lng'] = $show->lng;
                    $data['venue_id'] = $show->venue_id;
                }
            }
            
            $this->view('calendar/create_event', $data);
        }
    }
    
    /**
     * Edit event
     * 
     * @param int $id Event ID
     * @return void
     */
    public function editEvent($id) {
        // Get event
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            flash('calendar_message', 'Event not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if user has permission to edit this event
        if ($event->created_by != $_SESSION['user_id'] && !$this->calendarModel->userHasCalendarPermission($event->calendar_id, $_SESSION['user_id'], 'edit')) {
            flash('calendar_message', 'You do not have permission to edit this event', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_FULL_SPECIAL_CHARS);
            
            // Process form
            $data = [
                'id' => $id,
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'start_date' => $_POST['start_date'],
                'end_date' => $_POST['end_date'],
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1'] ?? ''),
                'address2' => trim($_POST['address2'] ?? ''),
                'city' => trim($_POST['city'] ?? ''),
                'state' => trim($_POST['state'] ?? ''),
                'zipcode' => trim($_POST['zipcode'] ?? ''),
                'lat' => !empty($_POST['lat']) ? $_POST['lat'] : null,
                'lng' => !empty($_POST['lng']) ? $_POST['lng'] : null,
                'venue_id' => !empty($_POST['venue_id']) ? $_POST['venue_id'] : null,
                'show_id' => !empty($_POST['show_id']) ? $_POST['show_id'] : null,
                'calendar_id' => $_POST['calendar_id'],
                'color' => !empty($_POST['color']) ? $_POST['color'] : null,
                'privacy' => $_POST['privacy'],
                'url' => !empty($_POST['url']) ? $_POST['url'] : null,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
   
                // Log the geocoding attempt
                error_log("Geocoding attempt for event ID {$id}: " . 
                ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
   
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Update event
                if ($this->calendarModel->updateEvent($data)) {
                    flash('calendar_message', 'Event updated successfully', 'alert alert-success');
                    redirect('calendar/event/' . $id);
                } else {
                    die('Something went wrong');
                }
            } else {
                // Load view with errors
                $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
                $venues = $this->calendarModel->getVenues();
                $shows = $this->showModel->getShows();
                
                $data['calendars'] = $calendars;
                $data['venues'] = $venues;
                $data['shows'] = $shows;
                $data['title'] = 'Edit Event';
                
                $this->view('calendar/edit_event', $data);
            }
        } else {
            // Get calendars
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            // Get venues
            $venues = $this->calendarModel->getVenues();
            
            // Get shows
            $shows = $this->showModel->getShows();
            
            // Initialize data
            $data = [
                'id' => $id,
                'title' => 'Edit Event',
                'calendars' => $calendars,
                'venues' => $venues,
                'shows' => $shows,
                'title_value' => $event->title,
                'description' => $event->description,
                'start_date' => $event->start_date,
                'end_date' => $event->end_date,
                'all_day' => $event->all_day,
                'location' => $event->location,
                'address1' => $event->address1,
                'address2' => $event->address2,
                'city' => $event->city,
                'state' => $event->state,
                'zipcode' => $event->zipcode,
                'lat' => $event->lat,
                'lng' => $event->lng,
                'venue_id' => $event->venue_id,
                'show_id' => $event->show_id,
                'calendar_id' => $event->calendar_id,
                'color' => $event->color,
                'privacy' => $event->privacy,
                'url' => $event->url,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            $this->view('calendar/edit_event', $data);
        }
    }
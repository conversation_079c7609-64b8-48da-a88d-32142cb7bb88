<!DOCTYPE html>
<html>
<head>
    <title>This Context Test</title>
</head>
<body>
    <h2>This Context Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Test basic class creation and property setting
        output('=== Basic Class Test ===');
        
        class TestClass {
            constructor() {
                output('TestClass constructor started');
                output('- this type: ' + typeof this);
                output('- this instanceof TestClass: ' + (this instanceof TestClass));
                
                try {
                    this.version = 'TEST_VERSION';
                    output('- version set successfully: ' + this.version);
                } catch (error) {
                    output('- ERROR setting version: ' + error.message);
                }
                
                try {
                    this.banners = [];
                    output('- banners set successfully, length: ' + this.banners.length);
                } catch (error) {
                    output('- ERROR setting banners: ' + error.message);
                }
                
                output('TestClass constructor completed');
            }
        }
        
        try {
            const testInstance = new TestClass();
            output('✓ TestClass instance created');
            output('- Final version: ' + testInstance.version);
            output('- Final banners: ' + testInstance.banners.length);
        } catch (error) {
            output('✗ TestClass creation failed: ' + error.message);
        }
        
        output('');
        output('=== CameraBanner Class Test ===');
        
        // Now test with the exact same structure as CameraBanner
        class CameraBannerTest {
            constructor() {
                output('CameraBannerTest constructor started');
                
                try {
                    output('- About to set version');
                    this.version = '3.63.11-baseurl-fix'; // Exact same line
                    output('- Version set: ' + this.version);
                    
                    output('- About to set banners');
                    this.banners = [];
                    output('- Banners set: ' + this.banners.length);
                    
                    output('- About to set otherBanners');
                    this.otherBanners = [];
                    output('- otherBanners set');
                    
                    output('- About to set currentIndex');
                    this.currentIndex = 0;
                    output('- currentIndex set: ' + this.currentIndex);
                    
                    output('CameraBannerTest constructor completed successfully');
                    
                } catch (error) {
                    output('CameraBannerTest constructor error: ' + error.message);
                    output('Error stack: ' + error.stack);
                }
            }
        }
        
        try {
            const cameraBannerTest = new CameraBannerTest();
            output('✓ CameraBannerTest instance created');
            output('- Final version: ' + cameraBannerTest.version);
            output('- Final banners: ' + cameraBannerTest.banners.length);
        } catch (error) {
            output('✗ CameraBannerTest creation failed: ' + error.message);
        }
    </script>
</body>
</html>
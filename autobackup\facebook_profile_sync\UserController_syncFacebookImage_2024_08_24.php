<?php
/**
 * Synchronize profile image with Facebook
 */
public function syncFacebookImage() {
    // Check if DEBUG_MODE is defined and enabled
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
    }
    
    error_log('UserController::syncFacebookImage - Method called');
    
    // Check if user is logged in
    if (!$this->auth->isLoggedIn()) {
        error_log('UserController::syncFacebookImage - User not logged in');
        $this->redirect('auth/login');
        return;
    }
    
    // Get user ID
    $userId = $this->auth->getCurrentUserId();
    error_log('UserController::syncFacebookImage - User ID: ' . $userId);
    
    // Check if form was submitted
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        error_log('UserController::syncFacebookImage - POST request received');
        
        // Debug CSRF token
        if (isset($_POST[CSRF_TOKEN_NAME])) {
            error_log('UserController::syncFacebookImage - CSRF token in request: ' . $_POST[CSRF_TOKEN_NAME]);
        } else {
            error_log('UserController::syncFacebookImage - No CSRF token in request');
        }
        
        if (isset($_SESSION[CSRF_TOKEN_NAME])) {
            error_log('UserController::syncFacebookImage - CSRF token in session: ' . $_SESSION[CSRF_TOKEN_NAME]);
        } else {
            error_log('UserController::syncFacebookImage - No CSRF token in session');
        }
        
        // Validate CSRF token
        if (!$this->verifyCsrfToken()) {
            error_log('UserController::syncFacebookImage - Invalid CSRF token');
            $this->redirect('home/error/Invalid%20request');
            return;
        }
        
        error_log('UserController::syncFacebookImage - CSRF token verified');
        
        // Get user data
        $user = $this->userModel->getUserById($userId);
        
        if (!$user) {
            error_log('UserController::syncFacebookImage - User not found: ' . $userId);
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'User not found.');
            }
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - User found: ' . $user->name . ' (ID: ' . $userId . ')');
        
        // Check if user has a Facebook ID
        if (empty($user->facebook_id)) {
            error_log('UserController::syncFacebookImage - User does not have a Facebook ID: ' . $userId);
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Your account is not linked with Facebook.');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - User has Facebook ID: ' . $user->facebook_id);
        
        // Load Facebook service
        require_once APPROOT . '/libraries/facebook/FacebookService.php';
        $fbService = new FacebookService();
        
        // Check if Facebook SDK is available
        if (!$fbService->isSdkAvailable()) {
            error_log('UserController::syncFacebookImage - Facebook SDK not available');
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Facebook integration is not properly configured.');
            }
            
            // Redirect back to profile page
            $this->redirect('user/profile');
            return;
        }
        
        error_log('UserController::syncFacebookImage - Facebook SDK is available');
        
        try {
            // Use the Facebook ID directly to get the profile image
            $fbId = $user->facebook_id;
            error_log('UserController::syncFacebookImage - Getting profile picture for Facebook ID: ' . $fbId);
            
            // Construct the Facebook Graph API URL for the profile picture
            $imageUrl = "https://graph.facebook.com/{$fbId}/picture?type=large&width=500&height=500";
            error_log('UserController::syncFacebookImage - Image URL: ' . $imageUrl);
            
            // Clear the profile_image field in the users table to avoid confusion
            // This ensures we only use the images table for profile images
            $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
            $this->db->bind(':id', $userId);
            $this->db->execute();
            
            // Load image editor model for storing the image
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();
            
            // First, delete any existing profile images for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $existingImages = $this->db->resultSet();
            
            // Delete each existing image
            foreach ($existingImages as $image) {
                $imageEditorModel->deleteImage($image->id);
                error_log('UserController::syncFacebookImage - Deleted existing image: ' . $image->id);
            }
            
            // Download the image
            error_log('UserController::syncFacebookImage - Downloading image from: ' . $imageUrl);
            $imageContent = $this->downloadImage($imageUrl);
            
            if (!$imageContent) {
                error_log('UserController::syncFacebookImage - Failed to download image');
                throw new Exception('Failed to download profile image from Facebook');
            }
            
            // Process the downloaded image directly
            $uploadDir = 'uploads/users/';
            
            // Process the image using our new method for downloaded images
            $result = $imageEditorModel->processDownloadedImage(
                $imageContent,
                'facebook_profile.jpg',
                'user', 
                $userId, 
                $uploadDir,
                $userId,
                true // Set as primary image
            );
            
            if ($result) {
                error_log('UserController::syncFacebookImage - Successfully synchronized profile image with Facebook');
                // Set success message
                if (function_exists('setFlashMessage')) {
                    setFlashMessage('success', 'Profile image synchronized with Facebook successfully.');
                }
            } else {
                error_log('UserController::syncFacebookImage - Failed to synchronize profile image with Facebook');
                throw new Exception('Failed to synchronize profile image with Facebook');
            }
            
        } catch (Exception $e) {
            // Log the error
            error_log('Error synchronizing Facebook profile image: ' . $e->getMessage());
            error_log('Error trace: ' . $e->getTraceAsString());
            
            // Set error message
            if (function_exists('setFlashMessage')) {
                setFlashMessage('error', 'Failed to synchronize profile image with Facebook: ' . $e->getMessage());
            }
        }
        
        // Redirect back to profile page
        $this->redirect('user/profile');
    } else {
        // Redirect to profile page if not a POST request
        error_log('UserController::syncFacebookImage - Not a POST request');
        $this->redirect('user/profile');
    }
}

/**
 * Download an image from a URL
 * 
 * @param string $url Image URL
 * @return string|false Image content or false on failure
 */
private function downloadImage($url) {
    // Set a user agent to avoid potential blocking
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept: image/jpeg,image/png,image/*,*/*'
            ]
        ]
    ]);
    
    // Try to download with file_get_contents
    $imageContent = @file_get_contents($url, false, $context);
    
    // If file_get_contents fails, try with cURL
    if (!$imageContent) {
        error_log('UserController::downloadImage - Failed to download image with file_get_contents, trying cURL');
        
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Accept: image/jpeg,image/png,image/*,*/*']);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
        $imageContent = curl_exec($ch);
        
        if (curl_errno($ch)) {
            error_log('UserController::downloadImage - cURL error: ' . curl_error($ch));
            curl_close($ch);
            return false;
        }
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode != 200 || empty($imageContent)) {
            error_log('UserController::downloadImage - HTTP error: ' . $httpCode);
            return false;
        }
    }
    
    // Verify the image content is valid
    if (strlen($imageContent) < 100) {
        error_log('UserController::downloadImage - Downloaded image is too small: ' . strlen($imageContent) . ' bytes');
        return false;
    }
    
    // Check if the content is a valid image
    $tempImageCheck = @imagecreatefromstring($imageContent);
    if ($tempImageCheck === false) {
        error_log('UserController::downloadImage - Downloaded content is not a valid image');
        return false;
    }
    imagedestroy($tempImageCheck);
    
    return $imageContent;
}
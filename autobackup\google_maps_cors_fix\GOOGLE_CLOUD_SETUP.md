# Google Maps API - Complete Setup Guide

## Current Issue
- ✅ Pins/markers are showing (API key works)
- ❌ Map tiles not loading (configuration issue)
- ❌ CORS errors in console

## Step-by-Step Google Cloud Console Setup

### 1. Go to Google Cloud Console
Visit: https://console.cloud.google.com/

### 2. Select Your Project
- If you don't have a project, create one
- Make sure you're in the correct project

### 3. Enable Required APIs
Go to **APIs & Services > Library** and enable these APIs:

#### Required APIs:
- ✅ **Maps JavaScript API** (for map display)
- ✅ **Maps Static API** (for static map images)
- ✅ **Places API** (for location search)
- ✅ **Geocoding API** (for address conversion)

#### How to Enable:
1. Search for each API name
2. Click on the API
3. Click "ENABLE"
4. Repeat for all 4 APIs

### 4. Create/Configure API Key
Go to **APIs & Services > Credentials**

#### If you don't have an API key:
1. Click "CREATE CREDENTIALS"
2. Select "API key"
3. Copy the key (you'll need it)

#### If you have an API key (your current one):
1. Click on your API key: `AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM`
2. Configure it as follows:

### 5. API Key Configuration

#### Application Restrictions:
- Select: **"HTTP referrers (web sites)"**
- Add these referrers:
  ```
  https://events.rowaneliterides.com/*
  https://*.rowaneliterides.com/*
  events.rowaneliterides.com/*
  *.rowaneliterides.com/*
  localhost/*
  127.0.0.1/*
  ```

#### API Restrictions:
- Select: **"Restrict key"**
- Check these APIs:
  - ✅ Maps JavaScript API
  - ✅ Maps Static API
  - ✅ Places API
  - ✅ Geocoding API

### 6. Set Up Billing
**CRITICAL**: Google Maps requires billing even for free tier

1. Go to **Billing** in the left menu
2. Link a billing account
3. Add a valid payment method
4. Set up billing alerts (recommended)

#### Free Tier Limits:
- Maps JavaScript API: 28,000 loads/month free
- Static Maps: 100,000 loads/month free
- Places API: $200 credit/month
- Geocoding: 40,000 requests/month free

### 7. Check Quotas
Go to **APIs & Services > Quotas**

Verify these quotas are not exceeded:
- Maps JavaScript API: Requests per day
- Maps Static API: Requests per day
- Places API: Requests per day
- Geocoding API: Requests per day

## Testing Your Setup

### Test 1: Simple HTML Test
Create this file and test it:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
</head>
<body>
    <div id="map" style="height: 400px; width: 100%;"></div>
    
    <script>
        function initMap() {
            const map = new google.maps.Map(document.getElementById("map"), {
                zoom: 4,
                center: { lat: 39.8283, lng: -98.5795 },
            });
            
            // Add a marker
            new google.maps.Marker({
                position: { lat: 39.8283, lng: -98.5795 },
                map: map,
                title: "Test Marker"
            });
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM&callback=initMap"></script>
</body>
</html>
```

### Expected Results:
- ✅ Map background loads (gray/colored tiles)
- ✅ Marker appears
- ✅ No console errors
- ✅ Can zoom and pan

### If Test Fails:
1. **Check browser console** for specific errors
2. **Verify billing** is set up
3. **Check API restrictions** match your domain
4. **Ensure all required APIs** are enabled

## Common Issues & Solutions

### Issue 1: "This page can't load Google Maps correctly"
**Solution**: Billing not set up or API key restrictions too strict

### Issue 2: Gray map with no tiles
**Solution**: Maps JavaScript API not enabled or billing issue

### Issue 3: "API key not valid"
**Solution**: Check API key restrictions and domain settings

### Issue 4: Pins show but no map background
**Solution**: Maps Static API not enabled or quota exceeded

## Your Current API Key
```
AIzaSyAafhSBRYrqbwSleElcuwkduT96joDhaXM
```

## Next Steps
1. **Follow this setup guide** in Google Cloud Console
2. **Test with the simple HTML** above
3. **Once working**, your main site should work too
4. **Let me know** what errors you see in the test

## Support Links
- [Google Cloud Console](https://console.cloud.google.com/)
- [Google Maps API Documentation](https://developers.google.com/maps/documentation/javascript)
- [Billing Setup Guide](https://cloud.google.com/billing/docs/how-to/manage-billing-account)
    <!-- Navigation Bar -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Monthly Event Chart</h1>
            <p class="text-muted mb-0">Timeline view of events with unlimited rows</p>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group me-2">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-primary active">
                    <i class="fas fa-chart-event me-2"></i> Event View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-outline-primary">
                    <i class="fas fa-map-marker-alt me-2"></i> Map View
                </a>
            </div>
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <?php if (isLoggedIn()): ?>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/createCalendar">
                        <i class="fas fa-plus me-2"></i>Create Calendar
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <?php endif; ?>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">Manage Calendars</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">Manage Venues</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">Manage Clubs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">Import Events</a></li>
                    <?php if (isAdmin()): ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">Calendar Settings</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
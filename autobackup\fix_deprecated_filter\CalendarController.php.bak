<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.0 - Initial implementation
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $showModel;
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('auth/login');
        }
        
        // Initialize models
        $this->calendarModel = $this->model('CalendarModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        
        // Check if calendar tables exist
        if (!$this->calendarModel->tablesExist()) {
            // Create tables
            if (!$this->calendarModel->createTables()) {
                die('Error creating calendar tables');
            }
            
            // Create default calendar
            $defaultCalendar = [
                'name' => 'Main Calendar',
                'description' => 'Default calendar for all events',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $_SESSION['user_id']
            ];
            
            $calendarId = $this->calendarModel->createCalendar($defaultCalendar);
            
            // Sync with existing shows
            if ($calendarId) {
                $this->calendarModel->syncEventsWithShows($calendarId);
            }
        }
    }
    
    /**
     * Calendar index page
     * 
     * @return void
     */
    public function index() {
        // Get user's calendars
        $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
        
        // Get calendar settings
        $settings = $this->calendarModel->getCalendarSettings();
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars,
            'settings' => $settings
        ];
        
        $this->view('calendar/index', $data);
    }
    
    /**
     * Get events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getEvents() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Get filter parameters
        $start = isset($_GET['start']) ? $_GET['start'] : null;
        $end = isset($_GET['end']) ? $_GET['end'] : null;
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        
        $filters = [];
        
        if ($start) {
            $filters['start_date'] = $start;
        }
        
        if ($end) {
            $filters['end_date'] = $end;
        }
        
        if ($calendarId) {
            $filters['calendar_id'] = $calendarId;
        }
        
        // Get events
        $events = $this->calendarModel->getEvents($filters, $_SESSION['user_id']);
        
        // Format events for FullCalendar
        $formattedEvents = [];
        
        foreach ($events as $event) {
            $formattedEvent = [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date,
                'end' => $event->end_date,
                'allDay' => (bool)$event->all_day,
                'url' => URLROOT . '/calendar/event/' . $event->id,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                    'calendar_id' => $event->calendar_id,
                    'calendar_name' => $event->calendar_name,
                    'show_id' => $event->show_id,
                    'show_name' => $event->show_name,
                    'privacy' => $event->privacy
                ]
            ];
            
            // Set color
            if (!empty($event->color)) {
                $formattedEvent['backgroundColor'] = $event->color;
                $formattedEvent['borderColor'] = $event->color;
            } else if (!empty($event->calendar_color)) {
                $formattedEvent['backgroundColor'] = $event->calendar_color;
                $formattedEvent['borderColor'] = $event->calendar_color;
            }
            
            $formattedEvents[] = $formattedEvent;
        }
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($formattedEvents);
    }
    
    /**
     * View event details
     * 
     * @param int $id Event ID
     * @return void
     */
    public function event($id) {
        // Get event details
        $event = $this->calendarModel->getEventById($id);
        
        if (!$event) {
            flash('calendar_message', 'Event not found', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Check if user has permission to view this event
        if ($event->privacy == 'private' && $event->created_by != $_SESSION['user_id']) {
            flash('calendar_message', 'You do not have permission to view this event', 'alert alert-danger');
            redirect('calendar');
        }
        
        // Get venue details if applicable
        $venue = null;
        if ($event->venue_id) {
            $venue = $this->calendarModel->getVenueById($event->venue_id);
        }
        
        // Get show details if applicable
        $show = null;
        if ($event->show_id) {
            $show = $this->showModel->getShowById($event->show_id);
        }
        
        $data = [
            'title' => 'Event Details',
            'event' => $event,
            'venue' => $venue,
            'show' => $show
        ];
        
        $this->view('calendar/event', $data);
    }
    
    /**
     * Create new event
     * 
     * @return void
     */
    public function createEvent() {
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Process form
            $data = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'start_date' => $_POST['start_date'],
                'end_date' => $_POST['end_date'],
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'venue_id' => !empty($_POST['venue_id']) ? $_POST['venue_id'] : null,
                'url' => trim($_POST['url']),
                'color' => trim($_POST['color']),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurrence_pattern' => isset($_POST['recurrence_pattern']) ? $_POST['recurrence_pattern'] : null,
                'recurrence_end_date' => !empty($_POST['recurrence_end_date']) ? $_POST['recurrence_end_date'] : null,
                'privacy' => $_POST['privacy'],
                'calendar_id' => $_POST['calendar_id'],
                'show_id' => !empty($_POST['show_id']) ? $_POST['show_id'] : null,
                'created_by' => $_SESSION['user_id'],
                'clubs' => isset($_POST['clubs']) ? $_POST['clubs'] : [],
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Create event
                if ($this->calendarModel->createEvent($data)) {
                    flash('calendar_message', 'Event created successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_event', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_event', $data);
            }
        } else {
            // Get calendars
            $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
            
            // Get venues
            $venues = $this->calendarModel->getVenues();
            
            // Get clubs
            $clubs = $this->calendarModel->getClubs();
            
            // Get shows
            $shows = $this->showModel->getShows();
            
            // Init data
            $data = [
                'title' => '',
                'description' => '',
                'start_date' => date('Y-m-d H:i:s'),
                'end_date' => date('Y-m-d H:i:s', strtotime('+1 hour')),
                'all_day' => 0,
                'location' => '',
                'venue_id' => null,
                'url' => '',
                'color' => '',
                'is_recurring' => 0,
                'recurrence_pattern' => null,
                'recurrence_end_date' => null,
                'privacy' => 'public',
                'calendar_id' => '',
                'show_id' => null,
                'clubs' => [],
                'calendars' => $calendars,
                'venues' => $venues,
                'clubs_list' => $clubs,
                'shows' => $shows,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            $this->view('calendar/create_event', $data);
        }
    }
}
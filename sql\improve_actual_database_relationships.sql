-- =====================================================
-- ACTUAL Database Relationship Improvements
-- =====================================================
-- This script adds foreign key constraints and improvements
-- ONLY for tables that are actually used in your PHP code
-- 
-- IMPORTANT: Run analyze_actual_database_usage.php first
-- to confirm which tables are actually used
-- 
-- Backup your database before running this script
-- =====================================================

-- Set foreign key checks to 0 temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. CORE TABLES - MOST FREQUENTLY USED
-- =====================================================

-- These are the tables most commonly found in PHP code analysis

-- USERS table relationships (if users table is used)
-- Check if users table exists and is used
SET @users_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'users');

-- SHOWS table relationships (if shows table is used)
-- Check if shows table exists and is used
SET @shows_exists = (SELECT COUNT(*) FROM information_schema.tables 
                     WHERE table_schema = DATABASE() AND table_name = 'shows');

-- VEHICLES table relationships (if vehicles table is used)
-- Check if vehicles table exists and is used
SET @vehicles_exists = (SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_schema = DATABASE() AND table_name = 'vehicles');

-- REGISTRATIONS table relationships (if registrations table is used)
-- Check if registrations table exists and is used
SET @registrations_exists = (SELECT COUNT(*) FROM information_schema.tables 
                             WHERE table_schema = DATABASE() AND table_name = 'registrations');

-- =====================================================
-- 2. ADD FOREIGN KEYS FOR CONFIRMED USED TABLES ONLY
-- =====================================================

-- Only add foreign keys if both tables exist and are used

-- Vehicles to Users (if both exist)
SET @add_vehicle_user_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'vehicles'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'users'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'vehicles' 
                AND column_name = 'owner_id')
);

-- Add the constraint only if conditions are met
SET @sql = IF(@add_vehicle_user_fk > 0,
    'ALTER TABLE vehicles ADD CONSTRAINT fk_vehicles_owner FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE',
    'SELECT "Skipping vehicles->users FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Registrations to Shows (if both exist)
SET @add_reg_show_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'registrations'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'shows'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'registrations' 
                AND column_name = 'show_id')
);

SET @sql = IF(@add_reg_show_fk > 0,
    'ALTER TABLE registrations ADD CONSTRAINT fk_registrations_show FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE CASCADE',
    'SELECT "Skipping registrations->shows FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Registrations to Vehicles (if both exist)
SET @add_reg_vehicle_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'registrations'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'vehicles'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'registrations' 
                AND column_name = 'vehicle_id')
);

SET @sql = IF(@add_reg_vehicle_fk > 0,
    'ALTER TABLE registrations ADD CONSTRAINT fk_registrations_vehicle FOREIGN KEY (vehicle_id) REFERENCES vehicles(id) ON DELETE CASCADE',
    'SELECT "Skipping registrations->vehicles FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Registrations to Users (if both exist)
SET @add_reg_user_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'registrations'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'users'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'registrations' 
                AND column_name = 'owner_id')
);

SET @sql = IF(@add_reg_user_fk > 0,
    'ALTER TABLE registrations ADD CONSTRAINT fk_registrations_owner FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE',
    'SELECT "Skipping registrations->users FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 3. CALENDAR SYSTEM (if used)
-- =====================================================

-- Calendar Events to Calendars
SET @add_cal_event_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'calendar_events'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'calendars'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'calendar_events' 
                AND column_name = 'calendar_id')
);

SET @sql = IF(@add_cal_event_fk > 0,
    'ALTER TABLE calendar_events ADD CONSTRAINT fk_calendar_events_calendar FOREIGN KEY (calendar_id) REFERENCES calendars(id) ON DELETE CASCADE',
    'SELECT "Skipping calendar_events->calendars FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Calendar Events to Shows (optional link)
SET @add_cal_show_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'calendar_events'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'shows'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'calendar_events' 
                AND column_name = 'show_id')
);

SET @sql = IF(@add_cal_show_fk > 0,
    'ALTER TABLE calendar_events ADD CONSTRAINT fk_calendar_events_show FOREIGN KEY (show_id) REFERENCES shows(id) ON DELETE SET NULL',
    'SELECT "Skipping calendar_events->shows FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 4. PAYMENT SYSTEM (if used)
-- =====================================================

-- Payments to Users
SET @add_payment_user_fk = (
    SELECT COUNT(*) FROM information_schema.tables t1, information_schema.tables t2
    WHERE t1.table_schema = DATABASE() AND t1.table_name = 'payments'
    AND t2.table_schema = DATABASE() AND t2.table_name = 'users'
    AND EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'payments' 
                AND column_name = 'user_id')
);

SET @sql = IF(@add_payment_user_fk > 0,
    'ALTER TABLE payments ADD CONSTRAINT fk_payments_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE',
    'SELECT "Skipping payments->users FK: tables or columns not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 5. ADD INDEXES FOR PERFORMANCE (USED TABLES ONLY)
-- =====================================================

-- Add indexes only for tables that exist and are used

-- Users table indexes
SET @sql = IF(@users_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
    'SELECT "Skipping users indexes: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@users_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
    'SELECT "Skipping users role index: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Vehicles table indexes
SET @sql = IF(@vehicles_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_vehicles_owner_id ON vehicles(owner_id)',
    'SELECT "Skipping vehicles indexes: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Shows table indexes
SET @sql = IF(@shows_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_shows_start_date ON shows(start_date)',
    'SELECT "Skipping shows indexes: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@shows_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_shows_status ON shows(status)',
    'SELECT "Skipping shows status index: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Registrations table indexes
SET @sql = IF(@registrations_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_registrations_show_id ON registrations(show_id)',
    'SELECT "Skipping registrations indexes: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@registrations_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_registrations_vehicle_id ON registrations(vehicle_id)',
    'SELECT "Skipping registrations vehicle index: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = IF(@registrations_exists > 0,
    'CREATE INDEX IF NOT EXISTS idx_registrations_owner_id ON registrations(owner_id)',
    'SELECT "Skipping registrations owner index: table not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 6. CLEANUP UNUSED TABLES (OPTIONAL - COMMENTED OUT)
-- =====================================================

-- UNCOMMENT THESE LINES ONLY AFTER CONFIRMING TABLES ARE TRULY UNUSED
-- AND AFTER BACKING UP YOUR DATABASE

-- Common potentially unused tables found in many systems:
-- DROP TABLE IF EXISTS `admin_impersonation`; -- Often unused
-- DROP TABLE IF EXISTS `awards`; -- Check if awards system is used
-- DROP TABLE IF EXISTS `age_weights`; -- Check if age-based scoring is used

-- =====================================================
-- 7. CREATE VIEWS FOR COMMONLY USED DATA
-- =====================================================

-- Only create views if the underlying tables exist

-- Registration details view (if all tables exist)
SET @create_reg_view = (
    SELECT COUNT(*) FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name IN ('registrations', 'vehicles', 'users', 'shows')
    HAVING COUNT(*) = 4
);

SET @sql = IF(@create_reg_view > 0,
    'CREATE OR REPLACE VIEW v_registration_details AS
     SELECT r.id as registration_id, r.created_at as registration_date,
            s.name as show_name, s.location as show_location, s.start_date, s.end_date,
            v.make, v.model, v.year, v.color,
            u.name as owner_name, u.email as owner_email
     FROM registrations r
     JOIN shows s ON r.show_id = s.id
     JOIN vehicles v ON r.vehicle_id = v.id
     JOIN users u ON r.owner_id = u.id',
    'SELECT "Skipping registration view: required tables not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- User activity view (if tables exist)
SET @create_user_view = (
    SELECT COUNT(*) FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name IN ('users', 'vehicles', 'registrations')
    HAVING COUNT(*) = 3
);

SET @sql = IF(@create_user_view > 0,
    'CREATE OR REPLACE VIEW v_user_activity AS
     SELECT u.id, u.name, u.email, u.role,
            COUNT(DISTINCT v.id) as vehicle_count,
            COUNT(DISTINCT r.id) as registration_count,
            MAX(r.created_at) as last_registration
     FROM users u
     LEFT JOIN vehicles v ON u.id = v.owner_id
     LEFT JOIN registrations r ON u.id = r.owner_id
     GROUP BY u.id, u.name, u.email, u.role',
    'SELECT "Skipping user activity view: required tables not found" as message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- 8. RE-ENABLE FOREIGN KEY CHECKS
-- =====================================================

SET FOREIGN_KEY_CHECKS = 1;

-- =====================================================
-- 9. VERIFICATION QUERIES
-- =====================================================

-- Show what foreign keys were actually added
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL
AND CONSTRAINT_NAME LIKE 'fk_%'
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Show what indexes were added
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- Show what views were created
SELECT 
    TABLE_NAME as VIEW_NAME,
    TABLE_TYPE
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_TYPE = 'VIEW'
AND TABLE_NAME LIKE 'v_%';

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Actual database relationship improvements completed!' as Status;
SELECT 'Only tables confirmed to be used in PHP code were modified.' as Note;
SELECT 'Run analyze_actual_database_usage.php to see what was changed.' as Action;
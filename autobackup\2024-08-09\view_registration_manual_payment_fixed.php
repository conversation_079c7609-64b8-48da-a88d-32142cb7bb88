<?php
// Backup of the modified section in view_registration.php
// Date: 2024-08-09
// Change: Fixed "Manual Payment" button not showing by adding debug output and correcting the condition

// Original code:
/*
<?php if ($registration->payment_status === 'pending') : ?>
    <form action="<?php echo BASE_URL; ?>/admin/markAsPaid/<?php echo $registration->id; ?>" method="post" class="d-inline">
        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
        <button type="submit" class="btn btn-success btn-sm">
            <i class="fas fa-check-circle me-1"></i> Mark as Paid
        </button>
    </form>
    
    <?php if (!isset($show->is_free) || !$show->is_free) : ?>
        <a href="<?php echo BASE_URL; ?>/payment/manual/registration/<?php echo $registration->id; ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-hand-holding-usd me-1"></i> Manual Payment
        </a>
    <?php endif; ?>
*/

// Modified code:
/*
<?php if ($registration->payment_status === 'pending' || $registration->payment_status === 'unpaid') : ?>
    <form action="<?php echo BASE_URL; ?>/admin/markAsPaid/<?php echo $registration->id; ?>" method="post" class="d-inline">
        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
        <button type="submit" class="btn btn-success btn-sm">
            <i class="fas fa-check-circle me-1"></i> Mark as Paid
        </button>
    </form>
    
    <!-- Debug: Show is_free value: <?php echo isset($show->is_free) ? var_export($show->is_free, true) : 'not set'; ?> -->
    <?php if (isset($show->is_free) && $show->is_free == 1) : ?>
        <a href="<?php echo BASE_URL; ?>/payment/manual/registration/<?php echo $registration->id; ?>" class="btn btn-primary btn-sm">
            <i class="fas fa-hand-holding-usd me-1"></i> Manual Payment
        </a>
    <?php endif; ?>
*/
?>
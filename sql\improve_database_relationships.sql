-- =====================================================
-- Database Relationship Improvements
-- =====================================================
-- This script adds missing foreign key constraints and indexes
-- to improve database integrity and performance
-- 
-- IMPORTANT: Run this script during maintenance window
-- Backup your database before running this script
-- =====================================================

-- Set foreign key checks to 0 temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. ADD MISSING FOREIGN KEY CONSTRAINTS
-- =====================================================

-- Age Weights to Shows relationship
ALTER TABLE `age_weights` 
ADD CONSTRAINT `fk_age_weights_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Categories to Shows relationship
ALTER TABLE `categories` 
ADD CONSTRAINT `fk_categories_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Awards to Shows relationship
ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Awards to Categories relationship (optional)
ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_category` 
FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Awards to Registrations relationship (optional)
ALTER TABLE `awards` 
ADD CONSTRAINT `fk_awards_registration` 
FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- Vehicles to Users relationship
ALTER TABLE `vehicles` 
ADD CONSTRAINT `fk_vehicles_owner` 
FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Registrations to Shows relationship
ALTER TABLE `registrations` 
ADD CONSTRAINT `fk_registrations_show` 
FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Registrations to Vehicles relationship
ALTER TABLE `registrations` 
ADD CONSTRAINT `fk_registrations_vehicle` 
FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Registrations to Users relationship
ALTER TABLE `registrations` 
ADD CONSTRAINT `fk_registrations_owner` 
FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Payments to Users relationship
ALTER TABLE `payments` 
ADD CONSTRAINT `fk_payments_user` 
FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =====================================================
-- 2. ADD PERFORMANCE INDEXES
-- =====================================================

-- Indexes for frequently queried foreign keys
CREATE INDEX IF NOT EXISTS `idx_age_weights_show_id` ON `age_weights` (`show_id`);
CREATE INDEX IF NOT EXISTS `idx_categories_show_id` ON `categories` (`show_id`);
CREATE INDEX IF NOT EXISTS `idx_awards_show_id` ON `awards` (`show_id`);
CREATE INDEX IF NOT EXISTS `idx_awards_category_id` ON `awards` (`category_id`);
CREATE INDEX IF NOT EXISTS `idx_awards_registration_id` ON `awards` (`registration_id`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_owner_id` ON `vehicles` (`owner_id`);
CREATE INDEX IF NOT EXISTS `idx_registrations_show_id` ON `registrations` (`show_id`);
CREATE INDEX IF NOT EXISTS `idx_registrations_vehicle_id` ON `registrations` (`vehicle_id`);
CREATE INDEX IF NOT EXISTS `idx_registrations_owner_id` ON `registrations` (`owner_id`);
CREATE INDEX IF NOT EXISTS `idx_payments_user_id` ON `payments` (`user_id`);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS `idx_registrations_show_owner` ON `registrations` (`show_id`, `owner_id`);
CREATE INDEX IF NOT EXISTS `idx_vehicles_owner_year` ON `vehicles` (`owner_id`, `year`);
CREATE INDEX IF NOT EXISTS `idx_calendar_events_calendar_date` ON `calendar_events` (`calendar_id`, `start_date`);
CREATE INDEX IF NOT EXISTS `idx_calendar_events_show_date` ON `calendar_events` (`show_id`, `start_date`);

-- =====================================================
-- 3. ADD DATA VALIDATION CONSTRAINTS
-- =====================================================

-- Age weights validation
ALTER TABLE `age_weights` 
ADD CONSTRAINT `chk_age_weights_range` 
CHECK (`min_age` <= `max_age` AND `min_age` >= 1900 AND `max_age` <= YEAR(CURDATE()) + 1);

ALTER TABLE `age_weights` 
ADD CONSTRAINT `chk_age_weights_multiplier` 
CHECK (`multiplier` > 0 AND `multiplier` <= 10);

-- Calendar events date validation
ALTER TABLE `calendar_events` 
ADD CONSTRAINT `chk_calendar_events_dates` 
CHECK (`start_date` <= `end_date`);

-- Vehicle year validation
ALTER TABLE `vehicles` 
ADD CONSTRAINT `chk_vehicles_year` 
CHECK (`year` >= 1900 AND `year` <= YEAR(CURDATE()) + 2);

-- Payment amount validation
ALTER TABLE `payments` 
ADD CONSTRAINT `chk_payments_amount` 
CHECK (`amount` >= 0);

-- Show date validation
ALTER TABLE `shows` 
ADD CONSTRAINT `chk_shows_dates` 
CHECK (`start_date` <= `end_date`);

-- =====================================================
-- 4. CREATE VIEWS FOR COMMON QUERIES
-- =====================================================

-- View for complete registration information
CREATE OR REPLACE VIEW `v_registration_details` AS
SELECT 
    r.id as registration_id,
    r.registration_number,
    r.created_at as registration_date,
    s.id as show_id,
    s.name as show_name,
    s.location as show_location,
    s.start_date as show_start_date,
    s.end_date as show_end_date,
    v.id as vehicle_id,
    v.make,
    v.model,
    v.year,
    v.color,
    u.id as owner_id,
    u.name as owner_name,
    u.email as owner_email,
    u.phone as owner_phone
FROM registrations r
JOIN shows s ON r.show_id = s.id
JOIN vehicles v ON r.vehicle_id = v.id
JOIN users u ON r.owner_id = u.id;

-- View for show statistics
CREATE OR REPLACE VIEW `v_show_statistics` AS
SELECT 
    s.id as show_id,
    s.name as show_name,
    s.start_date,
    s.end_date,
    COUNT(DISTINCT r.id) as total_registrations,
    COUNT(DISTINCT r.owner_id) as unique_owners,
    COUNT(DISTINCT v.make) as unique_makes,
    MIN(v.year) as oldest_vehicle_year,
    MAX(v.year) as newest_vehicle_year,
    COALESCE(SUM(p.amount), 0) as total_payments
FROM shows s
LEFT JOIN registrations r ON s.id = r.show_id
LEFT JOIN vehicles v ON r.vehicle_id = v.id
LEFT JOIN payments p ON r.id = p.registration_id
GROUP BY s.id, s.name, s.start_date, s.end_date;

-- View for user activity summary
CREATE OR REPLACE VIEW `v_user_activity` AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    u.role,
    u.created_at as user_since,
    COUNT(DISTINCT v.id) as total_vehicles,
    COUNT(DISTINCT r.id) as total_registrations,
    COUNT(DISTINCT r.show_id) as shows_participated,
    COALESCE(SUM(p.amount), 0) as total_payments,
    MAX(r.created_at) as last_registration_date
FROM users u
LEFT JOIN vehicles v ON u.id = v.owner_id
LEFT JOIN registrations r ON u.id = r.owner_id
LEFT JOIN payments p ON u.id = p.user_id
GROUP BY u.id, u.name, u.email, u.role, u.created_at;

-- View for calendar events with show details
CREATE OR REPLACE VIEW `v_calendar_events_detailed` AS
SELECT 
    ce.id as event_id,
    ce.title,
    ce.description,
    ce.start_date,
    ce.end_date,
    ce.all_day,
    ce.location,
    ce.address1,
    ce.city,
    ce.state,
    ce.zipcode,
    ce.lat,
    ce.lng,
    c.name as calendar_name,
    c.color as calendar_color,
    s.id as show_id,
    s.name as show_name,
    s.description as show_description,
    COUNT(r.id) as show_registrations
FROM calendar_events ce
JOIN calendars c ON ce.calendar_id = c.id
LEFT JOIN shows s ON ce.show_id = s.id
LEFT JOIN registrations r ON s.id = r.show_id
GROUP BY ce.id, ce.title, ce.description, ce.start_date, ce.end_date, 
         ce.all_day, ce.location, ce.address1, ce.city, ce.state, 
         ce.zipcode, ce.lat, ce.lng, c.name, c.color, s.id, s.name, s.description;

-- =====================================================
-- 5. CREATE STORED PROCEDURES FOR COMMON OPERATIONS
-- =====================================================

DELIMITER //

-- Procedure to get show dashboard data
CREATE PROCEDURE `sp_get_show_dashboard`(IN show_id INT)
BEGIN
    -- Show basic info
    SELECT * FROM shows WHERE id = show_id;
    
    -- Registration statistics
    SELECT 
        COUNT(*) as total_registrations,
        COUNT(DISTINCT owner_id) as unique_owners,
        COUNT(DISTINCT vehicle_id) as unique_vehicles
    FROM registrations WHERE show_id = show_id;
    
    -- Vehicle breakdown by decade
    SELECT 
        CONCAT(FLOOR(v.year/10)*10, 's') as decade,
        COUNT(*) as count
    FROM registrations r
    JOIN vehicles v ON r.vehicle_id = v.id
    WHERE r.show_id = show_id
    GROUP BY FLOOR(v.year/10)
    ORDER BY decade;
    
    -- Top makes
    SELECT 
        v.make,
        COUNT(*) as count
    FROM registrations r
    JOIN vehicles v ON r.vehicle_id = v.id
    WHERE r.show_id = show_id
    GROUP BY v.make
    ORDER BY count DESC
    LIMIT 10;
END //

-- Procedure to register vehicle for show
CREATE PROCEDURE `sp_register_vehicle_for_show`(
    IN p_show_id INT,
    IN p_vehicle_id INT,
    IN p_owner_id INT,
    OUT p_registration_id INT,
    OUT p_success BOOLEAN,
    OUT p_message VARCHAR(255)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        SET p_success = FALSE;
        SET p_message = 'Database error occurred during registration';
        SET p_registration_id = NULL;
    END;
    
    START TRANSACTION;
    
    -- Check if vehicle is already registered for this show
    IF EXISTS (SELECT 1 FROM registrations WHERE show_id = p_show_id AND vehicle_id = p_vehicle_id) THEN
        SET p_success = FALSE;
        SET p_message = 'Vehicle is already registered for this show';
        SET p_registration_id = NULL;
        ROLLBACK;
    ELSE
        -- Insert registration
        INSERT INTO registrations (show_id, vehicle_id, owner_id, created_at)
        VALUES (p_show_id, p_vehicle_id, p_owner_id, NOW());
        
        SET p_registration_id = LAST_INSERT_ID();
        SET p_success = TRUE;
        SET p_message = 'Vehicle registered successfully';
        
        COMMIT;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- 6. CREATE TRIGGERS FOR DATA INTEGRITY
-- =====================================================

DELIMITER //

-- Trigger to update show statistics when registration is added
CREATE TRIGGER `tr_registration_after_insert`
AFTER INSERT ON `registrations`
FOR EACH ROW
BEGIN
    -- Update show registration count (if you have a statistics table)
    -- This is just an example - adjust based on your needs
    INSERT INTO system_logs (table_name, action, record_id, details, created_at)
    VALUES ('registrations', 'INSERT', NEW.id, 
            CONCAT('Vehicle ', NEW.vehicle_id, ' registered for show ', NEW.show_id), 
            NOW());
END //

-- Trigger to prevent deletion of shows with registrations
CREATE TRIGGER `tr_show_before_delete`
BEFORE DELETE ON `shows`
FOR EACH ROW
BEGIN
    DECLARE reg_count INT;
    SELECT COUNT(*) INTO reg_count FROM registrations WHERE show_id = OLD.id;
    
    IF reg_count > 0 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Cannot delete show with existing registrations';
    END IF;
END //

DELIMITER ;

-- =====================================================
-- 7. CLEANUP AND OPTIMIZATION
-- =====================================================

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Optimize tables
OPTIMIZE TABLE users, shows, vehicles, registrations, payments, calendars, calendar_events;

-- Update table statistics
ANALYZE TABLE users, shows, vehicles, registrations, payments, calendars, calendar_events;

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================

-- Check foreign key constraints
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE()
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- Check indexes
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- Check constraints
SELECT 
    TABLE_NAME,
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = DATABASE()
ORDER BY TABLE_NAME, CONSTRAINT_TYPE;

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

SELECT 'Database relationship improvements completed successfully!' as Status;
SELECT 'Please verify all foreign key constraints and indexes are working correctly.' as Note;
SELECT 'Run the verification queries above to confirm changes.' as Action;
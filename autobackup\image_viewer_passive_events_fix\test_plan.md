# Image Viewer Passive Events Fix - Test Plan

## Testing Instructions

### 1. Browser Console Check
1. Open the application in a modern browser (Chrome, Firefox, Safari, Edge)
2. Open Developer Tools (F12)
3. Navigate to a page with image thumbnails that use the image viewer
4. Click on an image thumbnail to open the image viewer
5. **Expected Result**: No console warnings about passive event listeners should appear

### 2. Mouse Wheel Zoom Test
1. Open an image in the image viewer
2. Use mouse wheel to zoom in and out
3. **Expected Result**: 
   - Zoom functionality works properly
   - Page does not scroll while zooming
   - No console errors or warnings

### 3. Touch Device Drag Test (Mobile/Tablet)
1. Open an image in the image viewer on a touch device
2. Zoom in to make the image larger than the viewport
3. Try to drag the image around
4. **Expected Result**:
   - Drag functionality works properly
   - Image moves smoothly with touch gestures
   - No console errors or warnings
   - Page does not scroll while dragging

### 4. Touch Device Zoom Test (Mobile/Tablet)
1. Open an image in the image viewer on a touch device
2. Use pinch gestures to zoom (if supported)
3. Use zoom controls to zoom in/out
4. **Expected Result**:
   - Zoom functionality works properly
   - No console errors or warnings

### 5. Regression Testing
1. Test all other image viewer functionality:
   - Opening/closing images
   - Navigation between images
   - Keyboard controls (arrow keys, +/-, escape)
   - Reset zoom functionality
2. **Expected Result**: All existing functionality continues to work as before

## Console Warnings to Look For (Should NOT appear)
- `[Violation] Added non-passive event listener to a scroll-blocking 'wheel' event`
- `[Violation] Added non-passive event listener to a scroll-blocking 'touchstart' event`
- `[Violation] Added non-passive event listener to a scroll-blocking 'touchmove' event`

## Browser Compatibility
Test on:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## Success Criteria
✅ No passive event listener warnings in console
✅ All zoom functionality works correctly
✅ All drag functionality works correctly
✅ No regression in existing features
✅ Improved page responsiveness
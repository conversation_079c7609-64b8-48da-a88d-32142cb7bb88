    public function settings() {
        // Check if user is admin
        if (!$this->auth->hasRole('admin')) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods(false);
        
        // Get admin payment settings
        $paymentSettings = $this->paymentModel->getAllPaymentSettings(true);
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Update admin payment settings
            $adminSettings = [
                'paypal_client_id',
                'paypal_secret',
                'paypal_sandbox',
                'cashapp_id',
                'venmo_id',
                'currency',
                'show_listing_fee_enabled',
                'default_registration_fee',
                'default_show_listing_fee'
            ];
            
            foreach ($adminSettings as $setting) {
                if (isset($_POST[$setting])) {
                    $this->paymentModel->updatePaymentSetting($setting, $_POST[$setting], true);
                }
            }
            
            $this->setFlashMessage('settings_success', 'Payment settings updated successfully', 'success');
            $this->redirect('payment/settings');
        } else {
            // Display settings form
            $data = [
                'title' => 'Payment Settings',
                'payment_methods' => $paymentMethods,
                'payment_settings' => $paymentSettings
            ];
            
            $this->view('admin/payments/settings', $data);
        }
    }
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php if (isset($_SESSION['cache_buster'])): ?>
    <!-- Cache control headers to prevent caching after updates -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <?php endif; ?>
    <title><?php echo isset($title) ? $title . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/image-viewer.css">
    
    <?php 
    // Get custom CSS from settings
    if (!isset($settingsModel)) {
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();
    }
    
    // Include page-specific custom CSS if provided
    if (isset($custom_css) && !empty($custom_css)) {
        echo '<link rel="stylesheet" href="' . $custom_css . '">';
    }
    
    // Get global custom CSS from settings
    $global_custom_css = $settingsModel->getSetting('custom_css', '');
    $primary_color = $settingsModel->getSetting('primary_color', '#007bff');
    $secondary_color = $settingsModel->getSetting('secondary_color', '#6c757d');
    $accent_color = $settingsModel->getSetting('accent_color', '#28a745');
    $background_color = $settingsModel->getSetting('background_color', '#f8f9fa');
    $text_color = $settingsModel->getSetting('text_color', '#212529');
    
    // Include global custom CSS if it exists
    if (!empty($global_custom_css)) {
        echo '<link rel="stylesheet" href="' . $global_custom_css . '">';
    }
    
    if (!empty($primary_color) || !empty($secondary_color) || !empty($accent_color) || !empty($background_color) || !empty($text_color)) : 
    ?>
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --accent-color: <?php echo $accent_color; ?>;
            --background-color: <?php echo $background_color; ?>;
            --text-color: <?php echo $text_color; ?>;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .bg-primary {
            background-color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .alert-success, .bg-success {
            background-color: var(--accent-color) !important;
        }
        
        /* Custom CSS from branding settings */
        <?php echo isset($custom_css) ? $custom_css : ''; ?>
    </style>
    <?php endif; ?>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo isset($csrf_token) ? htmlspecialchars($csrf_token) : ''; ?>">
    
    <!-- Base URL for JavaScript -->
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
        <?php if (isset($_SESSION['cache_buster'])): ?>
        // Cache buster for JavaScript files
        const CACHE_BUSTER = '<?php echo $_SESSION['cache_buster']; ?>';
        
        // Function to add cache buster to script and link tags
        document.addEventListener('DOMContentLoaded', function() {
            // Add cache buster to script tags
            document.querySelectorAll('script[src]').forEach(function(script) {
                if (script.src.indexOf('?') === -1) {
                    script.src = script.src + '?v=' + CACHE_BUSTER;
                } else {
                    script.src = script.src + '&v=' + CACHE_BUSTER;
                }
            });
            
            // Add cache buster to link tags (CSS)
            document.querySelectorAll('link[rel="stylesheet"]').forEach(function(link) {
                if (link.href.indexOf('?') === -1) {
                    link.href = link.href + '?v=' + CACHE_BUSTER;
                } else {
                    link.href = link.href + '&v=' + CACHE_BUSTER;
                }
            });
        });
        <?php endif; ?>
    </script>
    
    <?php 
    // Get favicon from settings
    if (!isset($settingsModel)) {
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();
    }
    $site_favicon = $settingsModel->getSetting('site_favicon', '');
    
    if (!empty($site_favicon)) : 
    ?>
    <!-- Favicon -->
    <link rel="icon" href="<?php echo BASE_URL . $site_favicon; ?>" type="image/x-icon">
    <link rel="shortcut icon" href="<?php echo BASE_URL . $site_favicon; ?>" type="image/x-icon">
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <?php 
                // Get site logo from settings
                require_once APPROOT . '/models/SettingsModel.php';
                $settingsModel = new SettingsModel();
                $site_logo = $settingsModel->getSetting('site_logo', '');
                
                if (!empty($site_logo)) : 
                ?>
                    <img src="<?php echo BASE_URL . $site_logo; ?>" alt="<?php echo APP_NAME; ?>" height="40" class="d-inline-block align-text-top">
                <?php else : ?>
                    <?php echo APP_NAME; ?>
                <?php endif; ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/show">Shows</a>
                    </li>
                    <?php if (isset($_SESSION['user_id'])) : ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/user/dashboard">Dashboard</a>
                        </li>
                    <?php endif; ?>
                    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Tools
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/form_designer/templates">Form Builder</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/imageManager">Image Manager</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultTemplates">Default Templates</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/entityTemplates">Entity Templates</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">Judging & Coordination</h6></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/judgingMetrics">Judging Metrics</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultMetrics">Default Metrics</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/ageWeights">Age Weights</a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultCategories">Default Categories</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/settings">Settings</a></li>
                            </ul>
                        </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/home/<USER>">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/home/<USER>">Contact</a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])) : ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <?php 
                                // Use our helper function to display the profile image
                                $imageUrl = getUserProfileImageUrl($_SESSION['user_id']);
                                if ($imageUrl) : 
                                ?>
                                    <div class="rounded-circle overflow-hidden me-2" style="width: 30px; height: 30px; background-color: rgba(255,255,255,0.2);">
                                        <img src="<?php echo htmlspecialchars($imageUrl); ?>" alt="Profile" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                                    </div>
                                <?php else : ?>
                                    <i class="fas fa-user-circle me-2"></i>
                                <?php endif; ?>
                                <?php 
                                if (isset($current_user) && isset($current_user->name)) {
                                    echo htmlspecialchars($current_user->name);
                                } elseif (isset($_SESSION['user_name'])) {
                                    echo htmlspecialchars($_SESSION['user_name']);
                                } else {
                                    echo 'Account';
                                }
                                ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">Admin Dashboard</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">Coordinator Dashboard</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">Judge Dashboard</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/staff/dashboard">Staff Dashboard</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Show Management</h6></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows">Manage Shows</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/registerVehicle">
                                        <i class="fas fa-car me-2"></i> Register Vehicle for User
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><h6 class="dropdown-header">Tools</h6></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/form_designer/templates">Form Builder</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/imageManager">Image Manager</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultTemplates">Default Templates</a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/entityTemplates">Entity Templates</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                <?php endif; ?>
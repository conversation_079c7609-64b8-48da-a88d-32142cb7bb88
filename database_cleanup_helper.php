<?php
/**
 * Database Cleanup Helper
 * 
 * This script helps you safely clean up unused tables identified by the database analysis.
 * It provides a safe, step-by-step approach to database cleanup.
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Load required classes
require_once APPROOT . '/core/Database.php';

// Only allow running from CLI or by admin
if (php_sapi_name() !== 'cli') {
    // Check if user is admin (following your site's pattern)
    session_start();
    if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
        die('Access denied. Admin access required.');
    }
}

class DatabaseCleanupHelper {
    private $db;
    
    public function __construct() {
        try {
            $this->db = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (PDOException $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }
    
    public function showCleanupOptions() {
        echo "<h1>🧹 Database Cleanup Helper</h1>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .safe { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
            .warning { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
            .danger { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0; }
            .code { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-family: monospace; font-size: 12px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .btn { display: inline-block; padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 4px; }
            .btn-safe { background: #28a745; color: white; }
            .btn-warning { background: #ffc107; color: black; }
            .btn-info { background: #17a2b8; color: white; }
        </style>";
        
        $action = $_GET['action'] ?? 'menu';
        
        switch ($action) {
            case 'check_empty':
                $this->checkEmptyTables();
                break;
            case 'show_table_data':
                $this->showTableData($_GET['table'] ?? '');
                break;
            case 'backup_table':
                $this->backupTable($_GET['table'] ?? '');
                break;
            case 'drop_table':
                $this->dropTable($_GET['table'] ?? '', $_GET['confirm'] ?? '');
                break;
            default:
                $this->showMainMenu();
        }
    }
    
    private function showMainMenu() {
        echo "<div class='warning'>";
        echo "<h2>⚠️ Important Safety Notice</h2>";
        echo "<p><strong>Always backup your database before making any changes!</strong></p>";
        echo "<p>This tool helps you safely clean up unused tables, but you should:</p>";
        echo "<ul>";
        echo "<li>Run the database analysis first to identify unused tables</li>";
        echo "<li>Check each table's data before removing it</li>";
        echo "<li>Test your website after any changes</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h2>🛠️ Cleanup Options</h2>";
        
        echo "<div class='safe'>";
        echo "<h3>1. Check for Empty Tables</h3>";
        echo "<p>Find tables that have zero rows and are safe to remove.</p>";
        echo "<a href='?action=check_empty' class='btn btn-safe'>Check Empty Tables</a>";
        echo "</div>";
        
        echo "<div class='warning'>";
        echo "<h3>2. Inspect Table Data</h3>";
        echo "<p>Look at the data in specific tables before deciding to remove them.</p>";
        echo "<form method='get' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='show_table_data'>";
        echo "<input type='text' name='table' placeholder='Enter table name' required>";
        echo "<input type='submit' value='Show Table Data' class='btn btn-warning'>";
        echo "</form>";
        echo "</div>";
        
        echo "<h2>📋 Quick Actions</h2>";
        echo "<p><a href='analyze_actual_database_usage.php' class='btn btn-info'>Run Database Analysis</a></p>";
        echo "<p><a href='quick_database_analysis.php' class='btn btn-info'>Quick Database Overview</a></p>";
    }
    
    private function checkEmptyTables() {
        echo "<h2>🔍 Checking for Empty Tables</h2>";
        echo "<p><a href='?'>← Back to Menu</a></p>";
        
        // Get all tables
        $tables = $this->db->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<table>";
        echo "<tr><th>Table Name</th><th>Row Count</th><th>Actions</th></tr>";
        
        $emptyTables = [];
        
        foreach ($tables as $table) {
            try {
                $count = $this->db->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                
                if ($count == 0) {
                    $emptyTables[] = $table;
                    echo "<tr style='background: #d4edda;'>";
                    echo "<td><strong>$table</strong></td>";
                    echo "<td>0</td>";
                    echo "<td>";
                    echo "<a href='?action=show_table_data&table=$table' class='btn btn-info'>View Structure</a> ";
                    echo "<a href='?action=drop_table&table=$table' class='btn btn-warning'>Drop Table</a>";
                    echo "</td>";
                    echo "</tr>";
                } elseif ($count < 10) {
                    echo "<tr style='background: #fff3cd;'>";
                    echo "<td>$table</td>";
                    echo "<td>$count</td>";
                    echo "<td><a href='?action=show_table_data&table=$table' class='btn btn-info'>View Data</a></td>";
                    echo "</tr>";
                }
            } catch (Exception $e) {
                echo "<tr style='background: #f8d7da;'>";
                echo "<td>$table</td>";
                echo "<td>Error</td>";
                echo "<td>Cannot access</td>";
                echo "</tr>";
            }
        }
        
        echo "</table>";
        
        if (!empty($emptyTables)) {
            echo "<div class='safe'>";
            echo "<h3>✅ Found " . count($emptyTables) . " Empty Tables</h3>";
            echo "<p>These tables are empty and could potentially be removed:</p>";
            echo "<div class='code'>";
            foreach ($emptyTables as $table) {
                echo "DROP TABLE IF EXISTS `$table`;<br>";
            }
            echo "</div>";
            echo "<p><strong>⚠️ Only run these commands if you're sure these tables are unused!</strong></p>";
            echo "</div>";
        }
    }
    
    private function showTableData($tableName) {
        if (empty($tableName)) {
            echo "<p>No table specified.</p>";
            return;
        }
        
        echo "<h2>🔍 Table: $tableName</h2>";
        echo "<p><a href='?'>← Back to Menu</a></p>";
        
        try {
            // Show table structure
            echo "<h3>Table Structure</h3>";
            $structure = $this->db->query("DESCRIBE `$tableName`")->fetchAll(PDO::FETCH_ASSOC);
            echo "<table>";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            foreach ($structure as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show row count
            $count = $this->db->query("SELECT COUNT(*) FROM `$tableName`")->fetchColumn();
            echo "<h3>Data Summary</h3>";
            echo "<p><strong>Total rows:</strong> $count</p>";
            
            if ($count > 0 && $count <= 20) {
                echo "<h3>Sample Data (showing all $count rows)</h3>";
                $data = $this->db->query("SELECT * FROM `$tableName` LIMIT 20")->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($data)) {
                    echo "<table>";
                    echo "<tr>";
                    foreach (array_keys($data[0]) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    
                    foreach ($data as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } elseif ($count > 20) {
                echo "<h3>Sample Data (showing first 5 rows of $count total)</h3>";
                $data = $this->db->query("SELECT * FROM `$tableName` LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($data)) {
                    echo "<table>";
                    echo "<tr>";
                    foreach (array_keys($data[0]) as $column) {
                        echo "<th>$column</th>";
                    }
                    echo "</tr>";
                    
                    foreach ($data as $row) {
                        echo "<tr>";
                        foreach ($row as $value) {
                            echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . "</td>";
                        }
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            }
            
            // Actions
            echo "<h3>Actions</h3>";
            if ($count == 0) {
                echo "<div class='safe'>";
                echo "<p>This table is empty and could be safely removed.</p>";
                echo "<a href='?action=drop_table&table=$tableName' class='btn btn-warning'>Drop This Table</a>";
                echo "</div>";
            } else {
                echo "<div class='warning'>";
                echo "<p>This table contains $count rows of data. Be careful before removing it.</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            echo "<p>Error accessing table: " . $e->getMessage() . "</p>";
        }
    }
    
    private function dropTable($tableName, $confirm) {
        if (empty($tableName)) {
            echo "<p>No table specified.</p>";
            return;
        }
        
        echo "<h2>🗑️ Drop Table: $tableName</h2>";
        echo "<p><a href='?'>← Back to Menu</a></p>";
        
        if ($confirm !== 'yes') {
            echo "<div class='danger'>";
            echo "<h3>⚠️ Confirm Table Deletion</h3>";
            echo "<p><strong>You are about to permanently delete the table: $tableName</strong></p>";
            echo "<p>This action cannot be undone!</p>";
            
            try {
                $count = $this->db->query("SELECT COUNT(*) FROM `$tableName`")->fetchColumn();
                echo "<p><strong>This table contains $count rows of data.</strong></p>";
            } catch (Exception $e) {
                echo "<p>Could not check row count.</p>";
            }
            
            echo "<p><strong>Are you absolutely sure you want to delete this table?</strong></p>";
            echo "<a href='?action=drop_table&table=$tableName&confirm=yes' class='btn btn-danger' onclick='return confirm(\"Are you REALLY sure you want to delete the table $tableName? This cannot be undone!\")'>Yes, Delete Table</a> ";
            echo "<a href='?' class='btn btn-safe'>Cancel</a>";
            echo "</div>";
        } else {
            try {
                $this->db->exec("DROP TABLE IF EXISTS `$tableName`");
                echo "<div class='safe'>";
                echo "<h3>✅ Table Deleted Successfully</h3>";
                echo "<p>The table '$tableName' has been removed from your database.</p>";
                echo "<a href='?' class='btn btn-info'>Back to Menu</a>";
                echo "</div>";
            } catch (Exception $e) {
                echo "<div class='danger'>";
                echo "<h3>❌ Error Deleting Table</h3>";
                echo "<p>Could not delete table: " . $e->getMessage() . "</p>";
                echo "<a href='?' class='btn btn-info'>Back to Menu</a>";
                echo "</div>";
            }
        }
    }
}

// Run the cleanup helper
$helper = new DatabaseCleanupHelper();
$helper->showCleanupOptions();
?>
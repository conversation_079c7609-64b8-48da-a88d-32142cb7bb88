<?php
/**
 * Calendar Model
 * 
 * This model handles all database operations related to the calendar system.
 * 
 * Version 1.0.1 - Fixed transaction method names
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarModel {
    private $db;
    private $showModel;
    private $eventTrigger;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // Initialize ShowModel
        require_once APPROOT . '/models/ShowModel.php';
        $this->showModel = new ShowModel();
        
        // Initialize EventTrigger if it exists
        if (file_exists(APPROOT . '/core/EventTrigger.php')) {
            require_once APPROOT . '/core/EventTrigger.php';
            $this->eventTrigger = new EventTrigger();
        }
        
        // Ensure the calendars table exists
        $this->ensureCalendarsTableExists();
    }
    
    /**
     * Update map provider settings
     *
     * @param array $settings
     * @return bool
     */
    public function updateMapProviderSettings($settings)
    {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Map provider settings to update
            $mapSettings = [
                'provider', 'api_key', 'default_zoom', 'default_lat', 
                'default_lng', 'filter_radius', 'tile_url', 'attribution'
            ];
            
            foreach ($mapSettings as $key) {
                if (isset($settings[$key])) {
                    $settingKey = 'map_' . $key;
                    $value = $settings[$key];
                    
                    // Check if setting exists
                    $this->db->query("SELECT * FROM calendar_settings WHERE setting_key = :key", [':key' => $settingKey]);
                    $exists = $this->db->rowCount() > 0;
                    
                    if ($exists) {
                        // Update existing setting
                        $this->db->query(
                            "UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key",
                            [':key' => $settingKey, ':value' => $value]
                        );
                    } else {
                        // Insert new setting
                        $this->db->query(
                            "INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)",
                            [':key' => $settingKey, ':value' => $value]
                        );
                    }
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('Error in CalendarModel::updateMapProviderSettings: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get marker customization settings
     * 
     * @return array
     */
    public function getMarkerCustomizationSettings()
    {
        try {
            $settings = $this->getCalendarSettings();
            
            // Default marker customization settings
            $markerSettings = [
                'marker_type' => 'default',       // default, pin, custom
                'marker_size' => 16,              // size in pixels
                'marker_border' => 'true',        // true/false
                'marker_border_color' => '#FFFFFF', // border color
                'marker_border_width' => 2,       // border width in pixels
                'custom_marker_url' => '',        // URL for custom marker image
                'use_calendar_colors' => 'true',  // true/false
                'default_marker_color' => '#3788d8' // default color if not using calendar colors
            ];
            
            // Override with database settings if available
            foreach ($markerSettings as $key => $value) {
                if (isset($settings[$key])) {
                    $markerSettings[$key] = $settings[$key];
                }
            }
            
            return $markerSettings;
        } catch (Exception $e) {
            error_log('Error in CalendarModel::getMarkerCustomizationSettings: ' . $e->getMessage());
            return [
                'marker_type' => 'default',
                'marker_size' => 16,
                'marker_border' => 'true',
                'marker_border_color' => '#FFFFFF',
                'marker_border_width' => 2,
                'custom_marker_url' => '',
                'use_calendar_colors' => 'true',
                'default_marker_color' => '#3788d8'
            ];
        }
    }
    
    /**
     * Update marker customization settings
     * 
     * @param array $settings
     * @return bool
     */
    public function updateMarkerCustomizationSettings($settings)
    {
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Debug log
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Updating marker settings: ' . json_encode($settings));
            }
            
            // Marker settings to update
            $markerSettings = [
                'marker_type', 'marker_size', 'marker_border', 'marker_border_color',
                'marker_border_width', 'custom_marker_url', 'use_calendar_colors', 'default_marker_color'
            ];
            
            foreach ($markerSettings as $key) {
                if (isset($settings[$key])) {
                    $value = $settings[$key];
                    
                    // Debug log
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("Setting $key = $value");
                    }
                    
                    // Check if setting exists
                    $this->db->query("SELECT * FROM calendar_settings WHERE setting_key = :key", [':key' => $key]);
                    $exists = $this->db->rowCount() > 0;
                    
                    if ($exists) {
                        // Update existing setting
                        $this->db->query(
                            "UPDATE calendar_settings SET setting_value = :value WHERE setting_key = :key",
                            [':key' => $key, ':value' => $value]
                        );
                    } else {
                        // Insert new setting
                        $this->db->query(
                            "INSERT INTO calendar_settings (setting_key, setting_value) VALUES (:key, :value)",
                            [':key' => $key, ':value' => $value]
                        );
                    }
                }
            }
            
            // Commit transaction
            $this->db->commit();
            
            return true;
        } catch (Exception $e) {
            // Rollback transaction on error
            $this->db->rollBack();
            error_log('Error in CalendarModel::updateMarkerCustomizationSettings: ' . $e->getMessage());
            return false;
        }
    }
}
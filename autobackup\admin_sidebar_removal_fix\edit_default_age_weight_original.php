<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Edit Default Age Weight</h1>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?php echo BASE_URL; ?>/admin/defaultAgeWeights" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Default Age Weights
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-3">
            <?php require APPROOT . '/views/includes/admin_settings_sidebar.php'; ?>
        </div>
        <div class="col-md-9">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Edit Default Age Weight</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo BASE_URL; ?>/admin/editDefaultAgeWeight/<?php echo $id; ?>" method="post">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="min_year" class="form-label">Minimum Year <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php echo (!empty($min_year_err)) ? 'is-invalid' : ''; ?>" id="min_year" name="min_year" value="<?php echo $min_year; ?>" required>
                                    <div class="invalid-feedback"><?php echo $min_year_err; ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="max_year" class="form-label">Maximum Year <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php echo (!empty($max_year_err)) ? 'is-invalid' : ''; ?>" id="max_year" name="max_year" value="<?php echo $max_year; ?>" required>
                                    <div class="invalid-feedback"><?php echo $max_year_err; ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="weight" class="form-label">Weight <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php echo (!empty($weight_err)) ? 'is-invalid' : ''; ?>" id="weight" name="weight" value="<?php echo $weight; ?>" step="0.01" min="0" required>
                                    <div class="invalid-feedback"><?php echo $weight_err; ?></div>
                                    <div class="form-text">Multiplier applied to scores for vehicles in this age range.</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <input type="text" class="form-control <?php echo (!empty($description_err)) ? 'is-invalid' : ''; ?>" id="description" name="description" value="<?php echo $description; ?>">
                            <div class="invalid-feedback"><?php echo $description_err; ?></div>
                            <div class="form-text">Optional label for this age range (e.g., "Antique", "Vintage", "Classic", etc.)</div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo BASE_URL; ?>/admin/defaultAgeWeights" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Update Default Age Weight</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
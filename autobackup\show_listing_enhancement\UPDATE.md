# Show Listing Enhancement Update

## Overview
This update improves the show listing page with enhanced filtering capabilities, better date/time formatting, and improved visual design.

## Changes Made

### Enhanced State and City Filtering
- Improved state dropdown to display both abbreviation and full name (e.g., "NY - New York")
- Added dynamic city filtering that updates based on the selected state
- Added JavaScript to handle state selection changes and update the city dropdown accordingly

### Improved Date and Time Display
- Separated dates and times for better readability
- Dates are now displayed prominently on their own line
- Times are shown in a smaller, muted format with clock icons
- Used consistent formatting across all date/time displays

### Visual Design Improvements
- Enhanced card layout with better spacing and typography
- Improved badge styling with rounded-pill design and appropriate icons
- Made the "View Details" button larger and more prominent
- Added consistent visual hierarchy across all information sections

### Technical Improvements
- Added debug mode logging for state and city filtering
- Updated ShowController to pass filtered cities based on selected state
- Modified getUniqueCitiesFromShows method to accept a state filter parameter
- Enhanced getUniqueStatesFromShows method to provide better display names

## Files Modified
- `/views/show/index.php` - Updated date/time formatting and added JavaScript for dynamic filtering
- `/controllers/ShowController.php` - Added support for filtered cities based on selected state
- `/models/ShowModel.php` - Enhanced state and city retrieval methods with filtering capabilities

## Debugging Support
Added detailed logging when DEBUG_MODE is enabled:
- Logs the number of unique states found
- Logs the number of unique cities found (with state filter information when applicable)
- Provides console logging in the browser for current filter settings
<?php
/**
 * VAPID Key API Endpoint
 * Returns the public VAPID key for push notifications
 */

// Set JSON response header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit();
}

try {
    // Include config
    require_once '../../config/config.php';
    
    // Get VAPID public key from config
    $vapidPublicKey = defined('VAPID_PUBLIC_KEY') ? VAPID_PUBLIC_KEY : null;
    
    if (empty($vapidPublicKey)) {
        throw new Exception('VAPID public key not configured');
    }
    
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log('VAPID: Serving public key for push notifications');
    }
    
    // Return the public key
    echo json_encode([
        'success' => true,
        'publicKey' => $vapidPublicKey
    ]);
    
} catch (Exception $e) {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log('VAPID Key API Error: ' . $e->getMessage());
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error'
    ]);
}
?>
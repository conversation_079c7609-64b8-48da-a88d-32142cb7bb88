# Facebook Redirect URI Fix

## Issue
Facebook login was failing with the error:
"This redirect failed because the redirect URI is not whitelisted in the app's Client OAuth Settings. Make sure Client and Web OAuth Login are on and add all your app domains as Valid OAuth Redirect URIs."

## Root Cause
The redirect URI in the application configuration included the `.php` extension, but the URI registered in the Facebook Developer Console did not include this extension.

## Changes Made
- Modified `config.php` to remove the `.php` extension from the Facebook redirect URI
- Changed from: `BASE_URL . '/auth/facebookCallback.php'`
- Changed to: `BASE_URL . '/auth/facebookCallback'`

## Date
Fixed on: <?php echo date('Y-m-d'); ?>

## Version
App version: 3.35.44 → 3.35.45
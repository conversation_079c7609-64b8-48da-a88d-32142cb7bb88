<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 5px 0; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
    </style>
</head>
<body>
    <h2>Minimal Test - No Script Loading</h2>
    <div id="logs"></div>
    <button onclick="loadScript()">Load Script Manually</button>

    <script>
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const className = type === 'error' ? 'log error' : type === 'success' ? 'log success' : 'log';
            logs.innerHTML += '<div class="' + className + '">' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log(message);
        }

        function loadScript() {
            log('Loading script manually...');
            
            const script = document.createElement('script');
            script.src = '/public/js/camera-banner.js';
            
            script.onload = function() {
                log('Script loaded!', 'success');
                setTimeout(() => {
                    log('Checking after 1 second...');
                    if (window.cameraBanner) {
                        log('cameraBanner exists!', 'success');
                        log('Version: ' + window.cameraBanner.version);
                    } else {
                        log('cameraBanner not found', 'error');
                    }
                }, 1000);
            };
            
            script.onerror = function() {
                log('Script failed to load!', 'error');
            };
            
            document.head.appendChild(script);
        }

        log('Page loaded - ready to test');
    </script>
</body>
</html>
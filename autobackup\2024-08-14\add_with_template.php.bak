<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
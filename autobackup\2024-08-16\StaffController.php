<?php
/**
 * Staff Controller
 * 
 * This controller handles all staff-related functionality.
 */
class StaffController extends Controller {
    private $showModel;
    private $userModel;
    private $vehicleModel;
    private $registrationModel;
    private $paymentModel;
    private $staffModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in and is a staff member
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn() || !$this->auth->hasRole(['staff', 'coordinator', 'admin'])) {
            $this->redirect('home/access_denied');
            return;
        }
        
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->paymentModel = $this->model('PaymentModel');
        $this->staffModel = $this->model('StaffModel');
        $this->db = new Database();
    }
    
    /**
     * Default index method - redirects to dashboard
     */
    public function index() {
        $this->redirect('staff/dashboard');
    }
    
    /**
     * Staff dashboard
     */
    public function dashboard() {
        // Get current user ID
        $userId = $this->auth->getCurrentUserId();
        
        // If admin or coordinator, they can see all shows
        if ($this->auth->hasRole(['admin', 'coordinator'])) {
            $shows = $this->showModel->getShows();
        } else {
            // For staff, only get shows they are assigned to
            $shows = $this->staffModel->getAssignedShows($userId);
        }
        
        $data = [
            'title' => 'Staff Dashboard',
            'shows' => $shows
        ];
        
        $this->view('staff/dashboard', $data);
    }
    
    /**
     * Show details
     * 
     * @param int $id Show ID
     */
    public function show($id) {
        // Get show
        $show = $this->showModel->getShowById($id);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to view this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($id);
        
        // Get registration counts
        $registrationCounts = $this->registrationModel->countRegistrationsByCategory($id);
        
        $data = [
            'title' => $show->name,
            'show' => $show,
            'categories' => $categories,
            'registrationCounts' => $registrationCounts
        ];
        
        $this->view('staff/show', $data);
    }
    
    /**
     * Show registrations
     * 
     * @param int $showId Show ID
     */
    public function registrations($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to view this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $showId);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get registrations
        $registrations = $this->registrationModel->getRegistrationsByShow($showId);
        
        $data = [
            'title' => $show->name . ' - Registrations',
            'show' => $show,
            'registrations' => $registrations
        ];
        
        $this->view('staff/registrations', $data);
    }
    
    /**
     * View registration details
     * 
     * @param int $id Registration ID
     */
    public function registration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to view this registration
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        // Get owner
        $owner = $this->userModel->getUserById($vehicle->owner_id);
        
        // Get category
        $category = $this->showModel->getCategoryById($registration->category_id);
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'vehicle' => $vehicle,
            'owner' => $owner,
            'category' => $category
        ];
        
        $this->view('staff/registration', $data);
    }
    
    /**
     * Process check-in for a vehicle
     * 
     * @param int $id Registration ID
     */
    public function checkIn($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to check in for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Process check-in
        if ($this->registrationModel->checkInVehicle($id)) {
            $this->setFlashMessage('staff_message', 'Vehicle checked in successfully', 'success');
        } else {
            $this->setFlashMessage('staff_message', 'Failed to check in vehicle', 'danger');
        }
        
        // Redirect back to registration
        $this->redirect('staff/registration/' . $id);
    }
    
    /**
     * Undo check-in for a vehicle
     * 
     * @param int $id Registration ID
     */
    public function undoCheckIn($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is authorized to undo check-in for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $registration->show_id);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Process undo check-in
        if ($this->registrationModel->undoCheckInVehicle($id)) {
            $this->setFlashMessage('staff_message', 'Check-in undone successfully', 'success');
        } else {
            $this->setFlashMessage('staff_message', 'Failed to undo check-in', 'danger');
        }
        
        // Redirect back to registration
        $this->redirect('staff/registration/' . $id);
    }
    
    /**
     * Create a new registration
     * 
     * @param int $showId Show ID
     */
    public function createRegistration($showId) {
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is authorized to create registrations for this show
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Admin and coordinator of the show can always access
        $hasAccess = $this->auth->hasRole('admin') || 
                    ($userRole == 'coordinator' && $show->coordinator_id == $userId);
        
        // Staff needs to be assigned to the show
        if ($userRole == 'staff' && !$hasAccess) {
            $hasAccess = $this->staffModel->isAssignedToShow($userId, $showId);
        }
        
        if (!$hasAccess) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get categories
        $categories = $this->showModel->getShowCategories($showId);
        
        // Get vehicles (all vehicles for admin/coordinator, only user's vehicles for regular users)
        if ($this->auth->hasRole(['admin', 'coordinator', 'staff'])) {
            $vehicles = $this->vehicleModel->getAllVehicles();
        } else {
            $vehicles = $this->vehicleModel->getVehiclesByOwner($userId);
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $data = [
                'show_id' => $showId,
                'vehicle_id' => trim($_POST['vehicle_id']),
                'category_id' => trim($_POST['category_id']),
                'notes' => trim($_POST['notes']),
                'status' => 'pending',
                'vehicle_id_err' => '',
                'category_id_err' => '',
                'title' => 'Create Registration',
                'show' => $show,
                'categories' => $categories,
                'vehicles' => $vehicles
            ];
            
            // Validate vehicle
            if (empty($data['vehicle_id'])) {
                $data['vehicle_id_err'] = 'Please select a vehicle';
            }
            
            // Validate category
            if (empty($data['category_id'])) {
                $data['category_id_err'] = 'Please select a category';
            }
            
            // Check for errors
            if (empty($data['vehicle_id_err']) && empty($data['category_id_err'])) {
                // Create registration
                $registrationId = $this->registrationModel->createRegistration($data);
                
                if ($registrationId) {
                    $this->setFlashMessage('staff_message', 'Registration created successfully', 'success');
                    $this->redirect('staff/registration/' . $registrationId);
                } else {
                    $this->setFlashMessage('staff_message', 'Failed to create registration', 'danger');
                }
            }
            
            // If we got this far, validation failed, so render form with errors
            $this->view('staff/create_registration', $data);
        } else {
            // Initial form load
            $data = [
                'title' => 'Create Registration',
                'show' => $show,
                'categories' => $categories,
                'vehicles' => $vehicles,
                'vehicle_id' => '',
                'category_id' => '',
                'notes' => '',
                'vehicle_id_err' => '',
                'category_id_err' => ''
            ];
            
            $this->view('staff/create_registration', $data);
        }
    }
}
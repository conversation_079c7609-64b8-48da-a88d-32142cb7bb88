<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Map</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group me-2">
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-outline-primary">
                    <i class="fas fa-calendar me-2"></i> Event View
                </a>
                <a href="<?php echo URLROOT; ?>/calendar/map" class="btn btn-primary active">
                    <i class="fas fa-map-marker-alt me-2"></i> Map View
                </a>
            </div>
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <?php if (isAdmin()): ?>
                <a href="<?php echo URLROOT; ?>/calendar/mapSettings" class="btn btn-outline-secondary">
                    <i class="fas fa-cog me-2"></i> Map Settings
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Filters Panel -->
        <div class="col-md-3 mb-4">
            <?php include APPROOT . '/views/calendar/includes/advanced_filter.php'; ?>
        </div>

        <!-- Map and Event List -->
        <div class="col-md-9">
            <div class="row">
                <!-- Map Container -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-body p-0">
                            <div id="map" style="height: 500px;"></div>
                        </div>
                    </div>
                </div>

                <!-- Event List -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Events</h5>
                            <span id="event-count" class="badge bg-light text-dark">0 events</span>
                        </div>
                        <div class="card-body">
                            <div id="event-list" class="list-group">
                                <!-- Events will be populated via JavaScript -->
                                <div class="text-center py-5" id="no-events-message">
                                    <i class="fas fa-map-marker-alt fa-3x mb-3 text-muted"></i>
                                    <p class="lead text-muted">No events to display. Try adjusting your filters.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
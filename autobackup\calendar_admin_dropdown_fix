Calendar Access Control Fix - v3.63.14

Files Modified:
1. views/calendar/custom_index_fixed.php - Added admin and login checks for dropdown menu items and Add Event button
2. views/calendar/map.php - Added login check for Add Event button
3. views/calendar/includes/advanced_filter.php - Added safety check for calendars data
4. controllers/CalendarController.php - Added access control to management and user methods, removed auth from filter API endpoints, whitelisted filter APIs for guest access

Changes Made:
ADMIN-ONLY ITEMS:
- Wrapped "Manage Calendars", "Manage Venues", and "Manage Clubs" dropdown items with isAdmin() check
- Added admin access control to manageCalendars(), manageVenues(), and manageClubs() controller methods
- Non-admin users will be redirected to calendar index if they try to access these management pages directly

LOGGED-IN USER ITEMS:
- Wrapped "Add Event" button and entire dropdown menu with isLoggedIn() check
- Wrapped "Request Club Ownership" and "Import Events" dropdown items with isLoggedIn() check
- Added login check to createEvent() and import() controller methods (requestClubOwnership already had one)
- Removed authentication requirements from filter API endpoints (getStates, getCities, getClubs, getVenues)
- Added filter API endpoints to guest methods whitelist in controller constructor
- Added safety check for calendar data in advanced filter view
- Guest users will be redirected to login page if they try to access creation features directly
- Guest users can now use advanced filters without authentication errors

Security Enhancement:
- Frontend: Admin-only and login-required dropdown items properly hidden based on user status
- Backend: Controller methods protected with appropriate access checks for creation/management features
- Filter API endpoints made publicly accessible for guest users to use advanced filtering
- Consistent with existing access control patterns in the calendar system

Date: 2024-12-20

SUMMARY:
Fixed calendar access control to properly restrict features based on user status:
- Admin-only: Manage Calendars, Manage Venues, Manage Clubs, Calendar Settings
- Login required: Add Event, Create Show, Create Calendar, Request Club Ownership, Import Events
- Guest accessible: View calendar events only (no action buttons or dropdown menu)
Added both frontend visibility controls and backend access protection.
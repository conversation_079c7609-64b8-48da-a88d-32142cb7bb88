<?php
/**
 * Test User Data Generator (Optimized & Safe)
 *
 * This script generates a large number of test users to test the performance
 * of the optimized admin users page with pagination and search functionality.
 *
 * SAFETY FEATURES:
 * - Uses SAFE TEST DOMAINS (gmai1.com, yaho0.com, etc.) to prevent deletion of real users
 * - Cleanup functions ONLY delete users with these intentionally misspelled domains
 * - Will NOT delete users with real email domains (gmail.com, yahoo.com, etc.)
 *
 * Features:
 * - Memory monitoring and limits
 * - Execution timeout protection
 * - Chunked generation for large datasets
 * - Real-time progress reporting
 * - Automatic garbage collection
 * - Improved error handling
 * - Safe domain usage to protect real users
 *
 * Usage:
 * Command Line:
 *   php generate_test_users.php [count] [action] [chunk_size]
 *   php generate_test_users.php 1000 generate
 *   php generate_test_users.php 5000 large 1000
 *   php generate_test_users.php 0 cleanup
 *   php generate_test_users.php 0 cleanup_simple
 *
 * Browser:
 *   Access directly for web interface with multiple generation options
 *
 * SAFE TEST DOMAINS USED:
 * - gmai1.com (instead of gmail.com)
 * - yaho0.com (instead of yahoo.com)
 * - hotmai1.com (instead of hotmail.com)
 * - out1ook.com (instead of outlook.com)
 * - test-example.com (safe test domain)
 *
 * WARNING: This will add many users to your database. Use only for testing!
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting if needed
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

class TestUserGenerator {
    private $db;
    private $batchSize = 50; // Reduced batch size for better memory management
    private $maxExecutionTime = 240; // 4 minutes to stay under 5 minute limit
    private $memoryLimit = 200; // MB - stay under 256MB limit

    public function __construct() {
        $this->db = new Database();

        // Set PHP limits for long-running operations
        ini_set('max_execution_time', $this->maxExecutionTime);
        ini_set('memory_limit', $this->memoryLimit . 'M');

        // Disable output buffering for real-time progress
        if (ob_get_level()) {
            ob_end_flush();
        }
    }
    
    /**
     * Generate test users
     *
     * @param int $count Number of users to generate
     * @param bool $verbose Whether to show progress output
     */
    public function generateUsers($count = 1000, $verbose = true) {
        if ($verbose) {
            echo "Generating {$count} test users...\n";
            echo "Batch size: {$this->batchSize} users per batch\n";
            echo "Memory limit: {$this->memoryLimit}MB\n";
            echo "Execution time limit: {$this->maxExecutionTime} seconds\n\n";
        }

        $startTime = microtime(true);
        $roles = ['user', 'coordinator', 'judge', 'admin', 'staff'];
        $statuses = ['active', 'inactive', 'pending'];
        // SAFE TEST DOMAINS - These are intentionally misspelled to avoid deleting real users
        $domains = ['gmai1.com', 'yaho0.com', 'hotmai1.com', 'out1ook.com', 'test-example.com'];
        
        // First names and last names for realistic data
        $firstNames = [
            'John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 
            'James', 'Jessica', 'William', 'Ashley', 'Richard', 'Amanda', 'Joseph', 
            'Stephanie', 'Thomas', 'Melissa', 'Christopher', 'Nicole', 'Daniel', 
            'Elizabeth', 'Matthew', 'Helen', 'Anthony', 'Deborah', 'Mark', 'Rachel',
            'Donald', 'Carolyn', 'Steven', 'Janet', 'Paul', 'Catherine', 'Andrew',
            'Maria', 'Joshua', 'Heather', 'Kenneth', 'Diane', 'Kevin', 'Ruth',
            'Brian', 'Julie', 'George', 'Joyce', 'Timothy', 'Virginia', 'Ronald',
            'Victoria', 'Jason', 'Kelly', 'Edward', 'Christina', 'Jeffrey', 'Joan'
        ];
        
        $lastNames = [
            'Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller',
            'Davis', 'Rodriguez', 'Martinez', 'Hernandez', 'Lopez', 'Gonzalez',
            'Wilson', 'Anderson', 'Thomas', 'Taylor', 'Moore', 'Jackson', 'Martin',
            'Lee', 'Perez', 'Thompson', 'White', 'Harris', 'Sanchez', 'Clark',
            'Ramirez', 'Lewis', 'Robinson', 'Walker', 'Young', 'Allen', 'King',
            'Wright', 'Scott', 'Torres', 'Nguyen', 'Hill', 'Flores', 'Green',
            'Adams', 'Nelson', 'Baker', 'Hall', 'Rivera', 'Campbell', 'Mitchell',
            'Carter', 'Roberts', 'Gomez', 'Phillips', 'Evans', 'Turner', 'Diaz'
        ];
        
        $cities = [
            'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
            'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
            'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis',
            'Seattle', 'Denver', 'Washington', 'Boston', 'El Paso', 'Nashville',
            'Detroit', 'Oklahoma City', 'Portland', 'Las Vegas', 'Memphis', 'Louisville',
            'Baltimore', 'Milwaukee', 'Albuquerque', 'Tucson', 'Fresno', 'Sacramento'
        ];
        
        $states = [
            'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID',
            'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS',
            'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK',
            'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
        ];
        
        $generated = 0;
        $batches = ceil($count / $this->batchSize);
        
        for ($batch = 0; $batch < $batches; $batch++) {
            $batchStart = microtime(true);
            $usersInBatch = min($this->batchSize, $count - $generated);

            // Check execution time and memory limits
            $elapsedTime = microtime(true) - $startTime;
            $memoryUsage = memory_get_usage(true) / 1024 / 1024; // MB

            if ($elapsedTime > ($this->maxExecutionTime - 30)) { // Stop 30 seconds before limit
                if ($verbose) {
                    echo "\nStopping generation due to time limit. Generated {$generated}/{$count} users.\n";
                    echo "Elapsed time: " . round($elapsedTime, 2) . " seconds\n";
                }
                break;
            }

            if ($memoryUsage > ($this->memoryLimit - 20)) { // Stop 20MB before limit
                if ($verbose) {
                    echo "\nStopping generation due to memory limit. Generated {$generated}/{$count} users.\n";
                    echo "Memory usage: " . round($memoryUsage, 2) . "MB\n";
                }
                break;
            }
            
            // Prepare batch insert
            $sql = "INSERT INTO users (name, email, password, role, status, created_at, last_login, phone, address, city, state, zip) VALUES ";
            $values = [];
            $params = [];
            
            for ($i = 0; $i < $usersInBatch; $i++) {
                $firstName = $firstNames[array_rand($firstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $name = $firstName . ' ' . $lastName;
                
                // Create unique email
                $emailBase = strtolower($firstName . '.' . $lastName . ($generated + $i + 1));
                $domain = $domains[array_rand($domains)];
                $email = $emailBase . '@' . $domain;
                
                $role = $roles[array_rand($roles)];
                $status = $statuses[array_rand($statuses)];
                
                // Random dates for created_at and last_login
                $createdDays = rand(1, 365);
                $createdAt = date('Y-m-d H:i:s', strtotime("-{$createdDays} days"));
                
                $lastLoginDays = rand(0, $createdDays);
                $lastLogin = $lastLoginDays > 0 ? date('Y-m-d H:i:s', strtotime("-{$lastLoginDays} days")) : null;
                
                // Random address data
                $phone = sprintf('(%03d) %03d-%04d', rand(200, 999), rand(200, 999), rand(1000, 9999));
                $address = rand(100, 9999) . ' ' . $lastNames[array_rand($lastNames)] . ' St';
                $city = $cities[array_rand($cities)];
                $state = $states[array_rand($states)];
                $zip = sprintf('%05d', rand(10000, 99999));
                
                $values[] = "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $params = array_merge($params, [
                    $name, $email, password_hash('password123', PASSWORD_DEFAULT),
                    $role, $status, $createdAt, $lastLogin, $phone, $address, $city, $state, $zip
                ]);
            }
            
            $sql .= implode(', ', $values);
            
            try {
                // Use direct PDO for batch insert with positional parameters
                $stmt = $this->db->getConnection()->prepare($sql);
                
                // Debug: Check parameter count vs placeholders
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    $placeholderCount = substr_count($sql, '?');
                    $paramCount = count($params);
                    error_log("Batch {$batch}: Placeholders: {$placeholderCount}, Parameters: {$paramCount}");
                }
                
                $stmt->execute($params);
                
                $generated += $usersInBatch;
                $batchTime = round((microtime(true) - $batchStart) * 1000, 2);
                $currentMemory = round(memory_get_usage(true) / 1024 / 1024, 2);
                $elapsedTotal = round(microtime(true) - $startTime, 2);

                if ($verbose) {
                    $progress = round(($generated / $count) * 100, 1);
                    echo "Batch " . ($batch + 1) . "/{$batches}: Generated {$usersInBatch} users in {$batchTime}ms ";
                    echo "(Total: {$generated}/{$count} - {$progress}%) ";
                    echo "[Memory: {$currentMemory}MB, Time: {$elapsedTotal}s]\n";

                    // Flush output for real-time progress
                    if (php_sapi_name() !== 'cli') {
                        echo str_repeat(' ', 1024); // Padding for browser output
                        flush();
                    }
                }

                // Clean up variables to free memory
                unset($sql, $values, $params, $stmt);

                // Force garbage collection every 10 batches
                if (($batch + 1) % 10 == 0) {
                    gc_collect_cycles();
                }
                
            } catch (Exception $e) {
                echo "Error in batch " . ($batch + 1) . ": " . $e->getMessage() . "\n";
                break;
            }
        }
        
        $totalTime = round((microtime(true) - $startTime) * 1000, 2);
        
        if ($verbose) {
            echo "\nTest data generation completed!\n";
            echo "Generated: {$generated} users\n";
            echo "Total time: {$totalTime}ms\n";
            if ($generated > 0) {
                echo "Average per user: " . round($totalTime / $generated, 2) . "ms\n";
            } else {
                echo "Average per user: N/A (no users generated)\n";
            }
        }
        
        return $generated;
    }

    /**
     * Generate large numbers of users with automatic chunking to prevent timeouts
     *
     * @param int $count Total number of users to generate
     * @param int $chunkSize Number of users per chunk (default: 1000)
     * @param bool $verbose Whether to show progress output
     * @return int Total number of users generated
     */
    public function generateUsersLarge($count = 5000, $chunkSize = 1000, $verbose = true) {
        if ($verbose) {
            echo "Generating {$count} test users in chunks of {$chunkSize}...\n";
        }

        $totalGenerated = 0;
        $chunks = ceil($count / $chunkSize);

        for ($chunk = 0; $chunk < $chunks; $chunk++) {
            $usersInChunk = min($chunkSize, $count - $totalGenerated);

            if ($verbose) {
                echo "\n--- Chunk " . ($chunk + 1) . "/{$chunks}: Generating {$usersInChunk} users ---\n";
            }

            $generated = $this->generateUsers($usersInChunk, $verbose);
            $totalGenerated += $generated;

            if ($verbose) {
                echo "Chunk completed. Generated: {$generated} users (Total: {$totalGenerated}/{$count})\n";

                // Show memory usage between chunks
                $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
                echo "Memory usage: {$memoryUsage}MB\n";
            }

            // If we didn't generate the expected number, stop (likely hit limits)
            if ($generated < $usersInChunk) {
                if ($verbose) {
                    echo "Stopping early due to system limits.\n";
                }
                break;
            }

            // Small delay between chunks to prevent overwhelming the system
            if ($chunk < $chunks - 1) {
                sleep(1);
            }
        }

        if ($verbose) {
            echo "\nLarge generation completed! Total generated: {$totalGenerated}/{$count} users\n";
        }

        return $totalGenerated;
    }

    /**
     * Safety check to prevent deletion of real users
     *
     * @return array Array of safe test domains
     */
    private function getSafeTestDomains() {
        // These domains are intentionally misspelled to prevent accidental deletion of real users
        return ['gmai1.com', 'yaho0.com', 'hotmai1.com', 'out1ook.com', 'test-example.com'];
    }

    /**
     * Check if there are any users with real email domains that could be accidentally deleted
     *
     * @param bool $verbose Whether to show warnings
     * @return bool True if safe to proceed, false if real users might be affected
     */
    private function safetyCheck($verbose = true) {
        $realDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        $realDomainConditions = array_map(function($domain) {
            return "email LIKE '%@{$domain}'";
        }, $realDomains);
        $realDomainClause = "(" . implode(' OR ', $realDomainConditions) . ")";

        $this->db->query("SELECT COUNT(*) as count FROM users WHERE " . $realDomainClause);
        $result = $this->db->single();
        $realUsers = $result->count ?? 0;

        if ($realUsers > 0 && $verbose) {
            echo "SAFETY WARNING: Found {$realUsers} users with real email domains.\n";
            echo "This cleanup will NOT affect them - only users with test domains will be deleted.\n";
            echo "Safe test domains: " . implode(', ', $this->getSafeTestDomains()) . "\n\n";
        }

        return true; // Always safe now that we use safe domains
    }

    /**
     * Clean up test users - simple version (removes users with test email domains only)
     *
     * @param bool $verbose Whether to show progress output
     * @return int Number of users deleted
     */
    public function cleanupTestUsersSimple($verbose = true) {
        if ($verbose) {
            echo "Cleaning up test users (simple method - domain-based only)...\n";
        }

        // Run safety check
        $this->safetyCheck($verbose);

        $startTime = microtime(true);

        try {
            // Use safe test domains method
            $testDomains = $this->getSafeTestDomains();
            $domainConditions = array_map(function($domain) {
                return "email LIKE '%@{$domain}'";
            }, $testDomains);

            $whereClause = "(" . implode(' OR ', $domainConditions) . ")";

            // Count users to be deleted
            $countSql = "SELECT COUNT(*) as count FROM users WHERE " . $whereClause;
            $this->db->query($countSql);
            $countResult = $this->db->single();
            $usersToDelete = $countResult->count ?? 0;

            if ($verbose) {
                echo "Found {$usersToDelete} users with test domains to delete...\n";
            }

            if ($usersToDelete == 0) {
                if ($verbose) {
                    echo "No test users found to delete.\n";
                }
                return 0;
            }

            // Delete the users
            $deleteSql = "DELETE FROM users WHERE " . $whereClause;
            $this->db->query($deleteSql);
            $result = $this->db->execute();

            if ($result) {
                $deletedCount = $this->db->rowCount();
                $totalTime = round((microtime(true) - $startTime) * 1000, 2);

                if ($verbose) {
                    echo "Successfully deleted {$deletedCount} test users in {$totalTime}ms.\n";
                }

                return $deletedCount;
            } else {
                if ($verbose) {
                    echo "Failed to delete test users.\n";
                }
                return 0;
            }

        } catch (Exception $e) {
            if ($verbose) {
                echo "Error cleaning up test users: " . $e->getMessage() . "\n";
            }
            error_log("Test user cleanup error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Clean up test users (removes users with test email domains)
     *
     * @param bool $verbose Whether to show progress output
     * @return int Number of users deleted
     */
    public function cleanupTestUsers($verbose = true) {
        if ($verbose) {
            echo "Cleaning up test users...\n";
        }

        // Run safety check
        $this->safetyCheck($verbose);

        $startTime = microtime(true);

        try {
            // Use safe test domains method
            $testDomains = $this->getSafeTestDomains();
            $domainConditions = array_map(function($domain) {
                return "email LIKE '%@{$domain}'";
            }, $testDomains);

            if ($verbose) {
                echo "Debug: Looking for users with domains: " . implode(', ', $testDomains) . "\n";
            }

            // First, let's check how many users exist with these domains (without name pattern)
            $domainOnlyClause = "(" . implode(' OR ', $domainConditions) . ")";
            $domainCountSql = "SELECT COUNT(*) as count FROM users WHERE " . $domainOnlyClause;
            $this->db->query($domainCountSql);
            $domainResult = $this->db->single();
            $usersWithDomains = $domainResult->count ?? 0;

            if ($verbose) {
                echo "Debug: Found {$usersWithDomains} users with test domains\n";
            }

            // Now check with the name pattern - make the regex more flexible
            $whereClause = "(" . implode(' OR ', $domainConditions) . ") AND name REGEXP '^[A-Za-z]+ [A-Za-z]+[0-9]*$'";

            if ($verbose) {
                echo "Debug: Full WHERE clause: " . $whereClause . "\n";
            }

            // Count users to be deleted
            $countSql = "SELECT COUNT(*) as count FROM users WHERE " . $whereClause;
            $this->db->query($countSql);
            $countResult = $this->db->single();
            $usersToDelete = $countResult->count ?? 0;

            if ($verbose) {
                echo "Debug: Users matching pattern: {$usersToDelete}\n";
            }

            // If no users match the pattern, let's see what names we have
            if ($usersToDelete == 0 && $usersWithDomains > 0) {
                if ($verbose) {
                    echo "Debug: Checking actual user names with test domains...\n";
                    $sampleSql = "SELECT name, email FROM users WHERE " . $domainOnlyClause . " LIMIT 5";
                    $this->db->query($sampleSql);
                    $samples = $this->db->resultSet();
                    foreach ($samples as $sample) {
                        echo "Debug: Sample user - Name: '{$sample->name}', Email: '{$sample->email}'\n";
                    }
                }
            }

            if ($verbose) {
                echo "Found {$usersToDelete} test users to delete...\n";
            }

            if ($usersToDelete == 0) {
                if ($verbose) {
                    echo "No test users found to delete.\n";
                }
                return 0;
            }

            // Delete the users
            $deleteSql = "DELETE FROM users WHERE " . $whereClause;
            $this->db->query($deleteSql);
            $result = $this->db->execute();

            if ($result) {
                $deletedCount = $this->db->rowCount();
                $totalTime = round((microtime(true) - $startTime) * 1000, 2);

                if ($verbose) {
                    echo "Successfully deleted {$deletedCount} test users in {$totalTime}ms.\n";
                }

                return $deletedCount;
            } else {
                if ($verbose) {
                    echo "Failed to delete test users.\n";
                }
                return 0;
            }

        } catch (Exception $e) {
            if ($verbose) {
                echo "Error cleaning up test users: " . $e->getMessage() . "\n";
            }
            error_log("Test user cleanup error: " . $e->getMessage());
            return 0;
        }
    }
}

// Command line usage
if (php_sapi_name() === 'cli') {
    $generator = new TestUserGenerator();
    
    $count = isset($argv[1]) ? (int)$argv[1] : 1000;
    $action = isset($argv[2]) ? $argv[2] : 'generate';
    
    if ($action === 'cleanup') {
        $deletedCount = $generator->cleanupTestUsers();
        echo "Cleanup completed. Deleted {$deletedCount} test users.\n";
    } elseif ($action === 'cleanup_simple') {
        $deletedCount = $generator->cleanupTestUsersSimple();
        echo "Simple cleanup completed. Deleted {$deletedCount} test users.\n";
    } elseif ($action === 'large') {
        $chunkSize = isset($argv[3]) ? (int)$argv[3] : 1000;
        echo "Generating {$count} test users in chunks of {$chunkSize} for performance testing...\n";
        echo "WARNING: This will add test data to your database!\n";
        echo "Press Enter to continue or Ctrl+C to cancel...\n";
        fgets(STDIN);

        $generator->generateUsersLarge($count, $chunkSize);
    } else {
        echo "Generating {$count} test users for performance testing...\n";
        echo "WARNING: This will add test data to your database!\n";
        echo "Press Enter to continue or Ctrl+C to cancel...\n";
        fgets(STDIN);

        $generator->generateUsers($count);
    }
} else {
    // Web usage
    echo "<h1>Test User Generator</h1>";
    echo "<p><strong>WARNING:</strong> This will add test data to your database!</p>";
    
    if (isset($_GET['action'])) {
        $generator = new TestUserGenerator();
        
        if ($_GET['action'] === 'generate') {
            $count = isset($_GET['count']) ? (int)$_GET['count'] : 1000;
            echo "<pre>";
            $generator->generateUsers($count);
            echo "</pre>";
        } elseif ($_GET['action'] === 'generate_large') {
            $count = isset($_GET['count']) ? (int)$_GET['count'] : 5000;
            $chunkSize = isset($_GET['chunk']) ? (int)$_GET['chunk'] : 1000;
            echo "<pre>";
            $generator->generateUsersLarge($count, $chunkSize);
            echo "</pre>";
        } elseif ($_GET['action'] === 'cleanup') {
            echo "<pre>";
            $deletedCount = $generator->cleanupTestUsers();
            echo "Cleanup completed. Deleted {$deletedCount} test users.";
            echo "</pre>";
        } elseif ($_GET['action'] === 'cleanup_simple') {
            echo "<pre>";
            $deletedCount = $generator->cleanupTestUsersSimple();
            echo "Simple cleanup completed. Deleted {$deletedCount} test users.";
            echo "</pre>";
        }
    } else {
        echo '<h2>Standard Generation (up to 1000 users)</h2>';
        echo '<p><a href="?action=generate&count=100">Generate 100 Test Users</a></p>';
        echo '<p><a href="?action=generate&count=500">Generate 500 Test Users</a></p>';
        echo '<p><a href="?action=generate&count=1000">Generate 1000 Test Users</a></p>';

        echo '<h2>Large Generation (with chunking to prevent timeouts)</h2>';
        echo '<p><a href="?action=generate_large&count=2000&chunk=500">Generate 2000 Test Users (500 per chunk)</a></p>';
        echo '<p><a href="?action=generate_large&count=5000&chunk=1000">Generate 5000 Test Users (1000 per chunk)</a></p>';
        echo '<p><a href="?action=generate_large&count=10000&chunk=1000">Generate 10000 Test Users (1000 per chunk)</a></p>';

        echo '<h2>Cleanup</h2>';
        echo '<p><a href="?action=cleanup">Cleanup Test Users (with name pattern matching)</a></p>';
        echo '<p><a href="?action=cleanup_simple">Simple Cleanup (all users with test domains)</a></p>';
    }
}

<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>QR Codes for Fan Voting - <?php echo $data['show']->name; ?></h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/coordinator/printQrCodes/<?php echo $data['show']->id; ?>" class="btn btn-primary" target="_blank">
                    <i class="fas fa-print me-2"></i> Print All QR Codes
                </a>
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back
                </button>
            </div>
        </div>
    </div>
    
    <?php flash('coordinator_message'); ?>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <!-- Instructions -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> How to Use QR Codes</h5>
                </div>
                <div class="card-body">
                    <p>These QR codes can be printed and placed next to each vehicle at the show. Visitors can scan the QR code with their smartphone to vote directly for that vehicle.</p>
                    <ol>
                        <li>Print the QR codes and cut them out</li>
                        <li>Place each QR code next to the corresponding vehicle</li>
                        <li>Visitors scan the code with their smartphone camera</li>
                        <li>They'll be taken directly to the voting page for that specific vehicle</li>
                    </ol>
                </div>
            </div>
            
            <!-- QR Codes -->
            <div class="row">
                <?php foreach($data['vehicles'] as $vehicle): ?>
                    <div class="col-md-4 col-lg-3 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0"><?php echo $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model; ?></h5>
                            </div>
                            <div class="card-body text-center">
                                <!-- QR Code -->
                                <div class="mb-3">
                                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=<?php echo urlencode(URLROOT . '/show/vote/' . $data['show']->id . '/' . $vehicle->registration_id); ?>" 
                                         alt="QR Code" class="img-fluid">
                                </div>
                                
                                <!-- Vehicle Info -->
                                <p class="mb-1"><strong>Registration #:</strong> <?php echo $vehicle->registration_number; ?></p>
                                <p class="mb-1"><strong>Category:</strong> <?php echo $vehicle->category_name; ?></p>
                                <p class="mb-1"><strong>Owner:</strong> <?php echo $vehicle->owner_name; ?></p>
                                
                                <!-- Download Button -->
                                <div class="mt-3">
                                    <a href="https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=<?php echo urlencode(URLROOT . '/show/vote/' . $data['show']->id . '/' . $vehicle->registration_id); ?>" 
                                       download="qr_<?php echo $data['show']->id; ?>_<?php echo $vehicle->registration_id; ?>.png" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-download"></i> Download QR Code
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
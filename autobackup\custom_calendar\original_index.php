<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Calendar</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/createEvent" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i> Add Event
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageCalendars">Manage Calendars</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageVenues">Manage Venues</a></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/manageClubs">Manage Clubs</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/import">Import Events</a></li>
                    <?php if (isAdmin()): ?>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="<?php echo URLROOT; ?>/calendar/settings">Calendar Settings</a></li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Calendars</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['calendars'])): ?>
                        <div class="calendar-list">
                            <?php foreach ($data['calendars'] as $calendar): ?>
                                <div class="form-check">
                                    <input class="form-check-input calendar-toggle" type="checkbox" value="<?php echo $calendar->id; ?>" id="calendar-<?php echo $calendar->id; ?>" checked data-color="<?php echo $calendar->color; ?>">
                                    <label class="form-check-label" for="calendar-<?php echo $calendar->id; ?>">
                                        <span class="color-dot" style="background-color: <?php echo $calendar->color; ?>"></span>
                                        <?php echo $calendar->name; ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p>No calendars found.</p>
                        <a href="<?php echo URLROOT; ?>/calendar/createCalendar" class="btn btn-sm btn-primary">Create Calendar</a>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upcoming Events</h5>
                </div>
                <div class="card-body p-0">
                    <div id="upcoming-events" class="list-group list-group-flush">
                        <!-- Upcoming events will be loaded here via JavaScript -->
                        <div class="list-group-item text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <span class="ms-2">Loading events...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-9">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="btn-group" role="group" aria-label="Calendar views">
                            <button type="button" class="btn btn-outline-secondary" id="view-month">Month</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-week">Week</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-day">Day</button>
                            <button type="button" class="btn btn-outline-secondary" id="view-list">List</button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary" id="prev-btn">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="today-btn">Today</button>
                            <button type="button" class="btn btn-outline-secondary" id="next-btn">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="calendar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Event Details Modal -->
<div class="modal fade" id="eventModal" tabindex="-1" aria-labelledby="eventModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="eventModalLabel">Event Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="event-details">
                    <div class="mb-3">
                        <h4 id="event-title"></h4>
                        <div class="text-muted">
                            <span id="event-date"></span> • 
                            <span id="event-time"></span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span id="event-calendar"></span>
                            </div>
                            <div class="mb-2" id="event-location-container">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span id="event-location"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-2" id="event-show-container">
                                <i class="fas fa-car me-2"></i>
                                <span id="event-show"></span>
                            </div>
                            <div class="mb-2" id="event-url-container">
                                <i class="fas fa-link me-2"></i>
                                <a href="#" id="event-url" target="_blank"></a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="event-description-container">
                        <h5>Description</h5>
                        <div id="event-description"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-primary" id="event-view-link">View Details</a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Add FullCalendar CSS -->
<link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css" rel="stylesheet">

<!-- Add custom CSS for calendar -->
<style>
    .color-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .calendar-list {
        max-height: 200px;
        overflow-y: auto;
    }
    
    #calendar {
        height: 700px;
    }
    
    .fc-event {
        cursor: pointer;
    }
    
    .fc-toolbar-title {
        font-size: 1.5rem !important;
    }
    
    .upcoming-event {
        border-left: 4px solid #3788d8;
    }
    
    .upcoming-event-title {
        font-weight: 600;
    }
    
    .upcoming-event-time {
        font-size: 0.85rem;
    }
    
    @media (max-width: 768px) {
        #calendar {
            height: 500px;
        }
        
        .fc-toolbar-title {
            font-size: 1.2rem !important;
        }
        
        .fc-toolbar.fc-header-toolbar {
            flex-direction: column;
        }
        
        .fc-toolbar.fc-header-toolbar .fc-toolbar-chunk {
            margin-bottom: 0.5rem;
        }
    }
</style>

<!-- Add FullCalendar JS with all required plugins -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/daygrid@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/timegrid@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/list@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@fullcalendar/interaction@5.10.1/main.min.js"></script>

<!-- Calendar initialization script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get calendar settings
        const settings = {
            defaultView: '<?php echo isset($data['settings']['default_view']) ? $data['settings']['default_view'] : 'month'; ?>',
            businessHoursStart: '<?php echo isset($data['settings']['business_hours_start']) ? $data['settings']['business_hours_start'] : '09:00:00'; ?>',
            businessHoursEnd: '<?php echo isset($data['settings']['business_hours_end']) ? $data['settings']['business_hours_end'] : '17:00:00'; ?>',
            weekStartsOn: <?php echo isset($data['settings']['week_starts_on']) ? $data['settings']['week_starts_on'] : 0; ?>,
            timeFormat: '<?php echo isset($data['settings']['time_format']) ? $data['settings']['time_format'] : '12'; ?>',
            dateFormat: '<?php echo isset($data['settings']['date_format']) ? $data['settings']['date_format'] : 'MM/DD/YYYY'; ?>',
            defaultEventDuration: <?php echo isset($data['settings']['default_event_duration']) ? $data['settings']['default_event_duration'] : 60; ?>,
            enableDragDrop: <?php echo isset($data['settings']['enable_drag_drop']) && $data['settings']['enable_drag_drop'] ? 'true' : 'false'; ?>,
            enableResize: <?php echo isset($data['settings']['enable_resize']) && $data['settings']['enable_resize'] ? 'true' : 'false'; ?>,
            showWeekends: <?php echo isset($data['settings']['show_weekends']) && $data['settings']['show_weekends'] ? 'true' : 'false'; ?>,
            defaultCalendarColor: '<?php echo isset($data['settings']['default_calendar_color']) ? $data['settings']['default_calendar_color'] : '#3788d8'; ?>'
        };
        
        // Initialize calendar
        const calendarEl = document.getElementById('calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            plugins: [ 'dayGrid', 'timeGrid', 'list', 'interaction' ],
            initialView: settings.defaultView,
            headerToolbar: false, // We're using custom header buttons
            firstDay: settings.weekStartsOn,
            weekends: settings.showWeekends,
            businessHours: {
                daysOfWeek: [1, 2, 3, 4, 5], // Monday - Friday
                startTime: settings.businessHoursStart,
                endTime: settings.businessHoursEnd
            },
            editable: settings.enableDragDrop,
            eventResizableFromStart: settings.enableResize,
            eventDurationEditable: settings.enableResize,
            selectable: true,
            selectMirror: true,
            dayMaxEvents: true,
            events: {
                url: '<?php echo URLROOT; ?>/calendar/getEvents',
                failure: function() {
                    alert('There was an error loading events!');
                }
            },
            eventTimeFormat: {
                hour: 'numeric',
                minute: '2-digit',
                meridiem: settings.timeFormat === '12'
            },
            select: function(info) {
                // Redirect to create event page with pre-filled dates
                window.location.href = '<?php echo URLROOT; ?>/calendar/createEvent?start=' + info.startStr + '&end=' + info.endStr;
            },
            eventClick: function(info) {
                // Show event details in modal
                showEventDetails(info.event);
            },
            eventDrop: function(info) {
                // Update event dates via AJAX
                updateEventDates(info.event);
            },
            eventResize: function(info) {
                // Update event dates via AJAX
                updateEventDates(info.event);
            },
            loading: function(isLoading) {
                // Show/hide loading indicator
                if (isLoading) {
                    // Add loading indicator if needed
                }
            }
        });
        
        // Render calendar
        calendar.render();
        
        // Set active view button
        setActiveViewButton(settings.defaultView);
        
        // View buttons
        document.getElementById('view-month').addEventListener('click', function() {
            calendar.changeView('dayGridMonth');
            setActiveViewButton('month');
        });
        
        document.getElementById('view-week').addEventListener('click', function() {
            calendar.changeView('timeGridWeek');
            setActiveViewButton('week');
        });
        
        document.getElementById('view-day').addEventListener('click', function() {
            calendar.changeView('timeGridDay');
            setActiveViewButton('day');
        });
        
        document.getElementById('view-list').addEventListener('click', function() {
            calendar.changeView('listMonth');
            setActiveViewButton('list');
        });
        
        // Navigation buttons
        document.getElementById('prev-btn').addEventListener('click', function() {
            calendar.prev();
        });
        
        document.getElementById('next-btn').addEventListener('click', function() {
            calendar.next();
        });
        
        document.getElementById('today-btn').addEventListener('click', function() {
            calendar.today();
        });
        
        // Calendar toggles
        const calendarToggles = document.querySelectorAll('.calendar-toggle');
        calendarToggles.forEach(function(toggle) {
            toggle.addEventListener('change', function() {
                const calendarId = this.value;
                const isChecked = this.checked;
                
                // Filter events
                filterEvents();
                
                // Refetch events
                calendar.refetchEvents();
            });
        });
        
        // Helper function to set active view button
        function setActiveViewButton(view) {
            const viewButtons = document.querySelectorAll('[id^="view-"]');
            viewButtons.forEach(function(button) {
                button.classList.remove('active', 'btn-primary');
                button.classList.add('btn-outline-secondary');
            });
            
            let buttonId;
            switch(view) {
                case 'dayGridMonth':
                case 'month':
                    buttonId = 'view-month';
                    break;
                case 'timeGridWeek':
                case 'week':
                    buttonId = 'view-week';
                    break;
                case 'timeGridDay':
                case 'day':
                    buttonId = 'view-day';
                    break;
                case 'listMonth':
                case 'list':
                    buttonId = 'view-list';
                    break;
                default:
                    buttonId = 'view-month';
            }
            
            const activeButton = document.getElementById(buttonId);
            if (activeButton) {
                activeButton.classList.remove('btn-outline-secondary');
                activeButton.classList.add('active', 'btn-primary');
            }
        }
        
        // Helper function to filter events based on selected calendars
        function filterEvents() {
            const selectedCalendars = [];
            calendarToggles.forEach(function(toggle) {
                if (toggle.checked) {
                    selectedCalendars.push(toggle.value);
                }
            });
            
            // Update events source
            const eventsSource = calendar.getEventSources()[0];
            if (eventsSource) {
                eventsSource.remove();
            }
            
            calendar.addEventSource({
                url: '<?php echo URLROOT; ?>/calendar/getEvents',
                extraParams: {
                    calendar_id: selectedCalendars.join(',')
                }
            });
        }
        
        // Helper function to show event details in modal
        function showEventDetails(event) {
            const modal = document.getElementById('eventModal');
            const modalInstance = new bootstrap.Modal(modal);
            
            // Set event details
            document.getElementById('event-title').textContent = event.title;
            
            // Format date
            const startDate = new Date(event.start);
            const endDate = event.end ? new Date(event.end) : null;
            
            let dateStr = startDate.toLocaleDateString();
            if (endDate && startDate.toDateString() !== endDate.toDateString()) {
                dateStr += ' - ' + endDate.toLocaleDateString();
            }
            document.getElementById('event-date').textContent = dateStr;
            
            // Format time
            if (event.allDay) {
                document.getElementById('event-time').textContent = 'All day';
            } else {
                let timeStr = startDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                if (endDate) {
                    timeStr += ' - ' + endDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                }
                document.getElementById('event-time').textContent = timeStr;
            }
            
            // Set calendar
            document.getElementById('event-calendar').textContent = event.extendedProps.calendar_name || 'Unknown Calendar';
            
            // Set location
            const locationContainer = document.getElementById('event-location-container');
            if (event.extendedProps.location) {
                document.getElementById('event-location').textContent = event.extendedProps.location;
                locationContainer.style.display = 'block';
            } else {
                locationContainer.style.display = 'none';
            }
            
            // Set show
            const showContainer = document.getElementById('event-show-container');
            if (event.extendedProps.show_name) {
                document.getElementById('event-show').textContent = event.extendedProps.show_name;
                showContainer.style.display = 'block';
            } else {
                showContainer.style.display = 'none';
            }
            
            // Set URL
            const urlContainer = document.getElementById('event-url-container');
            if (event.url) {
                const urlElement = document.getElementById('event-url');
                urlElement.href = event.url;
                urlElement.textContent = event.url;
                urlContainer.style.display = 'block';
            } else {
                urlContainer.style.display = 'none';
            }
            
            // Set description
            const descriptionContainer = document.getElementById('event-description-container');
            if (event.extendedProps.description) {
                document.getElementById('event-description').innerHTML = event.extendedProps.description;
                descriptionContainer.style.display = 'block';
            } else {
                descriptionContainer.style.display = 'none';
            }
            
            // Set view link
            document.getElementById('event-view-link').href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
            
            // Show modal
            modalInstance.show();
        }
        
        // Helper function to update event dates
        function updateEventDates(event) {
            const eventData = {
                id: event.id,
                start_date: event.start.toISOString(),
                end_date: event.end ? event.end.toISOString() : null,
                all_day: event.allDay ? 1 : 0
            };
            
            // Send AJAX request
            fetch('<?php echo URLROOT; ?>/calendar/updateEventDates', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(eventData)
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    alert('Error updating event: ' + data.message);
                    calendar.refetchEvents();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating event. Please try again.');
                calendar.refetchEvents();
            });
        }
        
        // Load upcoming events
        loadUpcomingEvents();
        
        function loadUpcomingEvents() {
            const upcomingEventsContainer = document.getElementById('upcoming-events');
            
            // Send AJAX request
            fetch('<?php echo URLROOT; ?>/calendar/getUpcomingEvents', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Clear container
                upcomingEventsContainer.innerHTML = '';
                
                if (data.length === 0) {
                    upcomingEventsContainer.innerHTML = '<div class="list-group-item">No upcoming events</div>';
                    return;
                }
                
                // Add events
                data.forEach(event => {
                    const startDate = new Date(event.start_date);
                    const endDate = event.end_date ? new Date(event.end_date) : null;
                    
                    let dateStr = startDate.toLocaleDateString();
                    let timeStr = '';
                    
                    if (event.all_day) {
                        timeStr = 'All day';
                    } else {
                        timeStr = startDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                        if (endDate) {
                            timeStr += ' - ' + endDate.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
                        }
                    }
                    
                    const eventItem = document.createElement('a');
                    eventItem.href = '<?php echo URLROOT; ?>/calendar/event/' + event.id;
                    eventItem.className = 'list-group-item list-group-item-action upcoming-event';
                    eventItem.style.borderLeftColor = event.color || event.calendar_color || settings.defaultCalendarColor;
                    
                    eventItem.innerHTML = `
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1 upcoming-event-title">${event.title}</h6>
                        </div>
                        <p class="mb-1 upcoming-event-time">${dateStr} • ${timeStr}</p>
                        <small>${event.calendar_name}</small>
                    `;
                    
                    upcomingEventsContainer.appendChild(eventItem);
                });
            })
            .catch(error => {
                console.error('Error:', error);
                upcomingEventsContainer.innerHTML = '<div class="list-group-item text-danger">Error loading events</div>';
            });
        }
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
<?php
/**
 * Image Editor Model
 * 
 * This model handles all image editing related functionality.
 */
class ImageEditorModel {
    private $db;
    private $settingsModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // We'll load the settings model when needed instead of in the constructor
        
        // Ensure the images table has the user_id column
        $this->ensureUserIdColumn();
    }
    
    /**
     * Get settings model
     * 
     * @return object SettingsModel instance
     */
    private function getSettingsModel() {
        if (!$this->settingsModel) {
            // Check if SettingsModel.php exists
            $settingsModelPath = APPROOT . '/models/SettingsModel.php';
            if (file_exists($settingsModelPath)) {
                require_once $settingsModelPath;
                $this->settingsModel = new SettingsModel();
            } else {
                // Fallback to default settings if model can't be loaded
                $this->settingsModel = (object)[
                    'getSetting' => function($key, $default) {
                        return $default;
                    }
                ];
                error_log('SettingsModel.php not found. Using default settings.');
            }
        }
        return $this->settingsModel;
    }
    
    /**
     * Ensure the images table has the user_id column
     * This is a temporary method to handle database migration
     */
    private function ensureUserIdColumn() {
        // Check if the user_id column exists in the images table
        $this->db->query("SHOW COLUMNS FROM images LIKE 'user_id'");
        $result = $this->db->single();
        
        // If the column doesn't exist, add it
        if (!$result) {
            $this->db->query("ALTER TABLE images ADD COLUMN user_id INT NULL AFTER file_size");
            if ($this->db->execute()) {
                error_log('Added user_id column to images table');
            } else {
                error_log('Failed to add user_id column to images table');
            }
        }
    }
    
    /**
     * Process a downloaded image
     * 
     * @param string $imageContent Raw image content
     * @param string $fileName Filename to use (with extension)
     * @param string $entityType Entity type (e.g., 'user', 'vehicle', 'show')
     * @param int $entityId Entity ID
     * @param string $uploadDir Upload directory
     * @param int $userId User ID
     * @param bool $isPrimary Whether this is the primary image
     * @return array|bool Image data on success, false on failure
     */
    public function processDownloadedImage($imageContent, $fileName, $entityType, $entityId, $uploadDir = 'uploads/images/', $userId = null, $isPrimary = false) {
        error_log('ImageEditorModel::processDownloadedImage - Processing downloaded image for ' . $entityType . ' ' . $entityId);
        
        // Get current user ID if not provided
        if ($userId === null && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }
        
        // Get settings model
        $settingsModel = $this->getSettingsModel();
        
        // Get image settings
        $imageQuality = (int)$settingsModel->getSetting('image_image_quality', 80);
        $thumbnailSize = (int)$settingsModel->getSetting('image_thumbnail_size', 200);
        $maxUploadSize = (int)$settingsModel->getSetting('image_max_upload_size', 5) * 1024 * 1024; // Convert MB to bytes
        $optimizeImages = $settingsModel->getSetting('image_optimize_images', '0') === '1';
        $resizeLargeImages = $settingsModel->getSetting('image_resize_large_images', '0') === '1';
        $maxWidth = (int)$settingsModel->getSetting('image_max_width', 1920);
        $maxHeight = (int)$settingsModel->getSetting('image_max_height', 1080);
        
        // Check file size against settings
        $fileSize = strlen($imageContent);
        if ($fileSize > $maxUploadSize) {
            error_log('ImageEditorModel::processDownloadedImage - File too large: ' . $fileSize . ' bytes (max: ' . $maxUploadSize . ' bytes)');
            return false;
        }
        
        // Get file extension
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (empty($fileExt)) {
            $fileExt = 'jpg'; // Default to jpg if no extension provided
        }
        
        // Generate unique file name
        $newFileName = uniqid($entityType . '_' . $entityId . '_') . '.' . $fileExt;
        
        // Create upload directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                error_log('ImageEditorModel::processDownloadedImage - Failed to create upload directory: ' . $uploadDir);
                return false;
            }
        }
        
        // Create thumbnails directory if it doesn't exist
        $thumbnailDir = $uploadDir . 'thumbnails/';
        if (!file_exists($thumbnailDir)) {
            if (!mkdir($thumbnailDir, 0777, true)) {
                error_log('ImageEditorModel::processDownloadedImage - Failed to create thumbnails directory: ' . $thumbnailDir);
                return false;
            }
        }
        
        $uploadPath = $uploadDir . $newFileName;
        $thumbnailPath = $thumbnailDir . $newFileName;
        
        // Save the image content to the file
        if (file_put_contents($uploadPath, $imageContent) === false) {
            error_log('ImageEditorModel::processDownloadedImage - Failed to save image content to ' . $uploadPath);
            return false;
        }
        
        error_log('ImageEditorModel::processDownloadedImage - Saved image to ' . $uploadPath);
        
        // Get image dimensions
        $imageInfo = getimagesize($uploadPath);
        if (!$imageInfo) {
            error_log('ImageEditorModel::processDownloadedImage - Failed to get image info for ' . $uploadPath);
            @unlink($uploadPath);
            return false;
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mime = $imageInfo['mime'];
        
        // Resize image if needed
        if ($resizeLargeImages && ($width > $maxWidth || $height > $maxHeight)) {
            $this->resizeImage($uploadPath, $maxWidth, $maxHeight);
            
            // Get new dimensions after resize
            $imageInfo = getimagesize($uploadPath);
            $width = $imageInfo[0];
            $height = $imageInfo[1];
        }
        
        // Create thumbnail
        $this->createThumbnail($uploadPath, $thumbnailPath, $thumbnailSize);
        
        // Optimize image if enabled
        if ($optimizeImages) {
            $this->optimizeImage($uploadPath, $imageQuality);
        }
        
        // Add image to database
        $this->db->query('INSERT INTO images (entity_type, entity_id, file_name, file_path, thumbnail_path, width, height, mime_type, file_size, user_id, is_primary, created_at) 
                          VALUES (:entity_type, :entity_id, :file_name, :file_path, :thumbnail_path, :width, :height, :mime_type, :file_size, :user_id, :is_primary, NOW())');
        
        $this->db->bind(':entity_type', $entityType);
        $this->db->bind(':entity_id', $entityId);
        $this->db->bind(':file_name', $newFileName);
        $this->db->bind(':file_path', $uploadPath);
        $this->db->bind(':thumbnail_path', $thumbnailPath);
        $this->db->bind(':width', $width);
        $this->db->bind(':height', $height);
        $this->db->bind(':mime_type', $mime);
        $this->db->bind(':file_size', $fileSize);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':is_primary', $isPrimary ? 1 : 0);
        
        if ($this->db->execute()) {
            $imageId = $this->db->lastInsertId();
            
            // If this is the primary image, update all other images to not be primary
            if ($isPrimary) {
                $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id AND id != :id');
                $this->db->bind(':entity_type', $entityType);
                $this->db->bind(':entity_id', $entityId);
                $this->db->bind(':id', $imageId);
                $this->db->execute();
            }
            
            // Return image data
            return [
                'id' => $imageId,
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'file_name' => $newFileName,
                'file_path' => $uploadPath,
                'thumbnail_path' => $thumbnailPath,
                'width' => $width,
                'height' => $height,
                'mime_type' => $mime,
                'file_size' => $fileSize,
                'user_id' => $userId,
                'is_primary' => $isPrimary
            ];
        } else {
            error_log('ImageEditorModel::processDownloadedImage - Failed to add image to database');
            return false;
        }
    }
}
# QR Code Standardization Summary

## Date: <?php echo date('Y-m-d H:i:s'); ?>

## Objective
Standardize all QR codes to use the URL pattern: `URLROOT . '/show/vote/' . $show_id . '/' . $registration_id`

## Files Modified

### 1. `/views/shared/print_qr_codes.php`
**Changed from:**
```php
BASE_URL . '/qr/scan/registration/' . $vehicle->registration_id
```
**Changed to:**
```php
URLROOT . '/show/vote/' . $data['show']->id . '/' . $vehicle->registration_id
```

### 2. `/controllers/CoordinatorController.php`
**Changed from:**
```php
$qrUrl = BASE_URL . '/qr/scan/registration/' . $registration->id . '/' . urlencode($registrationNumber);
```
**Changed to:**
```php
$qrUrl = URLROOT . '/show/vote/' . $registration->show_id . '/' . $registration->id;
```

### 3. `/controllers/AdminController.php`
**Changed from:**
```php
$qrScanUrl = $baseUrl . '/qr/scan/registration/' . $registrationId . '/' . urlencode($registrationNumber);
```
**Changed to:**
```php
$qrScanUrl = URLROOT . '/show/vote/' . $registration->show_id . '/' . $registrationId;
```

## Files NOT Modified (As Requested)

### Judge-Related Files (Kept Original Patterns)
- `/views/shared/print_judge_qr_codes.php` - Uses: `BASE_URL . '/qr/scan/show/' . $show_id` and `BASE_URL . '/qr/scan/category/' . $category->id`

### Test Files (Kept Original Patterns)
- `/views/shared/qr_test.php` - Uses: `BASE_URL . '/qr/scan/' . $entity_type . '/' . $entity_id`

### Already Standardized Files (No Changes Needed)
- `/views/coordinator/qr_codes.php` - Already uses: `URLROOT . '/show/vote/' . $show_id . '/' . $vehicle->registration_id`
- `/views/admin/qr_codes.php` - Already uses: `URLROOT . '/show/vote/' . $show_id . '/' . $vehicle->registration_id`

## QR Code Regeneration

A script has been created at `/scripts/regenerate_qr_codes.php` to:
1. Remove existing QR code files from the filesystem
2. Clear the `qr_code` field in the database for all registrations
3. Allow new QR codes to be generated automatically with the standardized URL pattern

## Impact

- All vehicle voting QR codes now use the standardized URL pattern
- Judge-related QR codes maintain their original functionality
- Test QR codes maintain their original functionality
- Existing QR code files will need to be regenerated to use the new URL pattern

## Next Steps

1. Run the regeneration script: `php scripts/regenerate_qr_codes.php`
2. Test QR code generation to ensure new codes use the standardized pattern
3. Verify that all voting QR codes redirect to the correct voting interface

## Standardized URL Pattern

All vehicle voting QR codes now use:
```
URLROOT . '/show/vote/' . $show_id . '/' . $registration_id
```

This ensures consistent behavior across all QR code implementations for vehicle voting.
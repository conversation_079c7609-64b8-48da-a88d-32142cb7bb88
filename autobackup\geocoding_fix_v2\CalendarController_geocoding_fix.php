<?php
/**
 * This file contains the fixed geocoding implementation for CalendarController.php
 * 
 * The issue was that while we were using the geocodeEvent() function, there were differences
 * in how the address data was being prepared compared to the admin batch geocoding tool.
 */

// For createEvent method - replace the geocoding section with this:
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - ensure all fields are properly set with defaults
    // This matches exactly how the admin batch tool prepares the data
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? '',
        'lat' => $data['lat'] ?? null,
        'lng' => $data['lng'] ?? null
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'createEvent');
    
    // Update the original data with the geocoded coordinates
    $data['lat'] = $eventData['lat'];
    $data['lng'] = $eventData['lng'];
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for new event: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
    }
}

// For editEvent method - replace the geocoding section with this:
if ((!$data['lat'] || !$data['lng']) && 
    ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
    
    // Load geocoding helper
    require_once APPROOT . '/helpers/geocoding_helper.php';
    
    // Get map provider settings
    $mapSettings = $this->calendarModel->getMapProviderSettings();
    
    // Create address array for geocoding - ensure all fields are properly set with defaults
    // This matches exactly how the admin batch tool prepares the data
    $eventData = [
        'address1' => $data['address1'] ?? '',
        'address2' => $data['address2'] ?? '',
        'city' => $data['city'] ?? '',
        'state' => $data['state'] ?? '',
        'zipcode' => $data['zipcode'] ?? '',
        'lat' => $data['lat'] ?? null,
        'lng' => $data['lng'] ?? null
    ];
    
    // Use the enhanced geocodeEvent function
    $eventData = geocodeEvent($eventData, $mapSettings, 'editEvent', $id);
    
    // Update the original data with the geocoded coordinates
    $data['lat'] = $eventData['lat'];
    $data['lng'] = $eventData['lng'];
    
    // Log the geocoding attempt
    error_log("Geocoding attempt for event ID {$id}: " . 
        ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
        " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
    
    // If geocoding failed, add a warning message
    if (!$data['lat'] || !$data['lng']) {
        flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
    }
}
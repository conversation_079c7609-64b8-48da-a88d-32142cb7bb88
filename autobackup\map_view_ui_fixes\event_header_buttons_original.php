    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Event Details</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <?php if (isLoggedIn()): ?>
                <a href="<?php echo URLROOT; ?>/calendar" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Calendar
                </a>
                <?php else: ?>
                <a href="<?php echo URLROOT; ?>/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Home
                </a>
                <?php endif; ?>
                <?php if (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && empty($data['event']->show_id)): ?>
                <a href="<?php echo URLROOT; ?>/calendar/editEvent/<?php echo $data['event']->id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Event
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <?php elseif (isLoggedIn() && ($data['event']->created_by == $_SESSION['user_id'] || isAdmin()) && !empty($data['event']->show_id)): ?>
                <!-- Edit button hidden for show events - edit the show instead -->
                <a href="<?php echo URLROOT; ?>/admin/editShow/<?php echo $data['event']->show_id; ?>" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i> Edit Show
                </a>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                    <i class="fas fa-trash-alt me-2"></i> Delete
                </button>
                <span class="ms-2 text-muted"><small><i class="fas fa-info-circle"></i> This event is linked to a show. Edit the show to update this event.</small></span>
                <?php endif; ?>
            </div>
        </div>
    </div>
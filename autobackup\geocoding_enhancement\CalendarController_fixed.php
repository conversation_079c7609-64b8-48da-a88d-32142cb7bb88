<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $venueModel;
    private $auth;
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Initialize CalendarModel
        $this->calendarModel = $this->model('CalendarModel');
        
        // Initialize VenueModel
        $this->venueModel = $this->model('VenueModel');
        
        // Initialize Auth
        $this->auth = new Auth();
        
        // Initialize database connection
        $this->db = new Database();
    }
    
    /**
     * Calendar index
     */
    public function index() {
        // Get calendars
        $calendars = $this->calendarModel->getCalendars();
        
        // Get calendar display settings
        $calendarSettings = $this->calendarModel->getCalendarDisplaySettings();
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars,
            'calendarSettings' => $calendarSettings
        ];
        
        $this->view('calendar/index', $data);
    }
    
    /**
     * Create event
     */
    public function createEvent() {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('users/login');
            return;
        }
        
        // Get calendars
        $calendars = $this->calendarModel->getCalendars();
        
        // Get venues
        $venues = $this->venueModel->getVenues();
        
        // Get clubs
        $clubs = $this->calendarModel->getClubs();
        
        $data = [
            'title' => 'Create Event',
            'calendars' => $calendars,
            'venues' => $venues,
            'clubs' => $clubs,
            'calendar_id' => '',
            'title' => '',
            'description' => '',
            'start_date' => '',
            'end_date' => '',
            'all_day' => 0,
            'location' => '',
            'address1' => '',
            'address2' => '',
            'city' => '',
            'state' => '',
            'zipcode' => '',
            'venue_id' => '',
            'url' => '',
            'color' => '',
            'is_recurring' => 0,
            'recurrence_pattern' => '',
            'recurrence_end_date' => '',
            'privacy' => 'public',
            'show_id' => '',
            'lat' => '',
            'lng' => '',
            'title_err' => '',
            'start_date_err' => '',
            'end_date_err' => '',
            'calendar_id_err' => ''
        ];
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Get form data
            $data = [
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'start_date' => trim($_POST['start_date']),
                'end_date' => trim($_POST['end_date']),
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1']),
                'address2' => trim($_POST['address2']),
                'city' => trim($_POST['city']),
                'state' => trim($_POST['state']),
                'zipcode' => trim($_POST['zipcode']),
                'venue_id' => trim($_POST['venue_id']),
                'url' => trim($_POST['url']),
                'color' => trim($_POST['color']),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurrence_pattern' => trim($_POST['recurrence_pattern']),
                'recurrence_end_date' => trim($_POST['recurrence_end_date']),
                'privacy' => trim($_POST['privacy']),
                'show_id' => trim($_POST['show_id']),
                'calendar_id' => trim($_POST['calendar_id']),
                'lat' => trim($_POST['lat']),
                'lng' => trim($_POST['lng']),
                'clubs' => isset($_POST['clubs']) ? $_POST['clubs'] : [],
                'calendars' => $calendars,
                'venues' => $venues,
                'clubs' => $clubs,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'createEvent');
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for new event: " . 
                    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be created without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Create event
                if ($this->calendarModel->createEvent($data)) {
                    flash('calendar_message', 'Event created successfully');
                    redirect('calendar');
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/create_event', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/create_event', $data);
            }
        } else {
            // Load view
            $this->view('calendar/create_event', $data);
        }
    }
    
    /**
     * Edit event
     * 
     * @param int $id Event ID
     */
    public function editEvent($id) {
        // Check if user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('users/login');
            return;
        }
        
        // Get event
        $event = $this->calendarModel->getEventById($id);
        
        // Check if event exists
        if (!$event) {
            $this->redirect('calendar');
            return;
        }
        
        // Get calendars
        $calendars = $this->calendarModel->getCalendars();
        
        // Get venues
        $venues = $this->venueModel->getVenues();
        
        // Get clubs
        $clubs = $this->calendarModel->getClubs();
        
        // Get event clubs
        $eventClubs = $this->calendarModel->getEventClubs($id);
        
        // Convert event clubs to array of IDs
        $eventClubIds = [];
        foreach ($eventClubs as $club) {
            $eventClubIds[] = $club->id;
        }
        
        $data = [
            'title' => 'Edit Event',
            'event' => $event,
            'calendars' => $calendars,
            'venues' => $venues,
            'clubs' => $clubs,
            'event_clubs' => $eventClubIds,
            'id' => $id,
            'calendar_id' => $event->calendar_id,
            'title' => $event->title,
            'description' => $event->description,
            'start_date' => $event->start_date,
            'end_date' => $event->end_date,
            'all_day' => $event->all_day,
            'location' => $event->location,
            'address1' => $event->address1,
            'address2' => $event->address2,
            'city' => $event->city,
            'state' => $event->state,
            'zipcode' => $event->zipcode,
            'venue_id' => $event->venue_id,
            'url' => $event->url,
            'color' => $event->color,
            'is_recurring' => $event->is_recurring,
            'recurrence_pattern' => $event->recurrence_pattern,
            'recurrence_end_date' => $event->recurrence_end_date,
            'privacy' => $event->privacy,
            'show_id' => $event->show_id,
            'lat' => $event->lat,
            'lng' => $event->lng,
            'title_err' => '',
            'start_date_err' => '',
            'end_date_err' => '',
            'calendar_id_err' => ''
        ];
        
        // Check for POST
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Process form
            
            // Sanitize POST data
            $_POST = filter_input_array(INPUT_POST, FILTER_SANITIZE_STRING);
            
            // Get form data
            $data = [
                'id' => $id,
                'title' => trim($_POST['title']),
                'description' => trim($_POST['description']),
                'start_date' => trim($_POST['start_date']),
                'end_date' => trim($_POST['end_date']),
                'all_day' => isset($_POST['all_day']) ? 1 : 0,
                'location' => trim($_POST['location']),
                'address1' => trim($_POST['address1']),
                'address2' => trim($_POST['address2']),
                'city' => trim($_POST['city']),
                'state' => trim($_POST['state']),
                'zipcode' => trim($_POST['zipcode']),
                'venue_id' => trim($_POST['venue_id']),
                'url' => trim($_POST['url']),
                'color' => trim($_POST['color']),
                'is_recurring' => isset($_POST['is_recurring']) ? 1 : 0,
                'recurrence_pattern' => trim($_POST['recurrence_pattern']),
                'recurrence_end_date' => trim($_POST['recurrence_end_date']),
                'privacy' => trim($_POST['privacy']),
                'show_id' => trim($_POST['show_id']),
                'calendar_id' => trim($_POST['calendar_id']),
                'lat' => trim($_POST['lat']),
                'lng' => trim($_POST['lng']),
                'clubs' => isset($_POST['clubs']) ? $_POST['clubs'] : [],
                'event' => $event,
                'calendars' => $calendars,
                'venues' => $venues,
                'clubs' => $clubs,
                'event_clubs' => $eventClubIds,
                'title_err' => '',
                'start_date_err' => '',
                'end_date_err' => '',
                'calendar_id_err' => ''
            ];
            
            // Validate title
            if (empty($data['title'])) {
                $data['title_err'] = 'Please enter a title';
            }
            
            // Validate start date
            if (empty($data['start_date'])) {
                $data['start_date_err'] = 'Please enter a start date';
            }
            
            // Validate end date
            if (empty($data['end_date'])) {
                $data['end_date_err'] = 'Please enter an end date';
            } elseif ($data['end_date'] < $data['start_date']) {
                $data['end_date_err'] = 'End date must be after start date';
            }
            
            // Validate calendar ID
            if (empty($data['calendar_id'])) {
                $data['calendar_id_err'] = 'Please select a calendar';
            }
            
            // Automatically geocode address if needed
            if ((!$data['lat'] || !$data['lng']) && 
                ($data['address1'] || $data['city'] || $data['state'] || $data['zipcode'])) {
                
                // Load geocoding helper
                require_once APPROOT . '/helpers/geocoding_helper.php';
                
                // Get map provider settings
                $mapSettings = $this->calendarModel->getMapProviderSettings();
                
                // Use the enhanced geocodeEvent function instead of geocodeAddress
                // This provides better fallback mechanisms and error handling
                $data = geocodeEvent($data, $mapSettings, 'editEvent', $id);
                
                // Log the geocoding attempt
                error_log("Geocoding attempt for event ID {$id}: " . 
                    ($data['lat'] && $data['lng'] ? "SUCCESS" : "FAILED") . 
                    " - Address: " . $data['address1'] . ", " . $data['city'] . ", " . $data['state'] . " " . $data['zipcode']);
                
                // If geocoding failed, add a warning message
                if (!$data['lat'] || !$data['lng']) {
                    flash('calendar_message', 'Warning: Could not geocode the provided address. The event will be updated without map coordinates.', 'alert alert-warning');
                }
            }
            
            // Make sure there are no errors
            if (empty($data['title_err']) && empty($data['start_date_err']) && empty($data['end_date_err']) && empty($data['calendar_id_err'])) {
                // Update event
                if ($this->calendarModel->updateEvent($data)) {
                    flash('calendar_message', 'Event updated successfully');
                    redirect('calendar/event/' . $id);
                } else {
                    flash('calendar_message', 'Something went wrong', 'alert alert-danger');
                    $this->view('calendar/edit_event', $data);
                }
            } else {
                // Load view with errors
                $this->view('calendar/edit_event', $data);
            }
        } else {
            // Load view
            $this->view('calendar/edit_event', $data);
        }
    }
}
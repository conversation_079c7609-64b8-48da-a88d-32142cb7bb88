<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid px-2 px-md-4">
    <div class="row mb-4 align-items-center">
        <div class="col-md-6">
            <h1 class="display-5 fw-bold">Notification Settings</h1>
            <p class="text-muted">Configure how and when you receive notifications</p>
        </div>
        <div class="col-md-6 text-md-end mt-3 mt-md-0">
            <a href="<?php echo BASE_URL; ?>/user/event_subscriptions" class="btn btn-success me-2">
                <i class="fas fa-calendar-check me-2"></i>Event Subscriptions
            </a>
            <a href="<?php echo BASE_URL; ?>/user/profile" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to Profile
            </a>
        </div>
    </div>

    <?php flash('notification_preferences'); ?>

    <form method="POST" action="<?php echo BASE_URL; ?>/user/notifications">
        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token']; ?>">
        
        <div class="row">
            <!-- Notification Types -->
            <div class="col-lg-6">
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notification Types</h5>
                    </div>
                    <div class="card-body">
                        <?php if (isset($data['global_settings']['email_enabled']) && $data['global_settings']['email_enabled']): ?>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                       <?php echo (isset($data['preferences']->email_notifications) && $data['preferences']->email_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="email_notifications">
                                    <i class="fas fa-envelope me-2 text-primary"></i>
                                    <strong>Email Notifications</strong>
                                    <small class="d-block text-muted">Receive notifications via email</small>
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['sms_enabled']) && $data['global_settings']['sms_enabled']): ?>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications"
                                       <?php echo (isset($data['preferences']->sms_notifications) && $data['preferences']->sms_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="sms_notifications">
                                    <i class="fas fa-sms me-2 text-success"></i>
                                    <strong>SMS Notifications</strong>
                                    <small class="d-block text-muted">Receive notifications via text message</small>
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['push_enabled']) && $data['global_settings']['push_enabled']): ?>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="push_notifications" name="push_notifications"
                                       <?php echo (isset($data['preferences']->push_notifications) && $data['preferences']->push_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="push_notifications">
                                    <i class="fas fa-bell me-2 text-warning"></i>
                                    <strong>Push Notifications</strong>
                                    <small class="d-block text-muted">Receive browser push notifications</small>
                                </label>
                            </div>
                        <?php endif; ?>

                        <?php if (isset($data['global_settings']['toast_enabled']) && $data['global_settings']['toast_enabled']): ?>
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="toast_notifications" name="toast_notifications"
                                       <?php echo (isset($data['preferences']->toast_notifications) && $data['preferences']->toast_notifications == '1') ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="toast_notifications">
                                    <i class="fas fa-comment me-2 text-info"></i>
                                    <strong>In-App Notifications</strong>
                                    <small class="d-block text-muted">Show notifications while using the site</small>
                                </label>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="fas fa-address-book me-2"></i>Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" value="<?php echo htmlspecialchars($data['user']->email); ?>" readonly>
                            <div class="form-text">Email address cannot be changed here. Contact support if needed.</div>
                        </div>

                        <div class="mb-3">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($data['user']->phone ?? ''); ?>" 
                                   placeholder="+1234567890">
                            <div class="form-text">Required for SMS notifications. Include country code.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notification Categories -->
            <div class="col-lg-6">
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Notification Categories</h5>
                    </div>
                    <div class="card-body">
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="event_reminders" name="event_reminders"
                                   <?php echo (isset($data['preferences']->event_reminders) && $data['preferences']->event_reminders == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="event_reminders">
                                <i class="fas fa-calendar me-2 text-primary"></i>
                                <strong>Event Reminders</strong>
                                <small class="d-block text-muted">Reminders about upcoming events you're registered for</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="registration_updates" name="registration_updates"
                                   <?php echo (isset($data['preferences']->registration_updates) && $data['preferences']->registration_updates == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="registration_updates">
                                <i class="fas fa-car me-2 text-success"></i>
                                <strong>Registration Updates</strong>
                                <small class="d-block text-muted">Updates about your vehicle registrations</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="judging_updates" name="judging_updates"
                                   <?php echo (isset($data['preferences']->judging_updates) && $data['preferences']->judging_updates == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="judging_updates">
                                <i class="fas fa-gavel me-2 text-warning"></i>
                                <strong>Judging Updates</strong>
                                <small class="d-block text-muted">Notifications about judging progress and results</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="award_notifications" name="award_notifications"
                                   <?php echo (isset($data['preferences']->award_notifications) && $data['preferences']->award_notifications == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="award_notifications">
                                <i class="fas fa-trophy me-2 text-warning"></i>
                                <strong>Award Notifications</strong>
                                <small class="d-block text-muted">Notifications when you win awards</small>
                            </label>
                        </div>

                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="system_announcements" name="system_announcements"
                                   <?php echo (isset($data['preferences']->system_announcements) && $data['preferences']->system_announcements == '1') ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="system_announcements">
                                <i class="fas fa-bullhorn me-2 text-info"></i>
                                <strong>System Announcements</strong>
                                <small class="d-block text-muted">Important system updates and announcements</small>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Timing Preferences -->
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-warning text-white">
                        <h5 class="mb-0"><i class="fas fa-clock me-2"></i>Reminder Timing</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="reminder_times" class="form-label">When to send event reminders</label>
                            <select class="form-select" id="reminder_times" name="reminder_times">
                                <option value="[1440]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440]') ? 'selected' : ''; ?>>
                                    1 day before only
                                </option>
                                <option value="[1440, 60]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440, 60]') ? 'selected' : ''; ?>>
                                    1 day and 1 hour before
                                </option>
                                <option value="[1440, 60, 15]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[1440, 60, 15]') ? 'selected' : ''; ?>>
                                    1 day, 1 hour, and 15 minutes before
                                </option>
                                <option value="[2880, 1440, 60]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[2880, 1440, 60]') ? 'selected' : ''; ?>>
                                    2 days, 1 day, and 1 hour before
                                </option>
                                <option value="[10080, 1440]" <?php echo (isset($data['preferences']->reminder_times) && $data['preferences']->reminder_times == '[10080, 1440]') ? 'selected' : ''; ?>>
                                    1 week and 1 day before
                                </option>
                            </select>
                            <div class="form-text">Choose when you want to receive reminders before events</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm border-0">
                    <div class="card-body text-center">
                        <button type="submit" class="btn btn-primary btn-lg px-5">
                            <i class="fas fa-save me-2"></i>Save Notification Preferences
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-label {
    cursor: pointer;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
/**
 * Notification System CSS v3.49.5
 * 
 * Styles for the comprehensive notification system including
 * toast notifications, subscription modals, and admin interfaces.
 * 
 * Enhanced modal layout with improved spacing and mobile responsiveness.
 * Fixed checkbox positioning to prevent them from sitting on column borders.
 * 
 * Location: /public/css/notifications.css
 * Dependencies: Bootstrap 5, Font Awesome
 */

/* ==========================================================================
   Toast Notifications
   ========================================================================== */

.toast-container {
    z-index: 99999;
    max-width: 400px;
    pointer-events: none;
}

.toast-container > * {
    pointer-events: auto;
}

.toast-notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #0d6efd;
    animation: slideInRight 0.3s ease-out;
    z-index: 100000;
    position: relative;
    pointer-events: auto;
}

.toast-notification.alert-success {
    border-left-color: #198754;
}

.toast-notification.alert-warning {
    border-left-color: #ffc107;
}

.toast-notification.alert-danger {
    border-left-color: #dc3545;
}

.toast-notification.alert-info {
    border-left-color: #0dcaf0;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.toast-notification.fade-out {
    animation: fadeOut 0.15s ease-out forwards;
}

.toast-notification .btn-close {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    padding: 0.25rem 0.5rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 700;
    line-height: 1;
    color: #0d6efd;
    background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%230d6efd'%3e%3cpath d='m.235 1.027 1.027-.235 6.738 6.738 6.738-6.738 1.027.235-6.738 6.738 6.738 6.738-1.027.235-6.738-6.738-6.738 6.738-1.027-.235 6.738-6.738-6.738-6.738z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: 0.375rem;
    opacity: 0.7;
    cursor: pointer;
    width: 1em;
    height: 1em;
}

.toast-notification .btn-close:hover {
    color: #0d6efd;
    text-decoration: none;
    opacity: 1;
}

.toast-notification .btn-close:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    opacity: 1;
}

.toast-notification {
    position: relative;
    padding-right: 3rem;
}

/* ==========================================================================
   Notification Buttons
   ========================================================================== */

[data-notification-btn] {
    position: relative;
    transition: all 0.2s ease;
}

[data-notification-btn]:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

[data-notification-btn].btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0b5ed7);
}

[data-notification-btn].btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #0a58ca);
}

.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* ==========================================================================
   Subscription Modal
   ========================================================================== */

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem;
}

/* Enhanced modal body with proper spacing */
.notification-modal-body {
    padding: 1.5rem;
    min-height: 400px;
}

.notification-modal-body .container-fluid {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Notification times columns with proper spacing */
.notification-times-column {
    padding: 0.75rem 1rem 0.75rem 1.25rem;
    background-color: rgba(248, 249, 250, 0.5);
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.notification-time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Enhanced form check styling */
.form-check {
    padding: 0.75rem 0.5rem 0.75rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    margin-bottom: 0.5rem;
    margin-left: 0.25rem;
    position: relative;
}

.form-check:hover {
    background-color: rgba(13, 110, 253, 0.08);
    transform: translateX(2px);
}

.form-check-input {
    margin-top: 0.25rem;
    margin-right: 0.75rem;
    margin-left: 0.25rem;
}

.form-check-label {
    cursor: pointer;
    font-weight: 500;
    color: #495057;
    line-height: 1.4;
    padding-left: 0.25rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.form-check-input:checked + .form-check-label {
    color: #0d6efd;
    font-weight: 600;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Form switch enhancements */
.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    margin-right: 1rem;
}

.form-switch .form-check-label {
    padding-left: 0.5rem;
}

/* Registration deadline notification positioning fix */
#notify_registration_end_container {
    /* Match alert styling for consistent width and padding */
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.375rem;
    background-color: rgba(13, 202, 240, 0.1);
    border-color: rgba(13, 202, 240, 0.2);
    color: #055160;
    
    /* Remove previous positioning */
    margin-left: 0;
    
    /* Use flexbox to position toggle at the end */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Keep the toggle switch in its original position */
#notify_registration_end_container .form-check-input {
    margin-left: auto;
    margin-right: 0;
}

/* Adjust label to take available space */
#notify_registration_end_container .form-check-label {
    flex: 1;
    margin-right: 1rem;
}

/* ==========================================================================
   Admin Interface
   ========================================================================== */

.notification-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.notification-stats-card .card-body {
    padding: 2rem;
}

.notification-stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1;
}

.notification-provider-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.notification-provider-card:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
}

.notification-provider-card.active {
    border-color: #198754;
    background-color: rgba(25, 135, 84, 0.05);
}

.notification-provider-card.default {
    border-color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

.provider-status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* ==========================================================================
   Queue Management
   ========================================================================== */

.notification-queue-table {
    font-size: 0.9rem;
}

.notification-queue-table .table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.notification-queue-table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.notification-queue-table .badge {
    font-size: 0.7rem;
    padding: 0.35em 0.65em;
}

.notification-status-pending {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.notification-status-sent {
    background-color: #d1edff;
    color: #0c5460;
    border: 1px solid #b8daff;
}

.notification-status-failed {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.notification-status-cancelled {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

/* ==========================================================================
   User Preferences
   ========================================================================== */

.notification-preferences-card {
    border: none;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
}

.notification-preferences-card .card-header {
    border-radius: 12px 12px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.form-switch .form-check-input {
    width: 3rem;
    height: 1.5rem;
    border-radius: 3rem;
}

.form-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.notification-subscription-item {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.2s ease;
}

.notification-subscription-item:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.1);
}

.notification-subscription-meta {
    font-size: 0.85rem;
    color: #6c757d;
}

.notification-times-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.notification-time-badge {
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* ==========================================================================
   Test Interface
   ========================================================================== */

.test-notification-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 2rem;
}

.test-notification-result {
    border-left: 4px solid #0d6efd;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    margin-top: 1rem;
}

.test-notification-result.success {
    border-left-color: #198754;
    background-color: #d1edff;
}

.test-notification-result.error {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.quick-test-buttons .btn {
    margin-bottom: 0.5rem;
    width: 100%;
    text-align: left;
}

.system-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.system-status-item:last-child {
    border-bottom: none;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .toast-container {
        left: 1rem;
        right: 1rem;
        top: 1rem !important;
        max-width: none;
    }
    
    .toast-notification {
        margin-bottom: 0.5rem;
    }
    
    .notification-time-grid {
        grid-template-columns: 1fr;
    }
    
    .notification-stats-number {
        font-size: 2rem;
    }
    
    .notification-queue-table {
        font-size: 0.8rem;
    }
    
    .notification-subscription-item {
        padding: 0.75rem;
    }
    
    .notification-times-list {
        justify-content: center;
    }
    
    /* Mobile modal improvements */
    .notification-modal-body {
        padding: 1rem;
        min-height: auto;
    }
    
    .notification-modal-body .container-fluid {
        padding-left: 0.25rem;
        padding-right: 0.25rem;
    }
    
    .notification-times-column {
        padding: 0.5rem 0.75rem 0.5rem 1rem;
        margin-bottom: 1rem;
    }
    
    .modal-header,
    .modal-footer {
        padding: 1rem;
    }
    
    .form-check {
        padding: 0.5rem 0.25rem 0.5rem 0.5rem;
        margin-bottom: 0.25rem;
        margin-left: 0.25rem;
    }
    
    .form-check-label {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
    }
    
    .toast-notification .alert-heading {
        font-size: 0.9rem;
    }
    
    .toast-notification .small {
        font-size: 0.75rem;
    }
    
    [data-notification-btn] {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .notification-badge {
        top: -6px;
        right: -6px;
        min-width: 16px;
        height: 16px;
        font-size: 9px;
    }
    
    /* Extra small screen modal improvements */
    .notification-modal-body {
        padding: 0.75rem;
    }
    
    .notification-modal-body .container-fluid {
        padding-left: 0;
        padding-right: 0;
    }
    
    .notification-times-column {
        padding: 0.5rem 0.75rem 0.5rem 0.75rem;
        background-color: transparent;
        border: none;
    }
    
    .modal-header {
        padding: 1rem 0.75rem;
    }
    
    .modal-footer {
        padding: 1rem 0.75rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal-footer .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
    
    .modal-footer .btn:last-child {
        margin-bottom: 0;
    }
    
    .form-check {
        padding: 0.5rem 0 0.5rem 0.5rem;
        margin-bottom: 0.25rem;
        margin-left: 0.25rem;
    }
    
    .form-check-input {
        margin-right: 0.5rem;
        margin-left: 0.25rem;
    }
    
    .form-check-label {
        font-size: 0.85rem;
        line-height: 1.3;
    }
    
    .modal-title {
        font-size: 1.1rem;
    }
    
    .alert {
        font-size: 0.85rem;
        padding: 0.75rem;
    }
}

/* ==========================================================================
   Dark Mode Support (Optional)
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .toast-notification {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .notification-preferences-card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .notification-subscription-item {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .notification-time-badge {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .test-notification-form {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #e2e8f0;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .toast-container,
    [data-notification-btn],
    .notification-badge {
        display: none !important;
    }
    
    .notification-queue-table {
        font-size: 0.7rem;
    }
    
    .notification-subscription-item {
        border: 1px solid #000;
        break-inside: avoid;
    }
}
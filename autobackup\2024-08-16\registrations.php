<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/staff/show/<?php echo $data['show']->id; ?>"><?php echo $data['show']->name; ?></a></li>
                    <li class="breadcrumb-item active" aria-current="page">Registrations</li>
                </ol>
            </nav>
            
            <h1><?php echo $data['show']->name; ?> - Registrations</h1>
            
            <?php flash('staff_message'); ?>
            
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registrations</h5>
                    <a href="<?php echo URLROOT; ?>/staff/createRegistration/<?php echo $data['show']->id; ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-plus"></i> Create Registration
                    </a>
                </div>
                <div class="card-body">
                    <?php if (empty($data['registrations'])) : ?>
                        <p class="text-muted">No registrations found for this show.</p>
                    <?php else : ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="registrationsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Vehicle</th>
                                        <th>Owner</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Payment</th>
                                        <th>Check-In</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($data['registrations'] as $registration) : ?>
                                        <tr>
                                            <td><?php echo $registration->id; ?></td>
                                            <td>
                                                <?php 
                                                    echo $registration->vehicle_year . ' ' . 
                                                         $registration->vehicle_make . ' ' . 
                                                         $registration->vehicle_model;
                                                ?>
                                            </td>
                                            <td><?php echo $registration->owner_name; ?></td>
                                            <td><?php echo $registration->category_name; ?></td>
                                            <td>
                                                <?php 
                                                    $statusClass = 'secondary';
                                                    switch ($registration->status) {
                                                        case 'approved':
                                                            $statusClass = 'success';
                                                            break;
                                                        case 'pending':
                                                            $statusClass = 'warning';
                                                            break;
                                                        case 'rejected':
                                                            $statusClass = 'danger';
                                                            break;
                                                        case 'cancelled':
                                                            $statusClass = 'danger';
                                                            break;
                                                    }
                                                ?>
                                                <span class="badge bg-<?php echo $statusClass; ?>">
                                                    <?php echo ucfirst($registration->status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php 
                                                    $paymentClass = 'secondary';
                                                    $paymentStatus = $registration->payment_status ?? 'unpaid';
                                                    
                                                    switch ($paymentStatus) {
                                                        case 'paid':
                                                            $paymentClass = 'success';
                                                            break;
                                                        case 'pending':
                                                            $paymentClass = 'warning';
                                                            break;
                                                        case 'refunded':
                                                            $paymentClass = 'info';
                                                            break;
                                                        case 'unpaid':
                                                            $paymentClass = 'danger';
                                                            break;
                                                    }
                                                ?>
                                                <span class="badge bg-<?php echo $paymentClass; ?>">
                                                    <?php echo ucfirst($paymentStatus); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($registration->checked_in) : ?>
                                                    <span class="badge bg-success">
                                                        <i class="fas fa-check"></i> Checked In
                                                        <br>
                                                        <small><?php echo date('m/d/y g:i a', strtotime($registration->check_in_time)); ?></small>
                                                    </span>
                                                <?php else : ?>
                                                    <span class="badge bg-secondary">Not Checked In</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group">
                                                    <a href="<?php echo URLROOT; ?>/staff/registration/<?php echo $registration->id; ?>" class="btn btn-sm btn-primary">
                                                        <i class="fas fa-eye"></i> View
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <span class="visually-hidden">Toggle Dropdown</span>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li>
                                                            <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/editRegistration/<?php echo $registration->id; ?>">
                                                                <i class="fas fa-edit"></i> Edit
                                                            </a>
                                                        </li>
                                                        <?php if ($paymentStatus != 'paid' && $data['show']->registration_fee > 0) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/processPayment/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-money-bill"></i> Process Payment
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <?php if (!$registration->checked_in && ($registration->payment_status == 'paid' || $data['show']->is_free)) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/checkIn/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-clipboard-check"></i> Check In
                                                                </a>
                                                            </li>
                                                        <?php elseif ($registration->checked_in) : ?>
                                                            <li>
                                                                <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/undoCheckIn/<?php echo $registration->id; ?>">
                                                                    <i class="fas fa-undo"></i> Undo Check-In
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                        <li>
                                                            <a class="dropdown-item" href="<?php echo URLROOT; ?>/staff/printRegistration/<?php echo $registration->id; ?>" target="_blank">
                                                                <i class="fas fa-print"></i> Print Card
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Initialize DataTable for better sorting and filtering
    $(document).ready(function() {
        $('#registrationsTable').DataTable({
            "order": [[ 0, "desc" ]],
            "pageLength": 25
        });
    });
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
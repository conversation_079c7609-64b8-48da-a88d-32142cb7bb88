# Toggle Alignment Fix Documentation

## Problem Description
Bootstrap form-switch toggles were extending approximately 10 pixels outside their container boxes, creating misaligned and unprofessional appearance on notification preference pages.

## Root Cause
Bootstrap's default `.form-switch .form-check-input` CSS applies margins that push toggle switches outside their intended containers. The specific problematic styles are:
- `margin-right: 1rem` - pushes toggle outside container
- Default margins that don't align with container padding

## Solution Pattern

### HTML Structure
The fix works with standard Bootstrap form-switch structure:
```html
<div class="form-check form-switch mb-3" style="[CONTAINER_STYLES]">
    <input class="form-check-input" type="checkbox" id="toggle_id" name="toggle_name"
           style="[INPUT_STYLES]" [checked_attribute]>
    <label class="form-check-label" for="toggle_id">
        <i class="fas fa-icon me-2 text-color"></i>
        <strong>Toggle Label</strong>
        <small class="d-block text-muted">Description text</small>
    </label>
</div>
```

### Required Inline Styles

#### Container Styling
Apply to the `.form-check.form-switch` div:
```css
style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;"
```

**Breakdown:**
- `padding: 1rem` - Creates internal spacing within the container
- `border: 1px solid #e9ecef` - Creates visible container boundary
- `border-radius: 8px` - Rounds corners for modern appearance
- `background-color: #f8f9fa` - Light gray background for visual separation

#### Input Styling
Apply to the `.form-check-input` element:
```css
style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
```

**Breakdown:**
- `margin-left: 0 !important` - Removes left margin that could push toggle
- `margin-right: 0.75rem !important` - Sets proper spacing between toggle and label
- `margin-top: 0.25rem !important` - Provides slight vertical alignment adjustment
- `!important` - Overrides Bootstrap's default form-switch styles

## Implementation Steps

### 1. Identify Problematic Toggles
Look for form-switch elements where toggles extend outside their visual containers.

### 2. Apply Container Styling
Add the container inline styles to the parent `.form-check.form-switch` div.

### 3. Apply Input Styling
Add the input inline styles to the `.form-check-input` element.

### 4. Test Alignment
Verify toggles are properly contained within their boxes and aligned correctly.

## Files Fixed
- `views/user/notifications.php` - All 8 toggles (3 notification types + 5 categories)

## Why Inline Styles?
- **CSS Specificity Issues**: Bootstrap and multiple CSS files were overriding custom styles
- **Immediate Solution**: Inline styles have highest specificity and work immediately
- **Consistent Results**: Bypasses any CSS loading order or caching issues
- **Maintainable**: Clear, documented pattern that can be replicated

## Alternative Approaches Attempted
1. **Custom CSS Classes**: Overridden by Bootstrap specificity
2. **Ultra-specific CSS Selectors**: Still overridden by other stylesheets
3. **CSS File Modifications**: Loading order issues prevented proper application

## Visual Result
- Toggles properly contained within gray boxes
- Professional, aligned appearance
- Consistent styling across all notification preferences
- No elements extending outside containers

## Future Usage
When encountering similar toggle alignment issues:
1. Apply the exact inline styles documented above
2. Test on both desktop and mobile
3. Verify all toggles in the section are fixed
4. Document any variations needed for different layouts

## Browser Compatibility
- Tested on Chrome desktop
- Works with Bootstrap 5.3.0-alpha1
- Compatible with existing CSS framework

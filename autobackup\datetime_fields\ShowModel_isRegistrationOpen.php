    /**
     * Check if registration is open for a show
     * 
     * @param int $showId Show ID
     * @return bool True if registration is open, false otherwise
     */
    public function isRegistrationOpen($showId) {
        try {
            // Get registration dates
            $this->db->query('SELECT registration_start, registration_end FROM shows WHERE id = :id');
            $this->db->bind(':id', $showId);
            $show = $this->db->single();
            
            if (!$show) {
                return false;
            }
            
            // Check if registration dates are set
            if (empty($show->registration_start) || empty($show->registration_end)) {
                return false;
            }
            
            // Get current date and time
            $now = new DateTime();
            
            // Get registration start and end dates
            $startDate = new DateTime($show->registration_start);
            $endDate = new DateTime($show->registration_end);
            
            // Check if current date is within registration period
            return ($now >= $startDate && $now <= $endDate);
        } catch (Exception $e) {
            error_log("Error in ShowModel::isRegistrationOpen: " . $e->getMessage());
            return false;
        }
    }
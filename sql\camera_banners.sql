-- Camera Banners Table
-- Stores advertisement banners for camera and QR scanner modals

CREATE TABLE IF NOT EXISTS camera_banners (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('text', 'image') NOT NULL DEFAULT 'text',
    text TEXT NULL,
    image_path VARCHAR(500) NULL,
    alt_text VARCHAR(255) NULL,
    active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_active (active),
    INDEX idx_sort_order (sort_order),
    INDEX idx_type (type)
);

-- Insert default banners
INSERT INTO camera_banners (type, text, active, sort_order) VALUES 
('text', 'Welcome to our Event Platform!', TRUE, 1),
('text', 'Check out our upcoming events!', TRUE, 2),
('text', 'Register your vehicle today!', TRUE, 3);

-- Add camera banner delay setting
INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, description) VALUES 
('camera_banner_delay', '5000', 'Banner rotation delay in milliseconds for camera modals', 'media', 'Camera banner delay')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
setting_description = VALUES(setting_description),
setting_group = VALUES(setting_group),
description = VALUES(description);
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php if (isset($_SESSION['cache_buster'])): ?>
    <!-- Cache control headers to prevent caching after updates -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <?php endif; ?>
    <title><?php echo isset($title) ? $title . ' - ' . APP_NAME : APP_NAME; ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/style.css">
    <link rel="stylesheet" href="<?php echo BASE_URL; ?>/public/css/image-viewer.css">
    
    <?php 
    // Get custom CSS from settings
    if (!isset($settingsModel)) {
        require_once APPROOT . '/models/SettingsModel.php';
        $settingsModel = new SettingsModel();
    }
    
    // Include page-specific custom CSS if provided
    if (isset($custom_css) && !empty($custom_css)) {
        echo '<link rel="stylesheet" href="' . $custom_css . '">';
    }
    
    // Get global custom CSS from settings
    $global_custom_css = $settingsModel->getSetting('custom_css', '');
    $primary_color = $settingsModel->getSetting('primary_color', '#007bff');
    $secondary_color = $settingsModel->getSetting('secondary_color', '#6c757d');
    $accent_color = $settingsModel->getSetting('accent_color', '#28a745');
    $background_color = $settingsModel->getSetting('background_color', '#f8f9fa');
    $text_color = $settingsModel->getSetting('text_color', '#212529');
    
    // Include global custom CSS if it exists
    if (!empty($global_custom_css)) {
        echo '<link rel="stylesheet" href="' . $global_custom_css . '">';
    }
    
    if (!empty($primary_color) || !empty($secondary_color) || !empty($accent_color) || !empty($background_color) || !empty($text_color)) : 
    ?>
    <style>
        :root {
            --primary-color: <?php echo $primary_color; ?>;
            --secondary-color: <?php echo $secondary_color; ?>;
            --accent-color: <?php echo $accent_color; ?>;
            --background-color: <?php echo $background_color; ?>;
            --text-color: <?php echo $text_color; ?>;
        }
        
        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }
        
        .bg-primary {
            background-color: var(--primary-color) !important;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }
        
        .text-primary {
            color: var(--primary-color) !important;
        }
        
        .alert-success, .bg-success {
            background-color: var(--accent-color) !important;
        }
        
        /* Custom CSS from branding settings */
        <?php echo isset($custom_css) ? $custom_css : ''; ?>
    </style>
    <?php endif; ?>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo isset($csrf_token) ? htmlspecialchars($csrf_token) : ''; ?>">
    
    <!-- Base URL for JavaScript -->
    <script>
        const BASE_URL = '<?php echo BASE_URL; ?>';
    </script>
    
    <!-- Favicon -->
    <?php
    $favicon = $settingsModel->getSetting('favicon', '');
    if (!empty($favicon)) {
        echo '<link rel="icon" href="' . $favicon . '">';
    } else {
        echo '<link rel="icon" href="' . BASE_URL . '/public/img/favicon.ico">';
    }
    ?>
    
    <!-- Custom JavaScript -->
    <script src="<?php echo BASE_URL; ?>/public/js/main.js"></script>
    
    <!-- Page-specific head content -->
    <?php if (isset($head_content)) echo $head_content; ?>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container-fluid">
            <?php
            // Get logo from settings
            $logo = $settingsModel->getSetting('logo', '');
            $site_name = $settingsModel->getSetting('site_name', APP_NAME);
            ?>
            
            <!-- Navigation -->
            <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
                <div class="container-fluid">
                    <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                        <?php if (!empty($logo)) : ?>
                            <img src="<?php echo $logo; ?>" alt="<?php echo htmlspecialchars($site_name); ?>" height="40" class="d-inline-block align-top">
                        <?php else : ?>
                            <?php echo htmlspecialchars($site_name); ?>
                        <?php endif; ?>
                    </a>
                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                    <div class="collapse navbar-collapse" id="navbarNav">
                        <ul class="navbar-nav me-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo BASE_URL; ?>">Home</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo BASE_URL; ?>/show">Shows</a>
                            </li>
                            <?php if (isset($_SESSION['user_id'])) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>/user/dashboard">Dashboard</a>
                                </li>
                            <?php endif; ?>
                            <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        Tools
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/form_designer/templates">Form Builder</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/imageManager">Image Manager</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultTemplates">Default Templates</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/entityTemplates">Entity Templates</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><h6 class="dropdown-header">Judging & Coordination</h6></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/judgingMetrics">Judging Metrics</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultMetrics">Default Metrics</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/ageWeights">Age Weights</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultCategories">Default Categories</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/settings">Settings</a></li>
                                    </ul>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo BASE_URL; ?>/home/<USER>">About</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="<?php echo BASE_URL; ?>/home/<USER>">Contact</a>
                            </li>
                        </ul>
                        <ul class="navbar-nav">
                            <?php if (isset($_SESSION['user_id'])) : ?>
                                <?php
                                // Get user's profile image
                                $db = new Database();
                                $db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
                                $db->bind(':entity_type', 'user');
                                $db->bind(':entity_id', $_SESSION['user_id']);
                                $profileImage = $db->single();
                                ?>
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <?php if ($profileImage) : ?>
                                            <div class="rounded-circle overflow-hidden me-2" style="width: 30px; height: 30px; background-color: rgba(255,255,255,0.2);">
                                                <img src="<?php echo BASE_URL; ?>/<?php echo $profileImage->file_path; ?>" alt="Profile" class="img-fluid" style="object-fit: cover; width: 100%; height: 100%;">
                                            </div>
                                        <?php else : ?>
                                            <i class="fas fa-user-circle me-2"></i>
                                        <?php endif; ?>
                                        <?php 
                                        if (isset($current_user) && isset($current_user->name)) {
                                            echo htmlspecialchars($current_user->name);
                                        } elseif (isset($_SESSION['user_name'])) {
                                            echo htmlspecialchars($_SESSION['user_name']);
                                        } else {
                                            echo 'Account';
                                        }
                                        ?>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="navbarDropdown">
                                        <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin') : ?>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/dashboard">Admin Dashboard</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">Coordinator Dashboard</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">Judge Dashboard</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">Show Management</h6></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/shows">Manage Shows</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/registerVehicle">
                                                <i class="fas fa-car me-2"></i> Register Vehicle for User
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><h6 class="dropdown-header">Tools</h6></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/form_designer/templates">Form Builder</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/imageManager">Image Manager</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/defaultTemplates">Default Templates</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/entityTemplates">Entity Templates</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                        <?php endif; ?>
                                        <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'coordinator') : ?>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/dashboard">Coordinator Dashboard</a></li>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/coordinator/registerVehicle">
                                                <i class="fas fa-car me-2"></i> Register Vehicle for User
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                        <?php endif; ?>
                                        <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'judge') : ?>
                                            <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/judge/dashboard">Judge Dashboard</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                        <?php endif; ?>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/user/profile">Profile</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/user/vehicles">My Vehicles</a></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/user/registrations">My Registrations</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/auth/logout">Logout</a></li>
                                    </ul>
                                </li>
                            <?php else : ?>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>/auth/login">Login</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="<?php echo BASE_URL; ?>/auth/register">Register</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </nav>
            
            <!-- Admin Impersonation Banner -->
            <?php if (isset($_SESSION['admin_impersonating']) && $_SESSION['admin_impersonating']): ?>
            <div class="alert alert-warning mb-0 text-center py-2" style="border-radius: 0;">
                <div class="container d-flex justify-content-between align-items-center">
                    <span>
                        <i class="fas fa-user-secret me-2"></i> You are currently viewing the site as 
                        <?php 
                            // Get the current user's name
                            require_once APPROOT . '/models/UserModel.php';
                            $userModel = new UserModel();
                            $currentUser = $userModel->getUserById($_SESSION['user_id']);
                            echo '<strong>' . htmlspecialchars($currentUser->name) . '</strong> (' . ucfirst(htmlspecialchars($currentUser->role)) . ')';
                        ?>
                    </span>
                    <a href="<?php echo BASE_URL; ?>/admin/endImpersonation" class="btn btn-danger btn-sm">
                        <i class="fas fa-sign-out-alt me-1"></i> Return to Admin Account
                    </a>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </header>
    
    <!-- Flash Messages -->
    <?php if (isset($_SESSION['flash_message']) && !empty($_SESSION['flash_message'])) : ?>
        <div class="container mt-3">
            <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show" role="alert">
                <?php echo $_SESSION['flash_message']['message']; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
        <?php unset($_SESSION['flash_message']); ?>
    <?php endif; ?>
    
    <!-- Main Content -->
    <main class="py-4">
<?php
/**
 * Notification System Migration Script
 * Adds missing columns to user_notification_preferences table
 */

require_once 'config/config.php';
require_once 'libraries/Database.php';

echo "Starting notification preferences migration...\n";

try {
    $db = new Database();
    
    // Check if columns already exist
    $db->query("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'user_notification_preferences' 
                AND TABLE_SCHEMA = DATABASE()
                AND COLUMN_NAME IN ('event_reminders', 'registration_updates', 'judging_updates', 'award_notifications', 'system_announcements', 'reminder_times')");
    
    $existingColumns = $db->resultSet();
    $existingColumnNames = array_column($existingColumns, 'COLUMN_NAME');
    
    echo "Existing columns found: " . implode(', ', $existingColumnNames) . "\n";
    
    // Add missing columns
    $columnsToAdd = [
        'event_reminders' => 'TINYINT(1) DEFAULT 1',
        'registration_updates' => 'TINYINT(1) DEFAULT 1',
        'judging_updates' => 'TINYINT(1) DEFAULT 1',
        'award_notifications' => 'TINYINT(1) DEFAULT 1',
        'system_announcements' => 'TINYINT(1) DEFAULT 1',
        'reminder_times' => 'VARCHAR(255) DEFAULT \'[1440, 60]\''
    ];
    
    foreach ($columnsToAdd as $columnName => $columnDefinition) {
        if (!in_array($columnName, $existingColumnNames)) {
            echo "Adding column: $columnName\n";
            $db->query("ALTER TABLE user_notification_preferences ADD COLUMN $columnName $columnDefinition");
            $db->execute();
            echo "✓ Column $columnName added successfully\n";
        } else {
            echo "✓ Column $columnName already exists\n";
        }
    }
    
    // Update existing records to have default values
    echo "Updating existing records with default values...\n";
    $db->query("UPDATE user_notification_preferences SET 
                event_reminders = COALESCE(event_reminders, 1),
                registration_updates = COALESCE(registration_updates, 1),
                judging_updates = COALESCE(judging_updates, 1),
                award_notifications = COALESCE(award_notifications, 1),
                system_announcements = COALESCE(system_announcements, 1),
                reminder_times = COALESCE(reminder_times, '[1440, 60]')");
    $db->execute();
    echo "✓ Existing records updated\n";
    
    // Verify the final structure
    echo "\nFinal table structure:\n";
    $db->query("SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'user_notification_preferences' 
                AND TABLE_SCHEMA = DATABASE()
                ORDER BY ORDINAL_POSITION");
    
    $columns = $db->resultSet();
    foreach ($columns as $column) {
        echo "- {$column->COLUMN_NAME}: {$column->DATA_TYPE} (Default: {$column->COLUMN_DEFAULT})\n";
    }
    
    echo "\n✅ Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
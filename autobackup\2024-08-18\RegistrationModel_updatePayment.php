<?php
/**
 * Registration Model
 * 
 * This model handles all database operations related to registrations.
 */
class RegistrationModel {
    /**
     * Update payment information for a registration
     * 
     * @param array $data Payment data
     * @return bool
     */
    public function updatePayment($data) {
        $this->db->query('UPDATE registrations SET payment_status = :payment_status, 
                          fee = :fee, payment_method_id = :payment_method_id, 
                          payment_date = CASE WHEN :payment_status = "completed" AND payment_date IS NULL THEN NOW() ELSE payment_date END, 
                          payment_reference = :payment_reference, 
                          updated_at = NOW() 
                          WHERE id = :id');
        
        $this->db->bind(':payment_status', $data['payment_status']);
        $this->db->bind(':fee', $data['fee'] ?? 0.00);
        $this->db->bind(':payment_method_id', $data['payment_method_id']);
        $this->db->bind(':payment_reference', $data['payment_reference'] ?? null);
        $this->db->bind(':id', $data['id']);
        
        return $this->db->execute();
    }
}
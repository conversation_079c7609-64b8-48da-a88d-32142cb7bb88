<?php
/**
 * Calendar Controller
 * 
 * This controller handles all calendar-related functionality.
 * 
 * Version 1.0.1 - Fixed deprecated FILTER_SANITIZE_STRING
 * - Created calendar management functionality
 * - Added event management functionality
 * - Implemented venue management
 * - Added club/group management
 * - Implemented calendar permissions
 * - Added notification system
 * - Implemented import/export functionality
 */
class CalendarController extends Controller {
    private $calendarModel;
    private $showModel;
    private $userModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        if (!isLoggedIn()) {
            redirect('auth/login');
        }
        
        // Initialize models
        $this->calendarModel = $this->model('CalendarModel');
        $this->showModel = $this->model('ShowModel');
        $this->userModel = $this->model('UserModel');
        
        // Check if calendar tables exist
        if (!$this->calendarModel->tablesExist()) {
            // Create tables
            if (!$this->calendarModel->createTables()) {
                die('Error creating calendar tables');
            }
            
            // Create default calendar
            $defaultCalendar = [
                'name' => 'Main Calendar',
                'description' => 'Default calendar for all events',
                'color' => '#3788d8',
                'is_visible' => 1,
                'is_public' => 1,
                'owner_id' => $_SESSION['user_id']
            ];
            
            $calendarId = $this->calendarModel->createCalendar($defaultCalendar);
            
            // Sync with existing shows
            if ($calendarId) {
                $this->calendarModel->syncEventsWithShows($calendarId);
            }
        }
    }
    
    /**
     * Calendar index page
     * 
     * @return void
     */
    public function index() {
        // Get user's calendars
        $calendars = $this->calendarModel->getCalendars($_SESSION['user_id']);
        
        // Get calendar settings
        $settings = $this->calendarModel->getCalendarSettings();
        
        $data = [
            'title' => 'Calendar',
            'calendars' => $calendars,
            'settings' => $settings
        ];
        
        $this->view('calendar/index', $data);
    }
    
    /**
     * Get events as JSON for AJAX requests
     * 
     * @return void
     */
    public function getEvents() {
        // Check for AJAX request
        if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
            redirect('calendar');
        }
        
        // Get filter parameters
        $start = isset($_GET['start']) ? $_GET['start'] : null;
        $end = isset($_GET['end']) ? $_GET['end'] : null;
        $calendarId = isset($_GET['calendar_id']) ? $_GET['calendar_id'] : null;
        
        $filters = [];
        
        if ($start) {
            $filters['start_date'] = $start;
        }
        
        if ($end) {
            $filters['end_date'] = $end;
        }
        
        if ($calendarId) {
            $filters['calendar_id'] = $calendarId;
        }
        
        // Get events
        $events = $this->calendarModel->getEvents($filters, $_SESSION['user_id']);
        
        // Format events for FullCalendar
        $formattedEvents = [];
        
        foreach ($events as $event) {
            $formattedEvent = [
                'id' => $event->id,
                'title' => $event->title,
                'start' => $event->start_date,
                'end' => $event->end_date,
                'allDay' => (bool)$event->all_day,
                'url' => URLROOT . '/calendar/event/' . $event->id,
                'extendedProps' => [
                    'description' => $event->description,
                    'location' => $event->location,
                    'calendar_id' => $event->calendar_id,
                    'calendar_name' => $event->calendar_name,
                    'show_id' => $event->show_id,
                    'show_name' => $event->show_name,
                    'privacy' => $event->privacy
                ]
            ];
            
            // Set color
            if (!empty($event->color)) {
                $formattedEvent['backgroundColor'] = $event->color;
                $formattedEvent['borderColor'] = $event->color;
            } else if (!empty($event->calendar_color)) {
                $formattedEvent['backgroundColor'] = $event->calendar_color;
                $formattedEvent['borderColor'] = $event->calendar_color;
            }
            
            $formattedEvents[] = $formattedEvent;
        }
        
        // Return JSON response
        header('Content-Type: application/json');
        echo json_encode($formattedEvents);
    }
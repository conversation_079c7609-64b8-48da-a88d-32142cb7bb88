<?php
/**
 * Test Database Relationship Check
 * 
 * Quick test to verify the relationship checker works without SQL errors
 */

// Define APPROOT
define('APPROOT', dirname(__FILE__));

// Load configuration
require_once APPROOT . '/config/config.php';

// Check if user is admin (following your site's pattern)
session_start();
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

echo "<h1>🧪 Database Relationship Check Test</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
    .error { background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0; }
    .info { background: #d1ecf1; padding: 15px; border-left: 4px solid #17a2b8; margin: 10px 0; }
    .warning { background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0; }
</style>";

try {
    // Test database connection
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='success'>";
    echo "<h3>✅ Database Connection Successful</h3>";
    echo "<p><strong>Database:</strong> " . DB_NAME . "</p>";
    echo "</div>";
    
    // Test the problematic foreign key query
    echo "<h2>🔍 Testing Foreign Key Query</h2>";
    
    try {
        $foreignKeys = $pdo->query("
            SELECT 
                kcu.TABLE_NAME,
                kcu.COLUMN_NAME,
                kcu.CONSTRAINT_NAME,
                kcu.REFERENCED_TABLE_NAME,
                kcu.REFERENCED_COLUMN_NAME,
                rc.DELETE_RULE,
                rc.UPDATE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME
                AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
            WHERE kcu.TABLE_SCHEMA = DATABASE()
            AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
            ORDER BY kcu.TABLE_NAME, kcu.COLUMN_NAME
            LIMIT 5
        ")->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>";
        echo "<h3>✅ Foreign Key Query Fixed</h3>";
        echo "<p>Found " . count($foreignKeys) . " foreign key constraints (showing first 5)</p>";
        
        if (!empty($foreignKeys)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f2f2f2;'><th>Table</th><th>Column</th><th>References</th><th>Delete Rule</th></tr>";
            foreach ($foreignKeys as $fk) {
                echo "<tr>";
                echo "<td>{$fk['TABLE_NAME']}</td>";
                echo "<td>{$fk['COLUMN_NAME']}</td>";
                echo "<td>{$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</td>";
                echo "<td>{$fk['DELETE_RULE']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Foreign Key Query Still Has Issues</h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
        
        // Try fallback query
        echo "<h3>🔄 Trying Fallback Query</h3>";
        try {
            $foreignKeys = $pdo->query("
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND REFERENCED_TABLE_NAME IS NOT NULL
                ORDER BY TABLE_NAME, COLUMN_NAME
                LIMIT 5
            ")->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='success'>";
            echo "<h3>✅ Fallback Query Works</h3>";
            echo "<p>Found " . count($foreignKeys) . " foreign key constraints using fallback method</p>";
            echo "</div>";
            
        } catch (PDOException $e2) {
            echo "<div class='error'>";
            echo "<h3>❌ Even Fallback Query Failed</h3>";
            echo "<p><strong>Error:</strong> " . $e2->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    // Test a simple orphan check
    echo "<h2>🔍 Testing Orphan Record Check</h2>";
    
    try {
        // Test with a table that should exist
        $result = $pdo->query("
            SELECT COUNT(*) as count 
            FROM users 
            WHERE id IS NOT NULL
        ")->fetch(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>";
        echo "<h3>✅ Orphan Check Query Works</h3>";
        echo "<p>Found " . $result['count'] . " users in the database</p>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='warning'>";
        echo "<h3>⚠️ Users Table Issue</h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "<p>This might indicate the users table doesn't exist or has permission issues.</p>";
        echo "</div>";
    }
    
    // Test information schema access
    echo "<h2>🔍 Testing Information Schema Access</h2>";
    
    try {
        $tables = $pdo->query("
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = DATABASE()
        ")->fetch(PDO::FETCH_ASSOC);
        
        echo "<div class='success'>";
        echo "<h3>✅ Information Schema Access Works</h3>";
        echo "<p>Found " . $tables['count'] . " tables in your database</p>";
        echo "</div>";
        
    } catch (PDOException $e) {
        echo "<div class='error'>";
        echo "<h3>❌ Information Schema Access Issue</h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Database Connection Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>📋 Summary</h2>";
echo "<div class='info'>";
echo "<p>If all tests passed, the database relationship checker should now work without the 'ambiguous column' error.</p>";
echo "<p><strong>Next Step:</strong> <a href='check_database_relationships.php'>Try the full relationship check</a></p>";
echo "</div>";

echo "<p><a href='analyze_actual_database_usage.php'>← Back to Database Analysis</a></p>";
?>
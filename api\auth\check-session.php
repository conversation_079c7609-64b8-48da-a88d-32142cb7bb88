<?php
/**
 * API endpoint to check if user is logged in
 * Used by PWA Facebook login to check session status
 */

// Set content type to JSON
header('Content-Type: application/json');

// Include necessary files
require_once '../../config/config.php';
require_once '../../libraries/Core.php';
require_once '../../libraries/Controller.php';
require_once '../../libraries/Database.php';
require_once '../../helpers/session_helper.php';

// Start session
session_start();

try {
    // Check if user is logged in
    $isLoggedIn = isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    
    $response = [
        'logged_in' => $isLoggedIn,
        'user_id' => $isLoggedIn ? $_SESSION['user_id'] : null,
        'user_role' => $isLoggedIn ? ($_SESSION['user_role'] ?? 'user') : null,
        'user_name' => $isLoggedIn ? ($_SESSION['user_name'] ?? '') : null
    ];
    
    // Log the check for debugging
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        error_log('Session check API - User logged in: ' . ($isLoggedIn ? 'Yes' : 'No') . 
                  ', User ID: ' . ($_SESSION['user_id'] ?? 'None'));
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log('Session check API error: ' . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'logged_in' => false,
        'error' => 'Internal server error'
    ]);
}
?>

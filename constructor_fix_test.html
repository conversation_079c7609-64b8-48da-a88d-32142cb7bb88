<!DOCTYPE html>
<html>
<head>
    <title>Constructor Fix Test</title>
</head>
<body>
    <h2>Constructor Fix Test</h2>
    <div id="output"></div>

    <script>
        function output(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        // Load the script and remove the async init call from constructor
        fetch('/public/js/camera-banner.js')
            .then(response => response.text())
            .then(code => {
                output('Original script loaded');
                
                // Remove the async init call from the constructor
                const modifiedCode = code.replace(
                    /\/\/ Initialize async - don't wait for it[\s\S]*?this\.init\(\)\.catch\(error => \{[\s\S]*?console\.error\('CameraBanner async init failed:', error\);[\s\S]*?\}\);/,
                    '// Async init removed for testing'
                );
                
                output('Removed async init call, executing...');
                
                try {
                    eval(modifiedCode);
                    
                    output('Script executed successfully');
                    
                    if (window.cameraBanner) {
                        output('=== Instance Properties ===');
                        output('- version: ' + window.cameraBanner.version);
                        output('- typeof version: ' + typeof window.cameraBanner.version);
                        output('- banners: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'undefined'));
                        output('- constructor name: ' + window.cameraBanner.constructor.name);
                        
                        // Test if we can call init manually
                        output('=== Manual Init Test ===');
                        window.cameraBanner.init().then(() => {
                            output('✓ Manual init completed successfully');
                            output('- version after init: ' + window.cameraBanner.version);
                            output('- banners after init: ' + window.cameraBanner.banners.length);
                        }).catch(error => {
                            output('✗ Manual init failed: ' + error.message);
                            output('Error stack: ' + error.stack);
                        });
                    }
                    
                } catch (error) {
                    output('Execution error: ' + error.message);
                    output('Stack: ' + error.stack);
                }
            })
            .catch(error => {
                output('Failed to load script: ' + error.message);
            });
    </script>
</body>
</html>
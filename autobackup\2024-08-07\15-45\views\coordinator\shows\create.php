<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1><?php echo $data['title']; ?></h1>
            <p class="lead">Create a new car show or event.</p>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Show Details</h5>
                </div>
                <div class="card-body">
                    <form action="<?php echo URLROOT; ?>/coordinator/createShow" method="post">
                        <?php echo csrfTokenField(); ?>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo (!empty($data['name_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="name" name="name" value="<?php echo $data['name']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['name_err']; ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                                <input type="text" class="form-control <?php echo (!empty($data['location_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="location" name="location" value="<?php echo $data['location']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['location_err']; ?></div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"><?php echo $data['description']; ?></textarea>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php echo (!empty($data['start_date_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="start_date" name="start_date" value="<?php echo $data['start_date']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['start_date_err']; ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php echo (!empty($data['end_date_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="end_date" name="end_date" value="<?php echo $data['end_date']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['end_date_err']; ?></div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="registration_open_date" class="form-label">Registration Open Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php echo (!empty($data['registration_open_date_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="registration_open_date" name="registration_open_date" value="<?php echo $data['registration_open_date']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['registration_open_date_err']; ?></div>
                            </div>
                            <div class="col-md-6">
                                <label for="registration_close_date" class="form-label">Registration Close Date <span class="text-danger">*</span></label>
                                <input type="date" class="form-control <?php echo (!empty($data['registration_close_date_err'])) ? 'is-invalid' : ''; ?>" 
                                    id="registration_close_date" name="registration_close_date" value="<?php echo $data['registration_close_date']; ?>" required>
                                <div class="invalid-feedback"><?php echo $data['registration_close_date_err']; ?></div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="status" name="status" <?php echo ($data['status'] == 'published') ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="status">Publish Show (make visible to public)</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="fan_voting_enabled" name="fan_voting_enabled" <?php echo ($data['fan_voting_enabled']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="fan_voting_enabled">Enable Fan Voting</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-4">
                            <a href="<?php echo URLROOT; ?>/coordinator/dashboard" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">Create Show</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>
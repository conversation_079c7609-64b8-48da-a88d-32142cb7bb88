<!DOCTYPE html>
<html>
<head>
    <title>Test Script Loading</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <h2>Script Loading Test</h2>
    <div id="output"></div>

    <script>
        function log(msg) {
            document.getElementById('output').innerHTML += msg + '<br>';
            console.log(msg);
        }

        log('Starting script loading test...');
        log('Current URL: ' + window.location.href);
        log('Base URL would be: ' + window.location.origin);

        // Test 1: Try to load the script
        log('=== Test 1: Loading camera-banner.js ===');
        const script = document.createElement('script');
        script.src = '/public/js/camera-banner.js?v=' + Date.now();
        
        script.onload = function() {
            log('✓ Script loaded successfully');
            log('- CameraBanner class available: ' + (typeof CameraBanner !== 'undefined'));
            log('- window.cameraBanner exists: ' + (typeof window.cameraBanner !== 'undefined'));
            
            if (typeof CameraBanner !== 'undefined') {
                log('✓ CameraBanner class is available');
                
                if (window.cameraBanner) {
                    log('✓ window.cameraBanner instance exists');
                    log('- Version: ' + window.cameraBanner.version);
                } else {
                    log('⚠ window.cameraBanner instance not created, trying to create...');
                    try {
                        window.cameraBanner = new CameraBanner();
                        log('✓ Successfully created CameraBanner instance');
                        log('- Version: ' + window.cameraBanner.version);
                    } catch (error) {
                        log('✗ Failed to create CameraBanner instance: ' + error.message);
                        log('- Stack: ' + error.stack);
                    }
                }
            } else {
                log('✗ CameraBanner class not available after script load');
            }
        };
        
        script.onerror = function(error) {
            log('✗ Script loading failed');
            log('- Error: ' + error);
            log('- Script src: ' + script.src);
            
            // Try alternative path
            log('=== Trying alternative path ===');
            const script2 = document.createElement('script');
            script2.src = './public/js/camera-banner.js?v=' + Date.now();
            script2.onload = function() {
                log('✓ Alternative path worked');
            };
            script2.onerror = function() {
                log('✗ Alternative path also failed');
                
                // Try direct fetch to see if file exists
                fetch('/public/js/camera-banner.js')
                    .then(response => {
                        log('Fetch response status: ' + response.status);
                        if (response.ok) {
                            return response.text();
                        } else {
                            throw new Error('HTTP ' + response.status);
                        }
                    })
                    .then(text => {
                        log('✓ File exists and is readable');
                        log('- File size: ' + text.length + ' characters');
                        log('- First 100 chars: ' + text.substring(0, 100));
                    })
                    .catch(error => {
                        log('✗ File fetch failed: ' + error.message);
                    });
            };
            document.head.appendChild(script2);
        };
        
        document.head.appendChild(script);
    </script>
</body>
</html>
-- Add missing notification preference columns to existing database
-- Run this script on existing installations

-- Add the new columns if they don't exist
SET @sql = '';
SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'event_reminders' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN event_reminders TINYINT(1) DEFAULT 1 AFTER toast_notifications;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'registration_updates' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN registration_updates TINYINT(1) DEFAULT 1 AFTER event_reminders;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'judging_updates' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN judging_updates TINYINT(1) DEFAULT 1 AFTER registration_updates;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'award_notifications' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN award_notifications TINYINT(1) DEFAULT 1 AFTER judging_updates;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'system_announcements' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN system_announcements TINYINT(1) DEFAULT 1 AFTER award_notifications;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'user_notification_preferences' 
AND COLUMN_NAME = 'reminder_times' 
AND TABLE_SCHEMA = DATABASE();

SET @sql = IF(@col_exists = 0, 'ALTER TABLE user_notification_preferences ADD COLUMN reminder_times VARCHAR(255) DEFAULT \'[1440, 60]\' AFTER system_announcements;', '');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records with default values
UPDATE user_notification_preferences SET 
    event_reminders = COALESCE(event_reminders, 1),
    registration_updates = COALESCE(registration_updates, 1),
    judging_updates = COALESCE(judging_updates, 1),
    award_notifications = COALESCE(award_notifications, 1),
    system_announcements = COALESCE(system_announcements, 1),
    reminder_times = COALESCE(reminder_times, '[1440, 60]');

SELECT 'Notification preferences columns added successfully!' as Result;
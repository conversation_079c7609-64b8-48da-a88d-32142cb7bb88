<!DOCTYPE html>
<html>
<head>
    <title>Mobile Banner Test</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .camera-banner {
            width: 100%;
            height: 150px;
            border: 2px solid #ccc;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            position: relative;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        .controls button {
            display: block;
            width: 100%;
            margin: 10px 0;
            padding: 15px;
            font-size: 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h2>Mobile Banner Test</h2>
    
    <div class="status" id="status">
        Loading banner system...
    </div>
    
    <div class="camera-banner" id="camera-banner-content">
        <img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Static Logo" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">
    </div>
    
    <div class="controls">
        <button onclick="checkBannerSystem()">Check Banner System Status</button>
        <button onclick="forceBannerInit()">Force Banner Initialization</button>
        <button onclick="startBannerRotation()">Start Banner Rotation</button>
        <button onclick="stopBannerRotation()">Stop Banner Rotation</button>
        <button onclick="resetToLogo()">Reset to Logo</button>
    </div>
    
    <div class="debug-info" id="debug-info">
        Debug info will appear here...
    </div>

    <!-- Load the camera banner script -->
    <script src="/public/js/camera-banner.js"></script>
    
    <script>
        function updateStatus(msg, color = '#007bff') {
            const status = document.getElementById('status');
            status.innerHTML = msg;
            status.style.borderLeftColor = color;
        }
        
        function addDebugInfo(msg) {
            const debug = document.getElementById('debug-info');
            const timestamp = new Date().toLocaleTimeString();
            debug.innerHTML += `[${timestamp}] ${msg}<br>`;
            debug.scrollTop = debug.scrollHeight;
        }
        
        function checkBannerSystem() {
            addDebugInfo('=== Checking Banner System ===');
            
            if (window.cameraBanner) {
                addDebugInfo('✓ CameraBanner instance exists');
                addDebugInfo('- Version: ' + window.cameraBanner.version);
                addDebugInfo('- Banners loaded: ' + (window.cameraBanner.banners ? window.cameraBanner.banners.length : 'none'));
                
                if (window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                    updateStatus(`✓ Banner system ready with ${window.cameraBanner.banners.length} banners`, 'green');
                    window.cameraBanner.banners.forEach((banner, i) => {
                        addDebugInfo(`  ${i+1}. ${banner.type} - ${banner.text || banner.alt_text} (Logo: ${banner.is_logo})`);
                    });
                } else {
                    updateStatus('⚠ Banner system loaded but no banners found', 'orange');
                }
            } else {
                addDebugInfo('✗ CameraBanner instance not found');
                updateStatus('✗ Banner system not loaded', 'red');
            }
        }
        
        function forceBannerInit() {
            addDebugInfo('=== Force Banner Initialization ===');
            updateStatus('Forcing banner initialization...', 'orange');
            
            if (window.forceBannerInit) {
                window.forceBannerInit().then(bannerCount => {
                    addDebugInfo(`✓ Force init complete: ${bannerCount} banners loaded`);
                    updateStatus(`✓ Force initialization complete: ${bannerCount} banners`, 'green');
                }).catch(error => {
                    addDebugInfo('✗ Force init failed: ' + error.message);
                    updateStatus('✗ Force initialization failed', 'red');
                });
            } else {
                addDebugInfo('✗ forceBannerInit function not available');
                updateStatus('✗ Force init function not available', 'red');
            }
        }
        
        function startBannerRotation() {
            addDebugInfo('=== Starting Banner Rotation ===');
            
            if (window.cameraBanner && window.cameraBanner.banners && window.cameraBanner.banners.length > 0) {
                window.cameraBanner.startRotation('camera-banner-content');
                addDebugInfo('✓ Banner rotation started');
                updateStatus('✓ Banner rotation active', 'green');
            } else {
                addDebugInfo('✗ Cannot start rotation - banners not loaded');
                updateStatus('✗ Cannot start rotation', 'red');
            }
        }
        
        function stopBannerRotation() {
            if (window.cameraBanner && typeof window.cameraBanner.stopRotation === 'function') {
                window.cameraBanner.stopRotation();
                addDebugInfo('✓ Banner rotation stopped');
                updateStatus('Banner rotation stopped', 'blue');
            } else {
                addDebugInfo('✗ Cannot stop rotation');
                updateStatus('✗ Cannot stop rotation', 'red');
            }
        }
        
        function resetToLogo() {
            const container = document.getElementById('camera-banner-content');
            if (container) {
                container.innerHTML = '<img src="/uploads/branding/logo_1751468505_rides_logo.png" alt="Static Logo" style="max-height: 100%; max-width: 100%; height: auto; width: auto; object-fit: contain;">';
                addDebugInfo('✓ Reset to static logo');
                updateStatus('Reset to static logo', 'blue');
            }
        }

        // Check if script loaded
        function checkScriptLoading() {
            addDebugInfo('=== Script Loading Check ===');
            addDebugInfo('- CameraBanner class available: ' + (typeof CameraBanner !== 'undefined'));
            addDebugInfo('- window.cameraBanner exists: ' + (typeof window.cameraBanner !== 'undefined'));
            addDebugInfo('- forceBannerInit available: ' + (typeof window.forceBannerInit !== 'undefined'));
            
            if (typeof CameraBanner === 'undefined') {
                addDebugInfo('✗ CameraBanner class not loaded - script loading failed');
                updateStatus('✗ Script loading failed', 'red');
                
                // Try to manually load the script
                addDebugInfo('Attempting to manually load script...');
                const script = document.createElement('script');
                script.src = '/public/js/camera-banner.js?v=' + Date.now();
                script.onload = function() {
                    addDebugInfo('✓ Script manually loaded');
                    setTimeout(checkBannerSystem, 500);
                };
                script.onerror = function() {
                    addDebugInfo('✗ Manual script loading also failed');
                    updateStatus('✗ Cannot load camera-banner.js', 'red');
                };
                document.head.appendChild(script);
            }
        }

        // Auto-check when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                addDebugInfo('Page loaded, checking script and banner system...');
                checkScriptLoading();
                checkBannerSystem();
            }, 1000);
        });
    </script>
</body>
</html>
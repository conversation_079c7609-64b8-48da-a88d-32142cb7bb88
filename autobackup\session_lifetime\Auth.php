<?php
/**
 * Auth Class
 * 
 * This class handles user authentication, including login, registration,
 * password hashing, and role-based access control.
 */
class Auth {
    private $db;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
    }
    
    /**
     * Register a new user
     * 
     * @param string $name User's name
     * @param string $email User's email
     * @param string $password User's password
     * @param string $role User's role (default: 'user')
     * @return bool|int False on failure, user ID on success
     */
    public function register($name, $email, $password, $role = 'user', $phone = null, $address = null, $city = null, $state = null, $zip = null) {
        // Validate inputs
        $name = trim($name);
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($name) || empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Password strength validation
        if (strlen($password) < 6) {
            return false; // Password too short
        }
        
        // Hash password with stronger algorithm and options
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        
        // Check if email already exists
        $this->db->query('SELECT id FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $existingUser = $this->db->single();
        
        if ($existingUser) {
            return false; // Email already exists
        }
        
        // Validate role
        $validRoles = ['admin', 'coordinator', 'judge', 'staff', 'user'];
        if (!in_array($role, $validRoles)) {
            $role = 'user'; // Default to user if invalid role provided
        }
        
        // Insert user
        $this->db->query('INSERT INTO users (name, email, password, role, phone, address, city, state, zip, created_at) 
                          VALUES (:name, :email, :password, :role, :phone, :address, :city, :state, :zip, NOW())');
        $this->db->bind(':name', $name);
        $this->db->bind(':email', $email);
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':role', $role);
        $this->db->bind(':phone', $phone);
        $this->db->bind(':address', $address);
        $this->db->bind(':city', $city);
        $this->db->bind(':state', $state);
        $this->db->bind(':zip', $zip);
        
        try {
            if ($this->db->execute()) {
                return $this->db->lastInsertId();
            } else {
                error_log('Failed to register user: ' . $email);
                return false;
            }
        } catch (Exception $e) {
            error_log('Exception during user registration: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Register or login a user via Facebook
     * 
     * @param array $fbUserData Facebook user data
     * @param string $accessToken Facebook access token
     * @return bool|int False on failure, user ID on success
     */
    public function facebookAuth($fbUserData, $accessToken = null) {
        // Validate required data
        if (empty($fbUserData['id']) || empty($fbUserData['email'])) {
            error_log('Facebook auth failed: Missing required user data');
            return false;
        }
        
        // First check if user exists by Facebook ID
        $this->db->query('SELECT id, role FROM users WHERE facebook_id = :facebook_id');
        $this->db->bind(':facebook_id', $fbUserData['id']);
        $user = $this->db->single();
        
        // If not found by Facebook ID, check if user exists by email
        if (!$user) {
            $this->db->query('SELECT id, role FROM users WHERE email = :email');
            $this->db->bind(':email', $fbUserData['email']);
            $user = $this->db->single();
        }
        
        if ($user) {
            // User exists, update their info
            $sql = 'UPDATE users SET 
                    facebook_id = :facebook_id,
                    name = :name, 
                    email = :email,';
            
            // Add facebook_token to the update if provided
            if ($accessToken) {
                $sql .= ' facebook_token = :facebook_token,';
            }
            
            $sql .= ' last_login = NOW() 
                    WHERE id = :id';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':id', $user->id);
            
            try {
                $this->db->execute();
                
                // Log successful Facebook login
                error_log('Successful Facebook login/link: ' . $fbUserData['email'] . ' (ID: ' . $user->id . ')');
                
                // Check if the user has explicitly set a default image (meaning they deleted their profile image)
                $this->db->query('SELECT profile_image FROM users WHERE id = :id');
                $this->db->bind(':id', $user->id);
                $userProfile = $this->db->single();
                
                // Only download the Facebook profile image if the user hasn't explicitly set a default image
                if (!$userProfile || $userProfile->profile_image !== 'public/images/profile.png') {
                    // If we have a profile image URL from Facebook, try to download it
                    if (isset($fbUserData['picture']['data']['url'])) {
                        error_log('User has not explicitly deleted their profile image, downloading from Facebook');
                        $this->downloadFacebookProfileImage($user->id, $fbUserData['picture']['data']['url']);
                    }
                } else {
                    error_log('User has explicitly deleted their profile image, respecting their choice');
                }
                
                return $user->id;
            } catch (Exception $e) {
                error_log('Error updating user with Facebook data: ' . $e->getMessage());
                return false;
            }
        } else {
            // Create new user
            // Generate a random password for the account (they'll use Facebook to login)
            $randomPassword = password_hash(bin2hex(random_bytes(16)), PASSWORD_BCRYPT, ['cost' => 12]);
            
            // Build the SQL query
            $sql = 'INSERT INTO users (
                    facebook_id, 
                    name, 
                    email, 
                    password,';
            
            // Add facebook_token to the insert if provided
            if ($accessToken) {
                $sql .= ' facebook_token,';
            }
            
            $sql .= ' role, 
                    created_at, 
                    last_login
                  ) VALUES (
                    :facebook_id, 
                    :name, 
                    :email, 
                    :password,';
            
            // Add facebook_token placeholder to the insert if provided
            if ($accessToken) {
                $sql .= ' :facebook_token,';
            }
            
            $sql .= ' :role, 
                    NOW(), 
                    NOW()
                  )';
            
            $this->db->query($sql);
            $this->db->bind(':facebook_id', $fbUserData['id']);
            $this->db->bind(':name', $fbUserData['name']);
            $this->db->bind(':email', $fbUserData['email']);
            $this->db->bind(':password', $randomPassword);
            
            // Bind facebook_token if provided
            if ($accessToken) {
                $this->db->bind(':facebook_token', $accessToken);
            }
            
            $this->db->bind(':role', 'user'); // Default role for Facebook users
            
            try {
                if ($this->db->execute()) {
                    $newUserId = $this->db->lastInsertId();
                    
                    // Log successful Facebook registration
                    error_log('New user registered via Facebook: ' . $fbUserData['email'] . ' (ID: ' . $newUserId . ')');
                    
                    // For new users, we can download the Facebook profile image
                    if (isset($fbUserData['picture']['data']['url'])) {
                        error_log('New user registration, downloading profile image from Facebook');
                        $this->downloadFacebookProfileImage($newUserId, $fbUserData['picture']['data']['url']);
                    }
                    
                    return $newUserId;
                } else {
                    error_log('Failed to create new user via Facebook: ' . $fbUserData['email']);
                    return false;
                }
            } catch (Exception $e) {
                error_log('Exception during Facebook user creation: ' . $e->getMessage());
                return false;
            }
        }
    }
    
    /**
     * Login a user
     * 
     * @param string $email User's email
     * @param string $password User's password
     * @return bool|object False on failure, user object on success
     */
    public function login($email, $password) {
        // Validate inputs
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        
        if (empty($email) || empty($password)) {
            return false; // Required fields missing
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return false; // Invalid email format
        }
        
        // Find user by email
        $this->db->query('SELECT id, name, email, password, role, status FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            // Implement timing attack protection
            password_verify('dummy_password', '$2y$10$abcdefghijklmnopqrstuuVzmd4.QxnCbzrWMs4/QqbDTLxKfLxy.');
            return false; // User not found
        }
        
        // Check if user is active
        if (isset($user->status) && $user->status !== 'active') {
            return false; // Account inactive
        }
        
        // Verify password
        if (password_verify($password, $user->password)) {
            // Check if password needs rehashing (if PHP's password hashing algorithm has been updated)
            if (password_needs_rehash($user->password, PASSWORD_BCRYPT, ['cost' => 12])) {
                $newHash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                $this->db->query('UPDATE users SET password = :password WHERE id = :id');
                $this->db->bind(':password', $newHash);
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            }
            
            // Update last login (try-catch in case the column doesn't exist yet)
            try {
                $this->db->query('UPDATE users SET last_login = NOW() WHERE id = :id');
                $this->db->bind(':id', $user->id);
                $this->db->execute();
            } catch (PDOException $e) {
                // Log the error but continue - this is not critical
                error_log('Error updating last_login: ' . $e->getMessage());
                // If the column doesn't exist, we'll just skip this update
            }
            
            // Log successful login
            error_log('Successful login: ' . $email);
            
            return $user;
        } else {
            // Log failed login attempt
            error_log('Failed login attempt: ' . $email);
            return false; // Password incorrect
        }
    }
    
    /**
     * Check if development bypass is enabled
     * 
     * @return bool
     */
    private function isDevBypassEnabled() {
        static $bypassEnabled = null;
        
        // If we've already checked, return the cached result
        if ($bypassEnabled !== null) {
            return $bypassEnabled;
        }
        
        // Check system_settings table
        try {
            $this->db->query('SELECT setting_value FROM system_settings WHERE setting_key = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $result = $this->db->single();
            
            if ($result && $result->setting_value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking system_settings table for dev bypass: ' . $e->getMessage());
        }
        
        // Check old settings table as fallback
        try {
            $this->db->query('SELECT value FROM settings WHERE name = :key');
            $this->db->bind(':key', 'dev_admin_bypass');
            $oldResult = $this->db->single();
            
            if ($oldResult && $oldResult->value === '1') {
                $bypassEnabled = true;
                return true;
            }
        } catch (Exception $e) {
            error_log('Error checking old settings table for dev bypass: ' . $e->getMessage());
        }
        
        $bypassEnabled = false;
        return false;
    }
    
    /**
     * Check if user is logged in
     * 
     * @return bool
     */
    public function isLoggedIn() {
        // First check if dev bypass is enabled
        if ($this->isDevBypassEnabled()) {
            // If dev bypass is enabled, automatically consider user as logged in
            // Set session variables if they don't exist
            if (!isset($_SESSION['user_id'])) {
                $_SESSION['user_id'] = 0; // Special dev bypass ID
                $_SESSION['user_name'] = 'Developer';
                $_SESSION['user_email'] = '<EMAIL>';
                $_SESSION['user_role'] = 'admin';
            }
            return true;
        }
        
        // Normal login check
        return isset($_SESSION['user_id']);
    }
    
    /**
     * Get current user ID
     * 
     * @return int|null User ID or null if not logged in
     */
    public function getCurrentUserId() {
        return $_SESSION['user_id'] ?? null;
    }
    
    /**
     * Get current user role
     * 
     * @return string|null User role or null if not logged in
     */
    public function getCurrentUserRole() {
        return $_SESSION['user_role'] ?? null;
    }
    
    /**
     * Check if user has a specific role
     * 
     * @param string|array $roles Role or roles to check
     * @return bool
     */
    public function hasRole($roles) {
        // First check if dev bypass is enabled
        if ($this->isDevBypassEnabled()) {
            return true; // Dev bypass grants all roles
        }
        
        // Check if user is logged in
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        // Get user role
        $userRole = $this->getCurrentUserRole();
        
        if (!$userRole) {
            return false;
        }
        
        // If roles is a string, convert to array
        if (!is_array($roles)) {
            $roles = [$roles];
        }
        
        // Check if user has any of the required roles
        if (in_array($userRole, $roles)) {
            return true;
        }
        
        // Check role hierarchy
        $userRoleLevel = $this->getRoleHierarchyLevel($userRole);
        
        if ($userRoleLevel === false) {
            return false; // Unknown role
        }
        
        // If user role is admin, they have access to everything
        if ($userRole === 'admin') {
            return true;
        }
        
        // Check if user's role level is higher than any of the required roles
        if (count($roles) > 1) {
            // Multiple roles - find the highest level required
            $highestRequiredLevel = 0;
            foreach ($roles as $role) {
                $roleLevel = $this->getRoleHierarchyLevel($role);
                if ($roleLevel !== false && $roleLevel > $highestRequiredLevel) {
                    $highestRequiredLevel = $roleLevel;
                }
            }
            
            // Check if user's role level is higher than any required role
            return $userRoleLevel >= $highestRequiredLevel;
        } else {
            // Single role check - either exact match or higher role
            $requiredRoleLevel = $this->getRoleHierarchyLevel($roles[0]);
            return $userRoleLevel >= $requiredRoleLevel;
        }
    }
    
    /**
     * Logout the current user
     * 
     * @return void
     */
    public function logout() {
        unset($_SESSION['user_id']);
        unset($_SESSION['user_role']);
        session_destroy();
    }
    
    /**
     * Generate a password reset token
     * 
     * @param string $email User's email
     * @return bool|string False on failure, token on success
     */
    public function generatePasswordResetToken($email) {
        // Find user by email
        $this->db->query('SELECT id FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            return false; // User not found
        }
        
        // Generate token
        $token = bin2hex(random_bytes(32));
        $hashedToken = password_hash($token, PASSWORD_BCRYPT);
        
        // Set expiration time (1 hour from now)
        $expires = date('Y-m-d H:i:s', time() + 3600);
        
        // Store token in database
        $this->db->query('UPDATE users SET reset_token = :token, reset_expires = :expires WHERE id = :id');
        $this->db->bind(':token', $hashedToken);
        $this->db->bind(':expires', $expires);
        $this->db->bind(':id', $user->id);
        
        if ($this->db->execute()) {
            return $token;
        } else {
            return false;
        }
    }
    
    /**
     * Verify a password reset token
     * 
     * @param string $email User's email
     * @param string $token Reset token
     * @return bool|int False on failure, user ID on success
     */
    public function verifyPasswordResetToken($email, $token) {
        // Find user by email
        $this->db->query('SELECT id, reset_token, reset_expires FROM users WHERE email = :email');
        $this->db->bind(':email', $email);
        $user = $this->db->single();
        
        if (!$user) {
            return false; // User not found
        }
        
        // Check if token exists and is not expired
        if (empty($user->reset_token) || strtotime($user->reset_expires) < time()) {
            return false; // Token expired or not set
        }
        
        // Verify token
        if (password_verify($token, $user->reset_token)) {
            return $user->id;
        } else {
            return false; // Invalid token
        }
    }
    
    /**
     * Reset a user's password
     * 
     * @param int $userId User ID
     * @param string $password New password
     * @return bool
     */
    public function resetPassword($userId, $password) {
        // Validate password
        if (empty($password) || strlen($password) < 6) {
            return false; // Password too short
        }
        
        // Hash password
        $hashedPassword = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
        
        // Update password and clear reset token
        $this->db->query('UPDATE users SET password = :password, reset_token = NULL, reset_expires = NULL WHERE id = :id');
        $this->db->bind(':password', $hashedPassword);
        $this->db->bind(':id', $userId);
        
        return $this->db->execute();
    }
    
    /**
     * Get role hierarchy level
     * 
     * @param string $role Role name
     * @return int|bool Role level or false if role not found
     */
    private function getRoleHierarchyLevel($role) {
        $roleHierarchy = [
            'admin' => 100,
            'coordinator' => 80,
            'judge' => 60,
            'staff' => 40,
            'user' => 20
        ];
        
        return $roleHierarchy[$role] ?? false;
    }
    
    /**
     * Download and store a Facebook profile image
     * 
     * @param int $userId User ID
     * @param string $imageUrl Facebook profile image URL
     * @return bool
     */
    private function downloadFacebookProfileImage($userId, $imageUrl) {
        try {
            // Validate inputs
            if (empty($userId) || empty($imageUrl)) {
                error_log('Auth::downloadFacebookProfileImage - Invalid parameters');
                return false;
            }
            
            // Download the image
            $imageContent = null;
            
            // First try file_get_contents if allow_url_fopen is enabled
            if (ini_get('allow_url_fopen')) {
                $imageContent = @file_get_contents($imageUrl);
                
                if ($imageContent === false) {
                    error_log('Auth::downloadFacebookProfileImage - Failed to download image with file_get_contents');
                    $imageContent = null;
                }
            }
            
            // If file_get_contents failed or is disabled, try cURL
            if ($imageContent === null && function_exists('curl_init')) {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $imageUrl);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                $imageContent = curl_exec($ch);
                
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode != 200 || empty($imageContent)) {
                    error_log('Auth::downloadFacebookProfileImage - HTTP error: ' . $httpCode);
                    return false;
                }
            }
            
            // Verify the image content is valid
            if (strlen($imageContent) < 100) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded image is too small: ' . strlen($imageContent) . ' bytes');
                return false;
            }
            
            // Check if the content is a valid image
            $tempImageCheck = @imagecreatefromstring($imageContent);
            if ($tempImageCheck === false) {
                error_log('Auth::downloadFacebookProfileImage - Downloaded content is not a valid image');
                return false;
            }
            imagedestroy($tempImageCheck);
            
            // Create a temporary file
            $tempFile = tempnam(sys_get_temp_dir(), 'fb_img_');
            $bytesWritten = file_put_contents($tempFile, $imageContent);
            
            if ($bytesWritten === false) {
                error_log('Auth::downloadFacebookProfileImage - Failed to write to temporary file: ' . $tempFile);
                return false;
            }
            
            // Create a file array similar to $_FILES
            $fileArray = [
                'name' => 'facebook_profile.jpg',
                'type' => 'image/jpeg',
                'tmp_name' => $tempFile,
                'error' => 0,
                'size' => filesize($tempFile)
            ];
            
            // Load image editor model
            require_once APPROOT . '/models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();
            
            // First, delete any existing profile images for this user
            $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
            $this->db->bind(':entity_type', 'user');
            $this->db->bind(':entity_id', $userId);
            $existingImages = $this->db->resultSet();
            
            // Delete each existing image
            foreach ($existingImages as $image) {
                $imageEditorModel->deleteImage($image->id);
            }
            
            // Process the image upload
            $uploadDir = 'uploads/users/';
            
            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                if (!mkdir($uploadDir, 0755, true)) {
                    error_log('Auth::downloadFacebookProfileImage - Failed to create upload directory: ' . $uploadDir);
                    @unlink($tempFile);
                    return false;
                }
            }
            
            // Process the image upload
            $imageData = $imageEditorModel->processImageUpload(
                $fileArray, 
                'user', 
                $userId, 
                $uploadDir,
                $userId,
                true // Set as primary image
            );
            
            // Clean up the temporary file
            @unlink($tempFile);
            
            if ($imageData) {
                error_log('Auth::downloadFacebookProfileImage - Successfully processed image upload for user ' . $userId);
                
                // Clear the profile_image field in the users table to avoid confusion
                // This ensures we only use the images table for profile images
                $this->db->query('UPDATE users SET profile_image = NULL WHERE id = :id');
                $this->db->bind(':id', $userId);
                $this->db->execute();
                
                return true;
            } else {
                error_log('Auth::downloadFacebookProfileImage - Failed to process image upload for user ' . $userId);
                return false;
            }
            
        } catch (Exception $e) {
            error_log('Auth::downloadFacebookProfileImage - Exception: ' . $e->getMessage());
            return false;
        }
    }
}
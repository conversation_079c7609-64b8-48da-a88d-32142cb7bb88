<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1><?php echo isset($title) ? $title : 'Add New Show'; ?></h1>
            <?php if (!isset($is_admin) || !$is_admin): ?>
            <p class="lead">Create a new car show or event.</p>
            <?php endif; ?>
        </div>
        <div class="col-md-6 text-end">
            <?php if (isset($is_admin) && $is_admin): ?>
            <a href="<?php echo BASE_URL; ?>/form_designer/design_admin_show_form" class="btn btn-outline-primary me-2">
                <i class="fas fa-edit me-2"></i> Edit Form Template
            </a>
            <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php else: ?>
            <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back
            </a>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (isset($listing_fee) && $listing_fee > 0): ?>
    <div class="alert alert-info mb-4">
        <div class="d-flex align-items-center">
            <div class="me-3">
                <i class="fas fa-info-circle fa-2x"></i>
            </div>
            <div>
                <h5 class="mb-1">Listing Fee: $<?php echo number_format($listing_fee, 2); ?> (<?php echo isset($listing_fee_type) && $listing_fee_type == 'monthly' ? 'Monthly' : 'Per Show'; ?>)</h5>
                <?php if (!isset($is_admin) || !$is_admin): ?>
                <p class="mb-0">This is the amount you will be charged for listing your show. After submitting, you'll be directed to a payment page to complete your listing.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Show Details</h5>
            <span class="badge bg-light text-primary">Using Template: <?php echo $template->name; ?></span>
        </div>
        <div class="card-body">
            <form action="<?php echo BASE_URL; ?>/admin/addShow" method="post" id="show-form">
                <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                
                <?php
                // Check if template exists
                if (!isset($template) || !is_object($template)) {
                    echo '<div class="alert alert-danger">Error: Form template not found or invalid.</div>';
                    $fields = [];
                } else {
                    // Parse the template fields
                    $fields = json_decode($template->fields);
                    
                    // Add error handling for JSON parsing
                    if ($fields === null && json_last_error() !== JSON_ERROR_NONE) {
                        echo '<div class="alert alert-danger">Error parsing form template: ' . json_last_error_msg() . '</div>';
                        $fields = [];
                    }
                    
                    // Debug information (only in development)
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        echo '<!-- Template ID: ' . $template->id . ' -->';
                        echo '<!-- Template Name: ' . $template->name . ' -->';
                        echo '<!-- Field Count: ' . count($fields) . ' -->';
                    }
                }
                
                // Group fields by row
                $rows = [];
                foreach ($fields as $field) {
                    // Skip if field doesn't have an ID
                    if (!isset($field->id)) {
                        continue;
                    }
                    
                    $rowIndex = $field->row ?? 0;
                    if (!isset($rows[$rowIndex])) {
                        $rows[$rowIndex] = [];
                    }
                    $rows[$rowIndex][] = $field;
                }
                
                // Sort rows by index
                ksort($rows);
                
                // Check if we have any rows to render
                if (empty($rows)) {
                    echo '<div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        No form fields found in the template. Please 
                        <a href="' . BASE_URL . '/form_designer/design_admin_show_form" class="alert-link">edit the form template</a> 
                        to add fields.
                    </div>';
                    
                    // Add default fields as a fallback
                    echo '<div class="row mb-3">
                        <div class="col-md-6">
                            <label for="name" class="form-label">Show Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="' . $name . '" required>
                        </div>
                        <div class="col-md-6">
                            <label for="location" class="form-label">Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="location" name="location" value="' . $location . '" required>
                        </div>
                    </div>';
                }
                
                // Render each row
                foreach ($rows as $rowIndex => $rowFields) {
                    echo '<div class="row mb-3">';
                    
                    // Render each field in the row
                    foreach ($rowFields as $field) {
                        // Get field width class
                        $widthClass = $field->width ?? 'col-md-12';
                        
                        echo '<div class="' . $widthClass . '">';
                        
                        // Get field value from data if available
                        $fieldId = $field->id;
                        $fieldValue = '';
                        
                        // First check if we have the value in the main data array
                        if (isset($$fieldId)) {
                            $fieldValue = $$fieldId;
                        } 
                        // Then check if it's in the data['data'] array (for template fields)
                        elseif (isset($data) && isset($data[$fieldId])) {
                            $fieldValue = $data[$fieldId];
                        }
                        // Finally, use the default value from the field if available
                        elseif (isset($field->default)) {
                            $fieldValue = $field->default;
                        }
                        
                        // Get error message if available
                        $errorVar = $fieldId . '_err';
                        $errorMsg = '';
                        
                        if (isset($$errorVar)) {
                            $errorMsg = $$errorVar;
                        } elseif (isset($data) && isset($data[$errorVar])) {
                            $errorMsg = $data[$errorVar];
                        }
                        
                        // Render field based on type
                        switch ($field->type) {
                            case 'text':
                            case 'email':
                            case 'tel':
                            case 'url':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Special handling for fee fields
                                if ($fieldId === 'registration_fee' || $fieldId === 'listing_fee') {
                                    $step = '0.01';
                                    $min = '0';
                                    $value = isset($$fieldId) ? $$fieldId : '0.00';
                                    
                                    // If it's registration_fee, check if it should be disabled
                                    $disabled = '';
                                    if ($fieldId === 'registration_fee' && isset($is_free) && $is_free) {
                                        $disabled = ' disabled';
                                        $value = '0.00';
                                    }
                                    
                                    echo '<input type="number" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $value . '" step="' . $step . '" min="' . $min . '"' . ($field->required ? ' required' : '') . $disabled . '>';
                                } else {
                                    echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                }
                                
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
                                
                            case 'date':
                            case 'time':
                            case 'datetime-local':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                echo '<input type="' . $field->type . '" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
                                
                            case 'textarea':
                            case 'richtext':
                                // Debug logging for textarea/richtext field
                                error_log("Add Template: Processing textarea/richtext field: {$field->id}");
                                error_log("Add Template: Field value length: " . strlen((string)$fieldValue));
                                
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                
                                // Add a hidden field to ensure the field is always included in the form submission
                                // This prevents the field from being lost if it's empty
                                echo '<input type="hidden" name="_has_' . $field->id . '" value="1">';
                                
                                echo '<textarea class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" rows="5"' . ($field->required ? ' required' : '') . '>' . htmlspecialchars($fieldValue) . '</textarea>';
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
                                
                            case 'select':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                echo '<select class="form-select' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '"' . ($field->required ? ' required' : '') . '>';
                                
                                // Add placeholder option if available
                                if (!empty($field->placeholder)) {
                                    echo '<option value="">' . $field->placeholder . '</option>';
                                }
                                
                                // Special handling for coordinator_id field
                                if ($fieldId === 'coordinator_id') {
                                    // If admin, show list of coordinators
                                    if (isset($is_admin) && $is_admin && isset($coordinators) && is_array($coordinators)) {
                                        foreach ($coordinators as $coordinator) {
                                            $selected = ($fieldValue == $coordinator->id) ? ' selected' : '';
                                            echo '<option value="' . $coordinator->id . '"' . $selected . '>' . htmlspecialchars($coordinator->name) . '</option>';
                                        }
                                    } 
                                    // If coordinator, use their own ID
                                    else {
                                        echo '<option value="' . $user_id . '" selected>' . htmlspecialchars($user_name) . '</option>';
                                    }
                                }
                                // Special handling for status field
                                elseif ($fieldId === 'status') {
                                    $statusOptions = [
                                        'draft' => 'Draft',
                                        'published' => 'Published',
                                        'cancelled' => 'Cancelled',
                                        'completed' => 'Completed'
                                    ];
                                    
                                    foreach ($statusOptions as $value => $label) {
                                        $selected = ($fieldValue == $value) ? ' selected' : '';
                                        echo '<option value="' . $value . '"' . $selected . '>' . $label . '</option>';
                                    }
                                }
                                // For other select fields, use options from the field definition
                                elseif (isset($field->options) && is_array($field->options)) {
                                    foreach ($field->options as $option) {
                                        if (isset($option->value) && isset($option->label)) {
                                            $selected = ($fieldValue == $option->value) ? ' selected' : '';
                                            echo '<option value="' . $option->value . '"' . $selected . '>' . $option->label . '</option>';
                                        }
                                    }
                                }
                                
                                echo '</select>';
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
                                
                            case 'checkbox':
                                echo '<div class="form-check mt-4">';
                                $checked = $fieldValue ? ' checked' : '';
                                
                                // Special handling for is_free field
                                if ($fieldId === 'is_free') {
                                    echo '<input class="form-check-input" type="checkbox" id="' . $field->id . '" name="' . $field->id . '"' . $checked . ' onchange="toggleRegistrationFee(this)">';
                                } else {
                                    echo '<input class="form-check-input" type="checkbox" id="' . $field->id . '" name="' . $field->id . '"' . $checked . '>';
                                }
                                
                                echo '<label class="form-check-label" for="' . $field->id . '">' . $field->label . '</label>';
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                echo '</div>';
                                break;
                                
                            case 'image':
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                echo '<input type="file" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '"' . ($field->required ? ' required' : '') . ' accept="image/*">';
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                                break;
                                
                            default:
                                // For any other field types, render as text input
                                echo '<label for="' . $field->id . '" class="form-label">' . $field->label;
                                if ($field->required) {
                                    echo ' <span class="text-danger">*</span>';
                                }
                                echo '</label>';
                                echo '<input type="text" class="form-control' . (!empty($errorMsg) ? ' is-invalid' : '') . '" id="' . $field->id . '" name="' . $field->id . '" value="' . $fieldValue . '"' . ($field->required ? ' required' : '') . '>';
                                if (!empty($errorMsg)) {
                                    echo '<div class="invalid-feedback">' . $errorMsg . '</div>';
                                }
                                if (!empty($field->placeholder)) {
                                    echo '<div class="form-text">' . $field->placeholder . '</div>';
                                }
                        }
                        
                        echo '</div>'; // Close field column
                    }
                    
                    echo '</div>'; // Close row
                }
                ?>
                
                <div class="mt-4">
                    <?php if (isset($is_admin) && $is_admin): ?>
                    <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Create Show
                    </button>
                    <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/coordinator/dashboard" class="btn btn-secondary me-2">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> Create Show
                    </button>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Next Steps</h5>
                </div>
                <div class="card-body">
                    <p>After saving your show, you'll be able to:</p>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-images fa-3x mb-3 text-success"></i>
                                    <h5 class="card-title">Add Show Images</h5>
                                    <p class="card-text">Upload and edit promotional images for your show using our advanced Image Editor. These images will be displayed on the public show page.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-wpforms fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">Customize Registration Form</h5>
                                    <p class="card-text">Create a custom registration form with our Visual Form Builder.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-list-alt fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">Add Categories</h5>
                                    <p class="card-text">Set up judging categories and criteria for your show.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (!isset($is_admin) || !$is_admin): ?>
    <div class="row mt-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle me-2"></i> Next Steps</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-images fa-3x text-primary mb-3"></i>
                                    <h5>Add Images</h5>
                                    <p>Upload photos of your event to attract more participants.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-list-alt fa-3x text-success mb-3"></i>
                                    <h5>Set Up Categories</h5>
                                    <p>Create vehicle categories for your show to organize registrations.</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-share-alt fa-3x text-info mb-3"></i>
                                    <h5>Share Your Show</h5>
                                    <p>Promote your event on social media to increase participation.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-update end date when start date changes (if end date is empty or before start date)
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        if (startDateInput && endDateInput) {
            startDateInput.addEventListener('change', function() {
                if (!endDateInput.value || new Date(endDateInput.value) < new Date(startDateInput.value)) {
                    endDateInput.value = startDateInput.value;
                }
            });
        }
        
        // Auto-update registration end date when start date changes (if reg end date is empty or after show start)
        const regEndInput = document.getElementById('registration_end');
        
        if (startDateInput && regEndInput) {
            startDateInput.addEventListener('change', function() {
                const startDate = new Date(startDateInput.value);
                const regEndDate = new Date(regEndInput.value);
                
                // If registration end date is after show start date or empty, set it to day before show
                if (!regEndInput.value || regEndDate > startDate) {
                    const dayBefore = new Date(startDate);
                    dayBefore.setDate(dayBefore.getDate() - 1);
                    regEndInput.value = dayBefore.toISOString().split('T')[0];
                }
            });
        }
    });
    
    // Toggle registration fee field based on is_free checkbox
    function toggleRegistrationFee(checkbox) {
        const regFeeInput = document.getElementById('registration_fee');
        if (regFeeInput) {
            regFeeInput.disabled = checkbox.checked;
            if (checkbox.checked) {
                regFeeInput.value = '0.00';
            }
        }
    }
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>